<!-- @format -->

<template>
    <li @click="showCZTClick()">
        <i class="swfic" :class="{ on: showCZT }"></i> <span>插值图</span>
    </li>
</template>

<script>
import { getScz } from '@/api/gis/3D/AirMap/index';
export default {
    data() {
        return {
            layerID: '空气插值',
            showCZT: false,
            interpolationLayer: null //插值库
        };
    },
    props: ['map', 'curentTime', 'selectYZ'],

    unmounted() {
        this.clear();
    },

    mounted() {
        this.initPage();
    },
    methods: {
        initPage() {
            if (!this.map) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            let param = {
                layerName: this.layerID,
                upLayerName: '区划界线_乡镇街道境界线',
                map: this.map,
                renderType: 2, // 1：渐变效果  2：分级展示
                yzColor: null,
                yzRange: null,
                initExt: {
                    xmin: 73,
                    ymin: 18,
                    xmax: 136,
                    ymax: 54
                }
            };

            this.interpolationLayer = new PowerGL.InterpolationLayerCanvas(
                param
            );
        },

        showCZTClick() {
            this.showCZT = !this.showCZT;
            this.showCZTHandle();
        },

        showCZTHandle() {
            if (this.showCZT) {
                this.getCZData();
            } else {
                this.clear();
            }
        },

        getCZData() {
            if (this.showCZT) {
                //1、请求接口JSON
                // this.showData();

                //2、直接请求JSON
                // this.showData2();

                //3、请求图片
                this.showData3();
            }
        },

        //1、请求接口JSON
        showData() {
            this.getScz(this.curentTime, this.selectYZ, (flag, result) => {
                if (flag && result && result.data && result.data[0]) {
                    this.interpolationLayer.makeCanvasImg(
                        result.data,
                        this.selectYZ
                    );
                } else {
                    //清除图层
                    this.clear();
                }
            });
        },

        //2、直接请求json文件，一般本地测试
        showData2() {
            let url = './gis/3D/data/2023062900_AQI.json';
            fetch(url)
                .then(function (response) {
                    return response.json();
                })
                .then((json) => {
                    let result = json;
                    this.interpolationLayer.makeCanvasImg(result, 'AQI');
                })
                .catch(function (ex) {
                    console.log('parsing failed', ex);
                });
        },

        //3、直接读取文件，文件比JSON数据小，加载快
        showData3() {
            let url = this.getUrl();
            this.interpolationLayer.loadImage(url, this.selectYZ);
        },

        getUrl() {
            let year = this.curentTime.substring(0, 4);
            let month = year + this.curentTime.substring(5, 7);
            let day = month + this.curentTime.substring(8, 10);
            let hour = day + this.curentTime.substring(11, 13);

            let url =
                ServerGlobalConstant.czUrl +
                `/${year}/${month}/${day}/${hour}_${this.selectYZ}.png`;

            // 测试数据
            url = './gis/3D/data/2023072800_AQI.png';

            return url;
        },

        getScz(currentDate, yz, callBack) {
            getScz({
                date: currentDate + ':00',
                type: 'scz',
                wrw: yz
            }).then(
                (res) => {
                    if (callBack) {
                        callBack(true, res);
                    }
                },
                (res) => {
                    if (callBack) {
                        callBack(false, res);
                    }
                }
            );
        },

        clear() {
            if (this.interpolationLayer) {
                this.interpolationLayer.removeLayerFromName(this.layerID);
            }
        }
    },

    watch: {
        curentTime(val) {
            this.showCZTHandle();
        },
        selectYZ() {
            this.showCZTHandle();
        }
    }
};
</script>

<style></style>
