/**
 * /*
 *
 * @format
 * @Author: Caijw
 * @LastEditors: Caijw
 * @Description: localStorage 工具
 * @Date: 2019-03-28 08:53:24
 * @LastEditTime: 2019-03-28 11:30:49
 */

//设置本地存储
export const setLocal = (key, value) => {
    if (typeof value == 'object') {
        value = JSON.stringify(value);
    }
    localStorage.setItem(key, value);
};
//获取本地存储
export const getLocal = (key) => {
    return localStorage.getItem(key);
};
