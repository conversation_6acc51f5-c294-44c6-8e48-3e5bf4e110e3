/** @format */

export default [
    // {
    //     path: '/gis3D',
    //     name: 'gis3D',
    //     component: () => import('_v/gis/index.vue'),
    //     children: [
    //         {
    //             path: 'Basic',
    //             component: () =>
    //                 import('_v/gis/3D/BasicFramework/BasicFrameworkLayout.vue')
    //         }
    //     ]
    // },
    // {
    //     path: '/gis2D',
    //     name: 'gis2D',
    //     component: () => import('_v/gis/index.vue'),
    //     children: [
    //         {
    //             path: 'Basic',
    //             component: () =>
    //                 import('_v/gis/2D/BasicFramework/BasicFrameworkLayout.vue')
    //         }
    //     ]
    // },
    {
        path: '/gis',
        name: 'gis',
        component: () => import('_v/gis/Layout.vue'),
        children: [
            {
                path: 'air',
                name: 'air',
                component: () => import('@/views/gis/3D/AirMap/Layout.vue')
            },
            {
                path: 'water',
                name: 'water',
                component: () => import('_v/gis/3D/WaterMap/Layout.vue')
            },
            {
                path: 'basic',
                name: 'basic',
                component: () => import('_v/gis/3D/Basic/Layout.vue')
            },
            {
                path: 'demo',
                name: 'demo',
                component: () => import('_v/gis/3D/TestDemo/Layout.vue')
            }
        ]
    },
    {
        path: '/lidar',
        name: 'lidar',
        component: () => import('_v/gis/3D/Lidar/LidarLayout')
    },

    {
        path: '/AirJudgmentMap',
        name: 'AirJudgmentMap',
        component: () => import('_v/gis/3D/AirJudgmentMap/Layout')
    },

    {
        path: '/2d/getPoint',
        name: 'getPoint2D',
        component: () => import('_v/gis/2D/GetPoint/GetPointLayout')
    },
    {
        path: '/3d/getPoint',
        name: 'getPoint3D',
        component: () => import('_v/gis/3D/GetPoint/GetPointLayout')
    }
];
