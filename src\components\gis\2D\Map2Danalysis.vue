<!-- @format -->
<!-- mapbox 标签业务组件 -->
<template>
    <div></div>
</template>

<script>
import * as turf from '@turf/turf';
export default {
    data() {
        return {
            areaMarkers: [],
            pointSize: 'min',
            geometry: null,
            index: 0,
            timer: null
        };
    },
    props: ['map', 'markerParam'],
    components: {},
    mounted() {
        this.initEvent();
    },
    unmounted() {
        this.clear();

        PowerGis.removeCustomHtmlLayer('距离文字');
        PowerGis.removeLayer(this.map, '绘制扇形');
        PowerGis.removeLayer(this.map, '污染源点位');

        clearTimeout(this.timer);
        this.timer = null;

        // powerGis.removeLayerFromName(map, name);
    },
    methods: {
        initEvent() {
            // console.log(this.map);
            if (!this.map) {
                setTimeout(() => {
                    this.initEvent();
                }, 200);
                return;
            }
            let self = this;
            // this.map.on('zoomend', (evt) => {
            //     self.zoomHandle();
            // });
            this.map.on('zoom-end', (evt) => {
                self.zoomHandle();
            });
        },
        //地图缩放事件处理
        zoomHandle() {
            let zoom = this.map.getZoom();
            let fjzoom = this.markerParam.FJZOOM || 10;
            if (
                (zoom >= fjzoom && this.pointSize == 'min') ||
                (zoom < fjzoom && this.pointSize == 'max')
            ) {
                this.pointSize = this.pointSize == 'max' ? 'min' : 'max';
                this.addMarkers();
            }
        },

        //绘制标签
        addMarkers() {
            switch (this.markerParam.centraltype) {
                case '大气站点1':
                case '大气站点2':
                case '大气站点3':
                    this.addAirPoint();
                    break;
                case '水质站点':
                    this.addWaterPoint();
                    break;
            }
        },
        addSFXfw() {
            let JD = parseFloat(this.markerParam.centralPoint[0].JD);
            let WD = parseFloat(this.markerParam.centralPoint[0].WD);
            let esriarr = [];
            let geoarrpoint = [];
            let bearing1 = this.markerParam.angle - 45;
            let bearing2 = this.markerParam.angle + 45;
            let getColor = PowerGL.segmentedColorScale([
                [0, [191, 136, 0]],
                [50, [201, 141, 12]],
                [100, [204, 159, 90]]
            ]);
            let getColor2 = PowerGL.segmentedColorScale([
                [0, [247, 8, 3]],
                [50, [247, 123, 18]],
                [100, [215, 187, 55]]
            ]);
            this.markerParam.distanc.forEach((radius) => {
                let color = getColor(
                    radius,
                    0.5,
                    this.markerParam.distanc[0],
                    this.markerParam.distanc[
                        this.markerParam.distanc.length - 1
                    ]
                );
                let item = {
                    // title: '博沃智慧' + i, //鼠标移入提示必须字段、如果没有则不提示
                    JD: JD,
                    WD: WD,
                    // ClickType: 'ZD_CLICK', //postmessage 点击类型设置 没有则无点击事件
                    radis: radius * 1000, //扇形半径
                    startAngle: bearing1, //开始角度
                    endAngle: bearing2, //结束角度
                    option: {
                        //该对象是设置不同图片的、没有则使用默认图片
                        tcstyle: 'STYLE_SOLID', //填充样式
                        color: color, //填充颜色
                        lineColor: [236, 204, 104], //设置符号线的颜色
                        style: 'STYLE_SOLID', //设置线的样式  详见：http://172.16.10.33/arcgis_js_v321_sdk/arcgis_js_api/sdk/jsapi/simplelinesymbol-amd.html#simplelinesymbol2  -> Constants
                        lineWidth: 1 //先的宽度
                    }
                };
                esriarr.push(item);

                let color2 = getColor2(
                    radius,
                    1,
                    this.markerParam.distanc[0],
                    this.markerParam.distanc[
                        this.markerParam.distanc.length - 1
                    ]
                );
                let options = {
                    units: 'kilometers',
                    properties: { name: radius + '公里', color: color2 }
                };
                let destination = turf.destination(
                    [JD, WD],
                    radius,
                    bearing2,
                    options
                );

                // geoarrpoint.push({
                //     ...destination.properties,
                //     htmlTemplate: `<p style='    background: rgba(${destination.properties.color}); color: #fff; padding: 2px 5px;'>${destination.properties.name}</p>`,
                //     JD: destination.geometry.coordinates[0],
                //     WD: destination.geometry.coordinates[1]
                // });
            });

            PowerGis.addSector(this.map, '绘制扇形', esriarr, true, (layer) => {
                // 定位到图层

                PowerGis.pointTolayer(this.map, layer);
                console.log(111);
                this.geometry =
                    layer.graphics[layer.graphics.length - 1].geometry;

                layer.graphics.forEach((el) => {
                    let jdwd =
                        el.geometry.rings[0][el.geometry.rings[0].length - 2];

                    let attributes = el.attributes;

                    let raids = attributes.radis / 1000;
                    let color2 = getColor2(
                        raids,
                        1,
                        this.markerParam.distanc[0],
                        this.markerParam.distanc[
                            this.markerParam.distanc.length - 1
                        ]
                    );
                    geoarrpoint.push({
                        name: raids + '公里',
                        color: color2,
                        htmlTemplate: `<p style='    background: rgba(${color2}); color: #fff; padding: 2px 5px;'>${
                            raids + '公里'
                        }</p>`,
                        JD: jdwd[0],
                        WD: jdwd[1]
                    });
                });

                PowerGis.addCustomHtmlLayer(
                    this.map,
                    '距离文字',
                    geoarrpoint,
                    (e) => {
                        //鼠标事件回调
                    }
                );

                this.filterDatafunc();
            });

            console.log(geoarrpoint);
        },
        addZBFXfw() {
            let JD = parseFloat(this.markerParam.centralPoint[0].JD);
            let WD = parseFloat(this.markerParam.centralPoint[0].WD);
            let getColor = PowerGL.segmentedColorScale([
                [0, [191, 136, 0]],
                [50, [201, 141, 12]],
                [100, [204, 159, 90]]
            ]);
            let getColor2 = PowerGL.segmentedColorScale([
                [0, [247, 8, 3]],
                [50, [247, 123, 18]],
                [100, [215, 187, 55]]
            ]);
            let esriarr = [];
            let geoarrpoint = [];
            this.markerParam.distanc.forEach((radius) => {
                let color = getColor(
                    radius,
                    0.5,
                    this.markerParam.distanc[0],
                    this.markerParam.distanc[
                        this.markerParam.distanc.length - 1
                    ]
                );
                PowerGis.buffer(
                    this.markerParam.centralPoint[0],
                    radius * 1000,
                    (geometry) => {
                        let item = {
                            option: {
                                lineColor: [236, 204, 104],
                                style: 'STYLE_SOLID',
                                tcstyle: 'STYLE_SOLID',
                                lineWidth: 1,
                                color: color
                            },
                            rings: geometry.rings
                        };
                        esriarr.push(item);
                        let color2 = getColor2(
                            radius,
                            1,
                            this.markerParam.distanc[0],
                            this.markerParam.distanc[
                                this.markerParam.distanc.length - 1
                            ]
                        );
                        console.log(geometry.rings);

                        geoarrpoint.push({
                            name: radius + '公里',
                            color: color2,
                            htmlTemplate: `<p style='    background: rgba(${color2}); color: #fff; padding: 2px 5px;'>${
                                radius + '公里'
                            }</p>`,
                            JD: geometry.rings[0][0][0],
                            WD: geometry.rings[0][0][1]
                        });
                        // this.geometry = geometry;
                        // this.drawCircle();
                    },
                    4326
                );

                let color2 = getColor2(
                    radius,
                    1,
                    this.markerParam.distanc[0],
                    this.markerParam.distanc[
                        this.markerParam.distanc.length - 1
                    ]
                );
                let options = {
                    units: 'kilometers',
                    properties: { name: radius + '公里', color: color2 }
                };
                // let destination = turf.destination(
                //     [JD, WD],
                //     radius,
                //     bearing2,
                //     options
                // );

                // geoarrpoint.push({
                //     ...destination.properties,
                //     htmlTemplate: `<p style='    background: rgba(${destination.properties.color}); color: #fff; padding: 2px 5px;'>${destination.properties.name}</p>`,
                //     JD: destination.geometry.coordinates[0],
                //     WD: destination.geometry.coordinates[1]
                // });
            });

            console.log(esriarr);
            PowerGis.addPolygon(
                this.map,
                '绘制扇形',
                esriarr,
                true,
                (layer) => {
                    this.geometry =
                        layer.graphics[layer.graphics.length - 1].geometry;
                    this.filterDatafunc();
                    PowerGis.pointTolayer(this.map, layer);
                    // this.fitPolygon(this.geometry);
                }
            );

            PowerGis.addCustomHtmlLayer(
                this.map,
                '距离文字',
                geoarrpoint,
                (e) => {
                    //鼠标事件回调
                }
            );
        },

        filterDatafunc() {
            let result = this.rangeFilter();

            this.addGisData('污染源点位', result);
        },

        addGisData(layerId, data) {
            let fmtData = [];
            data.forEach((item) => {
                switch (layerId) {
                    case '污染源点位':
                        item.option = {
                            url: 'gis/2D/images/iconLibrary/hylx/gyy.png',
                            width: 32,
                            height: 32
                        };
                        break;

                    default:
                        break;
                }
                fmtData.push(item);
            });

            PowerGis.addImgPoint(this.map, layerId, fmtData, true, (layer) => {
                let map = this.map;
                layer.on('mouse-out', (e) => {
                    PowerGis.removeMapTip();
                });
                layer.on('mouse-move', (e) => {
                    PowerGis.removeMapTip();
                    let attr = e.graphic.attributes;
                    let pt = PowerGis.getPoint(map, attr); //e.mapPoint;
                    let tipStr = attr.ZDMC || attr.CDMC || attr.MC;
                    let cssObj = { 'margin-bottom': '20px' };
                    PowerGis.showMapTip(pt, map, cssObj, tipStr);
                });
                layer.on('click', (e) => {
                    let attr = e.graphic.attributes;
                    this.pointClick(layerId, attr);
                });
            });
        },
        rangeFilter() {
            let data = this.markerParam.filterData;
            let enable = true;
            let result = [];

            let center = this.getPointMct(this.markerParam.centralPoint[0]);
            data.forEach((item) => {
                let obj = { ...item };
                let pt = this.getPointMct(obj);
                let distance = this.getDistanceMct(pt, center);

                obj.DISTANCE = distance.toFixed(2);

                if (enable) {
                    if (this.geometry && this.geometry.contains(pt)) {
                        result.push(obj);
                    }
                } else {
                    result.push(obj);
                }
            });
            return result;
        },
        //获取墨卡托点位
        getPointMct(item) {
            let pt = new PowerGis.Point(item.JD, item.WD);
            pt = PowerGis.webMercatorUtils.geographicToWebMercator(pt);
            return pt;
        },
        //两点距离：米
        getDistanceMct(pt1, pt2) {
            return PowerGis.geometryEngine.distance(pt1, pt2, 9001);
        },
        addAirPoint() {
            let result = [];

            PowerGis.removeCustomHtmlLayer('空气站点图层');

            for (let item of this.markerParam.centralPoint) {
                item.JD = item.longitude || item.JD;
                item.WD = item.latitude || item.WD;

                let obj = { ...item };

                //需要显示设置宽高，否则会出现offset
                obj.htmlTemplate = this.getContent(item);
                result.push(obj);
            }

            PowerGis.addCustomHtmlLayer(
                this.map,
                '空气站点图层',
                result,
                (obj) => {
                    let item = obj.item;

                    if (this.callback) {
                        if (obj.type == 'click') {
                            this.callback('空气站', obj.item);
                        }
                    }
                    if (obj.type == 'mouseout') {
                        this.map.setMapCursor('default');
                        PowerGis.removeMapTip();
                    }
                    if (obj.type == 'mouseover') {
                        // this.map.setMapCursor('pointer');
                        // PowerGis.removeMapTip();
                        // let contentStr = '';
                        // let cssObj = {
                        //     background: 'rgba(0,0,0,0)'
                        // };
                        // contentStr = this.getContentStr('空气站', item);
                        // let pt = PowerGis.getPoint(this.map, item);
                        // PowerGis.showMapTip(pt, this.map, cssObj, contentStr);
                    }
                }
            );
        },

        getContent(item) {
            let level = PowerGis.getLevelByWrw(this.markerParam.yz, item, 1);
            let dj = PowerGis.getAirDJByLevel(level);
            let data = item[this.markerParam.yz]
                ? item[this.markerParam.yz]
                : '--';
            let wid = 18 * item.CDMC.length;
            let htmlTemplate = ``;
            let width = 80 + wid + 'px';

            if (this.markerParam.centraltype == '大气站点1') {
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        htmlTemplate = `
               <div class="location">   <div class=" airLevel dj${dj}">${data}</div></div>
              `;
                    } else {
                        let marginLeft = (45 - wid) / 2;
                        htmlTemplate = `<div class="location">   <div class="airLevel dj${dj}">${data}</div><div class="mc" style="width:${wid}px;margin-left:${marginLeft}px">${item.CDMC}</div></div>`;
                    }
                } else {
                    htmlTemplate = `
                 <div class="location">  <div class=" airLevel dj${dj}">${data}</div></div>
              `;
                    if (this.markerParam.showAno) {
                        let marginLeft = (45 - wid) / 2;
                        htmlTemplate = `<div class="location">   <div class="airLevel dj${dj}">${data}</div><div class="mc" style="width:${wid}px;margin-left:${marginLeft}px">${item.CDMC}</div></div>`;
                    }
                }
            } else if (this.markerParam.centraltype == '大气站点2') {
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        htmlTemplate = `<dl class="pd-dlpin-air dj${dj}"><dt>${data}</dt><dd></dd></dl>`;
                    } else {
                        htmlTemplate = `<dl style="width:${width}; height:103px" class="pd-dlpin-air dj${dj}"><dt>${item.CDMC}&emsp;${data}</dt><dd></dd></dl>`;
                    }
                } else {
                    htmlTemplate = `<dl style="width:${width}; height:103px" class="pd-dlpin-air dj${dj}"><dt>${item.CDMC}&emsp;${data}</dt><dd></dd></dl>`;

                    if (!this.markerParam.showAno) {
                        htmlTemplate = `<dl class="pd-dlpin-air dj${dj}"><dt>${data}</dt><dd></dd></dl>`;
                    }
                }
            } else if (this.markerParam.centraltype == '大气站点3') {
                let djtext = this.getDJTextByDJ(dj);
                let color = PowerGL.getAirColorByLevel(level);
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        htmlTemplate = `<div class="sw19-dwsite type${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    } else {
                        htmlTemplate = `<div class="sw19-dwsite type${dj}" >
				<h1><p>${item.CDMC}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                } else {
                    htmlTemplate = `<div class="sw19-dwsite type${dj}" >
				<h1><p>${item.CDMC}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    if (!this.markerParam.showAno) {
                        htmlTemplate = `<div class="sw19-dwsite type${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                }
            }
            return htmlTemplate;
        },
        getDJTextByDJ(val) {
            let str = '';
            switch (val) {
                case '0':
                    str = '无';
                    break;
                case '1':
                    str = '优';
                    break;
                case '2':
                    str = '良';
                    break;
                case '3':
                    str = '轻度';
                    break;
                case '4':
                    str = '中度';
                    break;
                case '5':
                    str = '重度';
                    break;
                case '6':
                    str = '严重';
                    break;
            }
            return str;
        },

        // 水点位
        addWaterPoint() {
            let result = [];

            PowerGis.removeCustomHtmlLayer('水质站点图层');

            for (let item of this.markerParam.centralPoint) {
                item.JD = item.longitude || item.JD;
                item.WD = item.latitude || item.WD;

                //需要显示设置宽高，否则会出现offset
                item.htmlTemplate = this.getWaterContent(item);
                result.push(item);
            }

            PowerGis.addCustomHtmlLayer(
                this.map,
                '水质站点图层',
                result,
                (obj) => {
                    let item = obj.item;

                    if (this.callback) {
                        if (obj.type == 'click') {
                            this.callback('水质站点图层', obj.item);
                        }
                    }
                    if (obj.type == 'mouseout') {
                        this.map.setMapCursor('default');
                        PowerGis.removeMapTip();
                    }
                    if (obj.type == 'mouseover') {
                        this.map.setMapCursor('pointer');
                        PowerGis.removeMapTip();
                        let contentStr = '';
                        let cssObj = {
                            background: 'rgba(0,0,0,0)'
                        };
                        contentStr = this.getContentStr('空气站', item);
                        let pt = PowerGis.getPoint(this.map, item);
                        PowerGis.showMapTip(pt, this.map, cssObj, contentStr);
                    }
                }
            );
        },
        getWaterContent(item) {
            let htmlTemplate = ``;

            let dj = item.szlbbs;
            let waterQuality = item.waterQuality || '--';
            let color = PowerGis.getWaterColorByLevel(waterQuality);
            let djtext = PowerGis.getDJMCByLevel(dj + '');

            if (this.markerParam.centraltype == '水质站点') {
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        htmlTemplate = `<div class="sw19-dwsite type1${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    } else {
                        htmlTemplate = `<div class="sw19-dwsite type1${dj}" >
				<h1><p>${item.pointName}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                } else {
                    htmlTemplate = `<div class="sw19-dwsite type1${dj}" >
				<h1><p>${item.pointName}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    if (!this.markerParam.showAno) {
                        htmlTemplate = `<div class="sw19-dwsite type1${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                }
            }

            return htmlTemplate;
        },
        //清除
        clear() {
            // PowerGis.removeLayerFromName(this.map, '');
            PowerGis.removeCustomHtmlLayer('空气站点图层');
            // for (let marker of this.areaMarkers) {
            //     if (marker) {
            //         marker.remove();
            //         marker = null;
            //     }
            // }
            // this.areaMarkers = [];
        },
        watchmarkerParam() {
            if (this.markerParam && this.markerParam.centralPoint) {
                if (this.markerParam.visible) {
                    this.addMarkers();
                    if (this.markerParam.type == 'SFX') {
                        this.addSFXfw();
                    } else {
                        this.addZBFXfw();
                    }
                } else {
                    this.clear();

                    PowerGis.removeCustomHtmlLayer('距离文字');
                    PowerGis.removeLayer(this.map, '绘制扇形');
                    PowerGis.removeLayer(this.map, '污染源点位');
                }
            }
        }
    },
    watch: {
        markerParam: {
            immediate: true,
            deep: true,
            handler(val) {
                if (this.timer) {
                    clearTimeout(this.timer);
                }
                this.timer = setTimeout(() => {
                    this.watchmarkerParam();
                }, 1000);
            }
        }
    }
};
</script>

<style>
@import '~_as/gis/commom/map3DMarker.css';
</style>
