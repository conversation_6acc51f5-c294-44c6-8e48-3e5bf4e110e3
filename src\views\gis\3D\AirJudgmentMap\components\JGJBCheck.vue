<!-- @format -->

<!-- 空气站-监管级别控制  -->
<template>
    <dl class="sw0725-dlbx1">
        <dt class="flx1 ac">
            <label class="sw0721-check1" @click="allLayerClick()"
                ><input type="checkbox" :checked="checkAll" disabled /><span
                    >周边站点</span
                ></label
            >
        </dt>
        <dd>
            <label
                class="sw0721-check1"
                v-for="(item, index) of zts"
                :key="index"
                @click="ztLayerClick(item)"
                ><input
                    type="checkbox"
                    :checked="item.selected"
                    style="pointer-events: none"
                    disabled
                /><span>{{ item.name }}({{ item.total }})</span></label
            >
        </dd>
    </dl>
</template>

<script>
export default {
    data() {
        return {
            jgjbArr: [],
            zts: [
                {
                    name: '国控站',
                    fiterType: '1',
                    dm: 'gk',
                    selected: true,
                    total: 0
                },
                {
                    name: '省控站',
                    fiterType: '2',
                    dm: 'sk',
                    selected: true,
                    total: 0
                },
                {
                    name: '市控站',
                    fiterType: '3',
                    dm: 'dsk',
                    selected: true,
                    total: 0
                }
            ],

            checkAll: true //全部选中
        };
    },
    props: ['zbzdtj'],

    mounted() {
        this.getSelectJGJB();
    },
    methods: {
        //专题点击
        allLayerClick() {
            this.checkAll = !this.checkAll;

            for (let o of this.zts) {
                o.selected = this.checkAll;
            }

            this.getSelectJGJB();
        },

        //子专题点击
        ztLayerClick(obj) {
            obj.selected = !obj.selected;

            //根据子级的选中状态，设置父级的选中状态
            this.checkAll = this.zts.some((item) => {
                return item.selected;
            });

            this.getSelectJGJB();
        },

        //获取选中的监管级别
        getSelectJGJB() {
            let arrTemp = this.zts.filter((item) => {
                return item.selected;
            });

            this.jgjbArr = arrTemp.map((item) => {
                return item.fiterType;
            });

            this.$emit('jgjbChange', this.jgjbArr);
        }
    },

    watch: {
        zbzdtj: {
            handler(newVal) {
                if (newVal) {
                    for (let item of this.zts) {
                        item.total = newVal[item.dm] || 0;
                    }
                }
            },
            deep: true
        }
    }
};
</script>

<style scoped>
.sw0725-dlbx1 dd label + label {
    margin-top: 10px;
}
</style>
