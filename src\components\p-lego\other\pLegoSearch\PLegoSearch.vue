<!-- @format -->

<template>
    <div class="p-lego-search" :style="{ height: mConfig.height || '40px' }">
        <!-- 具名插槽 -->
        <slot name="front"></slot>

        <template v-for="(item, index) in mConfig.childComponents" :key="index">
            <div :class="`${item}${timeNow}`"></div>
        </template>

        <!-- 插槽 -->
        <slot></slot>

        <!-- 下拉选择 -->
        <Teleport
            :to="`.Select${timeNow}`"
            v-if="selectBoxShow && mConfig.childComponents.includes('Select')"
        >
            <Select
                :style="{ width: selectOption.width || 'auto' }"
                :title="selectOption.title"
                :selectList="selectOption.selectList"
                :defaultVal="selectOption.defaultVal"
                @selectChange="selectChange"
            ></Select>
        </Teleport>

        <!-- 输入框 -->
        <Teleport
            :to="`.Input${timeNow}`"
            v-if="selectBoxShow && mConfig.childComponents.includes('Input')"
        >
            <Input
                :style="{ width: inputOption.width || 'auto' }"
                :title="inputOption.title"
                :defaultVal="inputOption.defaultVal"
                :placeholder="inputOption.placeholder"
                @inputChange="inputChange"
            ></Input>
        </Teleport>

        <!-- 单个时间选择 -->
        <Teleport
            :to="`.SingleTime${timeNow}`"
            v-if="
                selectBoxShow && mConfig.childComponents.includes('SingleTime')
            "
        >
            <SingleTime
                :style="{ width: singleTimeOption.width || 'auto' }"
                :title="singleTimeOption.title"
                :defaultVal="singleTimeOption.defaultVal"
                :type="singleTimeOption.type"
                :placeholder="singleTimeOption.placeholder"
                :clearable="singleTimeOption.clearable"
                :format="singleTimeOption.format"
                :valueFormat="singleTimeOption.valueFormat"
                @singleTimeChange="singleTimeChange"
            ></SingleTime>
        </Teleport>

        <!-- 时间范围选择 -->
        <Teleport
            :to="`.TimeRange${timeNow}`"
            v-if="
                selectBoxShow && mConfig.childComponents.includes('TimeRange')
            "
        >
            <TimeRange
                :style="{ width: timeRangeOption.width || 'auto' }"
                :title="timeRangeOption.title"
                :defaultVal="timeRangeOption.defaultVal"
                :type="timeRangeOption.type"
                :clearable="timeRangeOption.clearable"
                :format="timeRangeOption.format"
                :valueFormat="timeRangeOption.valueFormat"
                @timeRangeChange="timeRangeChange"
            ></TimeRange>
        </Teleport>
    </div>
</template>

<script>
import Select from './components/PLegoSearchSelect.vue';
import Input from './components/PLegoSearchInput.vue';
import SingleTime from './components/PLegoSingleTime.vue';
import TimeRange from './components/PLegoTimeRange.vue';

export default {
    name: '',
    props: {
        //下拉选择
        selectOption: {
            type: Object,
            default: () => {
                return {
                    width: 'auto',
                    title: '',
                    selectList: [],
                    defaultVal: ''
                };
            }
        },

        // 输入框
        inputOption: {
            type: Object,
            default: () => {
                return {
                    width: 'auto',
                    title: '',
                    defaultVal: '',
                    placeholder: '请输入'
                };
            }
        },

        // 单个时间选择
        singleTimeOption: {
            type: Object,
            default: () => {
                return {
                    width: 'auto',
                    title: '',
                    defaultVal: '',
                    type: 'date',
                    placeholder: '请选择',
                    clearable: true,
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD'
                };
            }
        },

        // 时间范围选择
        timeRangeOption: {
            type: Object,
            default: () => {
                return {
                    width: 'auto',
                    title: '',
                    defaultVal: [],
                    type: 'datetimerange',
                    clearable: true,
                    format: 'YYYY-MM-DD HH:mm:ss',
                    valueFormat: 'YYYY-MM-DD HH:mm:ss'
                };
            }
        },

        config: {
            type: Object,
            default: () => {
                return {
                    width: 'auto',
                    height: '40px',
                    childComponents: ['Input', 'Select']
                };
            }
        }
    },
    components: { Select, Input, SingleTime, TimeRange },

    data() {
        return {
            timeNow: Date.now(), //
            selectBoxShow: false,
            selectVal: '', //下拉选择
            inputVal: '', //输入框
            singleTimeVal: '', //单个时间值
            timeRangeVal: [] //时间范围
        };
    },
    computed: {
        mConfig() {
            return Object.assign(
                {
                    width: 'auto',
                    height: '40px',
                    childComponents: ['Input', 'Select']
                },
                this.config
            );
        }
    },
    mounted() {
        // 由于直接用不显示，所以先隐藏在显示 这样处理的
        this.selectBoxShow = true;

        // 设置默认值
        this.selectVal = this.selectOption.defaultVal || '';
        this.inputVal = this.inputOption.defaultVal || '';
        this.singleTimeVal = this.singleTimeOption.defaultVal || '';
        this.timeRangeVal = this.timeRangeOption.defaultVal || [];
    },
    methods: {
        //下拉选择
        selectChange(v) {
            this.selectVal = v;
            this.searchChange();
        },

        //输入框
        inputChange(v) {
            this.inputVal = v;
            this.searchChange();
        },

        // 单个时间切换
        singleTimeChange(v) {
            this.singleTimeVal = v;
            this.searchChange();
        },

        // 时间范围
        timeRangeChange(v) {
            this.timeRangeVal = v;
            this.searchChange();
        },

        searchChange() {
            let obj = {};

            this.mConfig.childComponents.forEach((item) => {
                switch (item) {
                    case 'Select':
                        Object.assign(obj, { selectVal: this.selectVal });
                        break;
                    case 'Input':
                        Object.assign(obj, { inputVal: this.inputVal });
                        break;
                    case 'SingleTime':
                        Object.assign(obj, {
                            singleTimeVal: this.singleTimeVal
                        });
                        break;
                    case 'TimeRange':
                        Object.assign(obj, { timeRangeVal: this.timeRangeVal });
                        break;
                }
            });
            this.$emit('searchChange', obj);
        }
    }
};
</script>

<style lang="scss" scoped>
.p-lego-search {
    // background-color: #fff;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    color: var(--font-color);

    & > div {
        // margin-right: 20px;
        // color: var(--font-color);
        display: flex;
        align-items: center;
    }
}

.search-type {
    margin-right: 20px;
    color: var(--font-color);
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
}
</style>
