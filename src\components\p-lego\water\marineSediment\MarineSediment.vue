<!-- @format -->

<template>
    <div>
        <div class="hyfx-charbox">
            <p-stack-bar
                :data="marineOrganismAnalysis"
                :option="barOpt"
                :config="{
                    barWidth: 8
                }"
                style="width: 600px; height: 220px"
            ></p-stack-bar>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        data: {
            type: Array
        },
        option: {
            type: Object
        }
    },
    created() {
        this.$pChart.setChartConfig({
            SHOW_TOOLBOX: false
        });
    },
    mounted() {
        this.arryData = this.data;
        const nowYear = this.arryData[0].dqdata[0].year;
        const lastYear = this.arryData[0].tbdata[0].year;
        // X轴
        const xAxis = this.arryData[0].dqdata.map((item) => {
            return `${nowYear} ${lastYear} ${item.xzqhmc}`;
        });
        // 今年
        const nowObj = this.arryData.map((item, index) => {
            return {
                name: item.szlb,
                sType: 'dq',
                stack: 'dq',
                year: item.dqdata[index].year,
                data: item.dqdata.map((i) => i['zb'])
            };
        });
        // 去年
        const lastObj = this.arryData.map((item, index) => {
            return {
                name: item.szlb,
                sType: 'tb',
                stack: 'tb',
                year: item.tbdata[index].year,
                data: item.tbdata.map((i) => i['zb'])
            };
        });
        const series = nowObj.concat(lastObj);
        this.marineOrganismAnalysis = {
            xAxis: xAxis,
            series: series
        };
        let inx1 = null;
        let inx2 = null;
        // 类型为当期（今年） 首次出现的下标
        for (const i in series) {
            if (series[i].sType === 'dq') {
                inx1 = i;
                break;
            }
        }
        // 类型为同比（去年） 首次出现的下标
        for (const i in series) {
            if (series[i].sType === 'tb') {
                inx2 = i;
                break;
            }
        }
    },
    data() {
        return {
            marineOrganismAnalysis: '',
            arryData: [],
            barOpt: {
                grid: {
                    top: 60,
                    bottom: 0,
                    right: 0,
                    left: 20
                },
                legend: {
                    left: 'center'
                },
                tooltip: {
                    formatter: (params) => {
                        const nowYear = params[0].name.split(' ')[0] + '年';
                        const lastYear = params[0].name.split(' ')[1] + '年';
                        let tip = params[0].name.split(' ')[2] + '<br/>';
                        params.forEach((item) => {
                            if (item.seriesIndex <= 3) {
                                tip += `${item.marker} ${
                                    item.seriesName
                                } ${nowYear} ${
                                    item.value || '-'
                                }% &nbsp;&nbsp; ${lastYear} ${
                                    params[item.seriesIndex + 4].value || '-'
                                }%<br/>`;
                            }
                        });
                        return tip;
                    }
                },
                xAxis: {
                    axisLabel: {
                        margin: 5,
                        interval: 0,
                        formatter: (item) => {
                            return `${item.split(' ')[0]} ${
                                item.split(' ')[1]
                            }\n\n${item.split(' ')[2]}`;
                        }
                    }
                },
                yAxis: {
                    name: '占比（%）',
                    max: 100,
                    nameTextStyle: {
                        padding: [0, 0, 0, 20]
                    }
                },
                series: []
            }
        };
    },
    methods: {}
};
</script>

<style scoped>
.lr29-1-r-bg {
    width: 500px;
}
.rt-txt {
    font-size: 12px;
    color: #fff;
    text-align: left;
}
.lr29-4-table {
    width: 100%;
}

.lr29-4-table tr td {
    height: 38px;
    font-size: 14px;
    color: #fff;
    text-align: center;
}

.lr29-4-table tr td:nth-child(1) {
    width: 15%;
}

.lr29-4-table tr td:nth-child(2) {
    width: 20%;
}

.lr29-4-table tr td:nth-child(3) {
    width: 65%;
    text-align: left;
}

.lr29-4-table .yst {
    display: inline-block;
    width: 88%;
    height: 8px;
    border-radius: 4px;
    background: #2ae3ff;
}

.lr29-4-table2 {
    width: 100%;
}

.lr29-4-table2 tr td {
    height: 38px;
    font-size: 14px;
    color: #fff;
    text-align: center;
}

.lr29-4-table2 thead {
    background: #0c2a61;
}

.lr29-4-table2 tr td:nth-child(1) {
    width: 18%;
}

.lr29-4-table2 tr td:nth-child(2) {
    width: 45%;
    text-align: left;
}

.lr29-4-table2 tr td:nth-child(3) {
    width: 37%;
}

.td-name {
    text-align: left;
    width: 90px;
}

.ell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
