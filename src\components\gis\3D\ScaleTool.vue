<!-- @format -->
<!-- 比例尺 -->
<template>
    <div class="bilichi-gis" ref="scaleBar"></div>
</template>

<script>
export default {
    name: 'ScaleTool',
    props: ['map'],
    data() {
        return {};
    },
    components: {},
    computed: {},
    mounted() {
        this.init();
    },
    methods: {
        init() {
            if (!this.map) {
                setTimeout(() => {
                    this.init();
                }, 1000);
            } else {
                let scalebar = new PowerGL.Scalebar(
                    {
                        map: this.map,
                        // attachTo: 'bottom-left',
                        scalebarUnit: 'metric',
                        scalebarStyle: 'line'
                    },
                    this.$refs.scaleBar
                );
            }
        }
    },
    watch: {}
};
</script>

<style scoped></style>

<style>
.esriScalebarSecondNumber {
    left: 40% !important;
}

.esriScalebarLabel {
    height: 15px !important;
    top: 0px !important;
}
</style>
