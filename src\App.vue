<!-- @format -->

<!--
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2023-04-07 09:14:19
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2023-04-07 12:27:27
 * @FilePath: /Front_Base_PC_GIS/src/App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- @format -->

<template>
    <div style="width: 100%; height: 100%; background: #f1f3f7">
        <router-view></router-view>
        <loading v-if="$store.state.isShowLoading" />
    </div>
</template>

<script>
import Loading from './components/Loading.vue';
import router from './router';
export default {
    name: 'app',
    components: {
        Loading
    },
    data() {
        return {
            // theme: ServerGlobalConstant.eleTheme
        };
    },
    created() {
        //  拦截判断页面色系
        router.beforeEach((to, from, next) => {
            let otherTheme = false;
            ServerGlobalConstant.otherThemeArrRoute.forEach((item) => {
                if (item == to.path) {
                    otherTheme = true;
                }
            });

            let theme = ServerGlobalConstant.eleTheme;
            //如果是其他主题色页面 就反一下
            if (otherTheme) {
                theme = theme === 'dark' ? 'light' : 'dark';
            }
            document.getElementsByTagName('body')[0].className =
                theme + 'Theme';
            this.$pChart.setChartConfig({
                THEME_COLOR: theme
            });

            next();
        });
    },
    mounted() {}
};
</script>

<style>
/* element.ui 样式 */
@import '_as/css/reset.css';
@import '_as/css/eleTheme/common.css';
@import '_as/css/eleTheme/light.css';
@import '_as/css/eleTheme/dark.css';
@import '_as/exam/css/reset.css';
@import '_as/exam/css/sw1212.css';
@import 'video.js/dist/video-js.css';

#app {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* text-align: center; */
    color: var(--font-color);
    height: 100%;
}

.text-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
}
</style>
