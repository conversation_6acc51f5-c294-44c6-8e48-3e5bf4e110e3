/** @format */
/** 地图要素绘制基础库 */
import * as turf from '@turf/turf';
class BasePointUtil {
    constructor(map, clickBack) {
        this.map = map;
        this.clickBack = clickBack;
        this.markers = {}; //html 标签

        this.hoveredSource = null;
        this.hoveredFeature = null;

        //弹出框参数
        this.popupOption = {
            anchor: 'bottom',
            className: 'mapbox-tooltip-tip',
            closeOnClick: false,
            closeButton: false,
            maxWidth: 'none',
            offset: [0, -4]
        };

        //marker 参数
        this.markerOption = {
            anchor: 'center',
            offset: [0, 0],
            draggable: false
        };

        //线默认样式
        this.linePaint = {
            'line-opacity': 1,
            'line-color': '#02AAE9',

            'line-width': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                5,
                2
            ]
            // 'line-width': 2
        };

        //面的默认样式 （深色图默认样式）
        this.fillPaint = {
            'fill-antialias': true,
            'fill-color': '#0f8cce',

            //设置鼠标移入改变透明度
            'fill-opacity': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                0.8,
                0.4
            ],
            'fill-outline-color': '#70cdff'
            // 'fill-opacity': 0.4
        };

        //面的默认样式 （浅色图默认样式）
        this.fillPaint2 = {
            'fill-antialias': true,
            'fill-color': '#2083f3',
            'fill-outline-color': '#2083f3',

            //设置鼠标移入改变透明度
            'fill-opacity': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                0.8,
                0.4
            ]
            // 'fill-opacity': 0.4
        };

        //简单符号样式，在mapbox 中，简单符号只有小圆点
        this.circlePaint = {
            'circle-color': '#F0F075',
            'circle-stroke-width': 1,
            'circle-stroke-color': '#fff',
            // 'circle-radius': 10,
            'circle-radius': [
                'case',
                ['boolean', ['feature-state', 'hover'], false],
                15,
                10
            ]
        };

        //面拉升
        this.fillExtrusionPaint = {
            'fill-extrusion-color': 'rgba(0, 233, 255, 1)',
            'fill-extrusion-height': 40,
            'fill-extrusion-opacity': 1,
            'fill-extrusion-vertical-gradient': true,
            'fill-extrusion-base': 0
        };

        //文本的属性
        this.txtLayout = {
            'text-rotation-alignment': 'viewport',
            'text-pitch-alignment': 'viewport',
            'text-font': ['Microsoft YaHei Bold'],
            'text-anchor': 'center',
            'text-size': 18,
            'text-max-width': 8,
            'text-field': '{NAME}'
            // 'text-ignore-placement': true,
            // 'text-allow-overlap': true
        };

        //文本的样式（深色地图默认）
        this.txtPaint = {
            'text-color': 'rgba(146, 217, 255, 1)',
            'text-halo-color': 'rgb(146, 217, 255)',
            'text-halo-width': 0
        };

        //文本的样式2 （浅色地图默认）
        this.txtPaint2 = {
            'text-color': 'rgba(77, 153, 239, 0.6)',
            'text-halo-color': 'rgb(151, 171, 192)',
            'text-halo-width': 1
        };

        this.heatPaint = {
            // 色带
            'heatmap-color': [
                'interpolate',
                ['linear'],
                ['heatmap-density'],
                0,
                'rgba(33,102,172,0)',
                0.2,
                'rgb(103,169,207)',
                0.4,
                'rgb(209,229,240)',
                0.6,
                'rgb(253,219,199)',
                0.8,
                'rgb(239,138,98)',
                1,
                'rgb(178,24,43)'
            ],
            // 按缩放级别调整热图半径
            'heatmap-radius': [
                'interpolate',
                ['linear'],
                ['zoom'],
                0,
                2,
                9,
                20
            ],
            // 透明度
            'heatmap-opacity': 0.8
        };

        //图层点击事件
        this.clickHandle = (e) => {
            let feature = e.features[0];

            if (feature && this.clickBack) {
                this.clickBack(
                    feature.properties.layerId,
                    feature.properties,
                    e.point
                );
            }
        };

        this.mouseMoveHandle = (e) => {
            this.map.getCanvas().style.cursor = 'pointer';

            debugger;

            if (window.glTooltip) {
                window.glTooltip.remove();
            }

            let pt = e.lngLat;
            let feature = e.features[0];

            window.glTooltip = this.getPopup(pt, feature);

            // this.map.setLayoutProperty('symbol1', 'icon-image', [
            //     'case',
            //     ['match', ['get', 'id'], feature.properties.id, true, false],
            //     'icon1-highlight',
            //     'icon1'
            // ]);

            //如果是图标样式
            if (feature.layer.layout['icon-size']) {
                // this.hoveredFeature = feature;
                // this.map.setLayoutProperty(feature.layer.id, 'icon-size', [
                //     'case',
                //     [
                //         'match',
                //         ['get', 'gisid'],
                //         feature.properties.gisid,
                //         true,
                //         false
                //     ],
                //     feature.layer.layout['icon-size'] * 1.5,
                //     feature.layer.layout['icon-size']
                // ]);
            } else {
                //其他样式图层
                if (this.hoveredFeature && this.hoveredSource) {
                    this.map.setFeatureState(
                        {
                            source: this.hoveredSource,
                            id: this.hoveredFeature.id
                        },
                        { hover: false }
                    );
                }

                this.hoveredFeature = feature;
                this.hoveredSource = feature.source;

                this.map.setFeatureState(
                    { source: this.hoveredSource, id: this.hoveredFeature.id },
                    { hover: true }
                );
            }
        };

        this.mouseMoveLeaveHandle = (e) => {
            this.map.getCanvas().style.cursor = '';
            this.removePopup();

            if (this.hoveredFeature && this.hoveredSource) {
                if (this.hoveredSource) {
                    this.map.setFeatureState(
                        {
                            source: this.hoveredSource,
                            id: this.hoveredFeature.id
                        },
                        { hover: false }
                    );
                } else {
                    // this.map.setLayoutProperty(
                    //     this.hoveredLayer.id,
                    //     'icon-size',
                    //     [
                    //         'case',
                    //         [
                    //             'match',
                    //             ['get', 'gisid'],
                    //             this.hoveredFeature.properties.gisid,
                    //             true,
                    //             false
                    //         ],
                    //         this.hoveredFeature.layer.layout['icon-size'] / 1.5,
                    //         this.hoveredFeature.layer.layout['icon-size']
                    //     ]
                    // );
                }
            }
            this.hoveredFeature = null;
            this.hoveredSource = null;
        };
    }

    /**
     * 加载图片数组
     * @param {*} arrImages 图片数组
     * @param {*} imageBack 完成后回调
     */
    loadImages(arrImages, imageBack) {
        let length = arrImages.length;
        let loadNum = 0;
        arrImages.forEach((item) => {
            if (this.map.hasImage(item.name)) {
                loadNum++;
                if (imageBack && loadNum == length - 1) {
                    imageBack();
                }
                return;
            }
            this.map.loadImage(item.url, (error, image) => {
                loadNum++;
                if (error) throw error;

                if (!this.map.hasImage(item.name)) {
                    this.map.addImage(item.name, image);
                }

                if (imageBack && loadNum == length - 1) {
                    imageBack();
                }
            });
        });
    }

    /**
     *  生成提示框
     * @param {*} lngLat
     * @param {*} feature
     * @returns
     */
    getPopup(lngLat, feature) {
        let id = feature.layer.id;
        this.map._currentHoverLayer = id;
        let popupOption = { ...this.popupOption };
        if (this.getPopupOption) {
            let option = this.getPopupOption(id, feature.properties) || {};
            Object.assign(popupOption, option);
        }
        let popupStr = this.getPopupStr(id, feature.properties);

        if (!popupStr) {
            return null;
        }
        let popup;
        if (typeof popupStr == 'string') {
            popup = new mapboxgl.Popup(popupOption)
                .setLngLat(lngLat)
                .setHTML(popupStr)
                .setMaxWidth('none')
                .addTo(this.map);
        } else {
            popup = new mapboxgl.Popup(popupOption)
                .setLngLat(lngLat)
                .setDOMContent(popupStr)
                .setMaxWidth('none')
                .addTo(this.map);
        }

        return popup;
    }

    /**
     *移除提示框
     */
    removePopup() {
        if (window.glTooltip) {
            window.glTooltip.remove();
        }
    }
    /**
     *   提示框参数 mapboxgl.Popup参数 可选择是否覆盖的方法
     * @param {*} id
     * @param {*} properties
     * @returns
     */
    getPopupOption(id, properties) {
        return {};
    }
    /**
     * 提示框内容  mapboxgl.Popup.HTML 需要覆盖的方法
     * @param {*} id
     * @param {*} properties
     * @returns
     */
    getPopupStr(id, properties) {
        let html = '';
        return html;
    }
    // 生成marker
    getMarker(properties, params) {
        let marker;
        let lnglat = this.getlngLat(properties);
        if (!lnglat) {
            return marker;
        }
        let that = this;
        let markerOption = { ...this.markerOption };
        if (this.getMarkerOption) {
            let option = this.getMarkerOption(params.id, properties) || {};
            Object.assign(markerOption, option);
        }
        let element = this.getMarkerHtml(properties, params);
        element.className = markerOption.className;
        markerOption.element = element;
        marker = new mapboxgl.Marker(markerOption)
            .setLngLat(lnglat)
            .addTo(this.map);
        if (!params.disableClick) {
            element.addEventListener('click', (e) => {
                if (
                    that.map._currentHoverLayer == params.id &&
                    that.clickBack
                ) {
                    that.clickBack(params.id, properties);
                }
                e.preventDefault();
            });
        }

        if (!params.disablePopup) {
            element.addEventListener('mouseenter', (e) => {
                if (this.glTooltip) {
                    this.glTooltip.remove();
                }
                this.glTooltip = this.getPopup(lnglat, {
                    properties: properties,
                    layer: {
                        id: params.id
                    }
                });
                this.map._currentHoverLayer = params.id;
                e.preventDefault();
            });
            element.addEventListener('mouseleave', (e) => {
                if (that.glTooltip) {
                    this.glTooltip.remove();
                }
                this.map._currentHoverLayer = undefined;
                e.preventDefault();
            });
        }

        return marker;
    }
    /**
     * marker参数：   marker.option 可选择是否覆盖方法
     * @param {*} id
     * @param {*} properties
     * @returns
     */
    getMarkerOption(id, properties) {
        return { className: 'coustom-marker' };
    }
    /**
     * 生成marker内容 marker.element 需要覆盖的方法
     * @param {*} properties
     * @param {*} params
     * @returns
     */
    getMarkerHtml(properties, params) {
        let el = document.createElement('div');
        el.className = 'air-marker';
        let template = ``;
        el.innerHTML = template;
        return el;
    }

    /**
     * 添加图片点位
     * @param {*} data 点位数据
     * @param {*} params 图层参数
     */
    addImgPoint(data, params) {
        this.removeLayerByName(params.id);

        let source;

        if (params.source) {
            source = params.source;
        } else {
            let features = [];
            for (let item of data) {
                let pt = this.getlngLat(item);

                item.gisid = PowerGL.genNonDuplicateID();

                if (pt) {
                    item.layerId = params.id;
                    let res = {
                        type: 'Feature',
                        geometry: {
                            type: 'Point',
                            coordinates: pt
                        },
                        properties: item
                    };

                    features.push(res);
                }
            }

            source = {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: features
                }
            };
        }

        let layout = this.getSymbolLayout(params);
        let paint = this.getSymbolPaint(params);

        debugger;

        let layerOption = {
            id: params.id,
            type: 'symbol',
            source: source,
            paint: paint,
            layout: layout
        };

        //图层相关的设置配置
        if (params.layerOption) {
            layerOption = Object.assign({}, layerOption, params.layerOption);
        }

        //给feature设置ID，只能是整数
        let index = 1;
        for (let item of layerOption.source.data.features) {
            item.id = index++;
        }

        if (params.beforeId) {
            this.map.addLayer(layerOption, params.beforeId);
        } else {
            this.map.addLayer(layerOption);
        }

        if (params) {
            this.addLayerEvent(params);
        }
    }

    /**
     * 获取点位数据   layout属性
     * @param {*} params
     * @returns
     */
    getSymbolLayout(params) {
        let layout = {};
        return layout;
    }

    /**
     * 获取symbol paint属性
     * @param {*} id
     * @returns
     */
    getSymbolPaint(params) {
        let paint = {};
        return paint;
    }

    /**
     *  添加自定义Maker图层
     * @param {*} data
     * @param {*} params
     */
    addCustomHtmlLayer(data, params) {
        this.clear(params.id);

        for (let item of data) {
            let marker = this.getMarker(item, params);

            if (marker) {
                this.markers[params.id].push(marker);
            }
        }
        this.map.triggerRepaint();
    }

    /**
     * 添加简单符号
     * @param { } data
     * @param {*} params
     */
    addCirclePoint(data, params) {
        this.removeLayerByName(params.id);

        let layout = this.getSymbolLayout(params);
        let paint = this.getSymbolPaint(params);

        paint = Object.assign({}, this.circlePaint, paint);

        delete layout['icon-allow-overlap']; //小圆点没有这个属性

        let layerOption = {
            id: params.id,
            type: 'circle',
            layout: layout,
            paint: paint
        };

        //图层相关的设置配置
        if (params.layerOption) {
            layerOption = Object.assign({}, layerOption, params.layerOption);
        }

        if (!params.source) {
            let features = [];
            data.forEach((item) => {
                let coordinates = this.getlngLat(item);
                if (coordinates) {
                    features.push({
                        type: 'Feature',
                        geometry: {
                            type: 'Point',
                            coordinates: coordinates
                        },
                        properties: item
                    });
                }
            });
            layerOption.source = {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: features
                }
            };
        } else {
            layerOption.source = params.source;
        }

        //给feature设置ID，只能是整数
        let index = 1;
        for (let item of layerOption.source.data.features) {
            item.id = index++;
        }

        if (params.beforeId) {
            this.map.addLayer(layerOption, params.beforeId);
        } else {
            this.map.addLayer(layerOption);
        }

        if (params) {
            this.addLayerEvent(params);
        }
    }

    /**
     * 添加文本点位
     * @param {*} data 点位数组
     * @param {*} params 图层参数
     */
    addTextPoint(data, params) {
        this.removeLayerByName(params.id);

        let source;

        if (params.source) {
            source = params.source;
        } else {
            let features = [];
            for (let item of data) {
                let pt = this.getlngLat(item);

                if (pt) {
                    item.layerId = params.id;
                    let res = {
                        type: 'Feature',
                        geometry: {
                            type: 'Point',
                            coordinates: pt
                        },
                        properties: item
                    };

                    features.push(res);
                }
            }

            source = {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: features
                }
            };
        }

        let layout = this.getSymbolLayout(params);
        let paint = this.getSymbolPaint(params);

        //按地图样式，选择默认样式
        let initPaint =
            params.mapType == 'dark' ? this.txtPaint : this.txtPaint2;

        paint = Object.assign({}, initPaint, paint);
        layout = Object.assign({}, this.txtLayout, layout);

        let layerOption = {
            id: params.id,
            type: 'symbol',
            source: source,
            layout: layout,
            paint: paint
        };

        //图层相关的设置配置
        if (params.layerOption) {
            layerOption = Object.assign({}, layerOption, params.layerOption);
        }

        //给feature设置ID，只能是整数
        let index = 1;
        for (let item of layerOption.source.data.features) {
            item.id = index++;
        }

        if (params.beforeId) {
            this.map.addLayer(layerOption, params.beforeId);
        } else {
            this.map.addLayer(layerOption);
        }
    }

    /**
     * 添加线要素图层
     * @param {*} features
     * @param {*} params 图层参数
     */
    addLine(features, params) {
        this.removeLayerByName(params.id);
        let linePaint = this.getLinePaint(params) || {};
        let paint = Object.assign({}, this.linePaint, linePaint);

        //给feature设置ID，只能是整数
        let index = 1;
        for (let item of features) {
            item.id = index++;
        }

        let beforeId = params.beforeId;
        let layerOption = {
            id: params.id,
            type: 'line',
            source: {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: features
                }
            },
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: paint
        };

        //图层相关的设置配置
        if (params.layerOption) {
            layerOption = Object.assign({}, layerOption, params.layerOption);
        }

        if (beforeId) {
            this.map.addLayer(layerOption, beforeId);
        } else {
            this.map.addLayer(layerOption);
        }

        if (params) {
            this.addLayerEvent(params);
        }
    }

    /**
     *  获取线要素样式  line-paint 需要覆盖方法
     * @param {*} params
     * @returns
     */
    getLinePaint(params) {
        let option = {};
        // switch (params.id) {
        //     case '':
        //         break;
        //     default:
        //         break;
        // }
        return option;
    }

    /**
     *  绘制面
     * @param {*} features geojson features数据
     * @param {*} params
     */
    addPolygon(features, params) {
        this.removeLayerByName(params.id);
        let fillPaint = this.getFillPaint(params) || {};

        let index = 1;

        for (let item of features) {
            item.id = index++;
        }

        //按地图样式，选择默认样式
        let initPaint =
            params.mapType == 'dark' ? this.fillPaint : this.fillPaint2;

        let paint = Object.assign({}, initPaint, fillPaint);
        let beforeId = params.beforeId;

        let layerOption = {
            id: params.id,
            type: 'fill',
            source: {
                type: 'geojson',
                cluster: false,
                data: {
                    type: 'FeatureCollection',
                    features: features
                }
            },
            layout: {
                visibility: 'visible' // 可见性（可选，可选值为 none、visible，默认值为 visible）
            },
            paint: paint
        };

        //图层相关的设置配置
        if (params.layerOption) {
            layerOption = Object.assign({}, layerOption, params.layerOption);
        }

        if (beforeId) {
            this.map.addLayer(layerOption, beforeId);
        } else {
            this.map.addLayer(layerOption);
        }

        if (params) {
            this.addLayerEvent(params);
        }
    }

    /**
     *  获取面要素样式  fill-paint 需要覆盖方法
     * @param {*} params
     * @returns
     */
    getFillPaint(params) {
        let option = {};
        // switch (params.id) {
        //     case '':
        //         break;
        //     default:
        //         break;
        // }
        return option;
    }

    /**
     * 添加echart图层
     * @param {*} data
     * @param {*} params
     */
    addTransportData(data, params) {
        this.removeLayerByName(params.id);
        let { lineData, fromData, toData } = this.getTransformData(data);

        let linePoint = 'image://./gis/3D/images/linePoint1.png';
        let symbolPoint = 'image://gis/3D/images/symbol1.png';

        let option = {
            GLMap: {
                roam: true
            },
            coordinateSystem: 'GLMap',
            tooltip: {
                show: true,
                formatter: function (evt) {
                    let data = evt.data;
                    let str = data.name;
                    if (data.toname) {
                        str = `从 ${data.name}<br>转移 ${data.csl} 吨<br>至 ${data.toname}`;
                    }
                    return str;
                }
            },
            geo: {
                map: 'GLMap',
                label: {
                    emphasis: {
                        show: false
                    }
                },
                roam: true,
                itemStyle: {
                    normal: {
                        areaColor: '#323c48',
                        borderColor: '#404a59'
                    },
                    emphasis: null
                }
            },
            series: [
                //线上的小圆点
                {
                    type: 'lines',
                    coordinateSystem: 'GLMap',
                    zlevel: 1,
                    lineStyle: {
                        normal: {
                            width: 0,
                            curveness: 0.3,
                            color: function (evt) {
                                if (evt.data.csl > 100) {
                                    return 'rgba(240,62,62)'; //红色
                                }
                                return 'rgb(5,247,213)'; //绿色
                            }
                        }
                    },
                    effect: {
                        constantSpeed: 35,
                        show: true,
                        trailLength: 0.3,
                        symbolSize: 6
                        // symbol: linePoint //移动小点的样式，不设置的话默认linestyle中的颜色
                    },
                    data: lineData
                },
                //线条
                {
                    type: 'lines',
                    coordinateSystem: 'GLMap',
                    zlevel: 0,
                    lineStyle: {
                        normal: {
                            width: 4,
                            curveness: 0.3,

                            // color: '#05f7d5',

                            //线条样式设置
                            color: function (evt) {
                                if (evt.data.csl > 100) {
                                    return 'rgba(240,62,62)'; //红色
                                }
                                return 'rgb(5,247,213)'; //绿色
                            },
                            opacity: 0.6
                        },
                        emphasis: {
                            width: 4,
                            curveness: 0.3,
                            color: 'rgb(252,145,13)',
                            opacity: 0.6
                        }
                    },
                    data: lineData
                },
                //接受单位
                {
                    name: 'to',
                    type: 'effectScatter',
                    coordinateSystem: 'GLMap',
                    zlevel: 3,
                    data: toData,
                    rippleEffect: {
                        period: 10,
                        scale: 5,
                        brushType: 'fill'
                    },

                    // symbolSize: function (evt) {
                    //     let num = evt[2] > 100 ? 100 : evt[2];
                    //     return num / 10;
                    // },

                    //点位闪烁样式
                    itemStyle: {
                        normal: {
                            color: function (evt) {
                                return '#ffff00';
                            },
                            shadowColor: 'rgb(0,255,0)',
                            shadowBlur: 30,
                            shadowOffsetX: 3,
                            shadowOffsetY: 3
                        }
                    }
                },

                //产废单位
                {
                    name: 'from',
                    type: 'effectScatter',
                    coordinateSystem: 'GLMap',
                    symbolSize: [20, 20],
                    symbolOffset: [0, -10],
                    zlevel: 3,
                    circular: {
                        rotateLabel: true
                    },

                    // symbolSize: function (evt) {
                    //     let num = evt[2] > 100 ? 100 : evt[2];
                    //     return num / 10;
                    // },

                    label: {
                        normal: {
                            show: true,
                            position: 'bottom',
                            formatter: '{b}',
                            fontSize: 18,
                            color: '#fff',
                            textBorderColor: '#2aa4e8',
                            offset: [0, 20]
                        }
                    },
                    itemStyle: {
                        color: '#fff',
                        normal: {
                            shadowColor: 'none',
                            color: '#fff',
                            textBorderColor: '#2aa4e8'
                        }
                    },
                    data: fromData // 数据上设置symbol图片,itemstyle不会起作用
                }
            ]
        };

        this.echartslayer = new PowerGL.EchartsLayer(this.map);
        this.echartslayer.chart.setOption(option);
        this.echartslayer.id = params.id;
        if (!params.disableEvent) {
            this.echartslayer.chart.on('click', (evt) => {
                let data = evt.data;
                let fmt = {
                    data: data,
                    type: evt.seriesName
                };
                this.callback(params.id, fmt);
            });
        }
        if (params.enableExtent) {
            let extent = this.getExtentByData(
                [...fromData, ...toData],
                'value'
            );
            let bbox = this.extent2bbox(extent);
            let bounds = this.bbox2Bounds(bbox);
            this.map.fitBounds(bounds, this.sourcFitOption);
        }
    }

    // 获取数据的经纬度范围
    getExtentByData(data, props) {
        let extent = {
            xmin: Infinity,
            xmax: -Infinity,
            ymin: Infinity,
            ymax: -Infinity
        };
        data.forEach((item) => {
            let lngLat = this.getlngLat(item);
            if (props) {
                lngLat = item[props];
            }
            extent.xmin = extent.xmin > lngLat[0] ? lngLat[0] : extent.xmin;
            extent.xmax = extent.xmax < lngLat[0] ? lngLat[0] : extent.xmax;
            extent.ymin = extent.ymin > lngLat[1] ? lngLat[1] : extent.ymin;
            extent.ymax = extent.ymax < lngLat[1] ? lngLat[1] : extent.ymax;
        });
        return extent;
    }

    /**
     * 转移图数据构造
     * 仔细分析数据，结构一下，外围传入的数据尽量结构常规，后续整理demo时优化
     * @param {*} data
     * @returns
     */
    getTransformData(data) {
        let symbolPoint = 'image://gis/3D/images/symbol1.png';

        let toData = [];
        let fromData = [];
        let lineData = [];
        let fromMap = {};
        let toMap = {};

        data.forEach((from) => {
            if (!fromMap[from.QYMC]) {
                fromData.push({
                    name: from.QYMC,
                    value: [from.JD, from.WD],
                    symbol: symbolPoint
                });
                fromMap[from.QYMC] = from;
            }

            from.WFCZ.forEach((to) => {
                lineData.push({
                    name: from.QYMC,
                    toname: to.QYMC,
                    csl: to.WFCSL,
                    coords: [
                        [from.JD, from.WD],
                        [to.JD, to.WD]
                    ]
                });

                if (!toMap[to.QYMC]) {
                    toData.push({
                        name: to.QYMC,
                        value: [to.JD, to.WD].concat(200)
                        // symbol: symbolPoint
                    });
                    toMap[to.QYMC] = to;
                }
            });
        });
        return {
            lineData,
            fromData,
            toData
        };
    }

    /**
     * 删除转移图
     * @param {*} 图层ID
     * @returns
     */
    removeTransport(id) {
        if (
            !this.echartslayer ||
            (id && this.echartslayer && this.echartslayer.id != id)
        ) {
            return;
        }
        this.echartslayer.remove();
        this.echartslayer._container.remove();
        this.echartslayer = null;
    }

    /**
     * 增加热力图图层
     * @param {*} data  点位数组数据
     * @param {*} params 图层参数
     */
    addHeatData(data, params) {
        this.removeLayerByName(params.id);
        let heatPaint = this.getHeatPaint(params) || {};
        let paint = Object.assign(this.heatPaint, heatPaint);

        let features = [];
        data.forEach((item) => {
            let coordinates = this.getlngLat(item);
            if (coordinates) {
                features.push({
                    type: 'Feature',
                    geometry: {
                        type: 'Point',
                        coordinates: coordinates
                    },
                    properties: item
                });
            }
        });

        let layerOption = {
            id: params.id,
            type: 'heatmap',
            source: {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: features
                }
            },
            paint: paint
        };

        //图层相关的设置配置
        if (params.layerOption) {
            layerOption = Object.assign({}, layerOption, params.layerOption);
        }

        if (params.beforeId) {
            this.map.addLayer(layerOption, params.beforeId);
        } else {
            this.map.addLayer(layerOption);
        }
    }

    /**
     * 定义热力图样式，可覆盖此方法
     * @param { } params
     * @returns
     */
    getHeatPaint(params) {
        return {
            // 根据频率和属性大小增加热图权重
            /* 'heatmap-weight': [
                'interpolate',
                ['linear'],
                ['get', 'mag'],
                0,
                0,
                6,
                1
            ], */
            // 按缩放级别增加热图颜色权重
            // 热图强度是热图权重之上的一个乘数
            /* 'heatmap-intensity': [
                'interpolate',
                ['linear'],
                ['zoom'],
                0,
                1,
                9,
                3
            ], */
            // 创建一个色带
            // 'heatmap-color': [
            //     'interpolate',
            //     ['linear'],
            //     ['heatmap-density'],
            //     0,
            //     'rgba(33,102,172,0)',
            //     0.2,
            //     'rgb(103,169,207)',
            //     0.4,
            //     'rgb(209,229,240)',
            //     0.6,
            //     'rgb(253,219,199)',
            //     0.8,
            //     'rgb(239,138,98)',
            //     1,
            //     'rgb(178,24,43)'
            // ],
            // // 按缩放级别调整热图半径
            // 'heatmap-radius': [
            //     'interpolate',
            //     ['linear'],
            //     ['zoom'],
            //     0,
            //     2,
            //     9,
            //     20
            // ],
            // // 透明度
            // 'heatmap-opacity': 0.8
        };
    }

    /**
     * 添加拉升面
     * @param {*} featureCollection
     * @param {*} params 图层参数 id,beforeId,disableClick,disablePopup
     */
    addExtrusionData(featureCollection, params) {
        this.removeLayerByName(params.id);
        let paint = this.getExtrusionPaint(params);

        paint = Object.assign({}, this.fillExtrusionPaint, paint);

        let layerOption = {
            id: params.id,
            type: 'fill-extrusion',
            source: {
                type: 'geojson',
                data: featureCollection
            },
            paint: paint
        };

        //图层相关的设置配置
        if (params.layerOption) {
            layerOption = Object.assign({}, layerOption, params.layerOption);
        }

        //给feature设置ID，只能是整数
        let index = 1;
        for (let item of layerOption.source.data.features) {
            item.id = index++;
        }

        if (params.beforeId) {
            this.map.addLayer(layerOption, params.beforeId);
        } else {
            this.map.addLayer(layerOption);
        }

        if (params) {
            this.addLayerEvent(params);
        }
    }

    /**
     *  拉升多边形样式，可覆盖改方法，没有覆盖则使用默认样式
     * @param {*} params  图层参数，主要是ID
     * @returns
     */
    getExtrusionPaint(params) {
        let paint = {};
        switch (params.id) {
            // case '园区边界线_拉伸':
            //     paint = {
            //         'fill-extrusion-color': 'rgba(0, 233, 255, 1)',
            //         'fill-extrusion-height': 40,
            //         'fill-extrusion-opacity': 1,
            //         'fill-extrusion-vertical-gradient': true,
            //         'fill-extrusion-base': 0
            //     };
            //     break;

            default:
                break;
        }
        return paint;
    }

    /**
     * 添加圆柱数据
     * @param {*} datas  点位数据， 需有height，base_height,color 字段
     * @param {*} params 参数： 需要有id , weight,breadth 参数
     */
    addCylinderData(datas, params) {
        this.removeLayerByName(params.id);

        let features = [];

        datas.forEach((item, index) => {
            features.push({
                id: index++,
                geometry: { coordinates: [], type: 'Polygon' },
                type: 'Feature',
                properties: item
            });
        });

        //重新拼接数据  weight=5（高度）, breadth =0.003 (宽度)
        this.getGeometry(features, params.weight, params.breadth);

        let initPaint = {
            'fill-extrusion-color': ['get', 'color'],
            'fill-extrusion-height': [
                'interpolate',
                ['linear'],
                ['zoom'],
                4,
                0,
                14.05,
                ['get', 'height']
            ],
            'fill-extrusion-base': [
                'interpolate',
                ['linear'],
                ['zoom'],
                4,
                0,
                14.05,
                ['get', 'base_height']
            ],
            'fill-extrusion-opacity': 0.8
        };

        let paint = this.getSymbolPaint(params);
        paint = Object.assign({}, initPaint, paint);

        let layerOption = {
            id: params.id,
            type: 'fill-extrusion',
            source: {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: features
                }
            },
            paint: paint
        };

        //给feature设置ID，只能是整数
        let index = 1;
        for (let item of layerOption.source.data.features) {
            item.id = index++;
        }

        //图层相关的设置配置
        if (params.layerOption) {
            layerOption = Object.assign({}, layerOption, params.layerOption);
        }

        this.map.addLayer(layerOption);

        if (params) {
            this.addLayerEvent(params);
        }
    }

    /**
     * 拼接立体柱状图需要的数据
     * @param {*} feature 要素数组
     * @param {*} weight  高度系数
     * @param {*} breadth 宽度
     */
    getGeometry(feature, weight, breadth) {
        feature.forEach((item) => {
            if (item.geometry && item.geometry.coordinates) {
                item.geometry.coordinates[0] = this.createSquaredByPoint(
                    item.properties,
                    breadth
                );
                item.properties.height = item.properties.height * weight;
            } else {
                console.log('item.geometry.coordinates 不存在');
            }
        });
    }

    /**
     * 根据一个经纬度和偏移宽度，生成一个正方形
     * @param {*} properties
     * @param {*} offset
     * @returns
     */
    createSquaredByPoint(properties, offset) {
        let smx = parseFloat(properties.JD);
        let smy = parseFloat(properties.WD);
        let offset_int = parseFloat(offset);
        return [
            [smx, smy],
            [smx, smy + offset_int],
            [smx - offset_int, smy + offset_int],
            [smx - offset_int, smy],
            [smx, smy]
        ];
    }

    /**
     *  geojson 数据渲染
     * @param {*} featureCollection
     * @param {*} params
     */
    addGeojsonData(featureCollection, params) {
        this.removeLayerByName(params.id);

        let beforeId = params.beforeId;
        let layerOption = {
            id: params.id,
            source: {
                type: 'geojson',
                cluster: false,
                data: featureCollection
            }
        };
        if (params.source) {
            layerOption.source = params.source;
        }
        switch (params.geoType) {
            case 'Point':
                layerOption.type = 'symbol';
                layerOption.layout = this.getSymbolLayout(params);
                layerOption.paint = this.getSymbolPaint(params);
                break;
            case 'LineString':
                layerOption.type = 'line';
                layerOption.paint = this.getLinePaint(params);
                break;
            case 'Polygon':
                layerOption.type = 'fill';
                layerOption.paint = this.getFillPaint(params);
                break;
        }

        //图层相关的设置配置
        if (params.layerOption) {
            layerOption = Object.assign({}, layerOption, params.layerOption);
        }

        //给feature设置ID，只能是整数
        let index = 1;
        for (let item of layerOption.source.data.features) {
            item.id = index++;
        }

        debugger;

        if (beforeId) {
            this.map.addLayer(layerOption, beforeId);
        } else {
            this.map.addLayer(layerOption);
        }

        if (params) {
            this.addLayerEvent(params);
        }
    }

    /**
     * 移除图层
     * @param {*} name  图层名称
     */
    removeLayerByName(name) {
        PowerGL.removeLayerFromName(this.map, name);
        this.removeLayerEvent(name);
    }

    /**
     * 清理marker 图层
     * @param {*} layerId  图层ID
     */
    clear(layerId) {
        if (!this.markers[layerId]) {
            this.markers[layerId] = [];
        }

        this.markers[layerId].forEach((item) => {
            item.remove();
            item = null;
        });
        this.markers[layerId] = [];

        if (window.glTooltip) {
            window.glTooltip.remove();
        }
    }

    /**
     * 图层添加鼠标点击，以及移入，移出事件
     * @param {*} params
     */
    addLayerEvent(params) {
        if (!params.disableClick) {
            this.map.on('click', params.id, this.clickHandle);
        }

        if (!params.disablePopup) {
            this.map.on('mouseenter', params.id, this.mouseMoveHandle);
            this.map.on('mouseleave', params.id, this.mouseMoveLeaveHandle);
        }
    }

    /**
     * 移除图层的事件
     * @param {*} layername 图层ID
     */
    removeLayerEvent(layername) {
        this.map.off('click', layername, this.clickHandle);
        this.map.off('mouseenter', layername, this.mouseMoveHandle);
        this.map.off('mouseleave', layername, this.mouseMoveLeaveHandle);
    }

    /**
     *  获取有效的坐标 ，无坐标则返回null,   可选择覆盖，
     * @param {*} properties
     * @returns
     */
    getlngLat(properties) {
        let jd =
            properties.JD ||
            properties.jd ||
            properties.lng ||
            properties.LON ||
            properties.longitude ||
            properties.LONGITUDE;
        let wd =
            properties.WD ||
            properties.wd ||
            properties.lat ||
            properties.LAT ||
            properties.latitude ||
            properties.LATITUDE;
        if (!jd || isNaN(jd) || !wd || isNaN(wd)) {
            return null;
        }
        return [jd, wd];
    }

    /**
     * 点、线、面缓冲
     * @param {*} data
     * @param {*} params
     * @returns  FeatureCollection|Feature <(Polygon|MultiPolygon)>
     */
    getBuffer(geojson, params) {
        let result = turf.buffer(geojson, params.radius, {
            units: 'kilometers'
        });

        return result;
    }

    /**
     *  将坐标转换为geojson
     * @param {*} data   [{coordinates:[],properties:{}}]
     * @param {*} type Point,MultiPoint,LineString,MultiLineString,Polygon,MultiPolygon
     * 具体格式参考这里 https://datatracker.ietf.org/doc/html/rfc7946#section-3.3
     */
    toGeojson(datas, type) {
        let features = [];
        for (let item of datas) {
            let feature = {
                type: 'Feature',
                geometry: {
                    type: type,
                    coordinates: item.coordinates
                },
                properties: item.properties
            };

            features.push(feature);
        }

        let result = {
            type: 'FeatureCollection',
            features: features
        };

        return result;
    }

    /**
     * 使用面过滤数据
     * @param {*} polygon 面范围
     * @param {*} data  数据数据
     * @returns  数据
     */
    areaFilter(polygon, data) {
        return data.filter((item) => {
            let lngLat = this.getlngLat(item);
            if (!lngLat) {
                return false;
            }
            let pt = turf.point(lngLat);
            return turf.booleanPointInPolygon(pt, polygon);
        });
    }

    /**
     * 面与点位数据的包含关系
     * @param {*} polygon  面范围
     * @param {*} item  对象
     * @returns
     */
    isContian(polygon, item) {
        let flag = false;
        let lngLat = this.getlngLat(item);
        if (!lngLat) {
            return false;
        }
        flag = turf.booleanPointInPolygon(lngLat, polygon);
        return flag;
    }

    /**
     * 大气上风向分析
     * @param {*} obj  分析点位数据
     * @param {*} fxlx  分析类型  SFX:上风向  ZB:周边
     * @param {*} distance 距离 ，单位为千米
     * @param {*} fxjd 风向角度
     */
    addAirRoundData(obj, fxlx, distance, fxjd, option) {
        let arrTemp = [];
        let point = turf.point(this.getlngLat(obj));
        if (fxlx == 'SFX') {
            //画扇形
            let py = 50;
            let bearing1 = fxjd - py;
            let bearing2 = fxjd + py;

            let sector1 = turf.sector(point, distance, bearing1, bearing2);
            arrTemp = [sector1];
        } else {
            //画圆
            let buffered = turf.buffer(point, distance, {
                units: 'kilometers'
            });
            arrTemp = [buffered];
        }

        this.addPolygon(arrTemp, option);

        return arrTemp;
    }
}

export default BasePointUtil;
