<!-- @format -->
<!-- 水质同比变化 -->
<template>
    <div id="yearchange" :style="{ width: width, height: height }"></div>
</template>
<script>
export default {
    props: {
        data: {
            type: Object
        },
        width: {
            type: String
        },
        height: {
            type: String
        }
    },
    data() {
        return {};
    },
    mounted() {
        this.yearchangeEcharts(this.data);
    },
    methods: {
        yearchangeEcharts: function (res) {
            let _this = this;
            let Data = {
                data1: [],
                data2: [],
                xData: [],
                name1: '',
                name2: ''
            };
            Data.xData = res.name;
            let series = [];
            let color = ['#bfe2ff', '#73bb31'];
            res.data.map((item, index) => {
                series.push({
                    name: item.name,
                    type: 'line',
                    data: item.data,
                    barWidth: '12px',
                    itemStyle: {
                        normal: {
                            color: function (params) {
                                if (params.data == '1') {
                                    return '#51a5fd';
                                } else if (params.data == '2') {
                                    return '#51a5fd';
                                } else if (params.data == '3') {
                                    return '#73bb31';
                                } else if (params.data == '4') {
                                    return '#eebd15';
                                } else if (params.data == '5') {
                                    return '#f88e17';
                                } else if (params.data == '6') {
                                    return '#ee3b5b';
                                }
                            },
                            lineStyle: {
                                color: color[index],
                                formatter: function (v) {}
                            }
                        }
                    }
                });
            });
            let option = {
                grid: {
                    top: '10%',
                    left: '7%',
                    right: '2%',
                    bottom: '6%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',

                    formatter: function (params) {
                        let seriesNam1 = params[0].seriesNam;
                        if (seriesNam1 == undefined) {
                            seriesNam1 = '-';
                        }
                        return params.map((item) => {
                            let text =
                                item.seriesName +
                                '年' +
                                item.name +
                                '月:' +
                                _this.getText(item.data) +
                                '<br>';
                            return text;
                        });
                        // params[0].seriesName + '年' + params[0].name + '月:' +
                        // _this.getText(params[0].data) +

                        // '<br>'
                        // +
                        // params[1].seriesName + '年' + params[1].name + '月:' +
                        // _this.getText(params[1].data)
                    }
                    // formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    show: false,
                    right: '3%',
                    top: '4%',
                    textStyle: {
                        color: '#A1D5FF',
                        fontSize: 12
                    },
                    itemWidth: 12, // 设置宽度
                    itemHeight: 16, // 设置高度
                    itemGap: 12 // 设置间距
                },
                xAxis: [
                    {
                        data: Data.xData,
                        axisLabel: {
                            interval: 0,
                            // margin: 10,
                            color: '#A1D5FF',
                            textStyle: {
                                fontSize: 12
                            }
                            // rotate: 45
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#102E74'
                            }
                        },
                        axisTick: {
                            show: false
                        }
                    }
                ],
                yAxis: [
                    {
                        // type: "value",
                        data: ['1', '2', '3', '4', '5', '6'],
                        axisLabel: {
                            // formatter: "{value} %",
                            textStyle: {
                                fontSize: 12,
                                color: function (data) {
                                    if (data == '1') {
                                        return '#51a5fd';
                                    } else if (data == '2') {
                                        return '#51a5fd';
                                    } else if (data == '3') {
                                        return '#73bb31';
                                    } else if (data == '4') {
                                        return '#eebd15';
                                    } else if (data == '5') {
                                        return '#f88e17';
                                    } else if (data == '6') {
                                        return '#ee3b5b';
                                    }
                                }
                            },
                            formatter: function (value, index) {
                                return _this.getText(value);
                            }
                        },
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#102E74'
                            }
                        }
                    }
                ],
                series: series
            };
            let chart = this.$echarts.init(
                document.getElementById('yearchange')
            );

            chart.clear();
            chart.setOption(option);
        },
        getText: function (num) {
            switch (num.toString()) {
                case '1':
                    return 'Ⅰ类';
                case '2':
                    return 'Ⅱ类';
                case '3':
                    return 'Ⅲ类';
                case '4':
                    return 'Ⅳ类';
                case '5':
                    return 'Ⅴ类';
                case '6':
                    return '劣Ⅴ类';
                case '-':
                    return '-';
            }
        }
    }
};
</script>
