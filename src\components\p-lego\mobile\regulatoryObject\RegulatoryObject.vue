<!-- @format -->

<template>
    <div class="zy-cell" style="width: 580px; height: 457px">
        <div class="zy-cell-hd">
            <p class="til til1">监管对象</p>
            <span class="more"></span>
        </div>

        <div>
            <div class="zy-data1">
                <div class="lp">
                    <img src="./images/data1-li1.png" alt="" />
                    <p>污染源</p>
                </div>
                <div class="rp">
                    <div class="item">
                        <p class="p1">{{ data.WRY_TOTAL || '0' }}</p>
                        <p class="p2">总数</p>
                    </div>
                    <div class="item">
                        <p class="p1">{{ data.WRY_ZDY_TOTAL || '0' }}</p>
                        <p class="p2">重点源</p>
                    </div>
                    <div class="item">
                        <p class="p1">{{ data.WRY_YBY_TOTAL || '0' }}</p>
                        <p class="p2">一般源</p>
                    </div>
                </div>
            </div>
            <div class="zy-data1">
                <div class="lp">
                    <img src="./images/data1-li2.png" alt="" />
                    <p>排污许可证</p>
                </div>
                <div class="rp">
                    <div class="item">
                        <p class="p1">{{ data.PWXKZ_TOTAL || '0' }}</p>
                        <p class="p2">总数</p>
                    </div>
                    <div class="item">
                        <p class="p1">{{ data.PWXKZ_ZDGL_TOTAL || '0' }}</p>
                        <p class="p2">重点管理</p>
                    </div>
                    <div class="item">
                        <p class="p1">{{ data.PWXKZ_JHGL_TOTAL || '0' }}</p>
                        <p class="p2">简化管理</p>
                    </div>
                    <div class="item">
                        <p class="p1">{{ data.PWXKZ_DJGL_TOTAL || '0' }}</p>
                        <p class="p2">登记管理</p>
                    </div>
                </div>
            </div>

            <div class="zy-data1">
                <div class="lp">
                    <img src="./images/data1-li3.png" alt="" />
                    <p>在线监测</p>
                </div>
                <div class="rp">
                    <div class="item">
                        <p class="p1">{{ data.ZXJC_TOTAL || '0' }}</p>
                        <p class="p2">总数</p>
                    </div>
                    <div class="item">
                        <p class="p1">{{ data.ZXJC_FS_TOTAL || '0' }}</p>
                        <p class="p2">废水</p>
                    </div>
                    <div class="item">
                        <p class="p1">{{ data.ZXJC_FQ_TOTAL || '0' }}</p>
                        <p class="p2">废气</p>
                    </div>
                </div>
            </div>

            <div class="zy-line1">
                <div class="zy-data1">
                    <div class="lp">
                        <img src="./images/data1-li4.png" alt="" />
                        <p>正面清单</p>
                    </div>
                    <div class="rp">
                        <div class="item">
                            <p class="p1">{{ data.ZMQD_TOTAL || '0' }}</p>
                        </div>
                    </div>
                </div>
                <div class="zy-data1">
                    <div class="lp">
                        <img src="./images/data1-li5.png" alt="" />
                        <p>人均监管</p>
                    </div>
                    <div class="rp">
                        <div class="item">
                            <p class="p1">{{ data.RJJG_TOTAL || '0' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'regulatoryObject',
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    WRY_YBY_TOTAL: 10012,
                    ZMQD_TOTAL: 3,
                    PWXKZ_ZDGL_TOTAL: 6,
                    PWXKZ_JHGL_TOTAL: 4,
                    PWXKZ_TOTAL: 19,
                    ZXJC_FS_TOTAL: 1,
                    RJJG_TOTAL: 3.98,
                    ZXJC_TOTAL: 7,
                    WRY_TOTAL: 10022,
                    PWXKZ_DJGL_TOTAL: 6,
                    ZXJC_FQ_TOTAL: 2,
                    WRY_ZDY_TOTAL: 10
                };
            }
        }
    },
    watch: {},
    data() {
        return {};
    },
    mounted() {},
    methods: {}
};
</script>

<style lang="scss" scoped>
/* -- Reset -- */
body,
ul,
ol,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
p,
form,
input,
textarea,
select,
button {
    margin: 0;
    padding: 0;
    font: 12px 'Microsoft YaHei', SimSun, Arial, Helvetica, sans-serif;
}
@font-face {
    font-family: 'TTTGB';
    src: url('./fonts/TTTGB-Medium.woff2') format('woff2'),
        url('./fonts/TTTGB-Medium.woff') format('woff'),
        url('./fonts/TTTGB-Medium.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'DIN-Bold';
    src: url('./fonts/DIN-Bold.woff2') format('woff2'),
        url('./fonts/DIN-Bold.woff') format('woff'),
        url('./fonts/DIN-Bold.ttf') format('truetype'),
        url('./fonts/DIN-Bold.eot') format('embedded-opentype'),
        url('./fonts/DIN-Bold.svg') format('svg'),
        url('./fonts/DIN-Bold.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}
.zy-cell {
    background-color: rgba(16, 37, 58, 1);
    position: relative;
    margin-bottom: 10px;
    padding-bottom: 1px;
}
.zy-cell::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    background-image: url(./images/cell-bg.png);
}
.zy-cell-hd {
    display: flex;
    height: 46px;
    padding: 0 10px;
    padding-bottom: 6px;
    background: url(./images/hd-bg.png) center bottom no-repeat;
    justify-content: space-between;
    align-items: center;
}

.zy-cell-hd .til {
    padding-left: 38px;
    font-size: 22px;
    color: #fff;
    line-height: 46px;
    background-position: 0 10px;
    background-repeat: no-repeat;
    font-family: 'TTTGB';
    text-shadow: 1px 1px 5px rgba(255, 255, 255, 0.8);
}

.zy-cell-hd .til1 {
    background-image: url(./images/til1.png);
}

.zy-cell-hd .more {
    width: 20px;
    height: 20px;
    background: url(./images/ic-more.png) no-repeat;
    cursor: pointer;
    margin-right: 20px;
}
.zy-data1 {
    display: flex;
    background: url(./images/data1-bg1.png) no-repeat;
    width: 559px;
    height: 84px;
    margin-left: 8px;
    margin-top: 13px;
}

.zy-data1 .lp {
    width: 120px;
    text-align: center;
    padding-left: 10px;
}

.zy-data1 .lp img {
    display: inline-block;
    margin-top: 8px;
}

.zy-data1 .lp p {
    font-size: 16px;
    color: #fff;
    line-height: 18px;
}

.zy-data1 .rp {
    display: flex;
    flex: 1;
    position: relative;
}

.zy-data1 .rp::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 50px;
    width: 1px;
    background-color: rgba(0, 149, 255, 0.3);
}

.zy-data1 .rp .item {
    width: 25%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-sizing: border-box;
    padding-left: 15px;
}

.zy-data1 .rp .item .p1 {
    font-size: 30px;
    color: #00b4ff;
    line-height: 42px;
    font-family: 'DIN-Bold';
}

.zy-data1 .rp .item .p2 {
    font-size: 16px;
    color: #fff;
    line-height: 30px;
}

.zy-line1 {
    display: flex;
    justify-content: space-between;
    width: 559px;
    margin-left: 8px;
}

.zy-line1 .zy-data1 {
    margin-left: 0;
    width: 279px;
    background-image: url(./images/data1-bg2.png);
}
.zy-data1 .rp .item .p2,
.zy-data1 .lp p {
    color: #eee;
}
</style>
