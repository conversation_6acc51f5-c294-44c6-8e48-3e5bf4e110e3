/** @format */
// eslint-disable-next-line no-unused-vars
const ServerGlobalConstant = {
    ctx: '', // 服务器地址 //http://yapi.powerdata.com.cn:31665/mock/1211
    dataUrl: '/stfs_gd', // 服务地址
    previewUrl: 'https://m.gdeei.cn/fsonlinePreview/onlinePreview?url=',
	flipbookPreviewUrl: 'https://m.gdeei.cn/stfs_gd/resources/pdf-flipbook-master/index.html?url=',
    fileDownloadUrl:
        'http://***********:8070/stfs_gd/platform/file/filemanagecontroller/downloadfilebyid/', //文件预览下载地址(需填写完整地址)
    videoStreamUrl: '/stfs_gd/xxks/ZskController/getfilestream/',
    bannerUrl: '/stfs_gd/platform/file/filemanagecontroller/getfilestream/', //封面预览地址
    eleTheme: 'light', // elementUI颜色 dark 深色系做大屏颜色 light 白色
    otherThemeArrRoute: ['/app/event'], // 和色系相反的颜色 深对应就是浅色
    needChangeTheme: false, // 是否需要换肤  （读缓存）

    // gis 配置地址
    isLoad3DMap: true, //是否引入3D相关参数
    isLoad2DMap: true, //是否引入2D相关参数

    arcgisApiHost: 'http://**************:9987/arcgis_js_v330/api/3.30/', //arcgis api 地址
    arcgisServerHost: '', //arcgis 服务前缀

    mapboxFontLocal: 'http://localhost:8099', //mapbox 本地字体，样式
    geoserverHost: 'http://**************:9988/geoserver', // mapbox geoserver 服务地址

    czUrl: 'https://***********:8899/SCZ/jsonFile',
	wordPreviewUrl: '/xxks/ZskController/toPageWord/',
};

// 需要换肤的时候加载
if (ServerGlobalConstant.needChangeTheme) {
    if (localStorage.getItem('themeType')) {
        ServerGlobalConstant.eleTheme = localStorage.getItem('themeType');
    } else {
        localStorage.setItem('themeType', ServerGlobalConstant.eleTheme);
    }
}

//随机赋值天地图key,避免一个key超过请求限制
window.arrTdtKey = [
    // 'ddad5622eece8875194a8e75bc3e3a02',
    // '11dd40f64ed740b8092c8fa1e3193eb4',
    // '716101b25a3eda5029e7e18b0804c77d',
    // '2c1bed67b4438d629ac9ac97cae00f74',
    // '61a0f4a5d9033182e868bd9df056a7dc',
    // '347a929eeda90006869fea9391429f7f',
    // '2ebdf01ebc100721617d8dd809df3c32',
    // '37ddc778af522657ddbe6450680d83a2',
    // '0643fc05d3788bfafc76869f55bb32ea',

    //新申请天地图可以
    'ddad5622eece8875194a8e75bc3e3a02', //一部
    '11dd40f64ed740b8092c8fa1e3193eb4', //二部
    '716101b25a3eda5029e7e18b0804c77d', //执法
    '2c1bed67b4438d629ac9ac97cae00f74', //质量
    '61a0f4a5d9033182e868bd9df056a7dc', //北京
    '347a929eeda90006869fea9391429f7f', //平台
    '2ebdf01ebc100721617d8dd809df3c32', //待定
    '37ddc778af522657ddbe6450680d83a2', //待定
    '0643fc05d3788bfafc76869f55bb32ea' //待定
];
let randNum = Math.floor(window.arrTdtKey.length * Math.random());
ServerGlobalConstant.ProjectTiandituKey = window.arrTdtKey[randNum];

// 是为了兼容不同ip访问段，如果有需要可以打开注释
// if (location.host.indexOf('**************') === 0) {
//     ServerGlobalConstant.ctx = '';
//     ServerGlobalConstant.dataUrl = '';
// }
