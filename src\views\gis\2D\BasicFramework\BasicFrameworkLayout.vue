<!-- @format -->

<template>
    <el-container class="main" direction="vertical" style="height: 100%">
        <el-main class="gisMain" style="padding: 0px; overflow: hidden">
            <div style="width: 100%; height: 100%">
                <esri-map
                    :mapOption="mapOption"
                    @onMapLoaded="onMapLoadedHandle"
                ></esri-map>
            </div>

            <!--  基础工具 (右上) -->
            <TopRightTool
                class="baseTool"
                :mapOption="mapOption"
                :mapCls="mapCls"
                :map="map"
                :class="{ off: !showPanel }"
                :dock="toolDock"
            ></TopRightTool>

            <!-- 右下角 -->
            <BottomRightTool
                :map="map"
                :mapOption="mapOption"
                class="bottomRightToolCls"
                :class="{ off: !showPanel }"
            ></BottomRightTool>

            <!-- 业务面板 -->
            <BasicMain
                :map="map"
                @mapEvent="mapEventHandle"
                ref="childMain"
            ></BasicMain>
        </el-main>
    </el-container>
</template>
<script>
import EsriMap from '@/components/gis/2D/EsriMap';
import BottomRightTool from '@/components/gis/2D/normal/BottomRightTool';
import TopRightTool from '@/components/gis/2D/normal/TopRightTool';
import BasicMain from './BasicMain';

export default {
    name: 'BasicFrameworkLayout', //基础框架源码
    created() {},
    data() {
        return {
            mapOption: {}, //地图配置上，可以用来覆盖配置文件中的对应值
            map: null,
            toolDock: 'right',

            mapCls: '.gisMain',
            showPanel: false //右侧面板是否关闭
        };
    },
    unmounted() {},
    components: {
        EsriMap,
        TopRightTool,
        BottomRightTool,
        BasicMain
    },
    mounted() {},
    methods: {
        //地图加载完成
        onMapLoadedHandle(map) {
            this.map = map;
        },

        //地图事件：与前端交互
        mapEventHandle(type, item) {
            //type：mapClick(地图点位点击)、
            this.$emit('mapEvent', type, item);
        },

        //地图交互方法：供前端调用
        mapFuc(type, item) {
            switch (type) {
                case 'RPanel': //右侧面板
                    this.showPanel = item;
                    // this.$refs.childMain.showPanleChangeHandle(this.showPanel);
                    break;

                case 'pointTo': //定位(item中必须包含JD、WD字段)
                    PowerGis.pointTo(this.map, item, true);
                    break;
            }
        }
    }
};
</script>

<style>
@import '~_as/gis/commom/mapCommon.css';
@import '~_as/gis/commom/mapTool.css';
@import '~_as/gis/2D/BasicFramework/css/style.css';
</style>

<style scoped>
.baseTool {
    position: absolute;
    top: 120px;
    right: 380px;
}

.baseTool.off {
    right: 10px;
}

.bottomRightToolCls {
    position: absolute;
    bottom: 80px;
    right: 380px;
}

.bottomRightToolCls.off {
    right: 10px;
}
</style>
