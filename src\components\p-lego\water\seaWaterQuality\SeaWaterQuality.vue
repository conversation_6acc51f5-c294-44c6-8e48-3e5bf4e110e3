<!-- @format -->

<template>
    <div class="pd-row1">
        <div class="pd-modbd" style="padding: 0 10px 0 26px">
            <div class="gap"></div>
            <div class="gap"></div>
            <div class="pd-row1">
                <div style="margin-left: -60px; margin-top: -50px">
                    <p-pie
                        :data="data"
                        :option="pieOpt"
                        :config="option"
                        :style="{ width: option.width, height: option.height }"
                    ></p-pie>
                </div>

                <div class="pd-tablebx1" style="margin-left: -50px">
                    <table cellpadding="0" class="pd-tablelst1 otr">
                        <thead>
                            <tr>
                                <td>水质类别</td>
                                <td>海域面积占比</td>
                                <td>同比变化</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item, index) in data" :key="index">
                                <td>{{ item.name }}</td>
                                <td>{{ item.value }}%</td>
                                <td>{{ item.TBBH }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        data: {
            type: Array
        },
        option: {
            type: Object
        }
    },
    created() {
        this.$pChart.setChartConfig({
            SHOW_TOOLBOX: false
        });
    },
    data() {
        return {
            SCList: [],
            pieOpt: {
                series: [
                    {
                        label: {
                            show: false
                        }
                    }
                ],
                title: {
                    textStyle: { color: '#fff', fontSize: 16 }
                }
            }
        };
    },
    mounted() {}
};
</script>

<style scoped>
.pd-row1 {
    display: flex;
}
.pd-tablebx1 {
    flex: 1;
    padding-left: 30px;
}
.pd-tablebx1 a {
    font-size: 14px;
    color: #fff;
    float: right;
    padding-right: 30px;
}
.pd-tablelst1 {
    width: 300px;
}
.pd-tablelst1 tr td {
    font-size: 14px;
    color: #fff;
    text-align: center;
    height: 40px;
}

.pd-tablelst1 tr td .prog {
    height: 16px;
    border-radius: 300px;
    background: #11578b;
    position: relative;
}
.pd-tablelst1 tr td .prog b {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background: #34dbff;
    border-radius: 300px 0 0 300px;
}
.pd-tablelst1 tbody tr:first-child td {
    padding-top: 13px;
}
.pd-tablelst1.otr tbody tr:first-child td {
    padding-top: 0;
}
.pd-tablelst1.thd thead tr td {
    font-size: 16px;
}
.pd-tablelst1 tr td.tal {
    text-align: left;
}
.pd-tablelst1.tbd tbody tr td {
    height: 60px;
}
.pd-tablelst1.tbd tbody tr td:last-child {
    font-size: 14px;
}
.pd-tablelst1.otr.htn tbody tr td {
    height: auto;
    padding: 5px 0;
    border-bottom-color: #35626d;
    line-height: 2;
}
.pd-tablelst1.otr.htn tr td:first-child {
    text-align: right;
}
.pd-tablelst1.otr tr td.tal {
    text-align: left;
}
.pd-tablelst1 thead tr td {
    font-size: 14px;
    color: #2691d5;
    height: 33px;
    background: rgba(39, 117, 155, 0.35);
}
.pd-tablelst1 tbody tr:nth-child(even) td {
    background: rgba(59, 160, 255, 0.1);
}
</style>
