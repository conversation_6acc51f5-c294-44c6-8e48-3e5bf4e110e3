<!-- @format -->

<template>
    <div class="sw0725-maparea1" style="width: 1000px">
        <MapboxMap
            :mapOption="mapOption"
            @onMapLoaded="onMapLoadedHandle"
        ></MapboxMap>
        <div class="abs" style="left: 20px; top: 10px; width: 200px">
            <JGJBCheck
                :zbzdtj="zbzdtj"
                @jgjbChange="jgjbChangeHandle"
            ></JGJBCheck>
            <div class="gap"></div>
            <dl class="sw0725-dlbx1">
                <dt class="flx1 ac">
                    <label class="sw0721-check1"><span>涉气污染源</span></label>
                </dt>
                <GDYControl
                    :zbwrytj="zbwrytj"
                    :paramsData="paramsData"
                    ref="childGDY"
                ></GDYControl>
            </dl>
            <div class="gap"></div>
            <!-- <dl class="sw0725-dlbx1">
                <dt><p>图层控制</p></dt>
                <dd class="sw0725-swclst">
                    <p>热点网格</p>
                </dd>
            </dl> -->
            <div class="gap"></div>
            <FactorCheck
                @factorChange="factorChangeHandle"
                ref="childFactor"
            ></FactorCheck>
        </div>

        <TopRightTool
            class="abs"
            :mapCls="mapCls"
            style="right: 10px; top: 10px"
        ></TopRightTool>

        <!-- 图例 -->
        <LegendTool
            class="gis-legend"
            :arrLegend="arrLegend"
            :expand="true"
        ></LegendTool>
    </div>
</template>

<script>
import MapboxMap from '@/components/gis/3D/MapBoxGLMap';
import JGJBCheck from './components/JGJBCheck.vue';
import PointUtil from './utils/PointUtil';
import FactorCheck from './components/FactorCheck';
import GDYControl from './components/GDYControl';
import TopRightTool from '@/components/gis/3D/normal/TopRightTool2.vue';
import LegendTool from '@/components/gis/3D/LegendTool';

import DataMock from './utils/DataMock';

import { getZbzdinfoList, getSqwryxxList } from '@/api/gis/3D/AirMap/index';

export default {
    props: ['paramsData'],
    data() {
        return {
            mapOption: {},
            map: null,
            mapCls: 'body',

            jcdArr: null, //空气站点
            selectYZ: 'AQI',
            jgjbArr: null, //监管级别

            zbzdtj: {
                gk: 0,
                sk: 0,
                dsk: 0,
                wz: 0
            }, //周边站点统计
            zbwrytj: {}, //周边污染源统计

            zdinfo: null, //站点数据

            fxjd: 50, //风向角度
            zbfw: 5,
            fxlx: 'ZB', //分析类型

            allData: {}, //所有结果数据

            arrLegend: [
                {
                    title: '空气站',
                    data: [
                        { name: '优', url: 'c-1.png' },
                        { name: '良', url: 'c-2.png' },
                        { name: '轻度污染', url: 'c-3.png' },
                        { name: '中度污染', url: 'c-4.png' },
                        { name: '重度污染', url: 'c-5.png' },
                        { name: '严重污染', url: 'c-6.png' }
                    ]
                },
                {
                    title: '在线监测',
                    data: [
                        { name: '正常', url: 'FQ-ZC.png' },
                        { name: '超标', url: 'FQ-CB.png' },
                        { name: '异常', url: 'FQ-WSJ.png' }
                    ]
                },
                {
                    title: '固定源',
                    data: [{ name: '重点源', url: 'SQZDY.png' }]
                }
            ]
        };
    },
    components: {
        MapboxMap,
        JGJBCheck,
        FactorCheck,
        GDYControl,
        TopRightTool,
        LegendTool
    },

    mounted() {},
    methods: {
        onMapLoadedHandle(map) {
            this.map = map;
            map.resize();

            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            if (this.paramsData.YJYZ) {
                let yzs = this.paramsData.YJYZ.split(',');
                if (yzs.length > 0) {
                    this.selectYZ = yzs[0].toUpperCase();
                    this.$refs.childFactor.selectYZ = this.selectYZ;
                }
            }
        },

        initData(fxlx, fw, fxjd) {
            this.fxjd = fxjd;
            this.zbfw = fw;
            this.fxlx = fxlx;

            if (this.pointUtil) {
                this.drawMapFW();
            } else {
                setTimeout(() => {
                    this.initData(fxlx, fw, fxjd);
                }, 1000);
            }
        },

        //查询周边站点
        getZbzdinfoList() {
            let param = {
                jd: this.paramsData.JD,
                wd: this.paramsData.WD,
                fw: this.zbfw,
                time: this.paramsData.YJSJ
            };

            getZbzdinfoList(param).then((res) => {
                this.jcdArr = res.data.map((item) => {
                    item.aqiLevel = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('AQI', item, 1)
                    );

                    item.pm25Level = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('PM25', item, 1)
                    );

                    item.pm10Level = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('PM10', item, 1)
                    );

                    item.o3Level = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('O3', item, 1)
                    );

                    item.so2Level = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('SO2', item, 1)
                    );

                    item.no2Level = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('NO2', item, 1)
                    );

                    item.coLevel = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('CO', item, 1)
                    );
                    return item;
                });

                this.jcdArr = this.jcdArr.filter((oo) => {
                    return (
                        this.pointUtil.isContian(this.jsqGeo.features[0], oo) ||
                        this.paramsData.CDDM == oo.CDDM
                    );
                });

                for (let item of this.jcdArr) {
                    if (item.JGJB == '1') {
                        this.zbzdtj.gk++;
                    } else if (item.JGJB == '2') {
                        this.zbzdtj.sk++;
                    } else if (item.JGJB == '3') {
                        this.zbzdtj.dsk++;
                    }
                }

                this.allData['zbzd'] = this.jcdArr;
                this.$emit('resultChange', 'zbzd', this.jcdArr);

                this.filterData();
            });
        },

        //监管级别统计
        jgjbChangeHandle(data) {
            this.jgjbArr = data;
            this.filterData();
        },

        // 大气因子改变
        factorChangeHandle(yz) {
            this.selectYZ = yz;
            this.filterData();
        },

        //过滤空气点位
        filterData() {
            if (this.jcdArr) {
                let arr = this.jcdArr.filter((item) => {
                    return this.jgjbArr.includes(item.JGJB.toString());
                });

                let params = {
                    id: '大气站点',
                    selectYZ: this.selectYZ,
                    showAno: true,
                    disablePopup: false,
                    disableClick: true,
                    mbzd: this.paramsData.CDDM,
                    style: '5'
                };

                this.pointUtil.addCustomHtmlLayer(arr, params);
            }
        },

        pointClickHandle(type, item) {
            console.log('地图点击：' + type);
        },

        //地图绘制圆或者扇形
        drawMapFW() {
            let obj = this.paramsData || {};

            let option = {
                id: '周边范围',
                disablePopup: true,
                disableClick: true
            };

            let arrTemp = this.pointUtil.addAirRoundData(
                obj,
                this.fxlx,
                this.zbfw,
                this.fxjd,
                option
            );

            this.jsqGeo = {
                features: arrTemp,
                type: 'FeatureCollection'
            };

            let dataType = 'Polygon';
            let option2 = {
                scale: 1.5,
                padding: {
                    // right: 500
                }
            };

            //定位到geojson
            PowerGL.pointToGeoJson(this.map, this.jsqGeo, dataType, option2);

            this.drawEndHandle();
        },

        //绘制完成
        drawEndHandle() {
            //查询周边站点
            this.getZbzdinfoList();

            //周边企业
            this.getData();
        },

        //周边企业
        getData() {
            for (let item of this.$refs.childGDY.zts) {
                this.getSqwryxxList(item);
            }
        },

        //周边企业
        getSqwryxxList(obj) {
            let type = obj.dm;
            let param = {
                jd: this.paramsData.JD,
                wd: this.paramsData.WD,
                fw: this.zbfw,
                dataType: type,
                time: this.paramsData.YJSJ
            };

            getSqwryxxList(param).then((res) => {
                this.allData[type] = res.data.filter((oo) => {
                    return this.pointUtil.isContian(
                        this.jsqGeo.features[0],
                        oo
                    );
                });

                //计算距离
                for (let item of this.allData[type]) {
                    if (item.JD && item.WD) {
                        let distance = PowerGL.getDistance(
                            item.WD,
                            item.JD,
                            this.paramsData.WD,
                            this.paramsData.JD
                        );

                        item.distance = distance.toFixed(1);
                    } else {
                        item.distance = '--';
                    }
                }

                this.allData[type].sort((a, b) => {
                    return a.distance > b.distance ? 1 : -1;
                });

                this.zbwrytj[obj.dm] = this.allData[type].length;

                this.$emit('resultChange', obj.dm, this.allData[type]);

                this.pointUtil.addImgPoint(this.allData[type], {
                    id: obj.name,
                    disablePopup: false
                });

                PowerGL.setLayerVisible(this.map, obj.name, obj.selected);
            });
        }
    }
};
</script>

<style scoped>
.gis-legend {
    position: absolute;
    right: 10px;
    bottom: 10px;
    height: 100px !important;
}
</style>

<style>
.mapboxgl-marker {
    opacity: 1 !important;
}

.darkTheme .zy-tools-gis li {
    background: #005861 !important;
}

.gis-tuli .item .lp {
    width: auto !important;
}

.gis-tuli .item .rp p img {
    max-height: 25px;
}
</style>
