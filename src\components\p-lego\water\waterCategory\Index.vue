<!-- @format -->

<template>
    <p-pie
        class="water-category"
        :option="pieOpt"
        :data="pieData"
        :config="{
            color: 'waterGradesColor',
            showLabel: false,
            showLegend: true,
            radius: '90%',
            center: ['25%', '52%'],
            legendOrient: 'vertical',
            legendTextStyle: {
                rich: {
                    name: { width: 55 },
                    value: { width: 40 }
                }
            }
        }"
    ></p-pie>
</template>

<script>
export default {
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        }
    },
    data() {
        return {
            pieData: [],
            pieOpt: {
                tooltip: {
                    formatter: function (obj) {
                        let data = obj.data;
                        return `${data.name}：${data.value}个, ${data.percent}%`;
                    }
                }
            }
        };
    },
    watch: {
        data: 'renderPieData'
    },
    mounted() {
        this.renderPieData();
    },
    methods: {
        renderPieData() {
            this.pieData = [];
            let sum = 0;
            this.data.forEach((v) => {
                sum += isNaN(v.value) ? 0 : Number(v.value);
            });

            this.data.forEach((item) => {
                let percent = item.percent || 0;
                if (item.percent === undefined && item.value > 0) {
                    percent = ((100 * item.value) / sum).toFixed(2);
                }

                this.pieData.push({
                    name: item.name,
                    value: item.value,
                    percent: percent,
                    legendName: `{name|${item.name}} {value|${item.value}个} ${percent}%`
                });
            });
        }
    }
};
</script>

<style lang="less" scoped>
.water-category {
    height: 160px;
}
</style>
