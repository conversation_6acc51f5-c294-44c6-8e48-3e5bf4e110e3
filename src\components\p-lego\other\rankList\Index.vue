<!-- @format -->

<template>
    <div class="rank-list">
        <table class="rank-list-table" v-if="data && data.length">
            <colgroup>
                <col width="35px" />
                <col width="15%" />
                <col />
                <col width="10%" />
            </colgroup>
            <tr v-for="(item, index) in data" :key="index">
                <td class="rank-list-td-index">
                    <span :class="getnoClass(index)">{{ index + 1 }}</span>
                </td>
                <td class="rank-list-td-label">
                    {{ item.name }}
                </td>
                <td>
                    <div class="rank-list-bar">
                        <div
                            class="rank-list-bar-progress"
                            :style="{ width: getWidth(item.value) }"
                        ></div>
                    </div>
                </td>
                <td class="rank-list-td-value">
                    <em>{{ item.value }}</em
                    ><sub v-if="finalOption.unit">{{ finalOption.unit }}</sub>
                </td>
            </tr>
        </table>

        <div class="rank-list-nomsg" v-else>暂无数据</div>
    </div>
</template>

<script>
export default {
    props: {
        data: {
            type: Array
        },
        option: {
            type: Object
        }
    },
    computed: {
        finalOption() {
            return this.option
                ? {
                      ...this.defaultOption,
                      ...this.option
                  }
                : this.defaultOption;
        },
        max() {
            //刻度最大值
            if (this.finalOption.max) {
                return this.finalOption.max;
            }
            let max = 0;
            this.data.forEach((v) => {
                if (v.value - max > 0) {
                    max = v.value;
                }
            });
            return max;
        }
    },
    data() {
        return {
            defaultOption: {
                max: null, //进度条满刻度 不传时 取数据中的最大值
                unit: '' //数值单位
            }
        };
    },
    methods: {
        getnoClass(e) {
            if (e <= 2) {
                let x = parseInt(e) + 1;
                return 'rank-list-index rank-list-index-' + x;
            } else {
                return 'rank-list-index';
            }
        },
        getWidth(value) {
            let max = this.max;
            if (!value || !max || isNaN(value) || isNaN(max)) {
                return '0';
            }
            return ((value * 100) / max).toFixed(1) + '%';
        }
    }
};
</script>

<style lang="less">
.darkTheme {
    --bar-bg-color: rgba(0, 255, 255, 0.1);
    --progress-color: linear-gradient(
        -90deg,
        rgba(9, 212, 209, 0.82),
        rgba(31, 247, 255, 0.71),
        rgba(17, 121, 119, 0.41)
    );
}
.lightTheme {
    --bar-bg-color: rgba(0, 0, 0, 0.1);
    --progress-color: linear-gradient(
        -90deg,
        rgba(9, 212, 209, 0.82),
        rgba(31, 247, 255, 0.71),
        #09d4d1
    );
}
</style>
<style lang="less" scoped>
.rank-list {
    width: 100%;
    height: 100%;
    overflow-y: auto;

    td {
        height: 53px;
        padding: 0;
        border: none;
        font-size: 16px;
    }

    .rank-list-td-index {
        text-align: center;
    }

    .rank-list-td-label {
        text-align: left;
        white-space: nowrap;
        padding: 0 10px;
    }

    .rank-list-td-value {
        text-align: left;
        white-space: nowrap;
        padding: 0 10px;

        & > em {
            font-size: 18px;
            font-family: 'DIN-Bold';
        }
        & > sub {
            font-size: 14px;
            vertical-align: baseline;
            padding-left: 5px;
        }
    }

    &-index {
        width: 35px;
        height: 35px;
        display: inline-block;
        line-height: 35px;
        text-align: center;
        background-size: 100% 100%;
        &-1 {
            background-image: url(./images/<EMAIL>);
            color: transparent;
        }
        &-2 {
            background-image: url(./images/<EMAIL>);
            color: transparent;
        }
        &-3 {
            background-image: url(./images/<EMAIL>);
            color: transparent;
        }
    }

    &-bar {
        height: 12px;
        background: var(--bar-bg-color);
        border-radius: 6px;
        &-progress {
            position: relative;
            height: 100%;
            border-radius: 6px;
            width: 0;
            background: var(--progress-color);

            &::before {
                content: '';
                position: absolute;
                width: 22px;
                height: 22px;
                right: -6px;
                top: -6px;
                background: url(./images/point.png);
                background-size: 100% 100%;
            }
        }
    }

    &-nomsg {
        font-size: 16px;
        text-align: center;
        line-height: 40px;
    }
}
</style>
