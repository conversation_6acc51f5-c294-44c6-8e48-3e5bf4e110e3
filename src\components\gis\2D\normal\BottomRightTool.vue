<!-- @format -->

<template>
    <div>
        <!-- 坐标 -->
        <Coordinate
            :map="map"
            :class="{ 'right-small': eyeType == 'small' }"
        ></Coordinate>
        <!-- 图层切换 -->
        <MapSwitchTool
            :class="{ 'MapSwitchTool-small': eyeType == 'small' }"
            @baseMapChange="baseMapChangeHandle"
            :map="map"
            :mapOption="mapOption"
            ref="childMapSwitch"
        ></MapSwitchTool>
        <!-- 鹰眼 -->
        <EagleEye
            @sizeChange="sizeChangeHandle"
            ref="childEye"
            :map="map"
        ></EagleEye>
        <!-- 比例尺 -->
        <ScaleTool
            :class="{ 'right-small': eyeType == 'small' }"
            :map="map"
        ></ScaleTool>
    </div>
</template>

<script>
import Coordinate from '../Coordinate';
import MapSwitchTool from '../MapSwitchTool';
import EagleEye from '../EagleEye';
import ScaleTool from '../ScaleTool';
export default {
    name: 'BottomRightTool',
    props: ['map', 'mapOption'],
    data() {
        return {
            eyeType: 'big' //鹰眼模式
        };
    },
    components: { Coordinate, MapSwitchTool, EagleEye, ScaleTool },
    computed: {},
    mounted() {},
    methods: {
        sizeChangeHandle(type) {
            this.eyeType = type;
        },

        baseMapChangeHandle() {
            this.$refs.childEye.loadMap();
        },

        setBaseMap(item) {
            this.$refs.childMapSwitch.changeMap(item);
        }
    },
    watch: {}
};
</script>

<style scoped>
.MapSwitchTool-small {
    bottom: 70px;
}

.right-small {
    margin-right: -70px;
}
</style>
