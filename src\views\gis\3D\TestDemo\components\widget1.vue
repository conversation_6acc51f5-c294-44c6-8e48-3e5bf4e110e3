<!-- @format -->
<!-- 热力图  -->
<template>
    <div></div>
</template>

<script>
import PointUtil from '../utils/PointUtil';
export default {
    props: [],
    data() {
        return {
            map: null,
            pointUtil: null,
            layerID: '热力图'
        };
    },

    mounted() {
        this.initPage();
    },

    unmounted() {
        this.clear();
    },

    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.addLayer();
        },

        //热力图
        addLayer() {
            let data = [];

            for (let i = 0; i < 200; i++) {
                let obj = {
                    JD: 120.59712716 + (Math.random() - 0.5) * 0.2,
                    WD: 30.96939348 + (Math.random() - 0.5) * 0.3,
                    mag: 6 * Math.random() //与heatpaint 配合设置
                };

                data.push(obj);
            }

            let params = {
                id: this.layerID
            };

            this.pointUtil.addHeatData(data, params);
        },

        clear() {
            this.pointUtil.removeLayerByName(this.layerID);
        },

        pointClickHandle() {}
    }
};
</script>

<style scoped></style>
