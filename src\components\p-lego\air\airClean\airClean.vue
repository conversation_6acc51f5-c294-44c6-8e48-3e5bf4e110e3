<!--·@format· 清新空气-->
<template>
    <div class="pd-aqibx2" :style="{ background: options.background }">
        <div class="pd-stithd">
            <span
                >清新空气
                <em style="font-size: 12px">({{ data.time }}时)</em></span
            >
        </div>
        <div class="gap"></div>
        <dl class="pd-dlbx4">
            <dt>负氧离子浓度</dt>
            <dd>
                <em :style="{ color: getColor(data.concentration) }">{{
                    data.oxygen
                }}</em
                ><i :style="{ color: getColor(data.concentration) }"
                    >{{ data.concentration }}<sub>个/Cm³</sub></i
                >
            </dd>
        </dl>
        <div class="gap"></div>
        <ul class="pd-ulbx4">
            <li>
                <h1>PM<sub>2.5</sub></h1>
                <p>{{ data.pm25 }}</p>
            </li>
            <li>
                <h1>O<sub>3</sub></h1>
                <p>{{ data.o3 }}</p>
            </li>
        </ul>
    </div>
</template>
<script>
export default {
    name: 'airClean',
    props: {
        data: {
            type: Object,
            default: function () {
                return {};
            }
        },
        options: {
            type: Object,
            default: function () {
                return { background: '#004280' };
            }
        }
    },
    watch: {},
    computed: {},
    data() {
        return {};
    },
    mounted() {
        console.log(this.data);
        console.log(this.options);
    },
    methods: {
        getColor(text) {
            let color = '#56e429';
            if (text == '清新') {
                color = '#56e429';
            } else if (text == '一般') {
                color = '#ff9000';
            } else if (text == '严重') {
                color = '#c7021d';
            }
            return color;
        }
    }
};
</script>
<style scoped>
body,
ul,
ol,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
p,
form,
input,
textarea,
select,
button {
    margin: 0;
    padding: 0;
    font: 12px 'Microsoft YaHei', SimSun, Arial, Helvetica, sans-serif;
}
ul,
ol {
    list-style-type: none;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
img {
    border: 0 none;
}
em,
i {
    font-style: normal;
}
a:link {
    color: #4f14f7;
    text-decoration: none;
}
a:visited {
    color: #551a8b;
}
a:hover {
    color: #ff9900;
    text-decoration: underline;
}
a:active {
    color: #cc0000;
}
*,
::before,
::after {
    box-sizing: content-box;
}

.el-input__inner {
    height: 28px !important;
    color: #fff !important;
    /* height: 30px !important; */
    border: 1px solid #3f92fe !important;
    border-radius: 3px !important;
    background: rgba(0, 0, 0, 0) !important;
}

.el-checkbox__inner,
.el-radio__inner {
    width: 18px !important;
    height: 18px !important;
}

.el-checkbox__inner:after {
    left: 6px !important;
    top: 3px !important;
}

.el-radio__inner:after {
    width: 6px !important;
    height: 6px !important;
}

.el-table td,
.el-table th {
    padding: 0 !important;
    height: 40px !important;
}

.el-table th > .cell {
    font-weight: normal !important;
    color: #fff !important;
}

.el-table th,
.el-table tr {
    background-color: rgba(0, 0, 0, 0) !important;
}

.el-table th {
    background-color: #10599e !important;
}

.el-table--border {
    border-color: #d3dfe6 !important;
}

.el-table,
.el-table__expanded-cell {
    background-color: rgba(0, 0, 0, 0);
}

.el-table__body tr.current-row > td .cell {
    color: #0aceff !important;
}

/* 选中行的背景 */

.el-table__body tr.current-row > td {
    background: #004d96 !important;
    color: #0aceff !important;
}

/* 行移入 */

.el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: #004d96 !important;
}

.el-table--border th {
    border-color: #c4d9e7 !important;
}

.el-table--border td {
    /* border-color: #d3dfe6!important; */
    border-bottom: 1px solid #245b8e;
}

.el-form-item__label,
.el-table .cell {
    color: #fff !important;
}

.el-table td,
.el-table th.is-leaf {
    border-bottom: 1px solid #245b8e !important;
}

.el-table__empty-text {
    color: #fff !important;
}

.el-table::before {
    background-color: #245b8e !important;
}

/*弹出框样式修改*/

.el-dialog {
    /* background: #226b81;
    border: 1px solid #226b81; */
}

.el-dialog--center .el-dialog__body {
    padding: 0px;
}

.el-dialog__headerbtn {
    top: 11px;
    right: 20px;
}

.el-dialog__headerbtn .el-dialog__close {
    color: #fff !important;
    width: 20px;
    height: 20px;
    font-size: 23px;
}

.el-dialog__header {
    background-color: #004a91 !important;
    padding: 0 10px !important;
    height: 40px !important;
    line-height: 40px;
}

.el-dialog__title {
    line-height: 38px !important;
    font-size: 16px !important;
    color: #fff !important;
    position: absolute;
    left: 15px;
}

.el-dialog__body {
    padding: 0 !important;
}

.el-dialog__wrapper .main,
.el-dialog__wrapper .content-wrap {
    background: #113f54 !important;
}

.el-icon-close:before {
    /* background-color: #056a99; */
}

.el-dialog__wrapper .el-button--primary,
.el-popper[x-placement^='bottom'] .el-button--primary,
.el-popper .el-button--primary {
    border-color: #1a81b5 !important;
    background-color: #1a81b5 !important;
}

/*下拉框*/

.el-select-dropdown {
    position: absolute;
    z-index: 1001;
    color: #fff;
    border: 1px solid #409eff;
    border-radius: 4px;
    background-color: #093757;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 5px 0;
}

.el-select-dropdown__item {
    font-size: 14px;
    padding: 0 20px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #fff;
    height: 34px;
    line-height: 34px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
    /* background-color: #409eff; */
    background-color: #2462a2;
}

/*下拉框end*/

.el-date-picker__time-header {
    position: relative;
    border-bottom: 1px solid #409eff;
    font-size: 12px;
    padding: 8px 5px 5px;
    display: table;
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.el-picker-panel__icon-btn {
    font-size: 12px;
    color: #fff;
    border: 0;
    background: 0 0;
    cursor: pointer;
    outline: 0;
    margin-top: 8px;
}

.el-date-picker__header-label {
    font-size: 16px;
    font-weight: 500;
    padding: 0 5px;
    line-height: 22px;
    text-align: center;
    cursor: pointer;
    color: #fff;
}

.el-picker-panel {
    color: #fff;
    border: 1px solid #409eff;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    background: #093757;
    border-radius: 4px;
    line-height: 30px;
    margin: 5px 0;
}

.el-date-table th {
    padding: 5px;
    color: #fff;
    font-weight: 400;
    border-bottom: solid 1px #759dc0;
}

.el-picker-panel__footer {
    border-top: 1px solid #759dc0;
    padding: 4px;
    text-align: right;
    background-color: #093757;
    position: relative;
    font-size: 0;
}

.el-button {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #19466d;
    border: 1px dashed #409eff;
    color: #409eff;
    -webkit-appearance: none;
    text-align: center;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    outline: 0;
    margin: 0;
    -webkit-transition: 0.1s;
    transition: 0.1s;
    font-weight: 500;
    font-size: 14px;
    padding: 7px 23px !important;
    border-radius: 3px !important;
}

.el-date-picker__editor-wrap .el-input__inner {
    color: #fff !important;
    border: 1px solid #759dc0 !important;
}

.el-time-panel {
    margin: 5px 0;
    border: 1px solid #409eff;
    background-color: #0b2e46;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    position: absolute;
    width: 180px;
    left: -35px !important;
    z-index: 1000;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
}

.el-time-spinner__item {
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    color: #abd6ff;
}

.el-time-spinner__item.active:not(.disabled) {
    color: #3fff !important;
    font-weight: 700;
}

.el-time-panel__footer {
    border-top: 1px solid #184873 !important;
    padding: 4px;
    height: 36px;
    line-height: 25px;
    text-align: right;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.pd-aqibx2:before {
    content: '';
    position: absolute;
    left: 5px;
    top: 30px;
    width: 1px;
    height: 130px;
    background: #246cae;
}

.pd-aqibx2 {
    overflow: hidden;
    padding-left: 20px;
    position: relative;
    background: #004280;
    width: 183px;
    height: 163px;
}

.pd-stithd {
    overflow: hidden;
}

.pd-stithd span {
    font-size: 14px;
    color: #fff;
    background: url(./images/titic2.png) no-repeat left center;
    padding-left: 18px;
    float: left;
}

.pd-dlbx4 {
    background: url(./images/bx3a.png) no-repeat;
    width: 171px;
    height: 77px;
}

.pd-dlbx4 dt {
    font-size: 14px;
    color: #fff;
    padding: 8px 16px 0;
}

.pd-dlbx4 dd {
    padding: 0 16px;
}

.pd-dlbx4 dd em {
    font-size: 34px;
    color: #56e429;
    display: inline-block;
    vertical-align: middle;
}

.pd-dlbx4 dd i {
    font-size: 14px;
    color: #56e429;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
}

.pd-dlbx4 dd i sub {
    display: block;
    font-size: 12px;
    vertical-align: baseline;
}
.pd-ulbx4 {
    overflow: hidden;
}

.pd-ulbx4 li {
    float: left;
    width: 50%;
    text-align: center;
}

.pd-ulbx4 li h1 {
    font-size: 16px;
    color: #ddd;
}

.pd-ulbx4 li h1 sub {
    font-size: 12px;
    vertical-align: baseline;
}

.pd-ulbx4 li p {
    font-size: 20px;
    color: #fff;
}
</style>
