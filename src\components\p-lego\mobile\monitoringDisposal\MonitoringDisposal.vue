<!-- @format -->

<!--
 * @Author: 张磊
 * @Date: 2022-04-14 17:37:59
 * @LastEditTime: 2022-04-14 19:30:33
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /Front_PC_COMPONENTS/src/components/business/mobile/monitoringDisposal/monitoringDisposal.vue
-->

<!-- @format -->

<template>
    <div class="wyl01126-line">
        <div class="gap"></div>
        <p class="wyl01126-line-p" v-if="title">{{ title }}</p>
        <div v-if="data.length">
            <div
                class="wyl01126-list"
                v-for="(item, index) in data"
                :key="index"
            >
                <div class="left">
                    <p class="p1">
                        {{ ((item.COUNT / item.VALUE) * 100).toFixed(1)
                        }}<span>%</span>
                    </p>
                    <p class="p2">{{ item.BL }}</p>
                </div>
                <div class="right">
                    <div class="title">
                        <p class="p3">{{ item.TITLE }}</p>
                        <p class="p4">
                            <span>{{ item.COUNT }}</span
                            >/{{ item.VALUE }}
                        </p>
                    </div>
                    <div class="bar">
                        <div
                            class="bili"
                            :style="{
                                width: (item.COUNT / item.VALUE) * 100 + '%'
                            }"
                        >
                            <em></em>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="nomsg" v-else>暂无数据</div>
    </div>
</template>

<script>
export default {
    props: {
        data: {
            type: Array
        },
        title: {
            type: String
        }
    },
    data() {
        return {};
    }
};
</script>

<style lang="less">
.darkTheme {
    --wyl01126-nomsg-color: #ccc;
    --wyl01126-p: #fff;
    --wyl01126-bili: linear-gradient(
        -90deg,
        rgba(9, 212, 209, 0.82),
        rgba(31, 247, 255, 0.71),
        rgba(17, 121, 119, 0.41)
    );
}
.lightTheme {
    --wyl01126-nomsg-color: #666;
    --wyl01126-p: #333;
    --wyl01126-bili: linear-gradient(
        -90deg,
        rgba(9, 212, 209, 0.82),
        rgba(31, 247, 255, 0.71),
        #09d4d1
    );
}
</style>
<style lang="less" scoped>
.wyl01126-line {
    overflow: hidden;
    .wyl01126-line-p {
        font-size: 22px;
        font-weight: bold;
        line-height: 23px;
        padding-bottom: 19px;
    }
    .wyl01126-list {
        display: flex;
        .left {
            height: 105px;
            width: 134px;
            background: url(./images/left.png) center bottom no-repeat;
            background-size: 100% 100%;
            text-align: center;
            .p1 {
                font-size: 28px;
                color: #06f3ef;
                font-family: 'DIN-Bold';
                text-shadow: 0px 2px 218px #029c99;
                font-weight: bold;
                line-height: 40px;
                span {
                    font-size: 16px;
                    vertical-align: baseline;
                }
            }
            .p2 {
                font-size: 16px;
                font-family: 'DIN-Bold';
                color: var(--wyl01126-p);
                line-height: 6px;
            }
        }
        .right {
            margin-left: 20px;
            flex: 1;
            .title {
                display: flex;
                height: 50px;
                align-items: center;
                justify-content: space-between;
                .p3 {
                    font-size: 16px;
                    font-family: 'DIN-Bold';
                    color: var(--wyl01126-p);
                    line-height: 50px;
                }
                .p4 {
                    font-weight: bold;
                    font-size: 16px;
                    color: var(--wyl01126-p);
                    font-family: 'DIN-Bold';
                    line-height: 54px;
                    span {
                        font-size: 22px;
                        color: #05f4f0;
                        vertical-align: baseline;
                    }
                }
            }
            .bar {
                border-radius: 6px;
                background: rgba(210, 209, 209, 0.14);
                height: 12px;
                .bili {
                    border-radius: 6px;
                    background: var(--wyl01126-bili);
                    height: 12px;
                    position: relative;
                    em {
                        display: block;
                        width: 22px;
                        height: 22px;
                        background: url(./images/guang.png) center center
                            no-repeat;
                        background-size: 100% 100%;
                        position: absolute;
                        right: -6px;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                    }
                }
            }
        }
    }
    .nomsg {
        font-size: 16px;
        color: var(--wyl01126-nomsg-color);
        text-align: center;
        line-height: 40px;
    }
}
</style>
