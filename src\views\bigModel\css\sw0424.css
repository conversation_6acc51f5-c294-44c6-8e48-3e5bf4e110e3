/* @font-face {
	font-family: 'DINPro-Bold';
	src: url('../fonts/DINPro-Bold.woff2') format('woff2'),
		url('../fonts/DINPro-Bold.woff') format('woff');
	font-weight: normal;
	font-style: normal;
} */
.gap40 {
	height: 40px;
}

.gap15 {
	height: 15px;
}

.sw0424-wrap {
	position: absolute;
	left: 0;
	top: 0;
	/* width: 1920px; */
	width: 100%;
	/* height: 1080px; */
	height: 99%;
	background: #EFF8FF;
}

.sw0424-msgwrap {
	position: absolute;
	left: 50%;
	/* top: 90px; */
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	transform: translateX(-50%);
}

.sw0424-msgmod {
	display: flex;
	height: 90%;
	background: #D5E7F9;
	box-shadow: 0px 0px 20px 0px rgba(129, 181, 255, 0.3);
	border-radius: 30px 30px 30px 30px;
}

.sw0424-txt1 {
	font-size: 16px;
	color: #666;
}

.sw0424-ulico1 {
	margin-left: 20px;
	display: flex;
	align-items: center;
}
.sw0424-ulico1 li {
	cursor: pointer;
	width: 30px;
	height: 30px;
	position: relative;
}
.sw0424-ulico1 li.li1 {
	background: url(../images/sw0424_ic1.png) no-repeat center;
}
.sw0424-ulico1 li.li2 {
	background: url(../images/sw0424_ic2.png) no-repeat center;
}
.sw0424-ulico1 li.li3 {
	background: url(../images/sw0424_ic3.png) no-repeat center;
}
.sw0424-ulico1 li + li {
	margin-left: 10px;
}
.sw0424-ulico1 li.on.li1,
.sw0424-ulico1 li:hover.li1 {
	background-image: url(../images/sw0424_ic1_on.png);
}
.sw0424-ulico1 li.on.li2,
.sw0424-ulico1 li:hover.li2 {
	background-image: url(../images/sw0424_ic2_on.png);
}
.sw0424-ulico1 li.on.li3,
.sw0424-ulico1 li:hover.li3 {
	background-image: url(../images/sw0424_ic3_on.png);
}
.sw0424-ulico1 li.on .sw0424-dlalt1,
.sw0424-ulico1 li:hover .sw0424-dlalt1 {
	display: flex;
}

.sw0424-lside {
	width: 300px;
	height: 100%;
	background: #f8faff;
	border-radius: 30px 0px 0px 30px;
	transition: transform 0.3s ease; /* 平滑过渡效果 */
}
.sw0424-lside-collapsed {
  width: 50px; /* 缩放后的宽度 */
}
.sw0424-lsthd {
	width: 229px;
	height: 50px;
	margin: 0 auto;
	background: #DDEBF9;
	border-radius: 10px 10px 10px 10px;
	padding: 0 15px;
	box-sizing: border-box;
}
.sw0424-lsthd strong {
	font-size: 18px;
	color: #3980C5;
	background: url(../images/sw0424_ic4.png) no-repeat left center;
	line-height: 50px;
	display: inline-block;
	padding-left: 30px;
}

.sw0424-lstbd {
	margin: 0 10px 0 36px;
}

.sw0424-ulnav1 li {
	cursor: pointer;
	padding: 0 12px;
}
.sw0424-ulnav1 li + li {
	margin-top: 35px;
}
.sw0424-ulnav1 li.on span,
.sw0424-ulnav1 li:hover span {
	color: #3980C5;
	font-weight: bold;
}
.sw0424-ulnav1 li.on span.ic1,
.sw0424-ulnav1 li:hover span.ic1 {
	background-image: url(../images/sw0424_ic1a_on.png);
}
.sw0424-ulnav1 li.on span.ic2,
.sw0424-ulnav1 li:hover span.ic2 {
	background-image: url(../images/sw0424_ic2a_on.png);
}
.sw0424-ulnav1 li.on span.ic3,
.sw0424-ulnav1 li:hover span.ic3 {
	background-image: url(../images/sw0424_ic3a_on.png);
}
.sw0424-ulnav1 li.on span.ic4,
.sw0424-ulnav1 li:hover span.ic4 {
	background-image: url(../images/sw0424_ic4a_on.png);
}
.sw0424-ulnav1 li.on span.ic5,
.sw0424-ulnav1 li:hover span.ic5 {
	background-image: url(../images/sw0424_ic5a_on.png);
}
.sw0424-ulnav1 li.on span.ic6,
.sw0424-ulnav1 li:hover span.ic6 {
	background-image: url(../images/sw0424_ic6a_on.png);
}
.sw0424-ulnav1 li span {
	font-size: 16px;
	color: #717171;
	padding-left: 39px;
	line-height: 30px;
	display: inline-block;
}
.sw0424-ulnav1 li span.ic1 {
	background: url(../images/sw0424_ic1a.png) no-repeat left center;
}
.sw0424-ulnav1 li span.ic2 {
	background: url(../images/sw0424_ic2a.png) no-repeat left center;
}
.sw0424-ulnav1 li span.ic3 {
	background: url(../images/sw0424_ic3a.png) no-repeat left center;
}
.sw0424-ulnav1 li span.ic4 {
	background: url(../images/sw0424_ic4a.png) no-repeat left center;
}
.sw0424-ulnav1 li span.ic5 {
	background: url(../images/sw0424_ic5a.png) no-repeat left center;
}
.sw0424-ulnav1 li span.ic6 {
	background: url(../images/sw0424_ic6a.png) no-repeat left center;
}

.sw0424-rside {
	flex: 1;
	position: relative;
}
.sw0424-rside .top {
	height: calc(100% - 170px);
	padding: 0 23px 0 20px;
	margin-right: 7px;
}
.sw0424-rside .bot {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 28px;
	padding: 0 30px 0 20px;
}

.sw0424-line {
	width: 240px;
	margin: 0 auto;
	height: 1px;
	background: #D8D8D8;
	position: relative;
}
.sw0424-line i {
	color: #707070;
	font-size: 14px;
	text-align: center;
	padding: 0 5px;
	background-color: #D5E7F9;
	position: absolute;
	bottom: -8px;
	left: 50%;
	transform: translateX(-50%);
}

.sw0424-mod {
	padding: 0 30px;
	background: #fff;
	color: rgb(0, 105, 255);
	border-radius: 20px;
}
.sw0424-mod.on {
	/* box-shadow: inset 0 0 20px rgba(0, 0, 100, 0.5); */
	animation: huxi 3.3s linear infinite;
}
@keyframes huxi {
	0% {
		box-shadow: 0 0 30px rgba(0, 105, 255, 0.25);
	}
	50% {
		box-shadow: 0 0 30px rgba(0, 105, 255, 0);
	}
	100% {
		box-shadow: 0 0 30px rgba(0, 105, 255, 0.25);
	}
}
.sw0424-txt2 {
	font-size: 24px;
	color: #000;
}

.sw0424-txt3 {
	font-size: 16px;
	color: #3d3d3d;
}
.sw0424-txt3.lh18 {
	line-height: 1.8;
}

.sw0424-ictxt1 {
	cursor: pointer;
	font-size: 14px;
	color: #3d3d3d;
	background: url(../images/sw0424_ic5.png) no-repeat left center;
	padding-left: 22px;
}

.sw0424-ulbx1 {
	display: flex;
	flex-wrap: wrap;
	margin-left: -18px;
	margin-top: -13px;
}
.sw0424-ulbx1 li {
	width: 233px;
	height: 97px;
	padding: 0 12px;
	box-sizing: border-box;
	background: #E8F1FB;
	position: relative;
	border-radius: 20px;
	margin-left: 19px;
	margin-top: 13px;
}
.sw0424-ulbx1 li p {
	font-size: 16px;
	color: #3d3d3d;
	line-height: 1.5;
	padding-top: 13px;
}
.sw0424-ulbx1 li .btn {
	width: 67px;
	height: 28px;
	background: #eff8ff;
	border-radius: 300px;
	border: 1px solid #3980C5;
	font-size: 14px;
	color: #3980C5;
	text-align: center;
	line-height: 28px;
	position: absolute;
	right: 13px;
	bottom: 11px;
}

.sw0424-mod1 {
	height: 60px;
	background: #ffffff;
	box-shadow: 0px 0px 6px 0px rgba(82, 65, 255, 0.2);
	padding: 0 10px;
	border-radius: 10px;
}

.sw0424-ico1 {
	cursor: pointer;
	width: 44px;
	height: 44px;
	background: url(../images/sw0424_ic6.png) no-repeat;
}
.sw0424-ico1.on,
.sw0424-ico1:hover {
	background-image: url(../images/sw0424_ic6_on.png);
}

.sw0424-ico2 {
	cursor: pointer;
	width: 44px;
	height: 44px;
	background: url(../images/sw0424_ic7.png) no-repeat;
}
.sw0424-ico2.on,
.sw0424-ico2:hover {
	background-image: url(../images/sw0424_ic7_on.png);
}

.sw0424-ico3 {
	cursor: pointer;
	width: 44px;
	height: 44px;
	background: url(../images/sw0424_ic8.png) no-repeat;
}

.sw0424-inptxt1 {
	font-size: 16px;
	color: #3d3d3d;
}
.sw0424-inptxt1::-moz-placeholder {
	color: #c1c1c1;
}
.sw0424-inptxt1::placeholder {
	color: #c1c1c1;
}

.sw0424-botinfo {
	font-size: 16px;
	color: #bebebe;
	position: absolute;
	left: 0;
	right: 0;
	bottom: 4%;
	text-align: center;
}

.sw0424-dlalt1 {
	display: none;
	z-index: 1;
	position: absolute;
	right: -10px;
	top: 100%;
	width: 134px;
	height: 94px;
	background: #ffffff;
	box-shadow: 0px 0px 10px 0px rgba(178, 199, 241, 0.5);
	border-radius: 14px 14px 14px 14px;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 15px;
}
.sw0424-dlalt1::before {
	content: '';
	position: absolute;
	right: 18px;
	top: -5px;
	width: 12px;
	height: 12px;
	background: #ffffff;
	transform: rotate(45deg);
}
.sw0424-dlalt1 dt {
	font-size: 16px;
	color: #3d3d3d;
}
.sw0424-dlalt1 dd {
	margin-top: 8px;
	width: 96px;
	height: 33px;
	background: #EFF8FF;
	border-radius: 100px 100px 100px 100px;
	border: 1px solid #3980C5;
	font-size: 14px;
	color: #3980C5;
	text-align: center;
	line-height: 33px;
}

.sw0424-msgtxt1 {
	font-size: 20px;
	color: #fff;
	padding: 12px 60px 12px 20px;
	background: #0069ff;
	border-radius: 20px 20px 0px 20px;
}

.sw0424-msgtxt2 {
	font-size: 14px;
	color: #a8a8a8;
}

.sw0424-mod2 {
	background: #fff;
	height: 33px;
	border-radius: 300px;
	padding: 0 12px;
}

.sw0424-ulbx2 {
	display: flex;
	align-items: center;
	height: 33px;
	margin: 0 -8px;
}
.sw0424-ulbx2 li {
	display: flex;
	align-items: center;
	padding: 0 15px;
	position: relative;
}
.sw0424-ulbx2 li + li:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	bottom: 0;
	width: 1px;
	background: #d8d8d8;
}
.sw0424-ulbx2 li.on .ic1,
.sw0424-ulbx2 li:hover .ic1 {
	background-image: url(../images/sw0424_ic1b_on.png);
}
.sw0424-ulbx2 li.on .ic2,
.sw0424-ulbx2 li:hover .ic2 {
	background-image: url(../images/sw0424_ic2b_on.png);
}
.sw0424-ulbx2 li.on .ic3,
.sw0424-ulbx2 li:hover .ic3 {
	background-image: url(../images/sw0424_ic3b_on.png);
}
.sw0424-ulbx2 li i {
	width: 15px;
	height: 15px;
	cursor: pointer;
}
.sw0424-ulbx2 li i.ic1 {
	background: url(../images/sw0424_ic1b.png) no-repeat center;
}
.sw0424-ulbx2 li i.ic2 {
	background: url(../images/sw0424_ic2b.png) no-repeat center;
}
.sw0424-ulbx2 li i.ic3 {
	background: url(../images/sw0424_ic3b.png) no-repeat center;
}

.laba {
	width: 16px;
	height: 15px;
	background: url(../images/laba.png) 0 center no-repeat;
	cursor: pointer;
}
.laba:hover {
	background-image: url(../images/laba3.png);
}

.sw0424-botols {
	border-top: 1px solid #eee;
	display: flex;
	align-items: center;
	padding: 14px 0;
}

.sw0424-numvdo {
	height: 33px;
	line-height: 33px;
	background: rgba(0, 105, 255, 0.1);
	border-radius: 300px;
	padding: 0 17px;
	cursor: pointer;
}
.sw0424-numvdo i {
	font-size: 14px;
	color: #0069ff;
	background: url(../images/sw0424_ic9.png) no-repeat left center;
	padding-left: 28px;
}

.sw0424-kbdbtn {
	margin-left: auto;
	cursor: pointer;
}

.sw0424-robot {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 110px;
	padding: 0 50px;
	box-sizing: border-box;
}
.sw0424-robot .robot {
	position: relative;
}
.sw0424-robot .robot .eye {
	position: absolute;
	top: 50px;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	align-items: center;
	height: 14px;
	width: 32px;
	justify-content: space-between;
}
.sw0424-robot .robot .eye .dot {
	width: 6px;
	height: 14px;
	background-color: #99fcfd;
	border-radius: 10px;
	transform-origin: center;
	animation: eye 2s ease infinite;
	box-shadow: 0 0 10px #99fcfd;
}
.sw0424-robot .robot p {
	font-size: 18px;
	color: #999;
	text-align: center;
	padding-top: 10px;
}

@keyframes eye {
	0% {
		height: 14px;
	}
	16% {
		height: 0;
	}
	33% {
		height: 14px;
	}
	66% {
		height: 14px;
	}
	83% {
		height: 0;
	}
	100% {
		height: 14px;
	}
}
