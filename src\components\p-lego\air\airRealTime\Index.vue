<!-- @format -->

<template>
    <dl class="air-real">
        <dt class="air-real-lf">
            <AqiRealtime :value="data.AQI" />
            <p class="air-real-lf-plt">
                <em>首要污染物：</em>
                <em class="plt">{{
                    replacePltName(data.firstPollution) || '无'
                }}</em>
            </p>
        </dt>
        <dd class="air-real-rt">
            <div class="air-real-rt-box">
                <div
                    class="air-real-rt-box-item"
                    v-for="item in opts"
                    :key="item.value"
                >
                    <h1 class="air-real-rt-box-item-label">
                        <em v-html="item.label"></em>
                        <i>{{ data[item.value] || '' }}</i>
                    </h1>
                    <p
                        class="air-real-rt-box-item-bar"
                        :style="{
                            backgroundColor: getPollutionColor(
                                item.value,
                                data[item.value]
                            )
                        }"
                    ></p>
                </div>
            </div>
            <p class="air-real-rt-tip">单位：μg/m³(CO:mg/m³)</p>
        </dd>
    </dl>
</template>

<script>
import AqiRealtime from './AqiRealtime.vue';
import util from './util';
export default {
    components: {
        AqiRealtime
    },
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    AQI: 0,
                    CO: 0,
                    JCSJ: '',
                    NO2: 0,
                    O3: 0,
                    PM10: 0,
                    PM25: 0,
                    SO2: 0,
                    firstPollution: '', //首要污染物
                    index: 0
                };
            }
        }
    },
    data() {
        return {
            opts: [
                { label: 'PM<sub>2.5</sub>', value: 'PM25' },
                { label: 'SO<sub>2</sub>', value: 'SO2' },
                { label: 'PM<sub>10</sub>', value: 'PM10' },
                { label: 'NO<sub>2</sub>', value: 'NO2' },
                { label: 'O<sub>3</sub>', value: 'O3' },
                { label: 'CO', value: 'CO' }
            ]
        };
    },
    methods: {
        replacePltName(value) {
            value = value || '';
            let labelObj = {
                'PM2.5': 'PM₂.₅',
                PM25: 'PM₂.₅',
                PM10: 'PM₁₀',
                O3: 'O₃',
                NO2: 'NO₂',
                SO2: 'SO₂'
            };
            return value.replace(/[A-Z]+[0-9]+\.*[0-9]*/g, function () {
                return labelObj[arguments[0]] || arguments[0];
            });
        },
        getPollutionColor(name, value) {
            return util.getLevelPollution(name, value).color;
        }
    }
};
</script>

<style scoped lang="less">
.air-real {
    display: flex;

    &-lf {
        margin-right: 20px;
        padding-top: 15px;

        &-plt {
            text-align: center;
        }
    }
    &-rt {
        flex: 1;
        padding-top: 15px;
        &-box {
            display: flex;
            flex-wrap: wrap;
            height: 170px;
            &-item {
                width: 50%;
                padding-right: 20px;
                padding-bottom: 20px;
                box-sizing: border-box;

                &-label {
                    display: flex;
                    justify-content: space-between;

                    > em {
                        font-size: 18px;
                        font-weight: normal;
                        ::v-deep sub {
                            vertical-align: baseline;
                            font-size: 12px;
                        }
                    }
                    > i {
                        font-family: 'DIN-Medium';
                        font-size: 18px;
                        float: right;
                    }
                }
                &-bar {
                    height: 5px;
                    border-radius: 5px;
                    margin-top: 5px;
                }
            }
        }

        &-tip {
            text-align: right;
        }
    }
}
</style>
