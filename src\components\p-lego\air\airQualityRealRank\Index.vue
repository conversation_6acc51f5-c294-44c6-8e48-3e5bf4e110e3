<!-- @format -->

<template>
    <div>
        <div class="zy-cell" style="width: 500px; height: 440px">
            <div class="hd">
                <p class="zy-til1">空气质量实时排名</p>
            </div>
            <div class="bd">
                <div class="gap"></div>
                <div class="zy-line1">
                    <el-select
                        v-model="pltValue"
                        @change="changePlt"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in pltList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div class="gap"></div>
                <div class="gap"></div>
                <div class="gap"></div>
                <table class="zy-table1">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>区县</th>
                            <th>{{ pltName }}</th>
                            <th v-show="pltName === 'AQI'">首要污染物</th>
                        </tr>
                    </thead>
                </table>

                <div style="height: 200px">
                    <table class="zy-table1">
                        <tbody>
                            <tr>
                                <td>
                                    {{ laData.rank }}
                                </td>
                                <td>
                                    {{ laData.xzqhmc }}
                                </td>
                                <td
                                    :style="{
                                        color: setPltColor(
                                            pltValue,
                                            laData[pltValue]
                                        )
                                    }"
                                >
                                    {{ laData[pltValue] }}
                                </td>
                                <td v-show="pltName === 'AQI'">
                                    {{ replacePltName(laData.sywrw) || '-' }}
                                </td>
                            </tr>
                            <tr v-for="(item, index) in tableList" :key="index">
                                <td
                                    :class="{
                                        no1: item.rank == 1,
                                        no2: item.rank == 2,
                                        no3: item.rank == 3
                                    }"
                                >
                                    {{ item.rank }}
                                </td>

                                <td>
                                    {{ item.xzqhmc || '-' }}
                                </td>

                                <td>
                                    <div
                                        :style="{
                                            color: setPltColor(
                                                pltValue,
                                                item[pltValue]
                                            )
                                        }"
                                    >
                                        {{ item[pltValue] || '-' }}
                                    </div>
                                </td>
                                <td v-show="pltName === 'AQI'">
                                    {{ replacePltName(item.sywrw) || '-' }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Utils from './util.js';
export default {
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    firstArea: '',
                    tableList: []
                };
            }
        }
    },

    data() {
        return {
            // 污染因子列表
            pltList: [
                {
                    label: 'AQI',
                    value: 'aqi'
                },
                {
                    label: 'PM₂.₅',
                    value: 'pm25'
                },
                {
                    label: 'PM₁₀',
                    value: 'pm10'
                },
                {
                    label: 'O₃',
                    value: 'o3'
                },
                {
                    label: 'NO₂',
                    value: 'no2'
                },
                {
                    label: 'SO₂',
                    value: 'so2'
                },
                {
                    label: 'CO',
                    value: 'co'
                }
            ],
            pltValue: 'aqi',
            pltName: 'AQI',

            tableList: [], //表格数据
            firstArea: ''
        };
    },

    watch: {
        data: 'getData'
    },

    created() {
        this.getData();
    },

    methods: {
        getData() {
            this.firstArea = this.data.firstArea;
            this.tableList = this.data.tableList;

            // 排序
            this.tableList.sort((a, b) => {
                return a[this.pltValue] - b[this.pltValue];
            });

            // 加序号
            this.tableList.forEach((item, index) => {
                item.rank = index + 1;
            });

            let arr = [];
            arr = this.tableList.filter((item) => {
                return item.xzqhmc === this.firstArea;
            });
            this.laData = arr[0];

            this.tableList = this.tableList.filter((item) => {
                return item.xzqhmc !== this.firstArea;
            });
        },

        //切换污染物
        changePlt(e) {
            this.pltName = Utils.replacePltName(e.toUpperCase());

            this.getData();
        },

        //替换污染物下标
        replacePltName(v) {
            return Utils.replacePltName(v.toUpperCase());
        },

        //转化颜色
        setPltColor(v1, v2) {
            return Utils.getLevelPollution(v1, v2).color;
        }
    }
};
</script>

<style scoped>
.zy-cell {
    background: rgba(4, 26, 34, 0.9);
    border-radius: 8px;
    padding: 10px 20px;
    border: 1px solid transparent;
    box-sizing: border-box;
}

.zy-cell .hd {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.zy-til1 {
    font-size: 20px;
    color: #fff;
    opacity: 0.6;
    line-height: 40px;
    font-weight: bold;
}
.zy-table1 {
    width: 100%;
}

.zy-table1 th {
    font-size: 16px;
    color: #fff;
    height: 40px;
    background-color: #0a2d39;
}

.zy-table1 td {
    font-size: 16px;
    color: #fff;
    height: 40px;
    text-align: center;
}

.zy-table1 td .bar {
    margin: 0 auto;
    width: 120px;
    height: 18px;
    background-color: #0c3544;
}

.zy-table1 td .bar .bili {
    height: 18px;
    background-color: #01fe85;
    width: 20%;
}

.zy-table1 td.no1 {
    background: url(./images/zy-no1.png) center no-repeat;
}

.zy-table1 td.no2 {
    background: url(./images/zy-no2.png) center no-repeat;
}

.zy-table1 td.no3 {
    background: url(./images/zy-no3.png) center no-repeat;
}

.zy-line1 {
    display: flex;
    justify-content: end;
}

.el-input--suffix .el-input__inner {
    width: 100px;
}
.el-input__inner,
.el-input__icon {
    line-height: 28px !important;
    height: 28px !important;
}

table {
    table-layout: fixed;
    display: table;
    margin: 0;
}
</style>
