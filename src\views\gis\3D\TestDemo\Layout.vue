<!-- @format -->
<!-- 业务主要入口 -->
<template>
    <div>
        <changeStyle @funcChange="funcChangeHandle"></changeStyle>

        <!-- echart 转移图 -->
        <widget0 v-if="style == '0'"></widget0>

        <!-- 热力图 -->
        <widget1 v-if="style == '1'"></widget1>

        <!-- 小圆点 -->
        <widget2 v-if="style == '2'"></widget2>

        <!-- 面填充与文本注记 -->
        <widget3 v-if="style == '3'"></widget3>

        <!-- 线数据 -->
        <widget4 v-if="style == '4'"></widget4>

        <!-- 拉升面 -->
        <widget5 v-if="style == '5'"></widget5>

        <!-- 立体柱子 -->
        <widget6 v-if="style == '6'"></widget6>

        <!-- geojson -->
        <widget7 v-if="style == '7'"></widget7>

        <!-- 点、先、面 -->
        <widget8 v-if="style == '8'"></widget8>
    </div>
</template>
<script>
import changeStyle from './components/changeStyle';
import widget0 from './components/widget0';
import widget1 from './components/widget1';
import widget2 from './components/widget2';
import widget3 from './components/widget3';
import widget4 from './components/widget4';
import widget5 from './components/widget5';
import widget6 from './components/widget6';
import widget7 from './components/widget7';
import widget8 from './components/widget8';

export default {
    data() {
        return {
            style: '0'
        };
    },

    props: [],
    unmounted() {},
    components: {
        changeStyle,
        widget0,
        widget1,
        widget2,
        widget3,
        widget4,
        widget5,
        widget6,
        widget7,
        widget8
    },
    mounted() {
        this.initPage();
    },
    methods: {
        //页面初始化
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.map = window.glMap;
        },

        funcChangeHandle(style) {
            this.style = style;
        }
    }
};
</script>
<style></style>
