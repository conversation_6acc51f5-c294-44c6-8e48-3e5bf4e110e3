## 2023.8.18
showloading 为true的时候，才进行请求队列
utils方法：
数组转成树形结构数组 

## 2023.5.16 
1. 深色浅色配置，可以拦截配置不同页面颜色是深色还是浅色  (config.js)

const ServerGlobalConstant = {
    eleTheme: 'dark', // elementUI颜色 dark 深色系做大屏颜色 light 白色
    otherThemeArrRoute: ['/app/event'], // 和色系相反的颜色 深对应就是浅色 
};
2.  dark.css 新增变量，时间,下拉等组件适配大屏配色
```
/* 底色 */
--bgScreen-background-color: #053360;
/* hover色（基于底色增强） */
--bgScreen-hover-color: #0855a0;
/* 选中色 */
--bgScreen-checked-color: #2AD9FF;
/* 选中范围色（基于选中色减弱） */
--bgScreen-checked-color-lighter: #0169A2;
```
3.  p-charts 默认字体调整为 16px


## 2023 年 3 月 20 日 V1.4

1. gis 模块集成水气弹框，点位动画，搜索，工具条，时间轴
2. 业务组件 inject 进 pc 端框架里面
3. 项目迁移功能优化（公共样式，模块样式）
4. gis：配置文件调整，sdk版本升级

## 2022 年 12 月 13 日

1. p-charts 升级 ^3.0.11
    - 调整浅色系图表配色
    - 暂无数据配图调整
2. element-plus 升级 ^2.2.26"
3. 修复 element-plus Table 表头兼容性问题
4. 支持 less sass
5. 解决苹果电脑适配问题
6. 添加 jsconfig.json（支持点击查找依赖组件）

## 2022 年 5 月 5 日更新内容

1.  vue2 升级到 vue3 版本
2.  vue-cli3 升级到 vue-cli5.0 （升级到 webpack5）
3.  p-charts 升级 (http://202.104.140.36:3000/chartDocs/#/update)

    -   依赖的 echarts 从^4.6.0 升级为^5.2.0。
    -   主题配色修改。
    -   各图表样式 UI 优化。
    -   全局配置中，有以下修改及添加项：

            主题颜色配置项修改；
            是否显示工具栏修改默认不显示；
            添加字体设置；
            添加直角坐标系边距设置，作用于折线图、柱状图、堆叠图、热力图等；
            支持图例形状及大小的配置；
            添加监听事件配置；
            添加tooltip提示框背景颜色、边框颜色、文字颜色配置。

    -   饼图添加 label、legend 格式化配置；添加纵向图例配置；圆环图样式修改。
    -   折线图默认连接空数据。
    -   柱状图添加宽度的配置；增加圆柱、棱柱、模拟立体柱状图样式，部分支持横向展示。
    -   堆叠图添加宽度配置，添加圆柱、棱柱形堆叠图。
    -   水球图样式修改，配置项修改。
    -   仪表盘新增样式类型，空气 aqi 仪表盘样式优化。
    -   条纹图默认样式修改，新增样式类型。

4.  和 gis 深度整合
    -   目录结构调整，优化, 配置 config.js 文件统一入口
    -   支持换肤功能 （默认有深色版本和浅色版本，根据设计规范调整），全局变量 (调整 public/config.js 下配置) 重点
    -   表单增删改查 （http://**************/）
    -   和 gis 通信传参确定
5.  p-lego 组件扩充完善 （新增 40 多个组件）
    统计功能，可以统计哪些组件用得最多 （二次优化升级）
6.  工具类整理

```
	"@turf/turf": "^6.5.0",
	"PowerArcgis": "^1.0.3",         // 地图类库
	"PowerMapbox": "^1.1.5",		 // 地图类库
	"axios": "^0.26.1",              // 请求数据
	"core-js": "^3.8.3",
	"dayjs": "^1.11.0",              // 时间处理
	"driver.js": "^0.9.8",           // 引导页
	"echarts": "^5.2.0",             // 图表类库
	"echarts-liquidfill": "^3.0.0",  // echarts 水球图
	"element-plus": "^2.1.7",        // vue ui组件
	"esri-loader": "^2.14.0",        // 地图loader
	"lodash": "^4.17.21",            // 常用数组，对象处理工具类
	"mapbox-gl": "^2.7.0",           // 地图类库
	"p-charts": "^3.0.7",            // 基于vue和echarts5封装的组件
	"vue": "^3.2.13",
	"vue-router": "^4.0.3",
	"vue3-gemini-scrollbar": "^1.0.1",// 滚动组件
	"vuex": "^4.0.0"
```

7. 优化 webpack 运行打包配置
