/** @format */
import { h } from 'vue';
export default {
    name: 'Cell',
    props: {
        row: Object,
        render: Function,
        index: Number,
        column: {
            type: Object,
            default: null
        }
    },
    render() {
        const { row, index, column, render } = this;
        const params = {
            row,
            index,
            column
        };
        return render(h, params);
    }
};
