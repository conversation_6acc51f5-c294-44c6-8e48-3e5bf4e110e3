<!-- @format -->

<template>
    <span v-html="html"></span>
</template>

<!-- @format -->

/** * 用于展示包含空气质量六项污染因子的文本，自动设置污染因子下标 */
<script>
import { POLLUTANTS } from './utils/constants';
import { split } from './utils/regex';
import { getPollutantUnit } from './utils/pollutant';

const SUBSCRIPT_POLLUTANTS = [
    'PM₂.₅',
    'PM₂₅',
    'PM2_5',
    'PM₁₀',
    'SO₂',
    'O₃',
    'NO₂'
];

//特殊下标字符与数字映射关系
const SUBSCRIPT_MAPPER = new Map([
    ['₂.₅', '2.5'],
    ['₂₅', '2.5'],
    ['2_5', '2.5'],
    ['₁₀', '10'],
    ['₂', '2'],
    ['₃', '3']
]);

export default {
    name: 'PollutantName',
    props: {
        name: String,
        withUnit: Boolean
    },

    data() {
        return {
            html: ''
        };
    },
    mounted() {
        let splitedText = this.splitTextWithPollutant(this.name);
        let rejoinContent = this.joinPollutantText(splitedText);
        this.html = '<span class="pollutant-name">' + rejoinContent + '</span>';
    },

    methods: {
        isPollutant(text) {
            return POLLUTANTS.concat(SUBSCRIPT_POLLUTANTS).includes(
                text.toUpperCase()
            );
        },

        /**
         * "首要污染物：O3" => ["首要污染物：", "O3"]
         */
        splitTextWithPollutant(textContainPollutants) {
            let regexPattern = '';
            POLLUTANTS.concat(SUBSCRIPT_POLLUTANTS).forEach((p, index) => {
                regexPattern += `|${p}`;
            });
            regexPattern = regexPattern.substring(1);
            let pollutantRegex = new RegExp(regexPattern, 'gi');
            return split(textContainPollutants, pollutantRegex);
        },

        /**
         * ["首要污染物：", "PM2.5"] => "首要污染物：PM<sub>2.5</sub>"
         */
        joinPollutantText(splitedText) {
            let result = '';
            splitedText.forEach((textFraction) => {
                if (this.isPollutant(textFraction)) {
                    let letters = textFraction
                        .replace(/[₀-₅0-9\._]+/g, '')
                        .toUpperCase();
                    result += letters;
                    let nums = textFraction.replace(/[a-zA-Z]+/g, '');
                    if (nums.length > 0) {
                        nums = this.replaceSubscriptNumber(nums);
                        result += `<sub style="display: inline-block; font-size: 12px; transform:scale(0.76)">${nums}</sub>`;
                    }
                    if (this.withUnit) {
                        let unit = getPollutantUnit(textFraction);
                        result += `(${unit})`;
                    }
                } else {
                    result += textFraction;
                }
            });
            return result;
        },

        //替换下标数字为正常的数字：₂.₅ -> 2.5, ₁₀ -> 10
        replaceSubscriptNumber(subscript) {
            if (SUBSCRIPT_MAPPER.has(subscript)) {
                return SUBSCRIPT_MAPPER.get(subscript);
            } else {
                return subscript;
            }
        },

        onClick(event) {
            event.name = this.name;
            this.$emit('click', event);
        }
    }
};
</script>

<style scoped>
.pollutant-name {
    font-size: 14px;
}

.pollutant-name sub {
    display: inline-block;
    font-size: 8px;
}
</style>
