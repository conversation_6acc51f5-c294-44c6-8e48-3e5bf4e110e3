<!-- @format -->

<template>
    <div class="year-calendar-background">
        <div class="gap"></div>
        <div class="gap"></div>
        <div
            style="
                display: flex;
                color: white;
                justify-content: space-between;
                padding: 0 8px;
            "
        >
            <div v-for="(item, index) in waterSummary" :key="index">
                <div style="font-size: 16px">
                    {{ item.name }} <span>{{ item.value }}次</span>
                </div>
                <div
                    :class="item.class"
                    style="
                        height: 54px;
                        width: 88px;
                        margin-top: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 20px;
                        border-radius: 3px;
                    "
                >
                    <div>{{ item.percent }}%</div>
                </div>
            </div>
        </div>
        <div class="gap"></div>
        <div class="gap"></div>
        <div class="year-calendar-pd-tithd">
            <span>{{ option.title }}</span>
        </div>
        <div class="gap"></div>
        <div class="gap"></div>
        <div
            class="year-calendar-table-wrapper"
            :style="{ height: option.height }"
        >
            <table class="year-calendar-zy-yueli1">
                <colgroup>
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                    <col width="100" />
                </colgroup>
                <tbody>
                    <tr v-for="(item, index) in data" :key="index">
                        <td>
                            <p class="year-calendar-p1">
                                {{ item.year }}
                            </p>
                        </td>
                        <td
                            v-for="(monthValue, innerIndex) in item.data"
                            :key="innerIndex"
                            class="year-calendar-p2"
                            :class="{
                                'year-calendar-sz1': monthValue == '1',
                                'year-calendar-sz2': monthValue == '2',
                                'year-calendar-sz3': monthValue == '3',
                                'year-calendar-sz4': monthValue == '4',
                                'year-calendar-sz5': monthValue == '5',
                                'year-calendar-sz6': monthValue == '6'
                            }"
                        ></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <table class="year-calendar-zy-yueli1">
            <colgroup>
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
                <col width="100" />
            </colgroup>
            <tbody>
                <tr>
                    <td>
                        <p class="year-calendar-p1"></p>
                    </td>
                    <td v-for="value in 12" :key="value">
                        <p class="year-calendar-month-text">{{ value }}月</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
export default {
    props: {
        //月历数据，数据格式 [{year: '2016',data: ['1','2','3','4','5','6','6','5','4','3','2','1']}]
        data: {
            type: Array,
            default: function () {
                return [];
            }
        },
        //配置项
        option: {
            type: Object,
            default: function () {
                return {
                    height: '400px', // 滚动区域高度
                    title: '主要污染物及超标率统计' //月历标题
                };
            }
        }
    },
    data() {
        return {
            waterTab: 'SZLB',
            dataLB: ['Ⅰ类', 'Ⅱ类 ', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类'],
            waterQualityTime: '' //水质时间
        };
    },
    computed: {
        waterSummary: function () {
            return this.getSummaryData();
        }
    },
    watch: {},
    created() {},
    mounted() {},
    methods: {
        getCategoryText(value) {
            switch (value) {
                case 0:
                case '0':
                    return '';
                case 1:
                case '1':
                case 'I':
                    return 'Ⅰ类';
                case 2:
                case '2':
                case 'II':
                    return 'Ⅱ类';
                case 3:
                case '3':
                case 'III':
                    return 'Ⅲ类';
                case 4:
                case '4':
                case 'IV':
                    return 'Ⅳ类';
                case 5:
                case '5':
                case 'V':
                    return 'Ⅴ类';
                case 6:
                case '6':
                    return '劣Ⅴ类';
            }
        },
        getSummaryData() {
            let summary = {
                total: 0,
                one: 0,
                two: 0,
                three: 0,
                four: 0,
                five: 0,
                lowFive: 0,
                empty: 0
            };

            if (this.data && this.data.length > 0) {
                this.data.forEach((yearValue) => {
                    yearValue.data.forEach((monthValue) => {
                        summary.total++;
                        switch (monthValue) {
                            case 1:
                            case '1':
                            case 'I':
                                summary.one++;
                                break;
                            case 2:
                            case '2':
                            case 'II':
                                summary.two++;
                                break;
                            case 3:
                            case '3':
                            case 'III':
                                summary.three++;
                                break;
                            case 4:
                            case '4':
                            case 'IV':
                                summary.four++;
                                break;
                            case 5:
                            case '5':
                            case 'V':
                                summary.five++;
                                break;
                            case 6:
                            case '6':
                                summary.lowFive++;
                                break;
                            case '':
                                summary.empty++;
                                break;
                        }
                    });
                });
            }
            return [
                {
                    name: 'Ⅰ类',
                    class: 'year-calendar-sz1',
                    value: summary.one,
                    percent: ((summary.one / summary.total) * 100).toFixed(2)
                },
                {
                    name: 'Ⅱ类',
                    class: 'year-calendar-sz2',
                    value: summary.two,
                    percent: ((summary.two / summary.total) * 100).toFixed(2)
                },
                {
                    name: 'Ⅲ类',
                    class: 'year-calendar-sz3',
                    value: summary.three,
                    percent: ((summary.three / summary.total) * 100).toFixed(2)
                },
                {
                    name: 'Ⅳ类',
                    class: 'year-calendar-sz4',
                    value: summary.four,
                    percent: ((summary.four / summary.total) * 100).toFixed(2)
                },
                {
                    name: 'Ⅴ类',
                    class: 'year-calendar-sz5',
                    value: summary.five,
                    percent: ((summary.five / summary.total) * 100).toFixed(2)
                },
                {
                    name: '劣Ⅴ类',
                    class: 'year-calendar-sz6',
                    value: summary.lowFive,
                    percent: ((summary.lowFive / summary.total) * 100).toFixed(
                        2
                    )
                },
                {
                    name: '无数据',
                    class: 'year-calendar-sz',
                    value: summary.empty,
                    percent: ((summary.empty / summary.total) * 100).toFixed(2)
                }
            ];
        },
        showMonthDetail(item) {}
    }
};
</script>

<style scoped>
.year-calendar-background {
    background: #033c58;
}
.year-calendar-pd-tithd {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.year-calendar-pd-tithd span {
    font-size: 18px;
    color: #fff;
    position: relative;
    padding-left: 17px;
}
tr:nth-child(2n) {
    background: none;
}
.year-calendar-pd-tithd span::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #00bbe8;
    margin-top: -4px;
}

.gap {
    height: 10px;
    width: 100%;
}

.year-calendar-table-wrapper {
    overflow: auto;
}

.year-calendar-zy-yueli1 {
    width: 100%;

    border: 1px solid #034d70;
}

.year-calendar-zy-yueli1 td {
    border: 1px solid #034d70;
}

.year-calendar-zy-yueli1 tr > :first-child {
    width: 6%;
}

.year-calendar-zy-yueli1 td .year-calendar-p1 {
    font-size: 16px;
    color: #fff;
    text-align: center;
    line-height: 30px;
}

.year-calendar-zy-yueli1 td.year-calendar-p2 {
    height: 80px;
    overflow: hidden;
    margin: 0 auto;
    font-size: 16px;
    color: #fff;
    background-color: #bfbfbf;
    text-align: center;
    line-height: 80px;
    cursor: pointer;
}

.year-calendar-zy-yueli1 tr td .year-calendar-month-text {
    color: white;
    text-align: center;
    font-size: 14px;
    height: 48px;
    line-height: 48px;
}

.year-calendar-sz {
    background-color: #bfbfbf !important;
}

.year-calendar-sz1 {
    background-color: #44c5fd !important;
}
.year-calendar-sz2 {
    background-color: #51a5fd !important;
}
.year-calendar-sz3 {
    background-color: #73bb31 !important;
}
.year-calendar-sz4 {
    background-color: #eebd15 !important;
}
.year-calendar-sz5 {
    background-color: #f88e17 !important;
}
.year-calendar-sz6 {
    background-color: #ee3b5b !important;
}

::-webkit-scrollbar {
    display: none;
}
</style>
