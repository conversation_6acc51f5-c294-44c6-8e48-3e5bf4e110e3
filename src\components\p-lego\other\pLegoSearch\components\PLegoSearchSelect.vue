<!-- @format -->
<!-- 下拉选择 -->

<template>
    <div class="search-type">
        <div class="title">{{ title }}</div>
        <el-select
            style="flex: 1"
            v-model="selectVal"
            placeholder="请选择"
            @change="$emit('selectChange', selectVal)"
        >
            <el-option
                v-for="item in selectList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
        </el-select>
    </div>
</template>

<script>
export default {
    name: '',
    props: {
        title: {
            type: String,
            default: () => {
                return '';
            }
        },
        defaultVal: {
            type: String || Number,
            default: () => {
                return '';
            }
        },
        selectList: {
            type: Array,
            required: true,
            default: () => {
                return [];
            }
        }
    },
    data() {
        return {
            selectVal: ''
        };
    },
    mounted() {
        this.selectVal = this.defaultVal;
    },
    methods: {}
};
</script>

<style lang="scss" scoped>
.search-type {
    margin-right: 20px;
    color: var(--font-color);
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
}
</style>
