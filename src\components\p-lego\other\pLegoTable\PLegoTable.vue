<!-- @format -->

<template>
    <table
        class="p-lego-table"
        :style="{
            width: config.width || '100%',
            height: config.height || 'auto'
        }"
    >
        <!-- <colgroup>
            <col width="20%" />
            <col width="30%" />
            <col width="20%" />
            <col width="30%" />
        </colgroup> -->
        <tbody>
            <tr v-for="(item, index) in data.columns" :key="index">
                <template v-for="(ch, _i) in item" :key="_i">
                    <td
                        class="td-hd"
                        :style="{
                            width: ch.titleWidth || '20%',
                            backgroundColor: config.headBgcolor || '#003054'
                        }"
                    >
                        {{ ch.title }}：
                    </td>
                    <td
                        :colspan="ch.colspan ? ch.colspan : 0"
                        :style="{ width: ch.keyWidth || '30%' }"
                    >
                        {{ data.tableData[ch.key] || '-' }}
                    </td>
                </template>
            </tr>
        </tbody>
    </table>
</template>

<script>
export default {
    name: '',
    props: {
        data: {
            type: Object,
            default: () => {
                return {
                    columns: [], //表格项
                    tableData: {} //数据
                };
            }
        },

        config: {
            type: Object,
            default: () => {
                return {
                    width: '100%',
                    height: 'auto',
                    headBgcolor: ''
                };
            }
        }
    },
    data() {
        return {};
    },
    mounted() {},
    methods: {}
};
</script>

<style lang="scss" scoped>
.p-lego-table {
    width: 100%;
    tbody {
        td {
            font-size: 16px;
            height: 45px;
            color: var(--font-color);
            border: 1px solid var(--el-border-color);
            padding: 0 10px;
            box-sizing: border-box;
        }
    }
    td {
        &.td-hd {
            text-align: right;
        }
        &.pd10 {
            padding: 10px;
        }
    }
}
</style>
