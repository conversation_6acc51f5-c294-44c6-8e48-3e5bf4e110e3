<!-- @format -->

<template>
    <div class="zj-wrap">
        <div class="zj-header" style="position: absolute; width: 100%">
            <img
                src="@/views/bgScreen/images/logo.png"
                class="zj-logo"
                style="left: 153px"
            />
            <div class="zj-ulmenu">
                <ul class="zj-flx">
                    <router-link
                        v-for="(item, i) of menuArr"
                        :key="i"
                        :to="item.url"
                        custom
                        v-slot="{ href, navigate, isActive }"
                    >
                        <li
                            :class="{ on: isActive }"
                            :href="href"
                            @click="navigate"
                        >
                            <span>{{ item.title }}</span>
                        </li>
                    </router-link>
                </ul>
                <ul class="zj-flx2">
                    <li>噪音</li>
                    <li>行政处罚</li>
                    <li>流程</li>
                </ul>
            </div>
            <p class="zj-lftime">{{ date }}</p>
            <p
                class="zj-rtenter"
                @click="goUrl('/app')"
                style="cursor: pointer"
            >
                前往业务系统
            </p>
        </div>

        <!-- 地图 -->
        <IndexMapVue></IndexMapVue>
        <div class="zj-content">
            <router-view></router-view>
        </div>
    </div>
</template>

<script>
import { reactive, toRefs } from 'vue';
import IndexMapVue from './3D/IndexMap/IndexMap.vue';
export default {
    setup() {
        const state = reactive({
            menuArr: [
                {
                    title: '蓝天',
                    selected: true,
                    url: '/gis/air'
                },
                {
                    title: '碧水',
                    selected: true,
                    url: '/gis/water'
                },
                {
                    title: '污染源',
                    selected: true,
                    url: '/gis/demo'
                }
            ]
        });

        return {
            ...toRefs(state)
        };
    },

    components: { IndexMapVue },
    mounted() {
        // elementUI 样式reset，分为深色系和浅色系
        this.$pChart.setChartConfig({
            THEME_COLOR: ServerGlobalConstant.eleTheme
        });
    },
    methods: {
        goUrl(url) {
            this.$router.push({
                path: url
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.zj-header {
    height: 85px;
    background: url(@/views/bgScreen/images/topbg.png) no-repeat top center;
    position: relative;
    z-index: 10;
}

.zj-header .zj-logo {
    position: absolute;
    left: 0;
    top: 8px;
    right: 0;
    bottom: 0;
    margin: 6px auto;
}

.zj-header .zj-ulmenu {
    display: flex;
    justify-content: space-between;
    padding-top: 12px;
}

.zj-header .zj-ulmenu .zj-flx {
    display: flex;
    margin-left: 9%;
}

.zj-header .zj-ulmenu .zj-flx2 {
    display: flex;
    margin-right: 9%;
}

.zj-header .zj-ulmenu li {
    font-size: 22px;
    color: #fff;
    text-align: center;
    width: 136px;
    height: 43px;
    line-height: 43px;
    cursor: pointer;
    background: url(@/views/bgScreen/images/mubg.png) no-repeat center center;
    background-size: 100% 100%;
    margin: 0 10px;
}

.zj-header .zj-ulmenu li.on {
    background: url(@/views/bgScreen/images/mubgs.png) no-repeat center center;
    background-size: 100% 100%;
    color: #fef901;
    font-size: 24px;
}

.zj-header .zj-lftime {
    font-size: 16px;
    color: #fff;
    position: absolute;
    left: 20px;
    top: 24px;
}

.zj-header .zj-rtenter {
    font-size: 16px;
    color: #fff;
    position: absolute;
    padding-right: 40px;
    background: url(@/views/bgScreen/images/nav-arr.png) no-repeat 90% center;
    right: 20px;
    top: 24px;
}

.zj-content {
    position: absolute;
    top: 0px;
    left: 0;
    right: 0;
    bottom: 0;

    pointer-events: none;
}

.zj-content > * {
    pointer-events: all;
}

.zj-wrap {
    position: absolute;
    top: 0;
    left: 0;
    // width: 1920px;
    // height: 1080px;
    width: 100%;
    height: 100%;
    background-color: #012137;
}
.zj-flx li {
    cursor: pointer;
}
</style>
