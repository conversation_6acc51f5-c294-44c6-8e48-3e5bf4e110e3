<!-- @format -->

<template>
    <el-main class="gisMain" style="padding: 0px; overflow: hidden">
        <div style="width: 100%; height: 100%">
            <MapboxMap
                :mapOption="mapOption"
                @onMapLoaded="onMapLoadedHandle"
            ></MapboxMap>
        </div>

        <!--  基础工具 (右上) -->
        <TopRightTool
            class="baseTool"
            :map="map"
            :mapCls="mapCls"
            :class="{ off: !showPanel }"
            :dock="toolDock"
        ></TopRightTool>

        <!-- 右下角 -->
        <!-- <BottomRightTool
                class="bottomRightToolCls"
                :class="{ off: !showPanel }"
            ></BottomRightTool> -->
    </el-main>
</template>
<script>
import MapboxMap from '@/components/gis/3D/MapBoxGLMap';
import BottomRightTool from '@/components/gis/3D/normal/BottomRightTool';
import TopRightTool from '@/components/gis/3D/normal/TopRightTool';
import ScanLayer from './Scan/ScanLayer';
import StretchLayer from './Stretch/StretchLayer';
import axios from 'axios';

export default {
    created() {},
    data() {
        return {
            mapOption: {},
            map: null,
            showPanel: true,
            toolDock: 'left',
            mapCls: 'body',
            layerID: '立体边界',
            routeName: ''
        };
    },
    unmounted() {},
    components: {
        MapboxMap,
        TopRightTool,
        BottomRightTool
    },
    mounted() {
        this.routeName = this.$route.name;
    },
    methods: {
        //地图加载完成时触发
        onMapLoadedHandle(map) {
            this.map = map;
            map.resize();

            window.glMap = map;

            if (GisServerGlobalConstant.common.needScan) {
                this.showScan();
            }

            //增加立体边界
            if (GisServerGlobalConstant.common.need3DBoundaryLine) {
                this.addLayer();
            }
        },

        showScan() {
            PowerGL.removeLayerFromName(this.map, '扫描图层');
            let lngLat = new mapboxgl.LngLat(119.697152415993, 30.82093287501);
            let scanLayer = new ScanLayer({
                id: '扫描图层',
                radius: 2000000, // 米
                opacity: 0.5,
                color: [14 / 255, 135 / 255, 184 / 255, 0],
                timeGap: 1.2, // 每次渲染角度变化值
                position: mapboxgl.MercatorCoordinate.fromLngLat(lngLat) // 中心点
            });
            if (this.map) {
                this.map.addLayer(scanLayer);
                this.map.setLayerZoomRange('扫描', 2, 13);
            }
        },

        addLayer() {
            axios.get('./gis/3D/data/xzq.json').then((res) => {
                let features = res.data.features;
                let feature = features[0];

                let geometry = feature.geometry;
                let coordinates = geometry.coordinates;

                let option2 = {
                    color: [54, 85, 72, 255],
                    offset: 0,
                    stretchHeight: 20
                };
                let layer2 = StretchLayer(this.layerID, option2);
                this.map.addLayer(layer2);
                layer2.stretch.initData(coordinates);
            });
        }
    },
    watch: {
        $route(to, from) {
            this.routeName = to.name;

            if (!this.map) {
                return;
            }

            this.map.flyTo(GisServerGlobalConstant.mapbox.mapBoxOption);
        },

        routeName(val) {
            if (this.routeName == 'demo') {
                this.showPanel = false;
            } else {
                this.showPanel = true;
            }
        }
    }
};
</script>

<style>
@import '~_as/gis/commom/mapCommon.css';
@import '~_as/gis/commom/mapTool.css';
@import '~_as/gis/commom/map3DMarker.css';
</style>

<style scoped>
.baseTool {
    position: absolute;
    top: 120px;
    left: 240px;
    right: inherit;
    bottom: inherit;
}

.baseTool.off {
    left: 20px;
}

.bottomRightToolCls {
    position: absolute;
    bottom: 10px;
    right: 520px;
}

.bottomRightToolCls.off {
    right: 10px;
}

.gisMain {
    width: 100%;
    height: 100%;
}
</style>
