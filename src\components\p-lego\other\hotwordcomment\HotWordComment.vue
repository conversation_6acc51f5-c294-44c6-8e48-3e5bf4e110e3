<!-- @format -->
<template>
    <div>
        <div ref="wordCloud" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import './world-cloud.min';

const lightColors = [
    '#6ebffb',
    '#a9df34',
    '#40c057',
    '#5a7fe8',
    '#ffea00',
    '#13c9d9',
    '#00a497',
    '#5363c5',
    '#218ede',
    '#f39800',
    '#4262d9',
    '#9799f3',
    '#0fd0b7',
    '#ffd351'
];

const darkColors = [
    '#2ad9ff',
    '#e9c613',
    '#26d267',
    '#f68b17',
    '#fc4a4a',
    '#4d76eb',
    '#00e1c4',
    '#9465f4',
    '#c0f02f',
    '#06a4ff'
];
export default {
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        }
    },
    data() {
        return {};
    },
    watch: {
        data: 'setCharts'
    },
    mounted() {
        this.setCharts();
    },
    methods: {
        setCharts() {
            let myChart = echarts.init(this.$refs.wordCloud);

            let colors =
                localStorage.themeType === 'dark' ? darkColors : lightColors;

            let data = this.data.map((v, i) => {
                return {
                    ...v,
                    textStyle: {
                        color: colors[i % colors.length]
                    }
                };
            });

            let option = {
                tooltip: {
                    show: true,
                    formatter: (val) => {
                        return `${val.marker} <span style="font-size:18px">${val.data.name}：${val.data.value}</span>`;
                    }
                },
                series: [
                    {
                        type: 'wordCloud',
                        // 网格大小，各项之间间距
                        gridSize: 15,
                        // 形状 circle 圆，cardioid  心， diamond 菱形，
                        // triangle-forward 、triangle 三角，star五角星
                        shape: 'rectangle',
                        // 字体大小范围
                        sizeRange: [12, 50],
                        // 文字旋转角度范围
                        rotationRange: [0, 0],
                        // 旋转步值
                        // rotationStep: 90,
                        // 自定义图形
                        // maskImage: maskImage,
                        left: 'center',
                        top: 'center',
                        right: null,
                        bottom: null,
                        // 画布宽
                        width: '100%',
                        // 画布高
                        height: '100%',
                        // 是否渲染超出画布的文字
                        drawOutOfBound: false,
                        /* textStyle: {
                            color: function () {
                                return (
                                    'rgb(' +
                                    [
                                        Math.round(Math.random() * 160),
                                        Math.round(Math.random() * 240),
                                        Math.round(Math.random() * 320)
                                    ].join(',') +
                                    ')'
                                );
                            }
                        }, */
                        data: data
                    }
                ]
            };
            myChart.setOption(option);
        }
    }
};
</script>

<style lang="less" scoped></style>
