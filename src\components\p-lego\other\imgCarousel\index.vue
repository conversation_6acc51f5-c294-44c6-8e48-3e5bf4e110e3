<!-- @format -->

<template>
    <div>
        <div class="img-carousel">
            <el-carousel
                indicator-position="outside"
                v-if="pageTotal"
                :height="imgHeight + 40 + 'px'"
                style="width: 100%"
            >
                <el-carousel-item v-for="item in pageTotal" :key="item">
                    <ul class="picList">
                        <li
                            v-for="n in pageTotal === item
                                ? lastPageNum
                                : pageSize"
                            :key="n"
                        >
                            <el-image
                                class="tu"
                                :style="{
                                    width: imgWidth + 'px',
                                    height: imgHeight + 'px'
                                }"
                                :src="data[(item - 1) * pageSize + n - 1].url"
                                @click="
                                    showImg(
                                        data[(item - 1) * pageSize + n - 1].url
                                    )
                                "
                                fit="scale-down"
                            />

                            <p class="zi text-ellipsis">
                                {{ data[(item - 1) * pageSize + n - 1].name }}
                            </p>
                        </li>
                    </ul>
                </el-carousel-item>
            </el-carousel>
        </div>
        <el-image
            ref="imgCon"
            style="width: 0; height: 0"
            :src="curUrl"
            :preview-src-list="[curUrl]"
        >
        </el-image>
    </div>
</template>

<script>
export default {
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        },
        config: {
            type: Object,
            default: function () {
                return {
                    /* imgWidth: 200,
                    imgHeight: 200,
                    pageSize: 2 */
                };
            }
        }
    },
    data() {
        return {
            pageTotal: 0,
            pageSize: 2,
            lastPageNum: 0,
            curUrl: ''
        };
    },
    watch: {
        data: 'setPage',
        config: 'setPage'
    },
    mounted() {
        this.setPage();
    },
    methods: {
        setPage() {
            this.imgWidth = this.config.imgWidth || 200;
            this.imgHeight = this.config.imgHeight || 200;
            this.pageSize = this.config.pageSize || 2;

            this.pageTotal = Math.ceil(this.data.length / this.pageSize);
            this.lastPageNum = this.data.length % this.pageSize;
            this.lastPageNum = this.lastPageNum || this.pageSize;
        },
        showImg(url) {
            this.curUrl = url;
            this.$refs.imgCon.showViewer = true;
        }
    }
};
</script>

<style lang="scss" scoped>
.img-carousel {
    /* width:450px;   */
    position: relative;
}
.img-carousel .picList {
    display: flex;
    justify-content: space-between;
    padding: 0;
    margin: 0;
}

.img-carousel .picList > li {
    border: 1px solid #0bb2ff;
    margin: 0 10px;
    flex-shrink: 0;
}
.img-carousel .picList > li .tu {
    cursor: pointer;
}
.img-carousel .picList > li .tu img {
    width: 100%;
    height: 100%;
}
.img-carousel .picList > li .zi {
    border-top: 1px solid #0bb2ff;
    font-size: 16px;
    color: #fff;
    line-height: 30px;
    background-color: rgba(11, 178, 255, 0.2);
    margin: 0;
    text-align: center;
}

.el-image__placeholder {
    background: rgba(0, 0, 0, 0.1);
}
.el-image__error {
    color: #aaa;
    background: rgba(0, 0, 0, 0.1);
    font-size: 12px;
}
.el-carousel {
    .el-carousel__indicator button {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #0569a6;
        opacity: 1;
    }

    .el-carousel__indicator.is-active button {
        background: #0bb2ff;
    }
}
</style>
