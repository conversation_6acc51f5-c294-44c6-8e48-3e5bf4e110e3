<!-- @format -->

<template>
    <div class="zy-cell" style="width: 580px; height: 494px">
        <div class="zy-cell-hd">
            <p class="til til1">能力建设</p>
            <span class="more"></span>
        </div>

        <div>
            <ul class="zy-data2">
                <li>
                    <div class="lp">
                        <img src="./images/data2-li1.png" alt="" />
                    </div>
                    <div class="rp">
                        <p class="p1">人员总数</p>
                        <p class="p2">{{ data.RY_TOTAL || '0' }}</p>
                    </div>
                </li>
                <li>
                    <div class="lp">
                        <img src="./images/data2-li2.png" alt="" />
                    </div>
                    <div class="rp">
                        <p class="p1">在编人数</p>
                        <p class="p2">{{ data.ZBRY_TOTAL || '0' }}</p>
                    </div>
                </li>
                <li>
                    <div class="lp">
                        <img src="./images/data2-li3.png" alt="" />
                    </div>
                    <div class="rp">
                        <p class="p1">执法人员数</p>
                        <p class="p2">{{ data.ZFRY_TOTAL || '0' }}</p>
                    </div>
                </li>
                <li>
                    <div class="lp">
                        <img src="./images/data2-li4.png" alt="" />
                    </div>
                    <div class="rp">
                        <p class="p1">执法车</p>
                        <p class="p2">{{ data.ZFC_TOTAL || '0' }}</p>
                    </div>
                </li>
                <li>
                    <div class="lp">
                        <img src="./images/data2-li5.png" alt="" />
                    </div>
                    <div class="rp">
                        <p class="p1">执法终端</p>
                        <p class="p2">{{ data.ZFZD_TOTAL || '0' }}</p>
                    </div>
                </li>
                <li>
                    <div class="lp">
                        <img src="./images/data2-li6.png" alt="" />
                    </div>
                    <div class="rp">
                        <p class="p1">无人机</p>
                        <p class="p2">{{ data.WRJ_TOTAL || '0' }}</p>
                    </div>
                </li>
            </ul>
            <p class="zy-til1">执法人员活跃率TOP5</p>

            <ul class="zy-data3" v-if="hyl">
                <div class="zy-data1 new-data1" v-show="errorArr.length > 0">
                    <div class="rp">
                        <div
                            class="item"
                            v-for="(item, index) in errorArr"
                            :key="index"
                        >
                            <p class="p1">
                                {{ item.total || '0'
                                }}<span style="font-size: 18px">%</span>
                            </p>
                            <p class="p2">{{ item.xzqhmc }}</p>
                        </div>
                    </div>
                </div>

                <div v-show="errorArr.length > 0">
                    <li v-for="(item, index) in hyl.slice(0, 3)" :key="index">
                        <span class="s1">
                            <span
                                class="tooltiptext"
                                :style="{ bottom: 46 - index * 8 + '%' }"
                                >{{ item.xzqhmc }}</span
                            >
                            <span>{{ item.xzqhmc }}</span>
                        </span>

                        <div class="bar">
                            <div
                                class="bili"
                                :style="{ width: item.total + '%' }"
                            ></div>
                        </div>
                        <span class="s2">{{ item.total || '0' }}%</span>
                    </li>
                </div>
                <div v-show="errorArr.length == 0">
                    <li v-for="(item, index) in hyl.slice(0, 5)" :key="index">
                        <span class="s1">
                            <span
                                class="tooltiptext"
                                :style="{ bottom: 46 - index * 8 + '%' }"
                                >{{ item.xzqhmc }}</span
                            >
                            <span>{{ item.xzqhmc }}</span>
                        </span>

                        <div class="bar">
                            <div
                                class="bili"
                                :style="{ width: item.total + '%' }"
                            ></div>
                        </div>
                        <span class="s2">{{ item.total || '0' }}%</span>
                    </li>
                </div>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    name: 'capacityBuilding',
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    RY_TOTAL: 2517,
                    ZFZD_TOTAL: 0,
                    WRJ_TOTAL: 0,
                    ZBRY_TOTAL: 1847,
                    ZFC_TOTAL: 0,
                    ZFRYHYL: [
                        [
                            {
                                total: 0,
                                xzqhdm: '430900',
                                name: '执法人员活跃率',
                                xzqhmc: '益阳市'
                            },
                            {
                                total: 0,
                                xzqhdm: '433100',
                                name: '执法人员活跃率',
                                xzqhmc: '湘西州'
                            },
                            {
                                total: 0,
                                xzqhdm: '430500',
                                name: '执法人员活跃率',
                                xzqhmc: '邵阳市'
                            },
                            {
                                total: 0,
                                xzqhdm: '431000',
                                name: '执法人员活跃率',
                                xzqhmc: '郴州市'
                            },
                            {
                                total: 0,
                                xzqhdm: '430100',
                                name: '执法人员活跃率',
                                xzqhmc: '长沙市'
                            },
                            {
                                total: 0,
                                xzqhdm: '430600',
                                name: '执法人员活跃率',
                                xzqhmc: '岳阳市'
                            },
                            {
                                total: 0,
                                xzqhdm: '431100',
                                name: '执法人员活跃率',
                                xzqhmc: '永州市'
                            },
                            {
                                total: 0,
                                xzqhdm: '430200',
                                name: '执法人员活跃率',
                                xzqhmc: '株洲市'
                            },
                            {
                                total: 0,
                                xzqhdm: '430700',
                                name: '执法人员活跃率',
                                xzqhmc: '常德市'
                            },
                            {
                                total: 0,
                                xzqhdm: '431200',
                                name: '执法人员活跃率',
                                xzqhmc: '怀化市'
                            },
                            {
                                total: 0,
                                xzqhdm: '430300',
                                name: '执法人员活跃率',
                                xzqhmc: '湘潭市'
                            },
                            {
                                total: 0,
                                xzqhdm: '430800',
                                name: '执法人员活跃率',
                                xzqhmc: '张家界市'
                            },
                            {
                                total: 0,
                                xzqhdm: '431300',
                                name: '执法人员活跃率',
                                xzqhmc: '娄底市'
                            },
                            {
                                total: 0,
                                xzqhdm: '430400',
                                name: '执法人员活跃率',
                                xzqhmc: '衡阳市'
                            }
                        ]
                    ],
                    ZFRY_TOTAL: 1564,
                    ERROR_ZFRYHYL: []
                };
            }
        }
    },
    data() {
        return {
            errorArr: [], // 超标数组
            hyl: [] // 活跃率
        };
    },
    watch: {},
    mounted() {
        this.getCapacityBldData();
    },
    methods: {
        getCapacityBldData() {
            let count = 0;
            try {
                this.hyl = this.data.ZFRYHYL[0];
            } catch (e) {
                this.hyl = [];
            }

            try {
                if (this.data.ERROR_ZFRYHYL.length > 0) {
                    this.errorArr = this.data.ERROR_ZFRYHYL[0];
                }
            } catch (e) {
                this.errorArr = [];
            }
        },
        // 按照value字段排序-大到小
        sortList(value) {
            return function (a, b) {
                let value1 = a[value];
                let value2 = b[value];
                return value2 - value1;
            };
        }
    }
};
</script>

<style lang="scss" scoped>
.zy-data3 li .s1 {
    width: 80px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    cursor: pointer;
    font-size: 16px;
    color: #fff;
    line-height: 20px;
}

.zy-data3 li .s1:hover .tooltiptext {
    visibility: visible;
}
.zy-data3 li .s1 .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #151414c4;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;

    /* 定位 */
    position: absolute;
    left: 1%;
    // z-index: 1;
    // bottom: 100%;
    // left: 50%;
    // margin-left: -60px;
}
.new-data1 {
    background: none;
    width: 520px;
    margin-top: 0;
    margin-bottom: 15px;
}
.new-data1 .rp .item {
    width: auto;
    flex: 1;
}
.new-data1 .rp::after {
    background: none;
}

/* -- Reset -- */
body,
ul,
ol,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
p,
form,
input,
textarea,
select,
button {
    margin: 0;
    padding: 0;
    font: 12px 'Microsoft YaHei', SimSun, Arial, Helvetica, sans-serif;
}

@font-face {
    font-family: 'TTTGB', sans-serif;
    src: url('./fonts/TTTGB-Medium.woff2') format('woff2'),
        url('./fonts/TTTGB-Medium.woff') format('woff'),
        url('./fonts/TTTGB-Medium.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'DIN-Bold', sans-serif;
    src: url('./fonts/DIN-Bold.woff2') format('woff2'),
        url('./fonts/DIN-Bold.woff') format('woff'),
        url('./fonts/DIN-Bold.ttf') format('truetype'),
        url('./fonts/DIN-Bold.eot') format('embedded-opentype'),
        url('./fonts/DIN-Bold.svg') format('svg'),
        url('./fonts/DIN-Bold.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}
.zy-cell {
    background-color: rgba(16, 37, 58, 1);
    position: relative;
    margin-bottom: 10px;
    padding-bottom: 1px;
}
.zy-cell::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    background-image: url(./images/cell-bg.png);
}
.zy-cell-hd {
    display: flex;
    height: 46px;
    padding: 0 10px;
    padding-bottom: 6px;
    background: url(./images/hd-bg.png) center bottom no-repeat;
    justify-content: space-between;
    align-items: center;
}

.zy-cell-hd .til {
    padding-left: 38px;
    font-size: 22px;
    color: #fff;
    line-height: 46px;
    background-position: 0 10px;
    background-repeat: no-repeat;
    font-family: 'TTTGB', sans-serif;
    text-shadow: 1px 1px 5px rgba(255, 255, 255, 0.8);
}

.zy-cell-hd .til1 {
    background-image: url(./images/til1.png);
}

.zy-cell-hd .more {
    width: 20px;
    height: 20px;
    background: url(./images/ic-more.png) no-repeat;
    cursor: pointer;
    margin-right: 20px;
}

.zy-data2 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-left: 22px;
    margin-top: -15px;
}

.zy-data2 li {
    display: flex;
    width: 180px;
    margin-top: 30px;
}

.zy-data2 li .lp {
    width: 58px;
    height: 58px;
    margin-right: 20px;
}

.zy-data2 li .rp {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
}

.zy-data2 li .rp .p1 {
    font-size: 16px;
    color: #fff;
}

.zy-data2 li .rp .p2 {
    font-size: 32px;
    color: #fff;
    font-family: 'DIN-Bold', sans-serif;
}

.zy-til1 {
    font-size: 18px;
    color: #fff;
    padding-left: 30px;
    background: url(./images/til1-bg.png) 0 center no-repeat;
    line-height: 50px;
    margin-left: 20px;
    text-align: left;
}

.zy-data3 {
    padding: 0 20px;
}

.zy-data3 li {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    height: 20px;
    align-items: center;
}

.zy-data3 li .bar {
    width: 400px;
    height: 15px;
    border-radius: 8px;
    background-color: #084171;
}

.zy-data3 li .bar .bili {
    width: 80%;
    height: 15px;
    border-radius: 8px;
    background: linear-gradient(to right, #3f95ff, #15cfff);
}

.zy-data3 li .s2 {
    font-size: 20px;
    color: #18ccff;
    line-height: 20px;
    font-family: 'DIN-Bold', sans-serif;
}
</style>
