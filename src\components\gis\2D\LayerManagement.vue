<!-- @format -->
<!--图层管理 -->
<template>
    <div style="position: relative">
        <ul class="zy-tools-gis">
            <li :class="{ on: showList }">
                <i class="tool-LayerManagement" @click="btnClick"></i>
            </li>
        </ul>

        <dl
            v-show="showList"
            class="gis-dloption1"
            :class="{ right: dock == 'right' }"
            style="width: 160px; top: 0; margin-right: 15px; position: absolute"
        >
            <dt>图层控制</dt>
            <dd class="gis-optwrap1">
                <label
                    class="pd-label"
                    v-for="(item, index) of layerList"
                    :key="index"
                    @click="checkBoxClick(item.value)"
                    ><input type="checkbox" /><i></i
                    ><span>{{ item.name }}</span></label
                >
            </dd>
        </dl>
    </div>
</template>

<script>
export default {
    name: 'LayerManagement',
    props: ['map', 'dock'],
    data() {
        return {
            showList: false,
            arrSelect: [], //选中的图层
            layerList: [
                { name: '地县级水源地', value: '地县级水源地', total: 0 },
                { name: '乡镇级水源地', value: '乡镇级水源地', total: 0 },
                { name: '入湖排污口', value: '入湖排污口', total: 0 },
                { name: '入河排污口', value: '入河排污口', total: 0 }
            ]
        };
    },
    components: {},
    computed: {},
    mounted() {
        this.initPage();
    },
    methods: {
        initPage() {
            if (this.map) {
                //
            } else {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
            }
        },
        btnClick() {
            this.showList = !this.showList;
        },

        checkBoxClick(val) {
            let vis = this.arrSelect.includes(val);
            if (vis) {
                this.arrSelect = this.arrSelect.filter((item) => {
                    return item != val;
                });
            } else {
                this.arrSelect.push(val);
            }

            switch (val) {
                case '地县级水源地':
                case '乡镇级水源地':
                    break;

                default:
                    break;
            }
        }
    },
    watch: {}
};
</script>

<style scoped>
.gis-dloption1 {
    border-radius: 5px;
    border: 1px solid #1c4d73;
    background-color: rgba(11, 34, 55, 0.8);
    box-sizing: border-box;
    overflow: hidden;
    left: 40px;
}
.gis-dloption1 dt {
    background: #075393;
    font-size: 18px;
    color: #fff;
    line-height: 40px;
    height: 40px;
    text-align: center;
}

.gis-optwrap1 {
    padding: 8px 10px 8px 20px;
}
.gis-optwrap1 .pd-label {
    display: flex;
    align-items: center;
    height: 35px;
}
.gis-optwrap1 .pd-label.on ~ .gis-sublevel {
    display: block;
}

.gis-optwrap1 .pd-label span {
    display: inline-block;
    vertical-align: middle;
    font-size: 16px;
    color: #fff;
}

.gis-optwrap1 .pd-label i {
    display: inline-block;
    vertical-align: middle;
    background-repeat: no-repeat;
    background-position: center;
    width: 16px;
    height: 16px;
    margin-right: 10px;
}

.gis-optwrap1 .pd-label input {
    display: none;
}

.lightTheme .gis-optwrap1 .pd-label span {
    color: #333;
}

.lightTheme .gis-dloption1 dt {
    background: #dfedff;
    color: #4895ea;
}

.gis-dloption1.right {
    left: -170px;
}
</style>
