<!-- @format -->

<template>
    <div>
        <p style="font-size: 20px">{{ data.name }}</p>
        <div ref="map" style="width: 100%; height: 100%"></div>
    </div>
</template>
<script>
// const echarts = require('echarts');
export default {
    data() {
        return {};
    },
    props: {
        data: {
            type: Object
        }
    },
    watch: {
        data: {
            deep: true,
            immediate: true,
            handler: function (New, Old) {
                this.$nextTick(() => {
                    this.getData();
                });
            }
        }
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            // 注册地图
            this.$echarts.registerMap('map', this.data.JWD);

            let mapChart = this.$echarts.init(this.$refs.map);

            // 数据源 ，这个数据 调用接口获取

            //自定义图形方法
            function addImage(url, params, api, realData, width, height) {
                return {
                    type: 'image',

                    style: {
                        image: url,
                        x: api.coord([
                            realData[params.dataIndex].lng,
                            realData[params.dataIndex].lat
                        ])[0],
                        y: api.coord([
                            realData[params.dataIndex].lng,
                            realData[params.dataIndex].lat
                        ])[1],
                        width: width,
                        height: height
                    }
                };
            }

            // echarts 的 配置
            let option = {
                geo: {
                    map: 'map',

                    itemStyle: {
                        // 定义样式
                        normal: {
                            // 普通状态下的样式
                            areaColor: '#55E6BD', // 地图的颜色
                            borderColor: '#55E6BD' //  地图边缘线的颜色
                        },
                        emphasis: {
                            // 高亮状态下的样式
                            areaColor: '#55E6BD'
                        }
                    },
                    label: {
                        normal: {
                            show: false
                        },
                        emphasis: {
                            show: false
                        }
                    }
                },

                series: [
                    {
                        name: '坐标', // series名称
                        type: 'custom', // series图表类型
                        coordinateSystem: 'geo' // series坐标系类型
                    }
                ]
            };

            mapChart.setOption(option);
        }
    }
};
</script>
<style scoped></style>
