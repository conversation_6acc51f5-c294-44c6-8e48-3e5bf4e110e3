/** @format */

import { createVNode, render } from 'vue';
import PopWater from '../dialog/PopWater'; //我想要塞进popup里面的一个组件，该组件负责展示一下点的详细信息
import PopSGZ from '../dialog/PopSGZ';
import BasePointUtil from '@/components/gis/3D/utils/BasePointUtil';
class PointUtil extends BasePointUtil {
    constructor(map, callback) {
        super(map, callback);

        let arr = [
            {
                name: 'SSZDY', //涉水企业
                url: './gis/3D/images/QT/SSZDY.png'
            },
            {
                name: 'WRYZX', //污染源在线
                url: './gis/3D/images/QT/WSCLC.png'
            }
        ];

        for (let i = 0; i <= 6; i++) {
            arr.push({
                name: 'ZDZ' + i,
                url: `./gis/3D/images/Water/ZDZ-${i}.png`
            });

            arr.push({
                name: 'DM' + i,
                url: `./gis/3D/images/Water/DM-${i}.png`
            });
        }

        this.loadImages(arr);
    }

    // mapboxgl.Popup参数 可选择是否覆盖的方法
    getPopupOption(id, properties) {
        let option = {};

        switch (id) {
            case '工业源':
            case '扬尘源':
            case '自动站':
            case '断面':
                option = {
                    offset: [0, -20]
                };
                break;
        }

        return option;
    }
    // mapboxgl.Popup.HTML 需要覆盖的方法
    getPopupStr(id, properties) {
        let html = '';
        let vNodePopup = null;

        switch (id) {
            case '废水在线监控':
                html = `<div class="protecttip" > ${properties.QYMC} </div>`;
                break;

            case '涉水企业':
                html = `<div class="protecttip" > ${properties.QYMC} </div>`;
                break;

            case '自动站':
            case '断面':
                html = `<div class="protecttip" > ${properties.CDMC} </div>`;
                break;

            // case '大气站点':
            //     // html = `<div class="protecttip" > ${properties.CDMC} </div>`;
            //     html = document.createElement('div');

            //     //comMarkerPopup为组件名称
            //     vNodePopup = createVNode(PopZD, {
            //         selectItem: properties,
            //         layerID: properties.layerId
            //     });
            //     render(vNodePopup, html);
            //     break;
        }

        return html;
    }

    // marker.option 可选择是否覆盖方法
    getMarkerOption(id, properties) {
        let result = { className: 'coustom-marker' };
        return result;
    }

    //生成marker.element 需要覆盖的方法
    getMarkerHtml(properties, params) {
        let el = document.createElement('div');
        el.className = 'air-marker';
        let template = ``;

        switch (params.id) {
            case '自动站':
            case '断面':
                template = this.getWaterMarkerStr(
                    properties,
                    params,
                    params.style
                );
                break;
        }
        el.innerHTML = template;
        return el;
    }

    /**
     * 水环境marker的样式
     * type: 1: 默认样式 2: 真气网-小圆点 3、真气网-背景+文字 4：图片+底部闪烁 5:图片+文字+地图闪烁
     */
    getWaterMarkerStr(properties, params, type = '1') {
        let level, dj, data, width, color, fontColor;
        level = properties.SZLB;
        dj = properties.SZLBBS;
        data = level;

        let template = '';

        switch (type.toString()) {
            case '1': //常见效果
                template = `<dl style="transform: translate(0%, -50%);" class="water-effect1 dj${dj}"><dt>${properties.CDMC}&emsp;${data}</dt><dd></dd></dl>`;

                if (!params.showAno) {
                    template = `<dl class="water-effect1 dj${dj}" style="transform: translate(0%, -50%);"><dt>${data}</dt><dd></dd></dl>`;
                }
                break;

            case '2': //真气网-小圆点
                template = `<div class="water-effect2 circlelevel${dj}"></div>`;
                break;

            case '3': //真气网-背景+文字
                width = 15 * properties.CDMC.length;
                color = PowerGL.getWaterColorByLevel(level);

                if (dj > 3) {
                    fontColor = '#fff';
                } else {
                    fontColor = '#000';
                }

                template = `
                <div class="water-effect3">
                <div class="marker_marker_1AA" style="background:${color};color:${fontColor}">
                <div class="marker_arrow_18U" style="border-top-color: ${color}"></div>${data}</div>
                <div class="marker_name" style="width: ${width}px; margin-left: -${(width - 35) / 2
                    }px;">${properties.CDMC}</div>
                </div>
                `;

                if (!params.showAno) {
                    template = `
                    <div class="water-effect3">
                    <div class="marker_marker_1AA" style="background:${color};color:${fontColor}">
                    <div class="marker_arrow_18U" style="border-top-color: ${color}"></div>${data}</div>
                    `;
                }
                break;
            case '4': // 图片+底部闪烁
                color = PowerGL.getWaterColorByLevel(level);
                template = `<div class="water-effect4">
                <img><img style="width: 26px;margin-left: 2px;" src="
                     ./gis/3D/images/Water/szdm-${dj}.png" alt="">
                <div class="water-effect4-dipan">
                    <svg width="34" height="16">
                        <ellipse class="e1" cx="17" cy="8" rx="17" ry="8" style="fill: ${color}"></ellipse>
                        <ellipse class="e2" cx="17" cy="8" rx="8" ry="4" style="fill:${color}></ellipse>
                    </svg>
                </div>
            </div>`;
                break;

            case '5': //宜昌大屏点位效果： 图片+文字+地图闪烁
                color = PowerGL.getWaterColorByLevel(level);
                template = `<div class="water-effect5 type${dj}" >
				<h1><p>${properties.CDMC}</p><i>${data}</i></h1>
				<div class="water-effect5-dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                if (!params.showAno) {
                    template = `<div class="water-effect5  type${dj}" >
				<h1><i>${data}</i></h1>
				<div class="water-effect5-dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                }
                break;

            case '6':
                color = PowerGL.getWaterColorByLevel(level);

                template = `<div class="water-effect6  szlb-${dj}">
                <h1>
                ${properties.CDMC}
                    <i style="color:${color};">${properties.JGJBMC}</i>
                    <i style="color:${color};">
                    <b>${data}</b></i>
                </h1>
                <div class="water-effect6-dipan">
                    <svg width="34" height="16" style=" display: block;margin: 0 auto;">
                        <ellipse class="e1" cx="17" cy="8" rx="17" ry="8" style="fill: ${color}"></ellipse>
                        <ellipse class="e2" cx="17" cy="8" rx="8" ry="4" style="fill: ${color}"></ellipse>
                    </svg>
                </div>
            </div>`;
                break;
        }

        return template;
    }

    // 获取symbol layout属性  需要覆盖的方法
    getSymbolLayout(params) {
        let layout = {};

        switch (params.id) {
            case '自动站':
                layout = {
                    'icon-image': [
                        'coalesce',
                        ['image', ['concat', 'ZDZ', ['get', 'SZLBBS']]],
                        //默认（一定是有的）
                        ['image', 'ZDZ0']
                    ],
                    'icon-size': [
                        'step',
                        ['zoom'],
                        0.3,
                        10,
                        0.5,
                        13,
                        0.8,
                        18,
                        1
                    ]
                };
                break;

            case '断面':
                layout = {
                    'icon-image': [
                        'coalesce',
                        ['image', ['concat', 'DM', ['get', 'SZLBBS']]],
                        //默认（一定是有的）
                        ['image', 'DM0']
                    ],
                    'icon-size': [
                        'step',
                        ['zoom'],
                        0.3,
                        10,
                        0.5,
                        13,
                        0.8,
                        18,
                        1
                    ]
                };
                break;

            case '涉水企业':
                layout = {
                    'icon-image': 'SSZDY',
                    'icon-size': [
                        'step',
                        ['zoom'],
                        0.3,
                        10,
                        0.5,
                        13,
                        0.8,
                        18,
                        1
                    ]
                };
                break;

            case '废水在线监控':
                layout = {
                    'icon-image': 'WRYZX',
                    'icon-size': [
                        'step',
                        ['zoom'],
                        0.3,
                        10,
                        0.5,
                        13,
                        0.8,
                        18,
                        1
                    ]
                };
                break;
        }

        layout['icon-allow-overlap'] = true;

        return layout;
    }

    // 获取symbol paint属性
    getSymbolPaint(params) {
        let paint = {};
        return paint;
    }
}
export default PointUtil;
