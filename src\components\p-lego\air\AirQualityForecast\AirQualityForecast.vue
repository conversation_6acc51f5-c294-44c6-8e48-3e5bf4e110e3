<!-- @format -->

<template>
    <div style="width: 528px; background: #004280">
        <div class="pd-modhd">
            <span
                >空气质量预报<strong style="font-size: 12px; font-weight: 400"
                    >(起报时间：{{ qbsj }})</strong
                ></span
            >
        </div>
        <div class="pd-modbd">
            <div class="gap"></div>
            <table cellpadding="0" class="pd-table2a">
                <tr>
                    <td>日期</td>
                    <td v-for="(item, index) of ybList.rqs" :key="index">
                        {{ getDays(item) }}
                        <p>{{ ybList.rq[index] }}</p>
                    </td>
                </tr>
                <tr>
                    <td>AQI最高</td>
                    <td v-for="(item, index) of ybList.aqi2" :key="index">
                        <em
                            :style="{
                                background: getBgColor(item),
                                color: getColor(item)
                            }"
                            >{{ item }}</em
                        >
                    </td>
                </tr>
                <tr>
                    <td>AQI最低</td>
                    <td v-for="(item, index) of ybList.aqi1" :key="index">
                        <em
                            :style="{
                                background: getBgColor(item),
                                color: getColor(item)
                            }"
                            >{{ item }}</em
                        >
                    </td>
                </tr>
                <tr>
                    <td>首要污染物</td>
                    <td v-for="(item, index) of ybList.sywrw" :key="index">
                        {{ item }}
                    </td>
                </tr>
            </table>
        </div>
    </div>
</template>
<script>
import util from './js/util';
export default {
    name: 'airQualityForecast',
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        }
    },
    data() {
        return {
            ybList: {}, //预报数据
            qbsj: '' //起报时间
        };
    },
    mounted() {
        this.getAirAQIYb();
    },
    methods: {
        getAirAQIYb() {
            this.ybList = {
                rqs: [],
                rq: [],
                aqi1: [],
                aqi2: [],
                sywrw: []
            };

            for (let item of this.data) {
                this.ybList.rqs.push(item.ybsj);
                this.ybList.rq.push(item.YBSJ);
                this.qbsj = item.YBCSSJ.substring(0, 10);

                let arr = item.AQI.split('~');
                this.ybList.aqi1.push(arr[0]);
                this.ybList.aqi2.push(arr[1]);
                this.ybList.sywrw.push(item.SYWRW);
            }
        },
        getDays(s) {
            let str = '日一二三四五六';
            let index = new Date(s).getDay();
            return '周' + str.charAt(index);
        },
        getBgColor(item) {
            return util.getLevelPollution('AQI', item).color;
        },

        getColor(item) {
            if (item <= 150) {
                return '#000';
            } else {
                return '#FFF';
            }
        }
    }
};
</script>

<style scoped>
@import './style.css';
</style>
