<!-- @format -->

<!--
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2022-05-05 10:40:23
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-05-05 11:19:59
 * @FilePath: /Front_PC_COMPONENTS/src/components/business/mobile/dateNumber/DateNumber.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- @format -->

<template>
    <div>
        <dl class="pd-dlbx3">
            <dt>—— {{ title }} ——</dt>
            <dd>
                <p v-for="item in data" :key="item.id">{{ item }}</p>
            </dd>
        </dl>
    </div>
</template>

<script>
export default {
    props: {
        dataNum: {
            type: String,
            default: '00000'
        },
        title: {
            type: String,
            default: '组件标题'
        }
    },

    data() {
        return {
            data: []
        };
    },

    mounted() {
        this.data = this.dataNum.split('');
    }
};
</script>

<style scoped>
.pd-dlbx3 dt {
    font-size: 16px;
    color: #fff;
    text-align: center;
    font-weight: bold;
}

.pd-dlbx3 dd {
    display: flex;
    justify-content: center;
    padding-top: 12px;
}

.pd-dlbx3 dd p {
    background: url(./images/numbg1.png) no-repeat;
    width: 48px;
    height: 57px;
    line-height: 57px;
    text-align: center;
    font-size: 45px;
    color: #fff;
    margin: 0 7px;
    font-family: 'DINPro-Black';
}

.pd-dlbx3 dd sub {
    background: url(./images/dot.png) no-repeat center bottom;
    width: 8px;
}
</style>
