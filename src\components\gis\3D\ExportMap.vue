<!-- @format -->
<!--导出地图 -->
<template>
    <ul class="zy-tools-gis">
        <li>
            <i class="tool-exportMap" @click="exportImg"></i>
        </li>
    </ul>
</template>

<script>
export default {
    name: 'ExportMap',
    props: ['map'],
    data() {
        return {};
    },
    components: {},
    computed: {},
    mounted() {},
    methods: {
        exportImg() {
            let dpi = 300;
            Object.defineProperty(window, 'devicePixelRatio', {
                get: function () {
                    return dpi / 96;
                }
            });

            // let content = this.map.getCanvas().toDataURL();
            // console.log(content);

            try {
                html2canvas($('.mapContainer')[0], {
                    logging: false, //Enable log (use Web Console for get Errors and Warings)
                    useCORS: true
                }).then(function (canvas) {
                    let url = canvas.toDataURL('image/png');
                    let tmp = Date.parse(new Date()).toString();
                    tmp = tmp.substr(0, 15);
                    let fileName = '地图导出' + tmp;
                    let $a = $('<a></a>')
                        .attr('href', url)
                        .attr('download', fileName + '.png');
                    $a[0].click();
                });
            } catch (err) {
                // $('.jimu-widget-MainMenu').show();
                // $('.jimu-widget-Main_Tool').show();
            }
        },
        //判断是否为ie浏览器
        browserIsIe: function () {
            if (!!window.ActiveXObject || 'ActiveXObject' in window)
                return true;
            else return false;
        }
    },
    watch: {}
};
</script>

<style scoped></style>
