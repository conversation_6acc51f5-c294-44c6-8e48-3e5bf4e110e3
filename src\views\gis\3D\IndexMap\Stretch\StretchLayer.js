/** @format */

import Stretch from './Stretch';
export default function StretchLayer(layerId, option) {
    return {
        id: layerId,
        type: 'custom',
        map: null,
        matrix: null,
        stretch: new Stretch(option),
        setLngLat(lngLat) {
            this.stretch.lngLat = lngLat;
        },
        onAdd: function (map, gl) {
            this.map = map;
            this.stretch.init(gl);

            map.on('resize', () => {
                if (this.stretch && this.matrix) {
                    console.log('地图resize');
                    this.stretch.init(gl);
                    this.stretch.initDraw();
                    this.stretch.render(this.matrix, this.map);
                    map.triggerRepaint();
                }
            });
        },
        onRemove: function () { },
        render: function (gl, matrix) {
            gl.enable(gl.BLEND);
            gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
            this.stretch.render(matrix, this.map);
            this.matrix = matrix;
            // this.map.triggerRepaint();
        }
    };
}
