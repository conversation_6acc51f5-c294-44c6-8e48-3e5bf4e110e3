<!-- @format -->

<!-- 日数据播放轴，每次展示一个月 -->
<template>
    <div class="gis-timeaxis">
        <img
            @click="playClick"
            v-show="!timeId"
            alt="渲染失败"
            src="../../../assets/gis/commom/images/dark/gis_plybtn.png"
            class="pd-play"
        />

        <img
            v-show="timeId"
            @click="pauseClick"
            alt="渲染失败"
            src="../../../assets/gis/commom/images/dark/gis_pausebtn.png"
            class="pd-play"
        />

        <el-date-picker
            v-model="selectDate"
            type="month"
            placeholder="选择日期"
            class="dateCls2"
            :clearable="false"
            value-format="YYYY-MM"
            format="YYYY-MM"
            @change="selectDateChanged"
            style="width: 130px; margin-top: 10px"
        >
        </el-date-picker>

        <ul class="pd-ulnumlst">
            <li
                v-for="(item, index) of arrDate"
                :key="index"
                :class="[
                    {
                        on: selectDay == item.sj
                    },
                    item.clsName
                ]"
                @click="itemClick(item)"
            >
                {{ item.sj }}<sup>{{ item.tipStr }}</sup>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    data() {
        return {
            selectDate: '2021-03', //当前选中的日历
            arrDate: [], //
            timeId: '',
            lastDate: '',
            selectDay: '01'
        };
    },
    components: {},
    unmounted() {
        this.clearTime();
    },
    methods: {
        selectDateChanged() {
            this.clearTime();

            if (this.selectDate == this.lastDate.substring(0, 7)) {
                this.selectDay = this.lastDate.substring(8, 10);
            } else {
                this.selectDay = '01';
            }

            this.$emit('datePickChange', this.selectDate);
            this.intData();
        },

        //初始化timeline
        initTimeLine(option) {
            this.lastDate = option.selectTime;
            this.selectDate = option.selectTime.substring(0, 7);
            this.selectDay = option.selectTime.substring(8, 10);

            this.intData();
        },

        intData() {
            let days = this.$dayjs(this.selectDate + '-01').daysInMonth();

            this.arrDate = [];

            for (let i = 1; i <= days; i++) {
                let sj = this.selectDate + (i >= 10 ? '-' + i : '-0' + i);

                //在最新时间（initDate）之后
                let flag = this.$dayjs(sj).isAfter(this.$dayjs(this.lastDate));

                this.arrDate.push({
                    sj: i >= 10 ? i : '0' + i,
                    tipStr: sj,
                    clsName: flag ? '' : 'init'
                });
            }

            this.$emit(
                'dateChange',
                this.selectDate + '-' + this.selectDay,
                false
            );
        },

        itemClick(item) {
            this.selectDay = item.sj;
            this.clearTime();

            this.$emit(
                'dateChange',
                this.selectDate + '-' + this.selectDay,
                false
            );
        },

        //暂停
        pauseClick() {
            this.clearTime();
        },

        //播放按钮点击
        playClick() {
            //如果正在播放，则停止
            if (this.timeId) {
                this.clearTime();
                return;
            }

            let sj = this.selectDate + '-' + this.selectDay;
            let flag1 = this.$dayjs(sj).isAfter(this.$dayjs(this.lastDate));
            let flag2 = this.$dayjs(sj).isSame(this.$dayjs(this.lastDate));

            //如果当前时间在最新时间的后面，则从第一个开始播放
            if (
                flag1 ||
                flag2 ||
                parseInt(this.selectDay) == this.arrDate.length
            ) {
                this.selectDay = '01';

                let sj2 = this.selectDate + '-' + this.selectDay;
                this.$emit('dateChange', sj2, true);
            }

            this.timeId = setInterval(() => {
                let i = parseInt(this.selectDay);
                i++;
                this.selectDay = i >= 10 ? i : '0' + i;

                let sj = this.selectDate + '-' + this.selectDay;

                this.$emit('dateChange', sj, true);

                let flag = this.$dayjs(sj).isSame(this.$dayjs(this.lastDate));

                //最后一个或者到达最大值时，暂停播放
                if (i == this.arrDate.length || flag) {
                    this.clearTime();
                }
            }, 2000);
        },

        //清除定时器
        clearTime() {
            if (this.timeId) {
                clearInterval(this.timeId);
                this.timeId = null;
            }
        }
    }
};
</script>

<style scoped></style>
