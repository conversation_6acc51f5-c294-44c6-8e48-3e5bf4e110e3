<!-- @format -->

<template>
    <div
        class="sw1212-wrap"
        style="background: #fff; width: 100%; height: 100%"
    >
        <div>
            <div style="padding: 20px; top: 0">
                <div>
                    <div style="display: flex; align-items: center">
                        <!-- <p style="font-size: 16px">监测类型：</p>
            <el-select
                v-model="value"
                placeholder="请选择"
                @change="typeChange"
            >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select> -->
                        <p style="font-size: 16px; margin-left: 10px">时间：</p>
                        <el-date-picker
                            @change="timeChange"
                            v-model="time"
                            type="year"
                            placeholder="选择年"
                            style="height: 35px"
                            value-format="YYYY"
                        >
                        </el-date-picker>
                        <p style="font-size: 16px; margin-left: 10px">地市：</p>
                        <el-select
                            v-model="SSDS"
                            placeholder="请选择"
                            @change="SSDSChange"
                        >
                            <el-option
                                v-for="item in SSDSOptions"
                                :key="item.XZQHDM"
                                :label="item.XZQH"
                                :value="item.XZQHDM"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div class="gap"></div>
                    <el-table
                        :data="listData"
                        style="width: 100%; height: 500px"
                    >
                        <el-table-column
                            type="index"
                            width="100"
                            label="序号"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="XZQH"
                            label="区域"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="ZFRWS"
                            label="检查任务总数"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="JCZRY"
                            label="检查总人数"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="DCJCRS"
                            label="单次检查人数"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column label="检查任务类型" align="center">
                            <el-table-column
                                prop="JDXJCZS"
                                label="监督性监测次数"
                                align="center"
                            >
                            </el-table-column>
                            <el-table-column
                                prop="ZXZFZS"
                                label="专项执法次数"
                                align="center"
                            >
                            </el-table-column>
                        </el-table-column>
                    </el-table>
                    <div class="gap"></div>
                    <p-bar
                        v-if="showChart"
                        :data="lineData"
                        :config="{
                            color: ['#4874cb', '#ef8330'],
                            showFillArea: true,
                            barWidth: 15
                        }"
                        style="width: 100%; height: 400px"
                    ></p-bar>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    zfjlStatistics,
    queryAdministrativeRegionByCondition
} from '@/api/knowledge.js';

export default {
    name: 'Annual',
    components: {},
    provide() {
        return {};
    },
    data() {
        return {
            options: [
                {
                    value: '1',
                    label: '常规剂量数据采集'
                },
                {
                    value: '2',
                    label: '特殊剂量数据采集'
                }
            ],
            value: '1',
            time: this.$dayjs().format('YYYY'),
            timer: null,
            listData: [],
            lineData: {},
            showChart: true,
            SSDS: '',
            SSDSOptions: [],
            XZQHDM: ''
        };
    },
    created() {
        this.getSSDSOptions();
    },
    computed: {},
    mounted() {
        window.addEventListener('resize', this.refreshChart);

        this.getData();

        this.$nextTick(() => {
            this.refreshChart();
        });
    },
    methods: {
        SSDSChange(e) {
            this.XZQHDM = e;
            this.getData();
        },
        getSSDSOptions() {
            queryAdministrativeRegionByCondition({
                LEVEL: '2',
                FDM: '440000',
                XZJB: '2'
            }).then((res) => {
                this.SSDSOptions = res;
                this.SSDSOptions.unshift({
                    XZQH: '全部',
                    XZQHDM: ''
                });
            });
        },
        getData() {
            zfjlStatistics({
                ND: this.time,
                XZQH: this.XZQHDM
            }).then((res) => {
                this.listData = res.data_json;
                this.lineData = {
                    xAxis: [],
                    series: [
                        {
                            name: '监督性监测',
                            data: []
                        },
                        {
                            name: '专项执法',
                            data: []
                        }
                    ]
                };
                if (res.data_json.length) {
                    res.data_json.map((e) => {
                        this.lineData.xAxis.push(e.XZQH);
                        this.lineData.series[0].data.push(e.JDXJCZS);
                        this.lineData.series[1].data.push(e.ZXZFZS);
                    });
                }
            });
        },

        refreshChart() {
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                this.showChart = false;
                let unsync = setTimeout(() => {
                    this.showChart = true;
                    clearTimeout(unsync);
                }, 0);
            }, 200);
        },
        typeChange(e) {
            this.value = e;
            this.getData();
        },
        timeChange(e) {
            this.time = e;
            this.getData();
        }
    }
};
</script>

<style lang="scss" scoped></style>
