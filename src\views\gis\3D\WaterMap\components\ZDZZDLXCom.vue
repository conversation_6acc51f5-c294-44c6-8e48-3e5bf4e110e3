<!-- @format -->

<template>
    <div>
        <dt class="flx1 ac" style="padding-left: 15px">
            <label class="sw0811-check1" @click="allLayerClick"
                ><input
                    type="checkbox"
                    disabled
                    style="pointer-events: none"
                    :checked="checkAll"
                /><strong>站点类型（{{ allTotal }}）</strong></label
            >
        </dt>
        <dd class="sw0811-checkbox1 yy0812-checkbox1">
            <label
                class="sw0811-check1"
                v-for="(item, index) of zts"
                :key="index"
                @click="ztLayerClick(item)"
                ><input
                    type="checkbox"
                    :checked="item.selected"
                    disabled
                    style="pointer-events: none"
                /><span>{{ item.name }}（{{ item.total }}）</span></label
            >
        </dd>
    </div>
</template>

<script>
export default {
    data() {
        return {
            zdlxArr: [],
            zts: [
                // {
                //     name: '常规自动站',
                //     fiterType: '1',
                //     selected: true,
                //     total: 0,
                //     DM: 'ZD-CGZDZ'
                // },
                // {
                //     name: '特征因子自动站',
                //     fiterType: '2',
                //     selected: true,
                //     total: 0,
                //     DM: 'ZD-TZYZZDZ'
                // }
            ],

            allTotal: 0,

            checkAll: true //全部选中
        };
    },
    props: ['tjData'],
    mounted() {
        this.getSelectJGJB();
    },
    methods: {
        //专题点击
        allLayerClick() {
            this.checkAll = !this.checkAll;

            for (let o of this.zts) {
                o.selected = this.checkAll;
            }

            this.getSelectJGJB();
        },

        //子专题点击
        ztLayerClick(obj) {
            obj.selected = !obj.selected;

            //根据子级的选中状态，设置父级的选中状态
            this.checkAll = this.zts.some((item) => {
                return item.selected;
            });

            this.getSelectJGJB();
        },

        //获取选中的监管级别
        getSelectJGJB() {
            let arrTemp = this.zts.filter((item) => {
                return item.selected;
            });

            this.zdlxArr = arrTemp.map((item) => {
                return item.fiterType;
            });

            this.$emit('jgjbChange');
        }
    },
    watch: {
        tjData: {
            deep: true,
            handler(val) {
                let tempArr = [];
                for (let item of val.DMSX) {
                    if (item.DM == 'ZS') {
                        this.allTotal = item.NUM;
                    } else {
                        tempArr.push({
                            name: item.MC,
                            fiterType: item.MC,
                            selected: true,
                            total: item.NUM,
                            DM: item.DM
                        });
                    }
                }

                this.zts = tempArr;

                this.getSelectJGJB();
            }
        }
    }
};
</script>

<style></style>
