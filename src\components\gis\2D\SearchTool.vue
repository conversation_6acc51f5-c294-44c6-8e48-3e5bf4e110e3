<!-- @format -->
<!-- 搜索控件 -->
<template>
    <div>
        <ul class="zy-tools-gis">
            <li :class="{ on: showInput }">
                <i class="tool-search" @click="btnClick"></i>
            </li>
        </ul>
        <div
            class="pd-srhbx1-gis"
            :class="{ right: dock == 'right' }"
            v-show="showInput"
        >
            <input type="text" v-model="keyStr" placeholder="关键字搜索" />
        </div>
        <div
            class="zy-results-gis"
            :class="{ right: dock == 'right' }"
            v-show="showResult"
        >
            <ul class="zy-history-gis">
                <li
                    v-for="(item, index) of fiterData"
                    :key="index"
                    @click="itemClick(item)"
                >
                    {{ item.name }}
                    <em style="color: #3b88e4" v-show="item.type"
                        >({{ item.type }})</em
                    >
                </li>
            </ul>
            <span class="del" @click="delClick">删除历史</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'SearchTool',
    props: ['map', 'arrSearch', 'dock'],
    data() {
        return {
            keyStr: '',
            fiterData: [],
            showInput: false
        };
    },
    components: {},
    computed: {
        showResult() {
            return this.fiterData.length > 0;
        }
    },
    mounted() {},
    methods: {
        btnClick() {
            this.showInput = !this.showInput;
        },
        //删除历史
        delClick() {
            this.keyStr = '';
        },

        //列表点击
        itemClick(item) {
            PowerGis.pointTo(this.map, item, true, 15);
        }
    },
    watch: {
        keyStr(val, oldVal) {
            if (this.arrSearch) {
                this.fiterData = this.arrSearch.filter((item) => {
                    if (val && item.name) {
                        return item.name.indexOf(val) >= 0;
                    } else {
                        return false;
                    }
                });
            }
        }
    }
};
</script>

<style scoped>
.pd-srhbx1-gis.right {
    left: -250px;
}

.zy-results-gis.right {
    left: -310px;
}
</style>
