<!-- @format -->

<template>
    <div class="styleTool">
        <ul>
            <li
                v-for="(item, index) of arrType"
                :key="index"
                @click="itemClick(item)"
                :class="{ on: item.value == style }"
            >
                {{ item.name }}
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    data() {
        return {
            arrType: [
                {
                    name: '样式0',
                    value: '0'
                },
                {
                    name: '样式1',
                    value: '1'
                },
                {
                    name: '样式2',
                    value: '2'
                },
                {
                    name: '样式3',
                    value: '3'
                },
                {
                    name: '样式4',
                    value: '4'
                },
                {
                    name: '样式5',
                    value: '5'
                },
                {
                    name: '样式6',
                    value: '6'
                }
            ],
            style: '1'
        };
    },

    methods: {
        itemClick(item) {
            this.style = item.value;

            this.$parent.$refs.childZDZ.test(this.style);
            this.$parent.$refs.childSGZ.test(this.style);
        }
    }
};
</script>

<style scoped>
.styleTool {
    position: fixed;
    top: 100px;
    right: 10px;
}

.styleTool ul {
    display: flex;
}

.styleTool ul li {
    flex: 1;
    border: 1px solid #0097a7;
    border-radius: 4px;
    font-size: 16px;
    color: #ddd;
    cursor: pointer;
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    text-align: center;
    padding: 0 10px;
    margin-left: 10px;
}

.styleTool ul li.on {
    background: #01cbe1;
    color: #0a343a;
}
</style>
