/** @format */

function safeValid(text, regex) {
    if (text) {
        return regex.test(text.toString().trim());
    } else {
        return false;
    }
}

/**
 * 检验字符串是否整数
 */
export const validInteger = num => {
    return safeValid(num, /^[0-9]+$/g);
};

/**
 * 检验字符串是否浮点数
 */
export const validFloat = num => {
    return safeValid(num, /^[0-9]*[\.]?[0-9]+$/g);
};

/**
 * 根据指定分割字符串，返回分割后的字符串数组，数组内包含正则表达式匹配的字符串
 */
export const split = (content, regex) => {
    let result = [];
    if (content) {
        let matcher = regex.exec(content);
        let subStart = 0;
        while (matcher !== null) {
            let matcherText = matcher[0];
            let lastIndex = regex.lastIndex;
            let interceptIndex = lastIndex - matcherText.length;
            //排除开头匹配放置空串
            if (subStart !== interceptIndex) {
                result.push(content.substring(subStart, interceptIndex));
            }
            result.push(matcherText);
            subStart = lastIndex;
            matcher = regex.exec(content);
        }
        //排除末尾匹配放置空串
        if (subStart !== content.length) {
            result.push(content.substring(subStart));
        }
    }
    return result;
};

export const extract = (content, regex) => {
    let result = [];
    if (content) {
        let matcher = regex.exec(content);
        while (matcher != null) {
            result.push(matcher[0]);
            matcher = regex.exec(content);
        }
    }
    return result;
};

const POLLUTANTS = [
    'PM₂.₅',
    'PM₂₅',
    'PM2_5',
    'PM₁₀',
    'SO₂',
    'O₃',
    'NO₂',
    'PM2.5',
    'PM10',
    'O3',
    'SO2',
    'NO2',
    'CO'
];

export const extractPollutants = content => {
    let regexPattern = '';
    POLLUTANTS.forEach(p => {
        regexPattern += `|${p}`;
    });
    regexPattern = regexPattern.substring(1);
    let pollutantRegex = new RegExp(regexPattern, 'gi');
    return extract(content, pollutantRegex);
};
