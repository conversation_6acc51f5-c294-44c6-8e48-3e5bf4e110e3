<!-- @format -->
<!-- echart 转移图 -->
<template>
    <div></div>
</template>

<script>
import PointUtil from '../utils/PointUtil';
export default {
    props: [],
    data() {
        return {
            map: null,
            pointUtil: null,
            layerID: 'echart 转移图'
        };
    },

    mounted() {
        this.initPage();
    },

    unmounted() {
        this.clear();
    },

    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.addLayer();
        },

        addLayer() {
            let params = {
                id: this.layerID,
                disableEvent: true,
                enableExtent: false
            };
            let data = [
                {
                    WFCZ: [
                        //处置单位
                        {
                            QYMC: '企业2',
                            WFCSL: 100,
                            JD: 120.66896714686828,
                            WD: 31.08553333070097
                        },
                        {
                            QYMC: '企业3',
                            WFCSL: 400,
                            JD: 120.73569105791262,
                            WD: 30.99996053683148
                        }
                    ],
                    QYMC: '企业1', //产生单位
                    JD: 120.52978933647688,
                    WD: 30.898566545175512
                }
            ];

            this.pointUtil.addTransportData(data, params);
        },

        clear() {
            this.pointUtil.removeTransport(this.layerID);
        },

        pointClickHandle() {}
    }
};
</script>

<style scoped></style>
