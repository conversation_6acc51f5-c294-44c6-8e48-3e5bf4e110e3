.sw0725-maparea1{position: relative;}

body, ul, dl, dd, h1, h2, h3, h4, h5, h6, p, form, input, textarea, select, button {
    margin: 0;
    padding: 0;
    font: 14px 'Microsoft YaHei', "宋体", Arial, Helvetica, sans-serif;
}

/* 去掉html 默认样式 */
input, button, textarea, select {
    /* -webkit-appearance: none; */
    -moz-appearance: none;
    appearance: none;
    box-sizing: border-box;
    border-radius: 0;
    padding: 0;
    margin: 0;
    border: none;
    outline: none;
}

ul, ol {
    padding-left: 0;
    list-style-type: none;
}

.gap {
    height: 10px;
    width: 100%;
    clear: both;
}

.flx1 {
    display: flex;
}

.flx1.ac {
    align-items: center;
}
.abs{
    position: absolute;
}

.gap15 {
    height: 15px;
    width: 100%;
}


img {
    border: 0 none;
}


.sw0725-dlbx1 {
    border: 1px solid #0095a2;
    border-radius: 5px;
    overflow: hidden;
}

.sw0725-dlbx1 dt {
    height: 40px;
    background: #008795;
    padding: 0 14px;
}

.sw0725-dlbx1 dd {
    background: rgba(0,58,62,.8);
    padding: 10px 0 10px 14px;
    margin: 0;
    cursor: pointer;
}


.sw0726-aqilst {
    display: flex;
    flex-wrap: wrap;
}

.sw0725-dlbx1 dd.on, .sw0725-dlbx1 p.on {
    background: #008795;
}


.sw0726-aqilst p {
    width: 34%;
    height: 35px;
    line-height: 35px;
    padding: 2px 15px;
}

.sw0726-aqilst p {
    width: 50%;
    font-size: 14px;
    color: #fff;
}





.sw0721-check1{display: flex; align-items: center;}
.sw0721-check1 input[type="checkbox"]{background: url(./images/sw0721_checkic.png) no-repeat; width: 18px; height: 18px;}
.sw0721-check1 input[type="checkbox"]:checked{background-image: url(./images/sw0721_checkicon.png)}

.sw0721-check1 span {
    font-size: 16px;
    color: #fff;
    padding-left: 10px;
}

.sw0726-aqilst p img {
    margin-right: 8px;
}

.sw0726-pdl15 {
    padding-left: 15px;
}

.sw0726-ultbs1 {
    display: flex;
    align-items: center;
}

.sw0726-ultbs1 li {
    flex: 1;
    border: 1px solid #0097a7;
    border-radius: 4px;
    font-size: 16px;
    color: #ddd;
    cursor: pointer;
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    text-align: center;
}

.sw0726-ultbs1 li.on {
    background: #01cbe1;
    color: #0a343a;
}

.sw0726-ultbs1 li + li {
    margin-left: 15px;
}

.sw0725-txt1 {
    font-size: 16px;
    color: #fff;
}

.sw0725-rarea1 {
    flex: 1;
    padding-left: 16px;
    background-color: #0a343a;
}

.sw0726-btn1 {
    border-radius: 4px;
    font-size: 16px;
    background: #01cbe1;
    color: #0a343a;
    cursor: pointer;
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    text-align: center;
}

.sw0725-inptxt1 {
    border: 1px solid #05515d;
    border-radius: 4px;
    font-size: 16px;
    color: #fff;
    text-indent: 10px;
    line-height: 36px;
    background: transparent;
}

.sw0725-grd2 .row {
    margin: 0 -30px;
}


.row:before, .row:after {
    content: '';
    display: table;
    clear: both;
}

.sw0725-grd2 .row .col {
    padding: 0 30px;
}


.col-4 {
    width: 33.333333%;
}

.col {
    min-height: 1px;
    float: left;
    position: relative;
    padding-left: 5px;
    padding-right: 5px;
    box-sizing: border-box;
}

.sw0725-ultbs3{display: flex; align-items: center; justify-content: space-around;}
.sw0725-ultbs3 li{ cursor: pointer; font-size: 16px; color: #fff; padding: 0 10px 14px; position: relative; text-align: center;}
.sw0725-ultbs3 li.on{font-weight: bold; color: #00d8ee;}
.sw0725-ultbs3 li.on:after{content: ''; position: absolute; left: 0; right: 0; bottom: 0; height: 4px; background: #00d8ee; border-radius: 300px;}


.sw0725-ullst1 {
    overflow-y: auto;
    overflow-x: hidden;
    height: calc(100% - 170px);
} 


.sw0725-ullst1 > li{background: rgba(0,108,120,.2); padding: 18px;}
.sw0725-ullst1 > li h1{display: flex; align-items: center;}
.sw0725-ullst1 > li h1 strong{font-size: 18px; color: #00eaff;}
.sw0725-ullst1 > li h1 em{border: 1px solid #00d8ee; border-radius: 300px; line-height: 23px; font-size: 16px; color: #fff; padding: 0 15px; margin: 0 15px 0 10px;}
.sw0725-ullst1 > li h1 em.type{background: #e85353; border-color: #e85353; margin: 0; line-height: 30px; display: inline-block;}
.sw0725-ullst1 > li h1 span{font-size: 16px; color: #fff;}
.sw0725-ullst1 > li h1 small{font-size: 14px; color: #fff; margin-left: 37px; position: relative;}
.sw0725-ullst1 > li h1 small:before{content: ''; position: absolute; left: -18px; top: 50%; width: 2px; height: 17px; background: #045a65; margin-top: -8.5px;}
.sw0725-ullst1 > li h1 i.grn{background-color: #00b657;}
.sw0725-ullst1 > li h1 i{width: 60px; height: 24px; line-height: 24px; background: #51a5fd; font-size: 16px; color: #fff; text-align: center;}
.sw0725-ullst1 > li + li{margin-top: 15px;}



.yy-mapalert1 {
    width: 585px;
    height: 404px;
    background-color: rgb(1, 48, 69, 0.7);
    border: solid 1px #02d9fd;
    border-radius: 20px;
    position: absolute;
    left: 0;
    bottom: 100%;
    /* margin-bottom: 20px; */
    padding: 0 20px 15px;
    box-sizing: border-box;
    box-shadow: 0 0 20px 7px rgba(0, 184, 251, 0.4) inset;
}
.yy-mapalert1 .hdbox {
    height: 50px;
    line-height: 50px;
    /*   background: url(../images/yy-alinebg.png) no-repeat left bottom; */
}
.yy-mapalert1 .t1 {
    font-size: 18px;
    color: #fff;
    font-weight: bold;
}
.yy-mapalert1 .time1 {
    font-size: 16px;
    color: #fff;
    padding-left: 24px;
    margin-top: 5px;
    background: url(./images/yy0816-time2.png) no-repeat 0 center;
}
.yy0816-acls {
    cursor: pointer;
    margin-right: 5px;
}
.yy-mapalert1 .mbkind {
    /* margin-top: 20px; */

    margin-top: 10px;
}
.yy-mapalert1 .mbkind .f1 {
    font-size: 20px;
    color: #fff;
    width: 94px;
    height: 54px;
    line-height: 54px;
    text-align: center;
    background-color: #73bb31;
    border-radius: 8px;
    margin-right: 20px;
}
.yy-mapalert1 .mbkind .rfont .p1 {
    font-size: 16px;
    color: #fff;
}
.yy-mapalert1 .mbkind .rfont .p1 + .p1 {
    margin-top: 10px;
}


.sw0630-dlbx2 {
    height: 135px;
    display: flex;
}

.sw0630-dlbx2 dt {
    width: 230px;
    background: #f87c12;
    padding: 20px 0 20px 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
}

.sw0630-dlbx2 dt h1 {
    font-size: 24px;
    color: #fff;
    font-family: "DIN-Bold";
}

.sw0630-dlbx2 dt h2 {
    display: flex;
    align-items: center;
}

.sw0630-dlbx2 dt h2 i {
    font-size: 40px;
    font-family: "DIN-Bold";
    color: #fff;
}

.sw0630-dlbx2 dt h2 p {
    flex: 1;
    padding-left: 10px;
}

.sw0630-dlbx2 dt h2 p span {
    font-size: 18px;
    color: #fff;
    display: block;
}

.sw0630-dlbx2 dd {
    flex: 1;
}


.sw0726-dlaqi{display: flex; flex-direction: column; align-items: center; justify-content: center; 
    background: url(./images/sw0726_bg1.png) no-repeat center bottom; padding-bottom: 15px;}
.sw0726-dlaqi dt{font-size: 16px; color: #fff;}
.sw0726-dlaqi dd{font-size: 38px; color: #d8bc37; font-family: "DIN-Medium";}

.sw0725-datatxt1{display: flex; align-items: center; justify-content: space-between;}
.sw0725-datatxt1 p.p1{font-size: 18px; color: #fff;}
.sw0725-datatxt1 p.p1 sub{font-size: 14px; vertical-align: baseline;}
.sw0725-datatxt1 p.p2{font-size: 20px; color: #fff; font-family: "DIN-Medium";}

.sw0630-dlbx2 dd ul {
    display: flex;
    flex-wrap: wrap;
    margin-top: -7px;
}

.sw0630-dlbx2 dd ul li {
    width: 92px;
    height: 54px;
    border: 1px solid #24bd5d;
    text-align: center;
    margin: 7px 0 0 7px;
}

.sw0630-dlbx2 dd ul li h1 {
    line-height: 28px;
    font-size: 18px;
    font-family: "DIN-Bold";
    background: #24bd5d;
    color: #fff;
}

.sw0630-dlbx2 dd ul li h1 sub {
    vertical-align: baseline;
    font-size: 14px;
}

.sw0630-dlbx2 dd ul li p {
    font-size: 18px;
    color: #24bd5d;
    font-family: "DIN-Bold";
    line-height: 26px;
}


.yygkbtn {
    display: inline-block;
    /* width: 61px; */
    padding: 0 15px;
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    color: #02d9fd;
    font-weight: bold;
    text-align: center;
    background: url(./images/yy0815-gkbg.png) no-repeat center center;
    background-size: 100% 100%;
    margin-left: 15px;
}


.flx1.jb {
    justify-content: space-between;
}


.flx1.ac {
    align-items: center;
}

.flx1 {
    display: flex;
}



.mapbox-tooltip-tip-air .mapboxgl-popup-tip {
    pointer-events: none;
    /* background: rgba(0, 0, 0, 0) !important; */
}

.mapbox-tooltip-tip-air .mapboxgl-popup-content {
    background: rgba(0, 0, 0, 0);
    padding: 0;

    /* pointer-events: none; */
}
.mapbox-tooltip-tip-air {
    z-index: 999;
}









