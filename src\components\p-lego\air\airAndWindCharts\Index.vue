<!-- @format -->

/* eslint-disable no-var */
<!-- @format -->

<template>
    <div>
        <div
            :style="{ height: option.airHeight, width: option.airWidth }"
            ref="airChart"
        ></div>
        <div
            :style="{ height: option.windHeight, width: option.windWidth }"
            ref="windChart"
        ></div>
    </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
    name: 'airAndWindCharts',
    data() {
        return {
            data: {
                airLegendData: []
            },
            yjsj: '',
            theme: 'light'
        };
    },
    props: {
        chartsData: Object,
        option: {
            // 配置宽高
            type: Object,
            default() {
                return {
                    airHeight: '240px',
                    airWidth: '100%',
                    windHeight: '200px',
                    windWidth: '100%'
                };
            }
        }
    },
    watch: {
        chartsData: 'renderData'
    },
    mounted() {
        this.theme = window.localStorage.getItem('themeType') || 'light';
        this.renderData();
    },
    methods: {
        renderData() {
            this.data = this.chartsData;
            this.getData();
        },
        getData() {
            //处理下标开始
            let self = this;
            let airLegendData = [];
            console.log('空气', this.data);
            if (this.data.hasOwnProperty('airLegendData')) {
                this.data.airLegendData.forEach(function (item) {
                    airLegendData.push(self.replacePltName(item));
                });
                this.data.airLegendData = airLegendData;

                //处理下标结束

                this.renderAirChart(this.data, 'air', 'airChart');
                this.renderWindChart(this.data);
                // 几个echats关联
                this.$echarts.connect([this.airChart, this.windChart]);
            }
        },
        // 渲染空气图
        renderAirChart: function (data, type, id) {
            let textColor = this.theme === 'light' ? '#333' : '#fff';
            let self = this;
            let myChart;
            if (type == 'air') {
                myChart = this.$echarts.init(this.$refs.airChart);
            }
            let seriesData = [];
            let legendData = [];
            let color = [];
            //预测数据时间范围 12/15 06   12/15 23
            let ycStart = dayjs(this.yjsj).format('MM/DD HH');
            let ycEnd = dayjs(this.yjsj).format('MM/DD') + ' 23';
            if (type === 'air') {
                color = [
                    '#138BD2',
                    '#F64975',
                    '#18DB60',
                    '#E1E02E',
                    '#F19F15',
                    '#9456F5',
                    '#1AD3D2'
                ];
                // 统一控制返回的提示
                // eslint-disable-next-line no-var
                var formatter = function (params) {
                    let index = params[0].dataIndex;
                    let returnHtml = params[0].name + '<br />';
                    params.forEach(function (item) {
                        if (item.seriesName == 'CO') {
                            returnHtml +=
                                item.marker +
                                item.seriesName +
                                '：' +
                                item.value +
                                'mg/m³<br />';
                        } else if (item.seriesName == 'AQI') {
                            returnHtml +=
                                item.marker +
                                item.seriesName +
                                '：' +
                                item.value +
                                '<br />';
                        } else {
                            returnHtml +=
                                item.marker +
                                item.seriesName +
                                '：' +
                                item.value +
                                'μg/m³<br />';
                        }
                    });

                    return (
                        returnHtml +
                        [
                            '风速：' + data.windData[index] + 'm/s',
                            '风向：' +
                                self.selectWindName(data.windArrowData[index]),
                            '温度：' + data.temperatureData[index] + '℃',
                            '湿度：' + data.pressureData[index]
                        ].join('<br>')
                    );
                };
                legendData = data.airLegendData;
                // eslint-disable-next-line no-var
                var yAxis = [
                    {
                        splitNumber: 3,
                        name: 'μg/m³',
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                fontSize: 14,
                                color: textColor
                            }
                        },
                        splitLine: {
                            //网格线
                            show: false
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: textColor
                            }
                        }
                    },
                    {
                        splitNumber: 3,
                        name: 'CO（ mg/m³）',
                        type: 'value',
                        axisTick: {
                            //刻度值线
                            show: false
                        },
                        splitLine: {
                            //网格线
                            show: false
                        },
                        axisLabel: {
                            textStyle: {
                                fontSize: 14,
                                color: textColor
                            }
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: textColor
                            }
                        }
                    }
                ];

                data.airSeriesData.forEach(function (item, index) {
                    if (data.airLegendData[index] == 'CO') {
                        seriesData.push({
                            yAxisIndex: '1',
                            name: data.airLegendData[index],
                            smooth: true, //平滑
                            symbol: 'none',
                            type: 'line',
                            data: item
                        });
                    } else {
                        seriesData.push({
                            yAxisIndex: '0',
                            name: data.airLegendData[index],
                            smooth: true, //平滑
                            symbol: 'none',
                            type: 'line',
                            data: item
                        });
                    }
                });
                var axisLabelType = false;
                this.airChart = myChart;
            }
            let preditDate = data.preditDate;
            seriesData.push({
                name: '平行于y轴的趋势线',
                type: 'line',
                color: ['black'],
                markLine: {
                    data: [
                        [
                            {
                                symbol: 'line',
                                coord: [preditDate, 0]
                            },
                            {
                                symbol: 'line',
                                coord: [preditDate, 1]
                            }
                        ]
                    ]
                }
            });

            let option = {
                color: color,
                tooltip: {
                    // position: 'bottom',
                    trigger: 'axis',
                    position: function (point, params, dom, rect, size) {
                        // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
                        // 提示框位置
                        let x = 0; // x坐标位置
                        let y = 0; // y坐标位置

                        // 当前鼠标位置
                        let pointX = point[0];
                        let pointY = point[1];

                        // 外层div大小
                        // var viewWidth = size.viewSize[0];
                        // var viewHeight = size.viewSize[1];

                        // 提示框大小
                        let boxWidth = size.contentSize[0];
                        let boxHeight = size.contentSize[1];

                        // boxWidth > pointX 说明鼠标左边放不下提示框
                        if (boxWidth > pointX) {
                            x = 5;
                        } else {
                            // 左边放的下
                            x = pointX - boxWidth;
                        }

                        // boxHeight > pointY 说明鼠标上边放不下提示框
                        if (boxHeight > pointY) {
                            y = 5;
                        } else {
                            // 上边放得下
                            y = pointY - boxHeight;
                        }

                        return [x, y];
                    },
                    formatter: formatter
                },

                legend: {
                    data: legendData,
                    width: 440,
                    left: 'center',
                    textStyle: {
                        color: textColor,
                        fontSize: 12
                    }
                },
                grid: {
                    left: '70',
                    top: '45%',
                    bottom: '23'
                },
                dataZoom: {
                    textStyle: {
                        color: textColor //滚动条两边字体样式
                    },
                    show: true, //axisLabelType,
                    bottom: '0',
                    height: '20',
                    // top: '0',
                    start: 70,
                    end: 100
                },
                xAxis: {
                    type: 'category',
                    data: data.xAxisData,
                    axisTick: {
                        show: false,
                        color: textColor
                    },
                    axisLabel: {
                        show: axisLabelType,
                        textStyle: {
                            fontSize: 14,
                            color: textColor
                        }
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: textColor
                        }
                    }
                },
                yAxis: yAxis,
                series: seriesData
            };
            myChart.clear();
            myChart.setOption(option);
        },
        selectWindName: function (name) {
            let obj = {
                W: '西风',
                WSW: '西南偏西',
                SW: '西南风',
                SSW: '西南偏南',
                S: '南风',
                SSE: '东南偏南',
                SE: '东南风',
                ESE: '东南偏东',
                E: '东风',
                ENE: '东北偏东',
                NE: '东北风',
                NNE: '东北偏北',
                N: '北风',
                NNW: '西北偏西',
                NW: '西北风',
                WNW: '西北偏西'
            };
            return obj[name] || '';
        },
        renderWindChart: function (data) {
            let textColor = this.theme === 'light' ? '#333' : '#fff';
            let self = this;
            let myChart = this.$echarts.init(this.$refs.windChart);
            this.windChart = myChart;
            let xAxisData = data.xAxisData;
            let seriesData = data.windData;
            let arrowArr = data.windArrowData;
            let preditDate = data.preditDate;
            let beforeEnsure = '09-04';
            let afterEnsure = '09-07';

            //总长度
            let num = xAxisData.length - 1;
            let index = 0;
            let actualPercent = '';
            let preditPercent = '';

            let firstNum = 0;
            let firstIndex = 0;
            let secondNum = 0;

            let arrowDataArr = [];
            xAxisData.forEach(function (item, i) {
                let arr = [];
                arr.push(xAxisData[i]);
                arr.push(seriesData[i] ? seriesData[i] : 0);
                arr.push(arrowArr[i]);
                arrowDataArr.push(arr);

                if (item == preditDate) {
                    index = i;
                    //实测比例
                    actualPercent = (i / num) * 90;
                    preditPercent = ((num - i) / num) * 90;
                }

                if (item == beforeEnsure) {
                    firstIndex = i;
                    firstNum = (i / num) * 90;
                }

                if (item == afterEnsure) {
                    secondNum = ((i - firstIndex) / num) * 90;
                }
            });

            //箭头处理函数
            let directionMap = {};
            this.$echarts.util.each(
                [
                    'W',
                    'WSW',
                    'SW',
                    'SSW',
                    'S',
                    'SSE',
                    'SE',
                    'ESE',
                    'E',
                    'ENE',
                    'NE',
                    'NNE',
                    'N',
                    'NNW',
                    'NW',
                    'WNW'
                ],
                function (name, index) {
                    directionMap[name] = (Math.PI / 8) * index;
                }
            );

            let dims = {
                time: 0,
                windSpeed: 1,
                R: 2,
                waveHeight: 3,
                weatherIcon: 2,
                minTemp: 3,
                maxTemp: 4
            };
            let arrowSize = 18;
            let weatherIconSize = 45;

            function renderArrow(param, api) {
                let point = api.coord([
                    api.value(dims.time),
                    api.value(dims.windSpeed)
                ]);
                return {
                    type: 'path',
                    shape: {
                        pathData: api.value(dims.R)
                            ? 'M31 16l-15-15v9h-26v14h26v9z'
                            : '',
                        x: -arrowSize / 2,
                        y: -arrowSize / 2,
                        width: arrowSize,
                        height: arrowSize
                    },
                    rotation: directionMap[api.value(dims.R)],
                    position: point,
                    style: api.style({
                        stroke: '#555',
                        lineWidth: 1
                    })
                };
            }

            let option = {
                color: ['#259e72', '#178fcd'],
                tooltip: {
                    trigger: 'axis',
                    show: false,
                    formatter: function (params) {
                        let index = params[0].dataIndex;
                        // eslint-disable-next-line no-unreachable
                        return [
                            '风速：' + params[1].value[dims.windSpeed] + 'm/s',
                            '风向：' +
                                self.selectWindName(params[1].value[dims.R]),
                            '温度：' + data.temperatureData[index] + '℃',
                            '湿度：' + data.pressureData[index] + '<br>'
                        ].join('<br>');
                    }
                },
                dataZoom: {
                    show: false,
                    bottom: '100',
                    height: '20',
                    start: 70,
                    end: 100,
                    xAxisIndex: [0, 1, 2]
                },
                xAxis: [
                    {
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                            textStyle: { color: textColor }
                        },
                        splitLine: {
                            //网格线
                            show: false
                        },
                        axisLabel: {
                            show: false,
                            textStyle: {
                                fontSize: 14,
                                color: textColor
                            }
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: textColor
                            }
                        },
                        data: xAxisData
                    },
                    {
                        name: '\n\n\n温度 ℃',
                        type: 'category',
                        position: 'bottom',
                        offset: 8,
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: 'white',
                                fontSize: 14,
                                backgroundColor: '#3b88e4',
                                lineHeight: 13,
                                padding: [3, 7]
                            },
                            interval: 0
                        },
                        nameTextStyle: {
                            color: textColor,
                            padding: [0, 0, -36]
                        },
                        nameLocation: 'start',
                        data: data.temperatureData
                    },
                    {
                        name: '\n\n\n湿度',
                        type: 'category',
                        position: 'bottom',
                        offset: 40,
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: 'white',
                                fontSize: 14,
                                backgroundColor: '#3b88e4',
                                lineHeight: 13,
                                padding: [3, 7]
                            },
                            interval: 0
                        },
                        nameTextStyle: {
                            color: textColor,
                            padding: [0, 0, -36]
                        },
                        nameLocation: 'start',
                        data: data.pressureData
                    }
                ],
                grid: {
                    top: '50',
                    bottom: '70',
                    left: '70'
                },
                yAxis: {
                    splitNumber: 3,
                    name: '风速：m/s',
                    splitLine: {
                        //网格线
                        show: false
                    },
                    axisLabel: {
                        textStyle: {
                            fontSize: 14,
                            color: textColor
                        }
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: textColor
                        }
                    }
                },
                visualMap: [
                    {
                        show: false,
                        dimension: 0,
                        seriesIndex: 0,
                        pieces: [
                            {
                                lte: index,
                                color: '#259e72'
                            },
                            {
                                gt: index,
                                lte: seriesData.length + 1,
                                color: '#51d9a7'
                            }
                        ]
                    }
                ],
                series: [
                    {
                        name: '风速',
                        type: 'line',
                        smooth: true,
                        yAxisIndex: '0',
                        xAxisIndex: '0',
                        data: seriesData,
                        markLine: {
                            label: {
                                color: '#666',
                                position: 'middle',
                                textStyle: {
                                    padding: -50
                                }
                            },
                            lineStyle: {
                                color: '#666'
                            }
                        }
                    },
                    {
                        name: '风向',
                        type: 'custom',
                        renderItem: renderArrow,
                        encode: {
                            x: 0,
                            y: 1
                        },
                        data: arrowDataArr,
                        z: 10
                    },
                    {
                        name: '平行于y轴的趋势线',
                        type: 'line',
                        color: ['black'],
                        markLine: {
                            data: [
                                [
                                    {
                                        symbol: 'line',
                                        coord: [preditDate, 0]
                                    },
                                    {
                                        symbol: 'line',
                                        coord: [preditDate, 1]
                                    }
                                ]
                            ]
                        }
                    }
                ]
            };
            myChart.clear();
            myChart.setOption(option);
        },
        // 下标处理开始
        replacePltName(value) {
            value = value || '';
            let labelObj = {
                'PM2.5': 'PM₂.₅',
                PM25: 'PM₂.₅',
                PM10: 'PM₁₀',
                O3: 'O₃',
                NO2: 'NO₂',
                SO2: 'SO₂'
            };
            return value.replace(/[A-Z]+[0-9]+\.*[0-9]*/g, function () {
                return labelObj[arguments[0]] || arguments[0];
            });
        }
    }
};
</script>

<style scoped lang="scss">
/* table */
.ec-wrap {
    text-align: center;
    padding-top: 10px;
}
.til1 {
    float: left;
    background-image: none;
    margin-left: 5px;
    color: #333333;
    line-height: normal;
    padding-left: 10px;
    height: 20px;
    font-size: 20px;
    margin-top: 8px;
}
table {
    color: white;
    width: 510px;
    margin: auto;
    /* background-color: #0c4e6d; */
}
.grep {
    color: #6c859d;
}
.head td span {
    color: #333333;
    font-weight: 550;
}
.head td {
    height: 50px;
}
.ctd {
    /* color: #323232; */
    font-weight: 550;
    width: 50px;
    white-space: nowrap;
}
.ctable .ctd {
    height: 50px;
}
.ctable tr:nth-of-type(odd) {
    background-color: #ebebeb;
}
tr {
    background: #ffff;
}
tr td {
    padding: 5px;
    height: 50px;
    color: #333333;
}
tr td span {
    display: inline-block;
    width: 34px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    font-size: 16px;
}
/* table :end */
.pd-modbd {
    padding: 0px;
}
.tool-tip11 {
    margin-left: 10px;
}
.tool-tip11 li {
    float: left;
    color: white;
    padding-left: 6px;
}
.tool-tip11 li span {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 3px;
    background: orange;
    position: relative;
    top: 2px;
}
.tab-right {
    display: flex;
    // width: 180px;
    font-size: 20px;
    padding-top: 8px;
    justify-content: space-between;
    color: #666;
    float: right;
    margin-right: 30px;
    cursor: pointer;
    li {
        border: 1px solid #999;
        border-left: 0;
        padding: 4px 8px;
        font-size: 18px;
        &:first-of-type {
            border-left: 1px solid #999;
        }
    }
    .li {
        color: #fff;
        background: dodgerblue;
        border: 1px solid dodgerblue;
        &:first-of-type {
            border-left: 1px solid dodgerblue;
        }
    }
}
</style>
