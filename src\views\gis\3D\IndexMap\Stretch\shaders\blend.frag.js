/** @format */

export default `#ifdef GL_FRAGMENT_PRECISION_HIGH
	precision highp float;
#else
	precision mediump float;
#endif
uniform sampler2D u_Texture;
uniform sampler2D u_TextureOrigin;
uniform vec4 u_Color;
varying vec2 v_UV;
void main(){
	vec4 color = texture2D(u_Texture, v_UV);
	vec4 colorOrigin = texture2D(u_TextureOrigin,v_UV);
	
	if(colorOrigin.a > 0.0){
		color.a = 0.0;
	}
    if(color.a>0.8){
        color = u_Color;//vec4(u_Color.rgb, 0.8);
    }
	gl_FragColor = color;
}`;
