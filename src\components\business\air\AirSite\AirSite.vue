<!-- @format -->

<template>
    <div>
        <div class="mask" style="display: block"></div>
        <div class="pd-dlgx dlg1">
            <div class="pd-dlgxhd">
                <span>空气站点</span>
                <i class="dlgcls" @click="$emit('close')"></i>
            </div>
            <div class="pd-dlgxbd">
                <div class="pd-bdltx">
                    <div class="gap h14"></div>
                    <div class="pd-tithdx">
                        <span>基本信息</span>
                    </div>

                    <div class="gap"></div>
                    <table  class="pd-table2ax">
                        <colgroup>
                            <col width="45%" />
                        </colgroup>
                        <tr v-for="item in baseDataOpt" :key="item.value">
                            <td class="td-hd">{{ item.label }}：</td>
                            <td>{{ baseData[item.value] || '-' }}</td>
                        </tr>
                    </table>

                    <div class="gap h14"></div>
                </div>
                <div class="pd-bdrtx">
                    <div class="gap h14"></div>

                    <div class="gap h14"></div>

                    <div class="pd-tithdx">
                        <span>实时空气质量</span>
                        <i>2021-11-22 11:00:00</i>
                    </div>
                    <air-quality-now
                        :data="airQualityNowdata"
                        style="width: 100%"
                    ></air-quality-now>
                    <div class="gap clear"></div>
                    <div class="pd-tithdx">
                        <span>近{{ timeType }}小时变化趋势</span>
                        <ul class="pd-ultbs3s">
                            <li
                                :class="{ on: item.value === timeType }"
                                v-for="(item, index) in timeTypeList"
                                :key="index"
                                @click="timeTypeChange(item.value)"
                            >
                                {{ item.name }}
                            </li>
                        </ul>
                    </div>
                    <div class="gap h14"></div>
                    <div class="gap clear"></div>

                    <air-quality-change
                        :data="airQualityChangeData"
                        style="width: 100%; height: 400px"
                    ></air-quality-change>
                    <div class="gap h14"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import airQualityNow from './airQualityNow/airQualityNow.vue';
import airRealTime from './airRealTime/Index.vue';
import airQualityChange from './airQualityChange/Index.vue';

export default {
    props: {},
    components: {
        airQualityNow,
        airRealTime,
        airQualityChange
    },
    data() {
        return {
            airQualityNowdata: {
                AQI: 53,
                CO: 0.5,
                JCSJ: '2021-10-26 13',
                NO2: 23,
                O3: 139,
                PM10: 56,
                PM25: 30,
                SO2: 5,
                firstPollution: 'PM10', //首要污染物
                index: 3.31
            },
            airQualityChangeData: [
                {
                    AQI: 52,
                    CO: 0.6,
                    JCSJ: '2021-11-22 08:00:00',
                    NO2: 32,
                    O3: 32,
                    PM10: 53,
                    PM25: 11,
                    SO2: 6
                },
                {
                    AQI: 42,
                    CO: 0.7,
                    JCSJ: '2021-11-22 09:00:00', // 日期
                    NO2: 36,
                    O3: 25,
                    PM10: 42,
                    PM25: 22,
                    SO2: 12
                },
                {
                    AQI: 42,
                    CO: 0.7,
                    JCSJ: '2021-11-22 10:00:00', // 日期
                    NO2: 36,
                    O3: 25,
                    PM10: 42,
                    PM25: 22,
                    SO2: 12
                },
                {
                    AQI: 52,
                    CO: 0.6,
                    JCSJ: '2021-11-22 11:00:00',
                    NO2: 32,
                    O3: 32,
                    PM10: 53,
                    PM25: 11,
                    SO2: 6
                },
                {
                    AQI: 42,
                    CO: 0.7,
                    JCSJ: '2021-11-22 12:00:00', // 日期
                    NO2: 36,
                    O3: 25,
                    PM10: 42,
                    PM25: 22,
                    SO2: 12
                }
            ],
            baseData: {
                ZDMC: 'xxx',
                ZDLX: 'xxx'
            }, //站点数据
            baseDataOpt: [
                { label: '站点名称', value: 'ZDMC' },
                { label: '站点类型', value: 'ZDLX' },
                { label: '所属行政区', value: 'XZQH' },
                { label: '经度', value: 'JD' },
                { label: '纬度', value: 'WD' }
            ], //站点展示配置列表
            timeTypeList: [
                { name: '近24小时', value: '24' },
                { name: '近48小时', value: '48' },
                { name: '近72小时', value: '72' }
            ],
            timeType: '24'
        };
    },
    computed: {},
    watch: {},
    mounted() {},
    unmounted() {},
    methods: {
        timeTypeChange(value) {
            this.timeType = value;
        }
    }
};
</script>
<style lang="less">
.darkTheme {
    --txt-color: #fff;
    --bg-color-right: #062a3d;
    --bg-color-left: #062a3d;
    --bg-hd-color: #075393;
    --table-hd-color: #0a3c6e;
    --table-border-color: #004d8c;
}
.lightTheme {
    --txt-color: #333;
    --bg-color-right: #fff;
    --bg-color-left: #eff7ff;
    --bg-hd-color: #3f88d4;
    --table-hd-color: #f5f6fa;
    --table-border-color: #eeeeee;
}
</style>
<style scoped lang="less">
table {
    border-collapse: collapse;
}
.pd-dlgx.dlg1 {
    width: 980px;
    height: 710px;
}
.pd-dlgx {
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 1000;
    background: var(--bg-color-left);
    border-radius: 8px;
    overflow: hidden;
    transform: translate(-50%, -50%);
}
.pd-dlgxhd {
    height: 50px;
    background: var(--bg-hd-color);
}
.pd-dlgxhd span {
    float: left;
    font-size: 20px;
    color: #fff;
    line-height: 50px;
    padding-left: 15px;
    font-weight: bold;
}
.pd-dlgxhd .dlgcls {
    float: right;
    background: url(~_as/images/close.png) no-repeat center;
    width: 48px;
    height: 50px;
    cursor: pointer;
}
.pd-dlgxbd {
    position: absolute;
    left: 0;
    right: 0;
    top: 50px;
    bottom: 0;
}
.pd-bdltx {
    width: 322px;
    padding: 0 15px;
    box-sizing: border-box;
    float: left;
    height: 100%;
    border-right: 1px solid var(--bg-color-left);
    overflow: auto;
}
.pd-bdrtx {
    overflow: hidden;
    padding: 0 15px;
    height: 100%;
    background-color: var(--bg-color-right);
}
.pd-tithdx {
    overflow: hidden;
    line-height: 30px;
}

.pd-tithdx span {
    font-size: 20px;
    color: var(--txt-colorr);
    position: relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAARCAYAAAAougcOAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDE1QjRCMkM2MTZGMTFFQzlEQjQ5REQ2Q0YwNzk3RTEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDE1QjRCMkQ2MTZGMTFFQzlEQjQ5REQ2Q0YwNzk3RTEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpEMTVCNEIyQTYxNkYxMUVDOURCNDlERDZDRjA3OTdFMSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpEMTVCNEIyQjYxNkYxMUVDOURCNDlERDZDRjA3OTdFMSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PpSRqf4AAAIPSURBVHjalJVPaxNBHIbfnf2TZLONjdgYSJs0QU/1IoKCFEzBi731lpvST9Ae2w/QHttjL4XWU0HwG0gqqEE96EE8ysYg+OegIO0GscZ3ppuk3cwm6Q+enczM7jw7M7/ZGNjsQBcvasD8AUr8uU+WWf+kue0qWSFb5DtiQsDkVcP8EwpM1Mk9WbJe0Qg2yQ2yQXIXlXQF5bBeVKKnPVE+FExFhDm9xOb1PCVSJ+VIe1G273zEnfDNpyJj5eJEAhavfUrkkJQj7YoZD8W5LJ4Ff9Ve6UIrMiZ3exsvHzwks9qnHeDhNWCCs7INBNcvoZE0cRwjk0mw1k0Go/RYSeTA9TjBZQpq7PHsfpstEFQmxhMZcwedoYIsB14qAmlrsM/ijMoeGokRIstzsBcnkPGgAGQcfR/XIPUlwM2Kh5dD9mhVeAk8Ik0CHW9/AX/+MYuNQRyB9rSLd4gPOZNt4TrwSZU0CaKcCJ5+KeJrm6KPY6I94+IVRaP25JuVPl0Kn1Tj9kamxpvfwN1JICnULIK8g4Ytxssuy+2vtxQtDEuC90fA7QyOCgm8ZhqPJVCHMcXsOYNPFsISUTomWj9PcJ+Cz+MK1EyS9sCN3aWLHsyWbL+VVl9jPzzZV0YJ1EwSzH8NTVIlflhvyfpSpve5/xoO+GOUQEmYJdCx6KLJskqey5L16P+JFK2TD8MEMv4LMABFpYel0nULCQAAAABJRU5ErkJggg==)
        no-repeat left center;
    padding-left: 30px;
    float: left;
    font-weight: bold;
}

.pd-tithdx i {
    float: right;
    font-size: 16px;
    color: var(--txt-colorr);
}
.pd-table2ax {
    width: 100%;
    border: 1px solid var(--table-border-color);
}

.pd-table2ax tr td {
    font-size: 16px;
    color: var(--txt-colorr);
    height: 44px;
    padding: 0 10px 0 15px;
    border-right: 1px solid var(--table-border-color);
    border-bottom: 1px solid var(--table-border-color);
}

.pd-table2ax tr td.td-hd {
    color: var(--txt-color);
    text-align: right;
    background: var(--table-hd-color);
}

.pd-ultbs3s {
    position: absolute;
    right: 10px;
}
.pd-ultbs3s li.on {
    color: #1ae1ff;
}
.pd-ultbs3s li {
    float: left;
    font-size: 16px;
    color: var(--txt-colorr);
    position: relative;
    cursor: pointer;
}
.pd-ultbs3s li + li {
    padding-left: 18px;
}
.pd-ultbs3s li + li:before {
    content: '';
    position: absolute;
    left: 9px;
    top: 50%;
    width: 1px;
    height: 15px;
    margin-top: -7.5px;
    background: #fff;
}
</style>
