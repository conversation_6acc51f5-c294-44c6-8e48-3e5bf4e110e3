<!-- @format -->

<template>
    <el-dialog title="新增" v-model="dialogFormVisible">
        <div>
            <el-form
                :model="formData"
                ref="formData"
                :rules="rules"
                label-position="left"
                label-width="110px"
                size="medium"
                @submit.prevent
                class="demo-ruleForm"
            >
                <el-form-item
                    label="事件名称"
                    prop="eventName"
                    class="required label-right-align"
                >
                    <el-input
                        v-model="formData.eventName"
                        type="text"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="发生时间"
                    prop="date"
                    class="required label-right-align"
                >
                    <el-date-picker
                        v-model="formData.date"
                        type="datetime"
                        class="full-width-input"
                        clearable
                    ></el-date-picker>
                </el-form-item>
                <el-form-item
                    label="接警时间"
                    prop="warmingDate"
                    class="required label-right-align"
                >
                    <el-date-picker
                        v-model="formData.warmingDate"
                        type="datetime"
                        class="full-width-input"
                        clearable
                    ></el-date-picker>
                </el-form-item>
                <el-form-item
                    label="事件类型名称"
                    prop="eventTypeName"
                    class="label-right-align"
                >
                    <el-input
                        v-model="formData.eventTypeName"
                        type="text"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="事件地址"
                    prop="address"
                    class="label-right-align"
                >
                    <el-input
                        v-model="formData.address"
                        type="text"
                        clearable
                    ></el-input>
                </el-form-item>

                <el-form-item
                    label="即时配送"
                    prop="delivery"
                    class="label-right-align"
                >
                    <el-switch v-model="formData.delivery"></el-switch>
                </el-form-item>
                <el-form-item
                    label="活动性质"
                    prop="type"
                    class="label-right-align"
                >
                    <el-checkbox-group v-model="formData.type">
                        <el-checkbox
                            label="美食/餐厅线上活动"
                            name="type"
                        ></el-checkbox>
                        <el-checkbox label="地推活动" name="type"></el-checkbox>
                        <el-checkbox
                            label="线下主题活动"
                            name="type"
                        ></el-checkbox>
                        <el-checkbox
                            label="单纯品牌曝光"
                            name="type"
                        ></el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item
                    label="特殊资源"
                    prop="resource"
                    class="label-right-align"
                >
                    <el-radio-group v-model="formData.resource">
                        <el-radio label="线上品牌商赞助"></el-radio>
                        <el-radio label="线下场地免费"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="submitForm('formData')"
                        >立即创建</el-button
                    >
                    <el-button @click="resetForm('formData')">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
    </el-dialog>
</template>

<script>
import { addUser } from '_a/user';
export default {
    data() {
        return {
            dialogFormVisible: false,
            name: '',
            formData: {
                eventName: '',
                date: null,
                warmingDate: null,
                eventTypeName: '',
                address: '',
                delivery: false,
                type: [],
                resource: ''
            },
            rules: {
                eventName: [
                    {
                        required: true,
                        message: '字段值不可为空'
                    }
                ],
                date: [
                    {
                        required: true,
                        message: '字段值不可为空'
                    }
                ],
                warmingDate: [
                    {
                        required: true,
                        message: '字段值不可为空'
                    }
                ]
            }
        };
    },
    methods: {
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    addUser(this.formData).then(() => {
                        // 搜索列表
                        this.$parent.fetchList();
                        this.dialogFormVisible = false;
                        this.$refs['formData'].resetFields();
                    });
                    this.$message({
                        showClose: true,
                        message: '恭喜你，这是一条成功消息',
                        type: 'success'
                    });
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        beforeClose(formName) {
            this.$refs[formName].resetFields();
        }
    }
};
</script>

<style lang="scss" scoped>
.label-left-align ::v-deep .el-form-item__label {
    text-align: left;
}

.label-center-align ::v-deep .el-form-item__label {
    text-align: center;
}

.label-right-align ::v-deep .el-form-item__label {
    text-align: right;
}
.el-input-number.full-width-input,
.el-cascader.full-width-input {
    width: 100% !important;
}
</style>
