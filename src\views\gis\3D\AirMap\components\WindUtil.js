/** @format */
// import dayjs from 'dayjs';
const windUtil = {
    //加载图片
    loadImage(url) {
        return new Promise((resolve, reject) => {
            // 在这里编写异步操作的逻辑
            fetch(url)
                .then((res) => res.arrayBuffer())
                .then((res) => {
                    try {
                        let textChunk = this.parseTextChunk(res);
                        // 前4个字节为tEXt块长度，需要解析出来
                        let lengthBytes = textChunk.slice(0, 4);
                        let textLength = new DataView(lengthBytes).getUint32(0);
                        // 解析出关键字和文本内容
                        let keywordBytes = textChunk.slice(8, 8 + textLength);
                        let keyword = new TextDecoder().decode(keywordBytes);
                        // var textBytes = textChunk.slice(4 + textLength);
                        // var text = new TextDecoder().decode(textBytes);
                        // 输出关键字和文本内容
                        console.log('Keyword: ' + keyword);
                        // console.log('Text: ' + text);
                        const image = new Image();
                        image.src = url;
                        image.onload = () => {
                            console.log(image.width, image.height);
                            const canvas = document.createElement('canvas');
                            canvas.width = image.width;
                            canvas.height = image.height;
                            const ctx = canvas.getContext('2d');
                            ctx.drawImage(
                                image,
                                0,
                                0,
                                canvas.width,
                                canvas.height
                            );
                            let imageData = ctx.getImageData(
                                0,
                                0,
                                canvas.width,
                                canvas.height
                            );
                            let paramArr = keyword.split(',');
                            let paramObj = {
                                maxU: parseFloat(paramArr[0]),
                                minU: parseFloat(paramArr[1]),
                                maxV: parseFloat(paramArr[2]),
                                minV: parseFloat(paramArr[3]),
                                xmax: parseFloat(paramArr[4]),
                                ymax: parseFloat(paramArr[5]),
                                xmin: parseFloat(paramArr[6]),
                                ymin: parseFloat(paramArr[7]),
                                dx: parseFloat(paramArr[8]) * 1000,
                                dy: parseFloat(paramArr[9]) * 1000,
                                nx: image.width,
                                ny: image.height
                            };
                            Object.assign(paramObj, {
                                lo2: paramObj.xmax,
                                lo1: paramObj.xmin,
                                la1: paramObj.ymax,
                                la2: paramObj.ymin
                            });
                            let arrDataU = imageData.data.filter(
                                (item, index) => {
                                    return index % 4 == 0;
                                }
                            );
                            let arrDataV = imageData.data.filter(
                                (item, index) => {
                                    return index % 4 == 1;
                                }
                            );
                            let arrU = [];
                            for (let item of arrDataU) {
                                let value =
                                    (item * (paramObj.maxU - paramObj.minU)) /
                                    255 +
                                    paramObj.minU;
                                arrU.push(value);
                            }
                            let arrV = [];
                            for (let item of arrDataV) {
                                let value =
                                    (item * (paramObj.maxV - paramObj.minV)) /
                                    255 +
                                    paramObj.minV;
                                arrV.push(value);
                            }
                            let result = [
                                {
                                    header: paramObj,
                                    data: arrU
                                },
                                {
                                    header: paramObj,
                                    data: arrV
                                }
                            ];
                            resolve(result);
                        };
                        image.onerror = (event) => {
                            //清除图层
                            reject(event);
                            // console.log(event);
                        };
                    } catch (error) {
                        reject(error);
                    }
                });
        });
    },

    getSrc(baseUrl, curentTime) {
        let base = baseUrl;
        //  '/caiyun_uv/QZS/';
        let formatStr = curentTime.split('-').join('').replace(' ', '');

        // dayjs(curentTime + ':00:00').format('YYYYMMDDHH');
        let url = base + `/HOR-WIND-${formatStr}.png`;
        return url;
    },

    parseTextChunk(pngData) {
        let pos = 8; // PNG文件头部长度为8字节
        let endPos = pngData.byteLength - 12; // PNG文件尾部长度为12字节
        let chunkType, chunkLength, chunkData;

        // 从PNG文件数据中循环读取块数据
        while (pos < endPos) {
            chunkLength = new DataView(pngData.slice(pos, pos + 4)).getUint32(
                0
            );
            chunkType = new TextDecoder().decode(
                pngData.slice(pos + 4, pos + 8)
            );
            if (chunkType === 'tEXt') {
                chunkData = pngData.slice(pos, pos + 8 + chunkLength);
                return chunkData;
            }
            pos += 12 + chunkLength; // 每个块长度为4字节类型+4字节长度+块数据长度
        }

        return null; // 如果没有找到tEXt块，则返回null
    }
};
export default windUtil;
