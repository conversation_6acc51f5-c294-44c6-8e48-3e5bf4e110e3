/* 基础工具--start */

.zy-tools-gis li {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    margin-bottom: 6px;
    width: 34px;
    height: 34px;
    cursor: pointer;
    border-radius: 3px;
}
.zy-tools-gis li.on {
    background-color: #4895ea;
}
.zy-tools-gis li i.tool-home {
    width: 20px;
    height: 20px;
    background: url(./images/light/gis_home.png);
}

/* 放大 */
.zy-tools-gis li i.tool-zoomIn {
    width: 16px;
    height: 16px;
    background: url(./images/light/gis_light_ic2a.png);
}

/* 缩小 */
.zy-tools-gis li i.tool-zoomOut {
    width: 17px;
    height: 2px;
    background: url(./images/light/gis_light_ic3a.png);
}

/* 测量 */
.zy-tools-gis li i.tool-measure {
    width: 20px;
    height: 20px;
    background: url(./images/tool4.png);
}

.zy-tools-gis li.on i.tool-measure {
    width: 20px;
    height: 20px;
    background: url(./images/tool4-w.png);
}

.darkTheme .zy-tools-gis li i.tool-measure {
    width: 20px;
    height: 20px;
    background: url(./images/tool4-w.png);
}

/* 图层管理 */
.zy-tools-gis li i.tool-LayerManagement {
    width: 20px;
    height: 17px;
    background: url(./images/light/gis_light_ic7a.png);
}

.lightTheme .zy-tools-gis li.on i.tool-LayerManagement {
    width: 18px;
    height: 16px;
    background: url(./images/light/gis_ic7a.png);
}

/* 全屏 */
.zy-tools-gis li i.tool-fullScreen {
    width: 20px;
    height: 20px;
    background: url(./images/light/gis_full.png);
}

/* 2d模式 */
.zy-tools-gis li i.tool-mapMode {
    width: 19px;
    height: 19px;
    background: url(./images/light/gis_light_ic4a.png);
}

/* 3d模式 */
.zy-tools-gis li i.tool-mapMode.threeMode {
    width: 19px;
    height: 19px;
    background: url(./images/light/gis_light_ic5a.png);
}

/* 地图导出 */
.zy-tools-gis li i.tool-exportMap {
    width: 18px;
    height: 16px;
    background: url(./images/tool7.png);
}

.darkTheme .zy-tools-gis li i.tool-exportMap {
    width: 18px;
    height: 16px;
    background: url(./images/tool7-w.png);
}

/* 指北针 */
.zy-tools-gis li i.tool-Compass {
    width: 20px;
    height: 20px;
    background: url(./images/light/gis_compass.png);
}

/* 搜索 */
.zy-tools-gis li i.tool-search {
    width: 17px;
    height: 17px;
    background: url(./images/light/gis_light_ic1a.png);
}
.lightTheme .zy-tools-gis li.on i.tool-search {
    width: 16px;
    height: 16px;
    background: url(./images/light/gis_ic1a.png);
}

/* 行政区选中 */
.zy-tools-gis li i.tool-mapXZQ {
    width: 15px;
    height: 13px;
    background: url(./images/light/gis_light_ic6a.png);
}
.lightTheme .zy-tools-gis li.on i.tool-mapXZQ {
    width: 15px;
    height: 13px;
    background: url(./images/light/gis_ic6a.png);
}
.darkTheme .zy-tools-gis li i.tool-zoomIn {
    background: url(./images/dark/gis_ic2a.png);
}
.darkTheme .zy-tools-gis li i.tool-zoomOut {
    background: url(./images/dark/gis_ic3a.png);
}
.darkTheme .zy-tools-gis li i.tool-mapMode {
    background: url(./images/dark/gis_ic4a.png);
}
.darkTheme .zy-tools-gis li i.tool-mapMode.threeMode {
    background: url(./images/dark/gis_ic5a.png);
}
.darkTheme .zy-tools-gis li i.tool-LayerManagement {
    width: 18px;
    height: 16px;
    background: url(./images/dark/gis_ic7a.png);
}
.darkTheme .zy-tools-gis li i.tool-home {
    background: url(./images/dark/gis_home.png);
}
.darkTheme .zy-tools-gis li i.tool-Compass {
    background: url(./images/dark/gis_compass.png);
}
.darkTheme .zy-tools-gis li i.tool-fullScreen {
    background: url(./images/dark/gis_full.png);
}

.darkTheme .zy-tools-gis li i.tool-search {
    width: 16px;
    height: 16px;
    background: url(./images/dark/gis_ic1a.png);
}
.darkTheme .zy-tools-gis li i.tool-mapXZQ {
    background: url(./images/dark/gis_ic6a.png);
}
.darkTheme .zy-tools-gis li.on {
    background-color: #1088ee;
}
.darkTheme .zy-tools-gis li {
    background: #0861ad;
}

/* 基础工具--end */

/* 经纬度--Start */

.jingwei-gis {
    position: absolute;
    bottom: 10px;
    right: 160px;
    height: 20px;
    line-height: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 2px;
    color: #fff;
    font-size: 12px;
    padding: 0 15px;
    white-space: nowrap;
}

.darkTheme .jingwei-gis {
    background: #0861ad;
}

/* 经纬度--End */

/*比例尺-- Start*/
.bilichi-gis {
    position: absolute;
    bottom: 42px;
    right: 300px;
    height: 30px;
    font-size: 14px;
    color: #fff;
    line-height: 30px;
    text-align: center;
}

/*比例尺-- end*/

/*底图切换 --Start */

.map-tabs-gis {
    position: absolute;
    bottom: 125px;
    right: 0px;
    display: flex;
    height: 42px;
    padding: 4px;
    background-color: #f3f4f2;
}

.map-tabs-gis.on li {
    display: block;
}

.map-tabs-gis li {
    width: 64px;
    height: 38px;
    border: 2px solid #fff;
    position: relative;
    cursor: pointer;
}

.map-tabs-gis li img {
    width: 100%;
    height: 100%;
}

.map-tabs-gis li.cur {
    border-color: #2178cf;
}

.map-tabs-gis li p {
    position: absolute;
    bottom: 4px;
    right: 4px;
    height: 20px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    line-height: 20px;
    background-color: #2178cf;
}

.darkTheme .map-tabs-gis {
    background-color: #bbb;
}
.darkTheme .map-tabs-gis li {
    background: #0861ad;
}

/*底图切换 --end */

/* 鹰眼 --satrt */

.small-map-gis {
    position: absolute;
    bottom: 10px;
    right: 0px;
    width: 148px;
    height: 98px;
    border: 1px solid #fff;
    background: rgba(255, 255, 255, 0.8);
    overflow: hidden;
}

.small-map-gis.small {
    width: 70px;
    height: 44px;
}

.small-map-gis.small .suoxiao {
    transform: rotate(180deg);
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=2);
}

.small-map-gis .suoxiao {
    position: absolute;
    top: 0;
    left: 0;
    width: 18px;
    height: 18px;
    background: #eaeaea url(./images/ic-suoxiao2.png) center no-repeat;
    cursor: pointer;
    z-index: 101;
}

.darkTheme .small-map-gis {
    border-color: rgba(4, 42, 53, 0.9);
}
.darkTheme .small-map-gis .suoxiao {
    background-color: #175060;
}

/* 鹰眼 --end */

/* 搜索框  --Start */

.pd-srhbx1-gis {
    position: absolute;
    top: 0;
    left: 40px;
    width: 250px;
    height: 33px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 1px 9px rgba(182, 194, 228, 0.48);
    border-radius: 3px;
    overflow: hidden;
}

.pd-srhbx1-gis input {
    width: 100%;
    height: 33px;
    font-size: 14px;
    color: #333;
    line-height: 33px;
    text-indent: 10px;
    border: none;
    outline: none;
}

.zy-results-gis {
    position: absolute;
    top: 44px;
    left: 40px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.9);
    width: 290px;
    padding-bottom: 53px;
    border-radius: 3px;
}

.zy-history-gis {
    margin-top: 10px;
    max-height: 200px;
    overflow-y: auto;
}

.zy-history-gis li {
    padding-left: 24px;
    background: url(./images/ic-clock.png) 0 center no-repeat;
    height: 40px;
    border-bottom: 1px solid #e0e1e0;
    font-size: 14px;
    color: #666;
    line-height: 40px;
    cursor: pointer;
    white-space: nowrap;
}

.zy-history-gis li:hover {
    color: #2178cf;
}

.zy-results-gis .del {
    position: absolute;
    bottom: 18px;
    right: 10px;
    font-size: 14px;
    color: #999;
    line-height: 14px;
    cursor: pointer;
}

.darkTheme .pd-srhbx1-gis input {
    background: none;
    color: #fff;
}
.darkTheme .pd-srhbx1-gis input::-webkit-input-placeholder {
    color: #ccc;
}
.darkTheme .zy-history-gis li {
    color: #fff;
}
.darkTheme .zy-til1-gis {
    color: #fff;
    background-image: url(./images/til1-bg2.png);
}
.darkTheme .pd-srhbx1-gis,
.darkTheme .zy-results-gis {
    background: #0c528e;
    border-radius: 3px 0 0 3px;
}

/* 搜索框  --end */

/* 图例 --Start*/
.gis-tuli {
    position: absolute;
    display: flex;
}

.gis-tuli-toggle i {
    font-size: 16px;
    padding-bottom: 25px;
}
.gis-tuli-toggle {
    width: 34px;
    padding: 0 10px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

.gis-tuli-toggle.on ~ .gis-tuli-img {
    display: block;
}
.gis-tuli-img {
    flex: 1;
    padding: 0 5px;
    display: none;
}

.gis-tuli .item {
    display: flex;
}
.gis-tuli .item .lp {
    width: 80px;
    text-align: right;
    font-size: 14px;
    line-height: 30px;
}
.gis-tuli .item .rp {
    display: flex;
}

.gis-tuli .item .rp p {
    margin-right: 10px;
    font-size: 14px;
    line-height: 30px;
    display: flex;
    align-items: center;
}

.gis-tuli .item .rp p img {
    margin-right: 6px;
}

.lightTheme .gis-tuli .item .rp p {
    color: #333;
}
.lightTheme .gis-tuli .item .lp {
    color: #333;
}

.lightTheme .gis-tuli-toggle {
    background: #dfedff;
}
.lightTheme .gis-tuli-toggle i {
    color: #4895ea;
    background: url(./images/light/gis_light_arwlt1.png) no-repeat center bottom;
}
.lightTheme .gis-tuli-toggle.on i {
    background-image: url(./images/light/gis_light_arwrt1.png);
}
.lightTheme .gis-tuli-img {
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid #eee;
}

.darkTheme .gis-tuli-toggle {
    background: #075393;
}
.darkTheme .gis-tuli-toggle i {
    color: #fff;
    background: url(./images/dark/gis_arwlt1.png) no-repeat center bottom;
}
.darkTheme .gis-tuli-toggle.on i {
    background-image: url(./images/dark/gis_arwrt1.png);
}
.darkTheme .gis-tuli-img {
    border: 1px solid #21497e;
    background-color: rgba(11, 34, 55, 0.8);
}

.darkTheme .gis-tuli .item .lp {
    color: #fff;
}
.darkTheme .gis-tuli .item .rp p {
    color: #fff;
}

/* 图例 --end */

/* 时间轴----Start */
.gis-timeline * {
    margin: 0;
    padding: 0;
}
.gis-timeline {
    display: flex;
    height: 60px;
}

.gis-timeline .pd-play {
    margin: 23px 10px 0 9px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}
.gis-timeline .slide {
    width: 100%;
    height: 100%;
    position: relative;
    cursor: pointer;
    display: flex;
    flex: 1;
    padding-left: 10px;
}

.gis-timeline .slide .item {
    flex: 1;
    text-align: center;
    box-sizing: border-box;
}
.gis-timeline .slide .item p {
    font-size: 14px;
    text-align: center;
    line-height: 33px;
}

.gis-timeline .slide .colorBar {
    width: 100%;
    height: 5px;
    display: flex;
    flex: 1;
}
.gis-timeline .slide .colorBar li {
    height: 5px;
    flex: 1;
    position: relative;
    list-style: none;
}
.gis-timeline .slide .colorBar li.on sup {
    display: block;
}
.gis-timeline .slide .colorBar li sup {
    display: none;
    position: absolute;
    left: 50%;
    bottom: 100%;
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    border-radius: 4px;
    padding: 0 10px;
    white-space: nowrap;
    margin-bottom: 8px;
    transform: translateX(-50%);
}
.gis-timeline .slide .colorBar li sup:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 100%;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 5px 0 5px;
    transform: translateX(-50%);
}
.gis-timeline .slide .colorBar li.cb {
    background: rgb(231, 74, 74);
}
.gis-timeline .slide .colorBar li.db {
    background: #009845;
}

.gis-timeline .slide .calendar {
    padding-bottom: 2px;
    box-sizing: border-box;
    height: 21px;
    width: 100%;
    display: flex;
    flex: 1;
}

.gis-timeline .slide .calendar li {
    height: 21px;
    line-height: 21px;
    font-size: 12px;
    flex: 1;
    list-style: none;
}

.darkTheme .gis-timeline .slide .calendar li {
    color: #d0d4d9;
}
.darkTheme .gis-timeline .slide .calendar {
    border-bottom: 1px solid #21497e;
}
.darkTheme .gis-timeline .slide .colorBar li.gray {
    background: #08426b;
}
.darkTheme .gis-timeline .slide .colorBar li sup:after {
    border-color: #3686e7 transparent transparent transparent;
}
.darkTheme .gis-timeline .slide .colorBar li sup {
    color: #fff;
    background: #3686e7;
}
.darkTheme .gis-timeline .slide .colorBar li.init {
    background: #19a4e5;
}
.darkTheme .gis-timeline .slide .colorBar li {
    background: #19a4e5;
}
.darkTheme .gis-timeline .slide .item p {
    color: #d0d4d9;
}
.darkTheme .gis-timeline .slide .item {
    border-left: 1px solid #21497e;
    color: #fff;
}
.darkTheme .gis-timeline {
    border: 1px solid #21497e;
    background-color: rgba(11, 34, 55, 0.8);
}

.lightTheme .gis-timeline .slide .calendar li {
    color: #fff;
}
.lightTheme .gis-timeline .slide .calendar {
    border-bottom: 1px solid #fff;
}
.lightTheme .gis-timeline .slide .colorBar li.gray {
    background: #d5e0ec;
}
.lightTheme .gis-timeline .slide .colorBar li sup:after {
    border-color: #4895ea transparent transparent transparent;
}
.lightTheme .gis-timeline .slide .colorBar li sup {
    color: #fff;
    background: #4895ea;
}
.lightTheme .gis-timeline .slide .colorBar li.init {
    background: #19a4e5;
}
.lightTheme .gis-timeline .slide .colorBar li {
    background: #19a4e5;
}
.lightTheme .gis-timeline .slide .item p {
    color: #fff;
}
.lightTheme .gis-timeline .slide .item {
    border-left: 1px solid #fff;
    color: #fff;
}
.lightTheme .gis-timeline {
    background-color: rgba(57, 57, 57, 0.6);
    border: none;
}

/* 时间轴----End */

/* 时间轴2---Start */

.gis-timeaxis {
    border: none;
    height: 50px;
    display: flex;
}
.gis-timeaxis .pd-play {
    margin: 18px 10px 0 9px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}
.gis-timeaxis .pd-inpdate1a {
    height: 22px;
    line-height: 22px;
    border: 1px solid #40d9ec;
    font-size: 14px;
    color: #fff;
    text-indent: 5px;
    width: 113px;
    margin-top: 7px;
}
.gis-timeaxis .pd-ulnumlst {
    flex: 1;
    display: flex;
    padding: 13px 10px 0;
}

.gis-timeaxis .pd-ulnumlst li {
    flex: 1;
    height: 24px;
    background: #6f7e88;
    font-size: 14px;
    color: #fff;
    text-align: center;
    line-height: 24px;
    margin: 0 5px;
    position: relative;
}
.gis-timeaxis .pd-ulnumlst li.grn {
    color: #2e2e2e;
    background: #24bd5d;
}
.gis-timeaxis .pd-ulnumlst li.ylw {
    color: #2e2e2e;
    background: #d9bd23;
}
.gis-timeaxis .pd-ulnumlst li.init {
    color: #fff;
    background: #19a4e5;
}
.gis-timeaxis .pd-ulnumlst li sup {
    display: none;
    position: absolute;
    left: 50%;
    bottom: 100%;
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    color: #fff;
    background: #3686e7;
    border-radius: 4px;
    padding: 0 10px;
    white-space: nowrap;
    margin-bottom: 8px;
    transform: translateX(-50%);
}

.gis-timeaxis .pd-ulnumlst li sup:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 100%;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 5px 0 5px;
    border-color: #3686e7 transparent transparent transparent;
    transform: translateX(-50%);
}
.gis-timeaxis .pd-ulnumlst li.on sup {
    display: block;
}
.gis-timeaxis .pd-ulnumlst li.cur:after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    margin-top: 2px;
    height: 2px;
    background: #ff3838;
}

.lightTheme .gis-timeaxis {
    background-color: rgba(57, 57, 57, 0.6);
}
.darkTheme .gis-timeaxis {
    border: 1px solid #21497e;
    background-color: rgba(11, 34, 55, 0.8);
}

/* 时间轴2---End */

/* 图层管理--start */

.pd-label input[type='checkbox']:checked ~ i {
    background-image: url(./images/dark/gis_checkicon.png);
}
.pd-label input[type='checkbox'] ~ i {
    background-image: url(./images/dark/gis_checkic.png);
}

/* 图层管理--end */

/* 地图标绘--Start */

.map-measure {
    text-align: center;
}

.map-measure .AllDiv {
    /* background: url(./images/bg.png) no-repeat; */
    padding: 7px 0px 0px 0px;
    background-size: 100%;
}

.map-measure .DTool {
    display: inline-block;
    cursor: pointer;
    width: 30px;
    height: 30px;
    margin: 1px 2px;
}

.map-measure .point {
    background: url(./images/i_draw_point.png) no-repeat;
}

.map-measure .point.on,
.map-measure .point:hover {
    background: url(./images/i_draw_point1.png) no-repeat;
}

.map-measure .line {
    background: url(./images/i_draw_line.png) no-repeat;
}

.map-measure .line.on,
.map-measure .line:hover {
    background: url(./images/i_draw_line1.png) no-repeat;
}

.map-measure .freeline {
    background: url(./images/i_draw_freeline.png) no-repeat;
}

.map-measure .freeline.on,
.map-measure .freeline:hover {
    background: url(./images/i_draw_freeline1.png) no-repeat;
}

.map-measure .extent {
    background: url(./images/i_draw_extent.png) no-repeat;
}

.map-measure .extent.on,
.map-measure .extent:hover {
    background: url(./images/i_draw_extent1.png) no-repeat;
}

.map-measure .polygon {
    background: url(./images/i_draw_polygon.png) no-repeat;
}

.map-measure .polygon.on,
.map-measure .polygon:hover {
    background: url(./images/i_draw_polygon1.png) no-repeat;
}

.map-measure .circle {
    background: url(./images/i_draw_circle.png) no-repeat;
}

.map-measure .circle.on,
.map-measure .circle:hover {
    background: url(./images/i_draw_circle1.png) no-repeat;
}

.map-measure .txt {
    background: url(./images/i_draw_txt.png) no-repeat;
}

.map-measure .txt.on,
.map-measure .txt:hover {
    background: url(./images/i_draw_txt1.png) no-repeat;
}

.map-measure .clear,
.map-measure .clear.on,
.map-measure .clear:hover {
    background: url(./images/i_clear1.png) no-repeat;
}

/* 地图标绘--end  */


/* mapbox tip 背景 */
.darkTheme .mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip{
    border-top-color: rgba(20,64,91,.8);
} 

.darkTheme .mapboxgl-popup-content{
    background: rgba(20,64,91,.8);
    color: #FFF;
    font-size: 14px;
}

/* mapbox tip 背景-- end */ 

