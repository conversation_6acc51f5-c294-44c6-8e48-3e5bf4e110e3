<!-- @format -->

<template>
    <div class="pagination-container">
        <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="pageSizes"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
    </div>
</template>

<script>
export default {
    props: {
        pageSizes: {
            type: Array,
            default() {
                return [10, 20, 30, 40];
            }
        },
        total: {
            type: Number,
            default: 0
        },
        pagination: {
            type: Object,
            default() {
                return {
                    currentPage: 1,
                    pageSize: 10
                };
            }
        }
    },
    methods: {
        handleSizeChange(val) {
            const npagination = {
                ...this.pagination,
                pageSize: val
            };
            this.$emit('update:pagination', npagination);
        },
        handleCurrentChange(val) {
            const npagination = {
                ...this.pagination,
                currentPage: val
            };
            this.$emit('update:pagination', npagination);
        }
    }
};
</script>

<style lang="scss" scoped>
.pagination-container {
    display: flex;
    justify-content: center;
}
</style>
