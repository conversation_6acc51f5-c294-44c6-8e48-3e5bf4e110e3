<!-- @format -->

<template>
    <div class="bowo-rt-panel lr29-1-r-bg">
        <div>
            <p class="rt-txt">单位（千克/万元）</p>
            <table class="lr29-4-table">
                <tbody>
                    <tr
                        v-for="(item, idx) in data"
                        :key="idx"
                        style="height: 30px"
                    >
                        <td>{{ idx + 1 }}</td>
                        <td>
                            <div class="ell td-name" :title="item.name">
                                {{ item.name }}
                            </div>
                        </td>
                        <td>
                            <span
                                class="yst"
                                :style="{ width: getWd(item.value) }"
                            ></span>
                        </td>
                        <!-- <td>{{ item.value }}</td> -->
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        data: {
            type: Array
        },
        option: {
            type: Object
        }
    },
    created() {
        this.$pChart.setChartConfig({
            SHOW_TOOLBOX: false
        });
    },
    data() {
        return {};
    },
    methods: {
        getWd(v) {
            return ((v * 100) / this.max).toFixed(1) + '%';
        }
    }
};
</script>

<style scoped>
.lr29-1-r-bg {
    width: 500px;
}
.rt-txt {
    font-size: 12px;
    color: #fff;
    text-align: left;
}
.lr29-4-table {
    width: 100%;
}

.lr29-4-table tr td {
    height: 38px;
    font-size: 14px;
    color: #fff;
    text-align: center;
}

.lr29-4-table tr td:nth-child(1) {
    width: 15%;
}

.lr29-4-table tr td:nth-child(2) {
    width: 20%;
}

.lr29-4-table tr td:nth-child(3) {
    width: 65%;
    text-align: left;
}

.lr29-4-table .yst {
    display: inline-block;
    width: 88%;
    height: 8px;
    border-radius: 4px;
    background: #2ae3ff;
}

.lr29-4-table2 {
    width: 100%;
}

.lr29-4-table2 tr td {
    height: 38px;
    font-size: 14px;
    color: #fff;
    text-align: center;
}

.lr29-4-table2 thead {
    background: #0c2a61;
}

.lr29-4-table2 tr td:nth-child(1) {
    width: 18%;
}

.lr29-4-table2 tr td:nth-child(2) {
    width: 45%;
    text-align: left;
}

.lr29-4-table2 tr td:nth-child(3) {
    width: 37%;
}

.td-name {
    text-align: left;
    width: 90px;
}

.ell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
