<!-- @format -->
<template>
    <div class="three-part-guage">
        <div ref="dashboardEchart" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script>
export default {
    props: {
        data: Object,
        option: {
            // 配置宽高
            type: Object,
            default() {
                return {};
            }
        }
    },

    data() {
        return {
            defaultOption: {
                color: '#2ad9ff', //表盘颜色
                detailColor: '#2ad9ff', //数值颜色 默认蓝底下白色 白底下黑色
                unit: '%', //数字单位 默认%
                max: 100 //仪表盘满刻度值 默认100
            },
            finalOption: {}
        };
    },

    watch: {
        option: 'setDashboardEchart',
        data: 'setDashboardEchart'
    },

    mounted() {
        this.setDashboardEchart(this.data);
    },

    methods: {
        // 配置总量仪运行情况图
        setDashboardEchart(data) {
            this.finalOption = {
                ...this.defaultOption,
                ...this.option
            };

            let fontColor =
                window.localStorage.themeType === 'dark' ? '#fff' : '#333';
            let guageColor = this.finalOption.color;
            let detailColor = this.finalOption.detailColor || guageColor;
            let unit = this.finalOption.unit;
            let max = this.finalOption.max;

            let guage = {
                type: 'gauge',
                center: ['50%', '55%'],
                radius: '90%',
                startAngle: 200,
                endAngle: -20,
                splitNumber: 5, //刻度数量
                min: 0,
                max,
                clockwise: true,
                axisLine: {
                    show: true,
                    lineStyle: {
                        width: 5,
                        shadowBlur: 0,
                        color: [[1, guageColor]]
                    }
                },
                axisTick: {
                    show: true,
                    lineStyle: {
                        color: guageColor,
                        width: 1
                    },
                    length: '12%',
                    splitNumber: 10
                },
                splitLine: {
                    show: true,
                    length: '15%',
                    lineStyle: {
                        color: guageColor
                    }
                },
                axisLabel: {
                    distance: 10,
                    textStyle: {
                        color: fontColor,
                        fontSize: '14'
                    }
                },
                pointer: {
                    //仪表盘指针
                    show: 1,
                    width: 4
                },
                itemStyle: {
                    normal: {
                        color: guageColor
                    }
                },
                detail: {
                    show: true,
                    formatter: `{value}{u|${unit}}`,
                    offsetCenter: [0, '45%'],
                    textStyle: {
                        fontSize: 30,
                        color: detailColor
                    },
                    rich: {
                        u: {
                            padding: [5, 0, 0, 0],
                            fontSize: 16
                        }
                    }
                },

                title: {
                    color: fontColor,
                    offsetCenter: [0, '75%']
                },
                data: []
            };
            let option = {
                series: [
                    // 中间仪表盘
                    {
                        ...guage,
                        data: [
                            {
                                name: data[1].name,
                                value: data[1].value || 0
                            }
                        ]
                    },

                    // 左侧仪表盘
                    {
                        ...guage,
                        center: ['22%', '55%'],
                        radius: '80%',
                        startAngle: -140,
                        endAngle: 45,
                        detail: {
                            show: true,
                            formatter: `{value}{u|${unit}}`,
                            offsetCenter: ['10%', '52%'],
                            textStyle: {
                                fontSize: 30,
                                color: detailColor
                            },
                            rich: {
                                u: {
                                    padding: [5, 0, 0, 0],
                                    fontSize: 16
                                }
                            }
                        },
                        title: {
                            color: fontColor,
                            offsetCenter: ['10%', '85%']
                        },
                        data: [
                            {
                                name: data[0].name,
                                value: data[0].value || 0
                            }
                        ]
                    },

                    // 右侧仪表盘
                    {
                        ...guage,

                        center: ['80%', '55%'], // 默认全局居中
                        radius: '80%',
                        startAngle: 140,
                        endAngle: -45,
                        detail: {
                            show: true,
                            formatter: `{value}{u|${unit}}`,
                            offsetCenter: ['-10%', '52%'],
                            textStyle: {
                                fontSize: 30,
                                color: detailColor
                            },
                            rich: {
                                u: {
                                    padding: [5, 0, 0, 0],
                                    fontSize: 16
                                }
                            }
                        },
                        title: {
                            color: fontColor,
                            offsetCenter: ['-10%', '85%']
                        },
                        data: [
                            {
                                name: data[2].name,
                                value: data[2].value || 0
                            }
                        ]
                    }
                ]
            };

            let el = this.$refs.dashboardEchart;

            //图表宽高比例需要保持2.5:1
            let ratio = 2.5;
            let w = el.parentNode.offsetWidth;
            let h = el.parentNode.offsetHeight;
            let w_ = w;
            let h_ = h;
            if (w > h * ratio) {
                w_ = h * ratio;
            } else {
                h_ = w / ratio;
            }
            this.$refs.dashboardEchart.style.width = w_ + 'px';
            this.$refs.dashboardEchart.style.height = h_ + 'px';

            let myChart = this.$echarts.init(this.$refs.dashboardEchart);
            myChart.setOption(option, true);
        }
    }
};
</script>
<style scoped lang="less">
.three-part-guage {
    display: flex;
    justify-content: center;
}
</style>
