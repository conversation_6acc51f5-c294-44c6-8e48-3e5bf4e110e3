<!-- @format -->

<!-- 空气站-监管级别控制  -->
<template>
    <dl class="gis-dloption1">
        <dt>
            <label
                class="pd-label arw on"
                @click="allLayerClick()"
                style="float: left; padding-left: 10px"
                ><input type="checkbox" :checked="checkAll" disabled /><i></i
                ><span>空气站</span></label
            >
        </dt>
        <dd class="gis-optwrap1">
            <div class="gis-sublevel" style="display: block">
                <label
                    class="pd-label"
                    v-for="(item, index) of zts"
                    :key="index"
                    @click="ztLayerClick(item)"
                    ><input
                        type="checkbox"
                        :checked="item.selected"
                        disabled
                    /><i></i><span>{{ item.name }}</span
                    >（<span>{{ item.total }}</span
                    >）</label
                >
            </div>
        </dd>
    </dl>
</template>

<script>
import { getAirTotal } from '@/api/gis/3D/AirMap/index'; //统计监管级别数量
export default {
    data() {
        return {
            jgjbArr: [],
            zts: [
                // {
                //     name: '国控站',
                //     fiterType: '1',
                //     selected: true,
                //     total: 0
                // },
                // {
                //     name: '省控站',
                //     fiterType: '2',
                //     selected: true,
                //     total: 0
                // },
                // {
                //     name: '市控站',
                //     fiterType: '3',
                //     selected: true,
                //     total: 0
                // }
            ],

            checkAll: true //全部选中
        };
    },
    props: [],

    mounted() {
        this.getAirTotal();
    },
    methods: {
        //专题点击
        allLayerClick() {
            this.checkAll = !this.checkAll;

            for (let o of this.zts) {
                o.selected = this.checkAll;
            }

            this.getSelectJGJB();
        },

        //子专题点击
        ztLayerClick(obj) {
            obj.selected = !obj.selected;

            //根据子级的选中状态，设置父级的选中状态
            this.checkAll = this.zts.some((item) => {
                return item.selected;
            });

            this.getSelectJGJB();
        },

        //获取选中的监管级别
        getSelectJGJB() {
            let arrTemp = this.zts.filter((item) => {
                return item.selected;
            });

            this.jgjbArr = arrTemp.map((item) => {
                return item.fiterType;
            });

            this.$emit('jgjbChange', this.jgjbArr);
        },

        //获取专题统计数据
        getAirTotal() {
            getAirTotal({}).then((res) => {

                this.zts = res.data;
                this.checkAll = this.zts.some((item) => {
                    return item.selected;
                });

                this.getSelectJGJB();
            });
        }
    }
};
</script>

<style></style>
