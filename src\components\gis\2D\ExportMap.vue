<!-- @format -->
<!--导出地图 -->
<template>
    <ul class="zy-tools-gis">
        <li>
            <i class="tool-exportMap" @click="exportImg"></i>
        </li>
    </ul>
</template>

<script>
export default {
    name: 'ExportMap',
    props: ['map'],
    data() {
        return {};
    },
    components: {},
    computed: {},
    mounted() {},
    methods: {
        exportImg() {
            let This = this;
            //以下是对svg的处理
            let nodesToRecover = [];
            let nodesToRemove = [];
            let svgElem = $('.mapContainer').find('svg'); //divReport为需要截取成图片的dom的id
            svgElem.each(function (index, node) {
                let parentNode = node.parentNode;

                svgAsPngUri(node, null, function (uri) {
                    nodesToRecover.push({
                        parent: parentNode,
                        child: node
                    });
                    parentNode.removeChild(node);

                    let imgHtml =
                        '<img  style="position:absolute" src="' +
                        uri +
                        '" alt="转换失败"/>';
                    $(parentNode).append(imgHtml);

                    let node1 = $(parentNode).children().last();

                    nodesToRemove.push({
                        parent: parentNode,
                        child: node1[0]
                    });
                });
            });

            //生成之后再导出
            setTimeout(function () {
                // $('.jimu-widget-MainMenu').hide();
                // $('.jimu-widget-Main_Tool').hide();

                //加了这句，天地图下载不需要代理
                $('.map_layers img').attr('crossorigin', 'anonymous');

                try {
                    html2canvas($('.mapContainer')[0], {
                        logging: true, //Enable log (use Web Console for get Errors and Warings)
                        // proxy: 'DotNet/simple-http-proxy-csharp.ashx', //换成java版本
                        useCORS: true
                    }).then(function (canvas) {
                        // $('.jimu-widget-MainMenu').show();
                        // $('.jimu-widget-Main_Tool').show();

                        let url = canvas.toDataURL('image/png');
                        for (let i = 0; i < nodesToRemove.length; i++) {
                            let obj = nodesToRemove[i];
                            // $(obj.child).remove();
                            obj.parent.removeChild(obj.child);
                        }

                        for (let i = 0; i < nodesToRecover.length; i++) {
                            let obj = nodesToRecover[i];
                            obj.parent.appendChild(obj.child);
                        }

                        if (This.browserIsIe()) {
                            // This.downLoadReportIMG(url);
                        } else {
                            let tmp = Date.parse(new Date()).toString();
                            tmp = tmp.substr(0, 15);
                            let fileName = '地图导出' + tmp;
                            let $a = $('<a></a>')
                                .attr('href', url)
                                .attr('download', fileName + '.png');
                            $a[0].click();
                        }
                    });
                } catch (err) {
                    // $('.jimu-widget-MainMenu').show();
                    // $('.jimu-widget-Main_Tool').show();
                }
            }, 800);
        },
        //判断是否为ie浏览器
        browserIsIe: function () {
            if (!!window.ActiveXObject || 'ActiveXObject' in window)
                return true;
            else return false;
        }
    },
    watch: {}
};
</script>

<style scoped></style>
