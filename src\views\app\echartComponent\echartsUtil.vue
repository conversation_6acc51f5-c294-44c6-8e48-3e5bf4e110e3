<!-- @format -->

<template>
    <div style="width: 100%; overflow: auto; padding-bottom: 50px">
        <h4>基础单系列数据</h4>
        <PLine style="width: 600px; height: 400px" :data="pltData1"></PLine>

        <h4>多系列数据1</h4>
        <PLine style="width: 600px; height: 400px" :data="pltData2"></PLine>

        <h4>多系列数据2</h4>
        <PLine style="width: 600px; height: 400px" :data="pltData3"></PLine>

        <h4>多系列数据3</h4>
        <PLine style="width: 600px; height: 400px" :data="pltData4"></PLine>
    </div>
</template>

<script>
import echartsUtils from '_u/echartsUtils';
export default {
    data() {
        return {
            pltData1: {},
            pltData2: {},
            pltData3: {},
            pltData4: {}
        };
    },
    mounted() {
        this.getPltData();
    },
    methods: {
        getPltData() {
            let res = {
                data: [
                    {
                        year: '2019',
                        no2: '39',
                        areaCode: '141100',
                        pm25: '33',
                        o3: '141',
                        areaName: '吕梁市',
                        so2: '14',
                        pm10: '90',
                        aqi: '85',
                        rank: 1,
                        co: '0.8'
                    },
                    {
                        year: '2019',
                        no2: '16',
                        areaCode: '140400',
                        pm25: '33',
                        o3: '145',
                        areaName: '长治市',
                        so2: '9',
                        pm10: '55',
                        aqi: '88',
                        rank: 2,
                        co: '0.8'
                    },
                    {
                        year: '2019',
                        no2: '7',
                        areaCode: '140800',
                        pm25: '31',
                        o3: '148',
                        areaName: '运城市',
                        so2: '5',
                        pm10: '49',
                        aqi: '90',
                        rank: 3,
                        co: '0.6'
                    },
                    {
                        year: '2019',
                        no2: '13',
                        areaCode: '140500',
                        pm25: '22',
                        o3: '155',
                        areaName: '晋城市',
                        so2: '4',
                        pm10: '55',
                        aqi: '96',
                        rank: 4,
                        co: '0.7'
                    },
                    {
                        year: '2020',
                        no2: '19',
                        areaCode: '140600',
                        pm25: '48',
                        o3: '162',
                        areaName: '吕梁市',
                        so2: '8',
                        pm10: '85',
                        aqi: '102',
                        rank: 5,
                        co: '1.0'
                    },
                    {
                        year: '2020',
                        no2: '16',
                        areaCode: '140700',
                        pm25: '52',
                        o3: '172',
                        areaName: '长治市',
                        monitorDate: '2020-07-30',
                        so2: '5',
                        pm10: '77',
                        aqi: '111',
                        rank: 6,
                        co: '1.1'
                    },
                    {
                        year: '2020',
                        no2: '18',
                        areaCode: '140200',
                        pm25: '31',
                        o3: '173',
                        areaName: '运城市',
                        monitorDate: '2020-07-30',
                        so2: '14',
                        pm10: '58',
                        aqi: '112',
                        rank: 7,
                        co: '0.8'
                    },
                    {
                        year: '2020',
                        no2: '24',
                        areaCode: '140300',
                        pm25: '68',
                        o3: '181',
                        areaName: '晋城市',
                        so2: '14',
                        pm10: '95',
                        aqi: '120',
                        rank: 8,
                        co: '1.4'
                    }
                ],
                status: '000'
            };

            this.pltData1 = echartsUtils.createLineBarOption(res.data, {
                x: 'areaName',
                seriesKey: 'aqi'
            });

            this.pltData2 = echartsUtils.createLineBarOption(res.data, {
                x: 'areaName',
                //useAirSub: true, //是否将系列名称（空气污染因子名称）中的数字转为下角标数字
                seriesKey: [
                    { name: 'AQI', key: 'aqi' },
                    { name: 'PM25', key: 'pm25' },
                    { name: 'PM10', key: 'pm10' },
                    { name: 'SO2', key: 'so2' },
                    { name: 'NO2', key: 'no2' },
                    { name: 'CO', key: 'co' },
                    { name: 'O3', key: 'o3' }
                ]
            });

            this.pltData3 = echartsUtils.createLineBarOption(res.data, {
                x: 'areaName',
                seriesKey: 'aqi',
                sameSeriesKey: 'year' //此字段的值相同，则属于同一个系列的数据
            });

            let data2 = [
                { JCSJ: '07-01', AD: 11, plt: '氨氮' },
                { JCSJ: '07-01', ZL: 15, plt: '总磷' },
                { JCSJ: '07-01', HXXYL: 18, plt: '化学需氧量' },
                { JCSJ: '07-02', AD: 12, plt: '氨氮' },
                { JCSJ: '07-02', ZL: 13, plt: '总磷' },
                { JCSJ: '07-02', HXXYL: 16, plt: '化学需氧量' },
                { JCSJ: '07-03', AD: 14, plt: '氨氮' },
                { JCSJ: '07-03', ZL: 15, plt: '总磷' },
                { JCSJ: '07-03', HXXYL: 17, plt: '化学需氧量' },
                { JCSJ: '07-04', AD: 12, plt: '氨氮' },
                { JCSJ: '07-04', ZL: 13, plt: '总磷' },
                { JCSJ: '07-04', HXXYL: 16, plt: '化学需氧量' }
            ];

            this.pltData4 = echartsUtils.createLineBarOption(data2, {
                x: 'JCSJ',
                sameSeriesKey: 'plt',
                seriesKey: [
                    { name: '氨氮', key: 'AD' },
                    { name: '总磷', key: 'ZL' },
                    { name: '化学需氧量', key: 'HXXYL' }
                ]
            });
            console.log(this.pltData4);
        }
    }
};
</script>

<style scoped>
h4 {
    font-size: 16px;
    padding: 20px;
    margin-top: 10px;
    border-top: 1px solid #ddd;
}
</style>
