<!-- @format -->

<!--  -->
<template>
    <div class="">
        <p-pie
            :data="pieData"
            :config="{
                type: 'ring',
                color: 'merge1n2WaterGradesColor',
                showLegend: true,
                showLabel: false,
                isShowInnerShadow: false,
                legendOrient: 'vertical',
                legendTextStyle: {
                    rich: {
                        name: { width: 120 },
                        value: { width: 50 }
                    }
                },
                radius: ['40%', '60%']
            }"
            :option="finalOption"
            style="width: 100%; height: 100%"
        ></p-pie>
    </div>
</template>

<script>
const lightColor = [
    '#6ebffb',
    '#a9df34',
    '#40c057',
    '#5a7fe8',
    '#ffea00',
    '#13c9d9',
    '#00a497',
    '#5363c5',
    '#218ede',
    '#f39800',
    '#4262d9',
    '#9799f3',
    '#0fd0b7',
    '#ffd351'
];
const darkColor = [
    '#2ad9ff',
    '#e9c613',
    '#26d267',
    '#f68b17',
    '#fc4a4a',
    '#4d76eb',
    '#00e1c4',
    '#9465f4',
    '#c0f02f',
    '#06a4ff'
];
export default {
    //import引入的组件需要注入到对象中才能使用
    name: 'airCompany',
    components: {},
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        },
        option: {
            type: Object,
            default: function () {
                return {};
            }
        },
        // 单位
        unit: {
            type: String,
            default: ''
        },
        // 标题
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        let fontColor =
            window.localStorage.themeType === 'dark' ? '#fff' : '#333';
        const color =
            window.localStorage.themeType === 'dark' ? darkColor : lightColor;
        return {
            pieData: [],
            defaultOption: {
                color,
                title: [
                    {
                        text: '{name|' + 0 + '}{val|' + this.unit + '}',
                        top: '40%',
                        left: '20%',
                        textStyle: {
                            rich: {
                                name: {
                                    fontSize: 14,
                                    fontWeight: 'normal',
                                    color: '#0099ff',
                                    padding: [10, 0]
                                },
                                val: {
                                    fontSize: 14,
                                    fontWeight: 'bold',
                                    color: fontColor,
                                    padding: [10, 10]
                                }
                            }
                        }
                    },
                    {
                        text: this.title,
                        top: '50%',
                        left: '15%',
                        textStyle: {
                            fontSize: 14,
                            color: fontColor,
                            fontWeight: 400
                        }
                    }
                ],
                series: [
                    {
                        center: ['25%', '50%']
                    }
                ]
            },
            finalOption: {}
        };
    },
    computed: {},
    watch: {
        data: function (v) {
            this.getData();
        },
        option: function (v) {
            this.getData();
        }
    },
    created() {},
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            this.pieData = [];
            let sum = 0;
            this.data.forEach((obj) => {
                sum += Number(obj.value);
            });
            this.finalOption = {
                ...this.defaultOption,
                ...this.option
            };
            this.finalOption.title[0].text =
                '{name|' + sum + '}{val|' + this.unit + '}';
            sum = sum || 1;
            this.data.forEach((obj, i) => {
                this.pieData.push({
                    name: obj.name,
                    value: obj.value,
                    legendName:
                        '{name|' +
                        obj.name +
                        '} {value|' +
                        ((100 * Number(obj.value)) / sum).toFixed(0) +
                        '%} ' +
                        obj.value +
                        this.unit
                });
            });
        }
    }
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
