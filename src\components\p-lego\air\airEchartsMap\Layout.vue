<!-- @format -->
<template>
    <div>
        <ul class="color-legend">
            <li>
                <em class="color1"></em>
                <i>优</i>
            </li>
            <li>
                <em class="color2"></em>
                <i>良</i>
            </li>
            <li>
                <em class="color3"></em>
                <i>轻度污染</i>
            </li>
            <li>
                <em class="color4"></em>
                <i>中度污染</i>
            </li>
            <li>
                <em class="color5"></em>
                <i>重度污染</i>
            </li>
            <li>
                <em class="color6"></em>
                <i>严重污染</i>
            </li>
        </ul>

        <div id="mapEchart" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script>
export default {
    props: {
        aqiMapData: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },

    data() {
        return {
            cityName: '',
            cityJSON: {},
            cityData: []
        };
    },
    mounted() {
        this.cityName = this.aqiMapData.cityName;
        this.cityJSON = this.aqiMapData.cityJSON;
        this.cityData = this.aqiMapData.cityData;

        this.setEchartMap();
    },

    methods: {
        // 地图配置
        setEchartMap() {
            let myChart = this.$echarts.init(
                document.getElementById('mapEchart')
            );
            this.$echarts.registerMap(this.cityName, this.cityJSON, {});

            let option = {
                title: {
                    text: this.cityName + 'AQI空气质量图',
                    left: 'center',
                    top: '10'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: (params) => {
                        // console.log(params, '--------');
                        let str = '';
                        str += '<div>' + params.seriesName + '</div>';
                        str +=
                            '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
                            params.color +
                            ';"></span>' +
                            params.data.name +
                            '</span> : <span>' +
                            params.data.value;
                        // if (title === '空气优良率') {
                        //     str += '%';
                        // } else if (title === 'CO') {
                        //     str += 'mg/m³';
                        // } else if (title === '综合污染指数') {
                        //     str += '';
                        // } else {
                        //     str += 'μg/m³';
                        // }
                        return str;
                    }
                },
                toolbox: { show: false },

                visualMap: {
                    show: false, //不显示自带的legend
                    pieces: [
                        { gte: 300, color: '#6f001f' },
                        { gte: 200, lt: 300, color: '#94004b' },
                        { gte: 150, lt: 200, color: '#f60000' },
                        { gte: 100, lt: 150, color: '#f87c12' },
                        { gte: 50, lt: 100, color: '#d8bc37' },
                        { gte: 0, lt: 50, color: '#24bd5d' },
                        { value: 0, color: '#fff' }
                    ],
                    // left: '20',
                    realtime: false
                },

                series: {
                    name: this.cityName,
                    type: 'map',
                    mapType: this.cityName, // 自定义扩展图表类型
                    // silent: true, //鼠标移入区域变色 如果设置为true则无法高亮
                    left: 10,
                    right: 120,
                    top: 80,
                    bottom: 10,
                    layoutSize: '100%',
                    label: {
                        normal: {
                            //静态的时候展示样式
                            show: true, //是否显示地图省份得名称
                            textStyle: {
                                color: '#fff',
                                fontSize: 10,
                                fontFamily: 'Arial'
                            }
                        }
                        // emphasis: {
                        //     //鼠标移入高亮显颜色
                        //     areaColor: null
                        // }
                    },
                    data: this.cityData,
                    itemStyle: {
                        emphasis: {
                            //鼠标移入高亮显颜色
                            areaColor: null
                        }
                    }
                }
            };
            myChart.setOption(option);
        }
    }
};
</script>

<style scoped>
.color-legend {
    position: absolute;
    bottom: 20px;
    right: 20px;
}

.color-legend li {
    display: flex;
    align-items: center;
}

.color-legend li em {
    width: 20px;
    height: 20px;
}

.color-legend li i {
    color: #333;
    font-size: 12px;
    padding-left: 4px;
}

.color-legend li .color1 {
    background-color: #24bd5d;
}

.color-legend li .color2 {
    background-color: #d8bc37;
}

.color-legend li .color3 {
    background-color: #f87c12;
}

.color-legend li .color4 {
    background-color: #f60000;
}

.color-legend li .color5 {
    background-color: #94004b;
}

.color-legend li .color6 {
    background-color: #6f001f;
}
</style>
