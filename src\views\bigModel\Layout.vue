<!-- @format -->

<template>
    <div class="sw0424-wrap">
        <div class="sw0424-msgwrap">
            <div class="gap"></div>

            <div class="sw0424-msgmod" style="height: 99%">
                <div
                    class="sw0424-lside"
                    :style="{ width: isMenuCollapsed ? '0px' : '300px' }"
                    :class="{
                        off: isMenuCollapsed
                    }"
                >
                    <i
                        class="lside-toggle"
                        @click="isMenuCollapsed = !isMenuCollapsed"
                    >
                        <img src="./images/zy0708_toggle_arw.png" alt="" />
                    </i>
                    <div class="wrapper">
                        <div class="gap40"></div>
                        <img
                            src="./images/sw0424_logo.png"
                            class="db auto"
                            id="img_model"
                        />
                        <div class="gap"></div>
                        <div class="sw0424-lsthd" id="mxlb">
                            <strong>模型列表</strong>
                            <span
                                style="
                                    margin-left: 40px;
                                    color: #006bff;
                                    cursor: pointer;
                                "
                                @click="newSession"
                                >新的会话</span
                            >
                        </div>
                        <div
                            class="sw0424-lstbd nice-scroll"
                            style="max-height: 150px"
                        >
                            <div class="scrolly">
                                <ul class="sw0424-ulnav1">
                                    <li
                                        :class="{
                                            on: item.DM === currentModel
                                        }"
                                        v-for="item in modelList"
                                        :key="item.DM"
                                        @click="tabChange(item)"
                                    >
                                        <span style="padding-left: 0">{{
                                            item.DMMC
                                        }}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="gap"></div>
                        <div class="sw0424-lsthd" id="mxlb">
                            <strong>历史记录</strong>
                        </div>
                        <div
                            class="sw0424-lstbd nice-scroll"
                            style="max-height: 500px"
                        >
                            <div class="scrolly">
                                <ul class="sw0424-ulnav1">
                                    <li
                                        :class="{
                                            on: item.XH === historyMessageId
                                        }"
                                        v-for="item in historyList"
                                        :key="item.XH"
                                        @click="historyClick(item)"
                                    >
                                        <span style="padding-left: 0">{{
                                            item.HHBT
                                        }}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sw0424-rside" id="content">
                    <!-- @click="isMenuCollapsed = true" -->
                    <div class="top">
                        <div class="scrolly">
                            <!-- 遍历historyMessages -->
                            <div
                                class="message-list"
                                v-for="(item, indx) in historyMessages"
                                :key="indx"
                            >
                                <div
                                    v-for="(message, index) in JSON.parse(
                                        item.NR
                                    )"
                                    :key="index"
                                    :class="['message', message.role]"
                                >
                                    <!-- 思考过程及文档引用 -->
                                    <div
                                        v-if="
                                            message.thoughts &&
                                            message.thoughts.length
                                        "
                                        class="thoughts-container"
                                    >
                                        <div
                                            v-for="(
                                                thought, tIndex
                                            ) in message.thoughts"
                                            :key="tIndex"
                                            class="thought-item"
                                        >
                                            <!-- 思考过程头部 -->
                                            <div
                                                class="thought-header"
                                                @click="
                                                    toggleThought(
                                                        thought,
                                                        indx,
                                                        tIndex,
                                                        'history'
                                                    )
                                                "
                                            >
                                                <span class="action-name">{{
                                                    thought.action_name
                                                }}</span>
                                                <span class="toggle-icon">
                                                    {{
                                                        !collapsedThought.has(
                                                            `history-${thought.action_type}-${indx}-${tIndex}`
                                                        )
                                                            ? '▼'
                                                            : '▶'
                                                    }}
                                                </span>
                                            </div>

                                            <!-- 思考内容 -->
                                            <div
                                                v-if="
                                                    thought.thought &&
                                                    !collapsedThought.has(
                                                        `history-${thought.action_type}-${indx}-${tIndex}`
                                                    )
                                                "
                                                class="thought-content"
                                                v-html="
                                                    renderMarkdown(
                                                        thought.thought
                                                    )
                                                "
                                            />

                                            <!-- 文档引用 -->
                                            <div
                                                v-if="
                                                    parseObservation(
                                                        thought.observation
                                                    ).length &&
                                                    !collapsedThought.has(
                                                        `history-${thought.action_type}-${indx}-${tIndex}`
                                                    )
                                                "
                                                class="reference-container"
                                            >
                                                <div class="reference-header">
                                                    <span
                                                        class="reference-title"
                                                        >相关文档</span
                                                    >
                                                    <span
                                                        class="reference-count"
                                                    >
                                                        共
                                                        {{
                                                            parseObservation(
                                                                thought.observation
                                                            ).length
                                                        }}
                                                        篇
                                                    </span>
                                                </div>

                                                <div
                                                    v-for="(
                                                        doc, docIndex
                                                    ) in parseObservation(
                                                        thought.observation
                                                    )"
                                                    :key="docIndex"
                                                    class="reference-item"
                                                >
                                                    <div
                                                        class="doc-header"
                                                        @click="toggleDoc(doc)"
                                                    >
                                                        <div
                                                            class="doc-title-wrapper"
                                                        >
                                                            <a
                                                                v-if="
                                                                    doc.webSearchUrl
                                                                "
                                                                :href="
                                                                    doc.webSearchUrl
                                                                "
                                                                target="_blank"
                                                                class="doc-title"
                                                                @click.stop
                                                            >
                                                                {{
                                                                    doc.title ||
                                                                    doc.dataName
                                                                }}
                                                            </a>
                                                            <span
                                                                v-else
                                                                class="doc-title"
                                                            >
                                                                {{
                                                                    doc.title ||
                                                                    doc.dataName
                                                                }}
                                                            </span>
                                                            <span
                                                                class="doc-index"
                                                                >#{{
                                                                    doc.referenceIndex
                                                                }}</span
                                                            >
                                                        </div>
                                                        <span
                                                            class="toggle-icon"
                                                        >
                                                            {{
                                                                expandedDocs.has(
                                                                    doc.id
                                                                )
                                                                    ? '▼'
                                                                    : '▶'
                                                            }}
                                                        </span>
                                                    </div>
                                                    <div
                                                        class="doc-content"
                                                        :class="{
                                                            expanded:
                                                                expandedDocs.has(
                                                                    doc.id
                                                                )
                                                        }"
                                                        v-html="
                                                            renderMarkdown(
                                                                cleanDocContent(
                                                                    doc.content
                                                                )
                                                            )
                                                        "
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div
                                        v-show="message.content"
                                        class="message-content"
                                        v-html="renderMarkdown(message.content)"
                                        @click="
                                            handleContentClick($event, message)
                                        "
                                    />
                                    <div
                                        v-if="message.isStreaming && isLoading"
                                        class="stream-cursor"
                                    ></div>
                                </div>
                            </div>

                            <div class="message-list">
                                <div
                                    v-for="(message, index) in messages"
                                    :key="index"
                                    :class="['message', message.role]"
                                >
                                    <!-- 思考过程及文档引用 -->
                                    <div
                                        v-if="
                                            message.thoughts &&
                                            message.thoughts.length
                                        "
                                        class="thoughts-container"
                                    >
                                        <div
                                            v-for="(
                                                thought, tIndex
                                            ) in message.thoughts"
                                            :key="tIndex"
                                            class="thought-item"
                                        >
                                            <!-- 思考过程头部 -->
                                            <div
                                                class="thought-header"
                                                @click="
                                                    toggleThought(
                                                        thought,
                                                        index,
                                                        tIndex,
                                                        'new'
                                                    )
                                                "
                                            >
                                                <span class="action-name">{{
                                                    thought.action_name
                                                }}</span>
                                                <span class="toggle-icon">
                                                    {{
                                                        !collapsedThought.has(
                                                            `new-${thought.action_type}-${index}-${tIndex}`
                                                        )
                                                            ? '▼'
                                                            : '▶'
                                                    }}
                                                </span>
                                            </div>

                                            <!-- 思考内容 -->
                                            <div
                                                v-if="
                                                    thought.thought &&
                                                    !collapsedThought.has(
                                                        `new-${thought.action_type}-${index}-${tIndex}`
                                                    )
                                                "
                                                class="thought-content"
                                                v-html="
                                                    renderMarkdown(
                                                        thought.thought
                                                    )
                                                "
                                            />

                                            <!-- 文档引用 -->
                                            <div
                                                v-if="
                                                    parseObservation(
                                                        thought.observation
                                                    ).length &&
                                                    !collapsedThought.has(
                                                        `new-${thought.action_type}-${index}-${tIndex}`
                                                    )
                                                "
                                                class="reference-container"
                                            >
                                                <div class="reference-header">
                                                    <span
                                                        class="reference-title"
                                                        >相关文档</span
                                                    >
                                                    <span
                                                        class="reference-count"
                                                    >
                                                        共
                                                        {{
                                                            parseObservation(
                                                                thought.observation
                                                            ).length
                                                        }}
                                                        篇
                                                    </span>
                                                </div>

                                                <div
                                                    v-for="(
                                                        doc, docIndex
                                                    ) in parseObservation(
                                                        thought.observation
                                                    )"
                                                    :key="docIndex"
                                                    class="reference-item"
                                                >
                                                    <div
                                                        class="doc-header"
                                                        @click="toggleDoc(doc)"
                                                    >
                                                        <div
                                                            class="doc-title-wrapper"
                                                        >
                                                            <a
                                                                v-if="
                                                                    doc.webSearchUrl
                                                                "
                                                                :href="
                                                                    doc.webSearchUrl
                                                                "
                                                                target="_blank"
                                                                class="doc-title"
                                                                @click.stop
                                                            >
                                                                {{
                                                                    doc.title ||
                                                                    doc.dataName
                                                                }}
                                                            </a>
                                                            <span
                                                                v-else
                                                                class="doc-title"
                                                            >
                                                                {{
                                                                    doc.title ||
                                                                    doc.dataName
                                                                }}
                                                            </span>
                                                            <span
                                                                class="doc-index"
                                                                >#{{
                                                                    doc.referenceIndex
                                                                }}</span
                                                            >
                                                        </div>
                                                        <span
                                                            class="toggle-icon"
                                                        >
                                                            {{
                                                                expandedDocs.has(
                                                                    doc.id
                                                                )
                                                                    ? '▼'
                                                                    : '▶'
                                                            }}
                                                        </span>
                                                    </div>
                                                    <div
                                                        class="doc-content"
                                                        :class="{
                                                            expanded:
                                                                expandedDocs.has(
                                                                    doc.id
                                                                )
                                                        }"
                                                        v-html="
                                                            renderMarkdown(
                                                                cleanDocContent(
                                                                    doc.content
                                                                )
                                                            )
                                                        "
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div
                                        v-show="message.content"
                                        class="message-content"
                                        v-html="renderMarkdown(message.content)"
                                        @click="
                                            handleContentClick($event, message)
                                        "
                                    />
                                    <div
                                        v-if="message.isStreaming && isLoading"
                                        class="stream-cursor"
                                    ></div>
                                </div>
                            </div>
                        </div>
                        <div class="end-line gap" ref="endline"></div>
                    </div>
                    <!-- <div class="bot flx1 ac">
                       
                        <div
                            class="sw0424-mod1 flx1 ac"
                            style="flex: 1; margin-left: 13px"
                        >
                            <input
                                type="text"
                                placeholder="有问题？为什么不问问"
                                class="sw0424-inptxt1"
                                style="flex: 1; padding-left: 20px"
                                v-model="inputMessage"
                                @keydown.enter.exact.prevent="sendMessage"
                            />
                            <p
                                class="sw0424-ico3"
                                :class="{ pouse2: isLoading }"
                                @click="sendMessage"
                            ></p>
                        </div>
                    </div> -->

                    <div class="bot">
                        <div
                            class="sw0424-mod1"
                            style="
                                height: 134px;
                                padding: 15px 24px;
                                position: relative;
                                box-sizing: border-box;
                            "
                        >
                            <textarea
                                class="zy0708-textarea1"
                                placeholder="有问题？为什么不问问"
                                v-model="inputMessage"
                                @keydown.enter.exact.prevent="sendMessage"
                            ></textarea>
                            <div class="flx1 ac" style="gap: 0 10px">
                                <button class="zy0708-btn1 on">
                                    <i class="ic1"></i>知识检索库
                                </button>
                                <button class="zy0708-btn1">
                                    <i class="ic2"></i>联网检索
                                </button>
                            </div>
                            <div class="abs" style="bottom: 10px; right: 10px">
                                <p
                                    class="sw0424-ico3"
                                    :class="{ pouse2: isLoading }"
                                    @click="sendMessage"
                                ></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 文档弹窗 -->
        <div v-if="currentDocument" class="document-modal-overlay">
            <div class="document-modal">
                <div class="modal-header">
                    <h3>
                        {{ currentDocument.title || currentDocument.dataName }}
                    </h3>
                    <button
                        @click="currentDocument = null"
                        class="close-button"
                    >
                        &times;
                    </button>
                </div>
                <div
                    class="modal-content"
                    v-html="
                        renderMarkdown(cleanDocContent(currentDocument.content))
                    "
                ></div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    getCommonCodesFromCache,
    getSessionData,
    saveChat,
    getChatData
} from '@/api/api.js';
import { marked } from 'marked';

export default {
    data() {
        return {
            messages: [],
            inputMessage: '',
            isLoading: false,
            eventSource: null,
            sessionId: '',
            currentStreamMessage: null,
            expandedDocs: new Set(),
            collapsedThought: new Set(),

            modelList: [],
            currentModel: '',
            isMenuCollapsed: false,

            historyList: [],
            historyMessageId: '',
            historyMessages: [],

            // 当前弹窗显示的文档
            currentDocument: null
        };
    },
    watch: {
        messages: {
            deep: true,
            handler() {
                this.$nextTick(() => {
                    this.scrollToBottom();
                });
            }
        }
    },
    mounted() {
        document.title = '辐射大模型';
        /* shipei($('.sw0424-wrap')); */

        this.getCommonCodesFromCache();

        //测试
        // this.modelList = [{ DM: 'x0yqds3or8', DMMC: '辐射大模型' }];
        // this.tabChange({ DM: 'x0yqds3or8', DMMC: '辐射大模型' });

        //获取历史记录List
        this.getSessionData();
    },
    beforeUnmount() {
        // 组件销毁时关闭连接
        if (this.eventSource) {
            this.eventSource.close();
        }
    },
    methods: {
        //历史记录点击
        historyClick(item) {
            this.historyMessageId = item.XH;
            this.sessionId = item.HHJLBH || '';
            this.currentModel = item.ZSKBH;
            this.messages = [];
            this.historyMessages = [];
            this.collapsedThought = new Set();
            this.getChatData();
        },
        //开启新的会话
        newSession() {
            this.historyMessageId = '';
            this.sessionId = '';
            this.messages = [];
            this.historyMessages = [];
            this.collapsedThought = new Set();
        },
        //获取历史记录List
        getSessionData() {
            getSessionData({
                pageSize: 10,
                pageNum: 1
            }).then((res) => {
                this.historyList = res.data.list || [];
            });
        },
        //获取历史记录消息
        getChatData() {
            getChatData({
                HHBH: this.historyMessageId //会话编号
            }).then((res) => {
                this.historyMessages = res.data;
            });
        },
        /*
        保存当前对话
        id: 会话编号
        messages: 消息列表
        sessionId: 会话ID
        currentModel: 当前模型
        */
        saveChat(id, messages, sessionId, currentModel) {
            if (!messages.length) return;
            // 只取最后两条消息（用户问题和AI回答）
            const latestMessages = messages.slice(-2);

            //只有最后一条消息是AI回答的时候，并且AI回答的内容不为空或者有思考过程的时候，才保存
            if (
                latestMessages[1].role === 'assistant' &&
                (latestMessages[1].content || latestMessages[1].thoughts.length)
            ) {
                let messageStr = JSON.stringify(latestMessages);
                saveChat({
                    HHBH: id, // 新建会话不用传
                    HHJLBH: sessionId,
                    ZSKBH: currentModel, //对应index_id
                    NR: messageStr,
                    LX: 1
                }).then((res) => {
                    let data = res.data;
                    this.historyMessageId = data.HHBH;
                    // 保存成功后更新历史记录列表
                    this.getSessionData();
                });
            }
        },
        getCommonCodesFromCache() {
            getCommonCodesFromCache({ DMJBH: 'GPT_ZSK' }).then((res) => {
                this.modelList = res;
                this.tabChange(this.modelList[0]);
            });
        },
        tabChange(item) {
            //开启新的会话
            this.newSession();
            this.currentModel = item.DM;
        },
        async sendMessage() {
            if (this.isLoading) {
                this.endStreaming();
                return;
            }

            if (!this.inputMessage.trim()) return;

            // 添加用户消息
            this.messages.push({
                role: 'user',
                content: this.inputMessage.trim()
            });

            // 添加初始 AI 消息占位符
            this.currentStreamMessage = {
                role: 'assistant',
                content: '',
                isStreaming: true,
                thoughts: [], // 用于存储思考过程
                session_id: ''
            };
            this.messages.push(this.currentStreamMessage);

            let query = this.inputMessage.trim();

            this.inputMessage = '';
            this.isLoading = true;

            try {
                // const apiUrl = `http://8.146.200.186:8002/base/agent/qa/retrieve/stream?authorization_token=KS20BL2B.B0DD85D89694716CB0FC0BBD6A520454&index_id=${
                //     this.currentModel
                // }&session_id=${this.sessionId}&query=${encodeURIComponent(
                //     query
                // )}`;
                const apiUrl = `https://gdgpt.mgpark.cn/onlinePreview/gd/gpt/qa/stream?index_id=${
                    this.currentModel
                }&session_id=${this.sessionId}&query=${encodeURIComponent(
                    query
                )}`;
                // 创建 EventSource 连接
                this.eventSource = new EventSource(apiUrl);

                // 处理流式数据
                this.eventSource.onmessage = (event) => {
                    try {
                        const chunk = JSON.parse(JSON.parse(event.data));

                        if (chunk.status === 204) {
                            // 结束标记
                            this.endStreaming();
                            return;
                        }

                        if (chunk.status !== 200) return;

                        // ======== 增量更新逻辑 ========
                        // 1. 更新消息内容（增量追加）
                        if (chunk.data?.output) {
                            this.currentStreamMessage.content +=
                                chunk.data.output;
                        }

                        // 更新文档引用
                        // if (chunk.data.doc_reference) {
                        //     this.currentStreamMessage.doc_reference =
                        //         chunk.data.doc_reference;
                        // }

                        // 更新思考过程
                        if (chunk.data.thoughts) {
                            this.mergeThoughts(chunk.data.thoughts);
                        }

                        // 3. 更新会话 ID
                        if (chunk.data?.session_id) {
                            this.sessionId = chunk.data.session_id;

                            this.currentStreamMessage.session_id =
                                this.sessionId;
                        }
                    } catch (error) {
                        console.error('数据解析失败:', error);
                    }
                };

                // 处理错误
                this.eventSource.onerror = (error) => {
                    console.error('EventSource 错误:', error);
                    this.endStreaming();
                };

                // 处理流结束
                this.eventSource.addEventListener('done', () => {
                    this.endStreaming();
                });
            } catch (error) {
                console.error('连接失败:', error);
                this.endStreaming();
            }

            this.$nextTick(() => {
                this.scrollToBottom();
            });
        },
        // 安全滚动到底部
        scrollToBottom() {
            // 使用原生js实现滚动到底部 ，用endline元素作为锚点
            this.$nextTick(() => {
                const endline = this.$refs.endline;
                if (endline) {
                    endline.scrollIntoView({ behavior: 'smooth' });
                }
            });
        },
        // 合并思考过程
        mergeThoughts(newThoughts) {
            newThoughts.forEach((thought) => {
                const existing = this.currentStreamMessage.thoughts.find(
                    (t) =>
                        t.action === thought.action &&
                        t.action_type === thought.action_type
                );

                if (existing) {
                    // 合并现有条目
                    if (thought.response)
                        existing.response =
                            (existing.response || '') + thought.response;
                    if (thought.thought)
                        existing.thought =
                            (existing.thought || '') + thought.thought;
                } else {
                    // 添加新条目
                    this.currentStreamMessage.thoughts.push({
                        ...thought,
                        response: thought.response || '',
                        thought: thought.thought || ''
                    });
                }
            });
        },
        // 解析文档数据
        parseObservation(observation) {
            if (!observation) return [];
            try {
                const docs = JSON.parse(observation);
                return docs.map((doc) => ({
                    ...doc
                }));
            } catch (e) {
                console.error('文档解析失败:', e);
                return [];
            }
        },

        // 清理文档内容
        cleanDocContent(content) {
            // .replace(/【标题】:/g, '')
            return content
                .replace(/【文档名】:.*?\n/g, '')
                .replace(/【标题】:.*?\n/g, '')
                .replace(/【正文】:/g, '')
                .trim();
        },
        // 切换文档展开状态
        toggleDoc(doc) {
            let docId = doc.id || doc.dataId;
            if (this.expandedDocs.has(docId)) {
                this.expandedDocs.delete(docId);
            } else {
                this.expandedDocs.add(docId);
            }
        },
        // 切换思考过程展开状态
        toggleThought(thought, index, tIndex, type) {
            let key = `${type}-${thought.action_type}-${index}-${tIndex}`;
            if (this.collapsedThought.has(key)) {
                this.collapsedThought.delete(key);
            } else {
                this.collapsedThought.add(key);
            }
        },
        // 点击消息内容
        handleContentClick(event, message) {
            const target = event.target;
            if (target.classList.contains('doc-ref')) {
                event.preventDefault();
                const refIndex = parseInt(target.dataset.refIndex, 10);
                if (isNaN(refIndex)) return;

                // 从历史消息和当前消息中查找文档
                let allDocs = [];
                const thoughts = message.thoughts || [];

                thoughts.forEach((thought) => {
                    const docs = this.parseObservation(thought.observation);
                    // 为每个文档添加唯一的引用索引
                    docs.forEach((doc, i) => {
                        // 假设 parseObservation 已经处理了 referenceIndex
                        allDocs.push(doc);
                    });
                });

                // 查找具有匹配引用索引的文档
                const targetDoc = allDocs.find(
                    (doc) => doc.referenceIndex === refIndex
                );

                if (targetDoc) {
                    this.currentDocument = targetDoc;
                } else {
                    console.warn(`未找到引用索引为 ${refIndex} 的文档。`);
                }
            }
        },
        // 安全渲染Markdown
        renderMarkdown(content) {
            if (!content) return '';

            // 首先，使用 marked 将 markdown 转换为 HTML
            let html = marked(content || '');

            // 格式 2: <ref>[N],[M]</ref> -> [#N][#M]
            html = html.replace(/<ref>(.*?)<\/ref>/g, (match, innerContent) => {
                const refs = innerContent.match(/\[(\d+)\]/g) || [];
                return refs
                    .map((ref) => {
                        const num = ref.match(/\d+/)[0];
                        return `[#${num}]`;
                    })
                    .join('');
            });

            // 然后，将所有 [#N] 格式统一转换为可点击的 span
            html = html.replace(
                /\[#(\d+)\]/g,
                '<span class="doc-ref" data-ref-index="$1" title="点击查看引用文档">[$1]</span>'
            );

            return html;
        },
        buildQueryParams() {
            return new URLSearchParams({
                model: 'deepseek-chat',
                messages: JSON.stringify(this.messages.slice(0, -1)),
                session_id: this.sessionId || '',
                stream: true
            }).toString();
        },
        // 结束流式处理
        endStreaming() {
            //保存当前会话
            this.saveChat(
                this.historyMessageId,
                this.messages,
                this.sessionId,
                this.currentModel
            );
            this.isLoading = false;
            if (this.currentStreamMessage) {
                this.currentStreamMessage.isStreaming = false;
            }
            if (this.eventSource) {
                this.eventSource.close();
                this.eventSource = null;
            }
            this.scrollToBottom();
        },
        clearMsg() {
            this.endStreaming();
            this.messages = [];
            let cacheKey = `${this.currentModel}_msgList`;
            localStorage.removeItem(cacheKey);
            this.sessionId = '';
        }
    }
};
</script>

<style scoped lang="scss">
@import './css/reset.css';
@import './css/sw0424.css';
@import './css/zy0708.css';

.sw0424-rside .top {
    overflow-y: auto;
}
.nice-scroll {
    overflow-x: hidden;
    overflow-y: auto;
}
.sw0424-ulnav1 li + li {
    margin-top: 10px;
}
</style>

<style scoped lang="scss">
/* Markdown 渲染样式 */
.message-content ::v-deep * {
    line-height: 35px;
    font-size: 18px;
}

.message-content ::v-deep h1,
.message-content ::v-deep h2,
.message-content ::v-deep h3 {
    margin: 0.5em 0;
}

.message-content ::v-deep code {
    background: #f3f3f3;
    padding: 2px 4px;
    border-radius: 4px;
}

.message-content ::v-deep pre {
    background: #f8f8f8;
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
}

.message-content ::v-deep a {
    color: #007bff;
    text-decoration: none;
}

/* 文档引用标签 ref 的样式 */
.message-content ::v-deep .doc-ref {
    color: #007bff;
    /* 变成上标 */
    vertical-align: super;
    font-size: 0.8em;
}

/* 滚动容器样式 */
.message-list {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch; /* 启用惯性滚动 */
}

/* 自定义滚动条样式 */
.message-list::-webkit-scrollbar {
    width: 8px;
}

.message-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.message-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    &:hover {
        background: #a8a8a8;
    }
}

/* 消息样式优化 */
.message {
    margin: 15px 0;
    display: flex;
    flex-direction: column;
}

.message.user {
    align-items: flex-end;
    margin-left: auto;
}

.message.assistant {
    align-items: flex-start;
    margin-right: auto;
    width: 100%;
}

.message-content {
    padding: 12px 16px;
    border-radius: 12px;
    background: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.user .message-content {
    background: #007bff;
    color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.assistant .message-content {
    width: 95%;
}

/* 思考过程样式优化 */
.thoughts-container {
    margin: 12px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    width: 95%;
}

.thought-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.thought-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    justify-content: space-between;
    cursor: pointer;
}

.thought-header:hover {
    background: #f8f9fa;
}

.action-name {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    background: #e3f2fd;
    padding: 4px 8px;
    border-radius: 4px;
}

/* 文档引用样式优化 */
.reference-container {
    margin-top: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.reference-header {
    padding: 12px 16px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.reference-title {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

.reference-count {
    font-size: 12px;
    color: #666;
    background: #e3f2fd;
    padding: 2px 8px;
    border-radius: 12px;
}

.reference-item {
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.reference-item:hover {
    background: #f8f9fa;
}

.doc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.doc-title {
    color: #007bff;
    font-size: 14px;
    text-decoration: none;
    &:hover {
        text-decoration: underline;
    }
}
.doc-index {
    color: #999;
    margin-left: 5px;
    font-size: 14px;
    text-decoration: none;
}

.doc-content {
    font-size: 14px;
    color: #333;
    padding: 0;
    max-height: 0;
    overflow: hidden;
}

.doc-content ::v-deep * {
    line-height: 20px;
}

.doc-content.expanded {
    padding: 12px;
    max-height: 500px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-top: 8px;
}

/* 输入框样式优化 */
.sw0424-inptxt1 {
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 10px 20px;
    transition: all 0.3s ease;
    &:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
        outline: none;
    }
}

/* 发送按钮样式优化 */
.sw0424-ico3 {
    width: 32px;
    height: 32px;
    margin-left: 10px;
    cursor: pointer;
    background-size: 100% 100%;
    transition: transform 0.2s;
    &:hover {
        transform: scale(1.1);
    }
}

/* 流式输出光标动画优化 */
.stream-cursor {
    display: inline-block;
    width: 2px;
    height: 16px;
    background: currentColor;
    margin-left: 4px;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}

.sw0424-ico3.pouse2 {
    background: url(./images/stop1.png) no-repeat;
    background-size: 100% 100%;
}

.thought-content {
    font-size: 14px;
    color: #34495e;
    margin-top: 8px;
    line-height: 26px;
}

.thought-content ::v-deep * {
    line-height: 26px;
    font-size: 14px;
    color: #8b8b8b;
}

/* 文档弹窗样式 */
.document-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.document-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 70%;
    max-width: 800px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f7f7f7;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close-button {
    background: none;
    border: none;
    font-size: 28px;
    cursor: pointer;
    color: #888;
    padding: 0;
    line-height: 1;
}

.modal-content {
    padding: 24px;
    overflow-y: auto;
    line-height: 1.6;
}

.modal-content ::v-deep * {
    font-size: 16px;
    line-height: 30px;
}

/* 可点击的文档引用 */
::v-deep .doc-ref {
    color: #007bff;
    cursor: pointer;
    // text-decoration: underline;
    // font-weight: bold;
}

::v-deep .doc-ref:hover {
    color: #0056b3;
}
</style>
