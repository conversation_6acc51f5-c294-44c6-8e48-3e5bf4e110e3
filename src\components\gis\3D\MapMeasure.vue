<!-- @format -->

<!-- 地图测量: 高程数据的加载，会影响测量工具，现在把高程数据加载延时了1秒，可以解决这个问题 -->
<template>
    <div style="position: relative">
        <ul class="zy-tools-gis">
            <li @click="btnClick">
                <i class="tool-measure"></i>
            </li>
        </ul>
        <div
            class="map-measure"
            v-show="show"
            :class="{ left: dock == 'left' }"
        >
            <div
                class="point DTool"
                title="绘制点"
                @click="toolbtnClick('点')"
            ></div>
            <div
                class="line DTool"
                title="绘制线"
                @click="toolbtnClick('线')"
            ></div>
            <div
                class="polygon DTool"
                title="绘制多边形"
                @click="toolbtnClick('多边形')"
            ></div>
            <div
                class="extent DTool"
                title="绘制长方形"
                @click="toolbtnClick('长方形')"
            ></div>
            <!-- <div
                class="circle DTool"
                title="绘制圆"
                @click="toolbtnClick('圆')"
            ></div> -->
            <div
                class="clear DTool"
                @click="toolbtnClick('clear')"
                title="清除绘图"
            ></div>
        </div>
    </div>
</template>

<script>
import * as turf from '@turf/turf';
import DrawRectangle from './utils/DrawRectangle';
// import {
//     CircleMode,
//     DragCircleMode,
//     DirectMode,
//     SimpleSelectMode
// } from 'mapbox-gl-draw-circle';

export default {
    data() {
        return {
            show: false,
            draw: null,
            activeType: ''
        };
    },
    props: ['map', 'dock'],
    components: {},
    mounted() {
        setTimeout(() => {
            this.initPage();
        }, 1000);
    },
    methods: {
        initPage() {
            if (this.map && MapboxDraw) {
                const modes = MapboxDraw.modes;
                modes.draw_rectangle = DrawRectangle;

                this.draw = new MapboxDraw({
                    // 是否显示默认控件
                    displayControlsDefault: false,
                    // 选择哪些控件显示在地图上
                    // controls: {
                    //   polygon: true,
                    //   trash: true,
                    //   point:true,
                    //   line_string:true,
                    //   combine_features:false,
                    //   uncombine_features:false
                    // },

                    // modes: {
                    //     ...MapboxDraw.modes,
                    //     draw_circle: CircleMode,
                    //     drag_circle: DragCircleMode,
                    //     direct_select: DirectMode,
                    //     simple_select: SimpleSelectMode
                    // },

                    //默认模式: simple_select, direct_select, draw_point, draw_line_string, draw_polygon
                    defaultMode: 'simple_select'
                });
                this.map.addControl(this.draw);

                this.map.on('draw.create', this.drawHandle);
                this.map.on('draw.delete', this.drawHandle);
                this.map.on('draw.update', this.drawHandle);
            } else {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
            }
        },

        btnClick() {
            this.show = !this.show;
        },
        toolbtnClick(type) {
            // this.map.triggerRepaint();
            // this.map.zoomIn();
            // this.map.zoomOut();

            this.map.resize();
            switch (type) {
                case '多边形':
                    this.draw.changeMode('draw_polygon');
                    break;

                case '线':
                    this.draw.changeMode('draw_line_string');
                    break;

                case '点':
                    this.draw.changeMode('draw_point');
                    break;

                case '长方形':
                    this.draw.changeMode('draw_rectangle');
                    break;

                case '圆':
                    this.draw.changeMode('draw_circle', {
                        initialRadiusInKm: 0.5
                    });
                    break;

                case 'clear':
                    this.draw.deleteAll();
                    this.clearMeasureText();
                    break;

                default:
                    break;
            }

            this.activeType = type;
        },

        drawHandle() {
            if (this.activeType == '线') {
                this.updateLine();
            } else if (
                this.activeType == '多边形' ||
                this.activeType == '长方形'
            ) {
                this.updateArea();
            }
        },

        updateLine(e) {
            const data = this.draw.getAll();

            if (data.features.length > 0) {
                let data1 = {
                    type: 'FeatureCollection',
                    features: [data.features[data.features.length - 1]]
                };

                const line = turf.length(data1, { units: 'kilometers' });
                const lastPoint = turf.coordAll(data1);
                // 测量的距离（千米）
                const rounded_line = line.toFixed(3);
                this.addMeasureText(lastPoint.at(-1), `${rounded_line}千米`);
            } else {
                // if (e.type !== 'draw.delete')
                //     message.info('请点击地图以绘制线段');
            }
        },

        updateArea(e) {
            const data = this.draw.getAll();

            if (data.features.length > 0) {
                let data1 = {
                    type: 'FeatureCollection',
                    features: [data.features[data.features.length - 1]]
                };
                const area = turf.area(data1);
                const center = turf.getCoord(turf.centroid(data1));

                this.addMeasureText(
                    center,
                    `${(area / 1000000).toFixed(3)}平方千米`
                );
            } else {
                // if (e.type !== 'draw.delete')
                //     message.info('请点击地图以绘制面');
            }
        },

        //添加注记
        addMeasureText(coordinates, properties) {
            let source = this.map.getSource('measureText');
            if (!source) {
                this.map.addSource('measureText', {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: [
                            {
                                type: 'Feature',
                                geometry: {
                                    type: 'Point',
                                    coordinates
                                },
                                properties: {
                                    Name: properties
                                }
                            }
                        ]
                    }
                });
                this.map.addLayer({
                    id: 'measureTextLabel',
                    type: 'symbol',
                    source: 'measureText',
                    layout: {
                        visibility: 'visible',
                        'text-field': ['get', 'Name'],
                        'text-justify': 'right',
                        // 'text-offset': [3, 0],
                        'text-size': 15,
                        'text-font': ['Microsoft YaHei Regular'],
                        'text-allow-overlap': true //显示全部要素
                    },
                    paint: {
                        'text-color': '#e73f32',
                        'text-halo-width': 2,
                        'text-halo-color': '#ffffff',
                        'text-translate': [50, 0]
                    }
                });
            } else {
                let datas = source._data.features;
                datas.push({
                    type: 'Feature',
                    geometry: {
                        type: 'Point',
                        coordinates
                    },
                    properties: {
                        Name: properties
                    }
                });

                source.setData({
                    type: 'FeatureCollection',
                    features: datas
                });
            }
        },

        clearMeasureText() {
            if (this.map.getSource('measureText')) {
                this.map.removeLayer('measureTextLabel');
                this.map.removeSource('measureText');
            }
        }
    },
    watch: {}
};
</script>

<style scoped>
.map-measure {
    position: absolute;
    top: 0px;
    width: 200px;
    left: -200px;
}

.map-measure.left {
    left: 20px;
}
</style>
