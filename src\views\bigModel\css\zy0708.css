.sw0424-lside {
    position: relative;
    z-index: 99;
    transition: all .3s;
}

.sw0424-lside .wrapper {

    transition: all .3s;
}

.sw0424-lside.off {
    width: 0;
}

.sw0424-lside.off .wrapper {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sw0424-lside.off .lside-toggle img {
    transform: rotate(180deg);
}

.lside-toggle {
    position: absolute;
    top: 50%;
    right: -36px;
    transform: translateY(-50%);
    width: 36px;
    height: 108px;
    background-color: #ccc;
    background: url(../images/zy0708_toggle.png) no-repeat center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.lside-toggle img {
    transition: all .3s;
}

.zy0708-textarea1 {
    font-size: 16px;
    color: #3d3d3d;
    line-height: 24px;
    height: 72px;
    width: 100%;
}

.zy0708-btn1 {
    height: 32px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid #CBCBCB;
    border-radius: 16px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    padding: 0 14px;
}

.zy0708-btn1 i {
    width: 16px;
    height: 16px;
    margin-right: 6px;
}

.zy0708-btn1 .ic1 {
    background: url(../images/zy0708_ic_jiansuo.png) no-repeat center;
    margin-right: 8px;
}

.zy0708-btn1 .ic2 {
    background: url(../images/zy0708_ic_web.png) no-repeat center;
}

.zy0708-btn1.on {
    color: #3980C5;
    border-color: #3980C5;
    background: #EAF5FF;
}

.zy0708-btn1.on .ic1 {
    background-image: url(../images/zy0708_ic_jiansuo_on.png);
}

.zy0708-btn1.on .ic2 {
    background-image: url(../images/zy0708_ic_web_on.png);
}