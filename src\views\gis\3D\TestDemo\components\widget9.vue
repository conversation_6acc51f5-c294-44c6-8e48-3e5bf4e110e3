<!-- @format -->
<!-- echart 转移图 -->
<template>
    <div></div>
</template>

<script>
import PointUtil from '../utils/PointUtil';
export default {
    props: [],
    data() {
        return {
            map: null,
            pointUtil: null,
            layerID: '热力图'
        };
    },

    unmounted() {
        this.clear();
    },

    mounted() {
        this.initPage();
    },

    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.addLayer();
        },

        addLayer() {
            //
        },

        clear() {
            //
        },

        pointClickHandle() {}
    }
};
</script>

<style scoped></style>
