/**
 * /*
 *
 * @format
 * @Author: Caijw
 * @LastEditors: Caijw
 * @Description: echarts数据转换工具类
 * @Date: 2019-02-12 08:53:24
 * @LastEditTime: 2019-02-12 08:53:24
 */

import util from './util';

const getValue = function (obj, key) {
    if (typeof key == 'string') {
        return obj[key];
    } else if (typeof key == 'function') {
        return key(obj);
    }
    return '';
};

export default new (class echartsUtils {
    // 创建线形图，柱状图，堆叠图，热力图数据转换工具
    // data = [[{year: '2019',month:'1', yll: 81, dbl: 61},{year: '2019',month:'2', yll: 70, dbl: 72},],[{year: '2018',month:'1', yll: 80, dbl: 71},{year: '2018',month:'2', yll: 80, dbl: 71}]]
    // createLineBarOptionDefault(data, {
    //     x: 'month',  // x（y）轴的字段
    //     name: 'year',  // legend标题字段
    //     data: 'yll'  // x（y）轴对应的数据字段
    // })
    createLineBarOptionDefault(data, opt) {
        // 如果有数据的话，取第一个，然后转换为数组，否则为空数组，也可以传一个回调函数进来
        let xAxis = [];
        if (typeof opt.x == 'string') {
            xAxis = data.length > 0 ? data[0].map((d) => d[opt.x]) : [];
        } else if (typeof opt.x == 'function') {
            xAxis = data.length > 0 ? data[0].map((d) => opt.x(d)) : [];
        }
        let series = data.map((datum) => {
            return {
                name: datum[0][opt.name],
                data: datum.map((i) => i[opt.data])
            };
        });
        return {
            xAxis: xAxis,
            series: series
        };
    }

    createLineBarOption(data, opt) {
        let xAxis = [];
        let series = [];
        let seriesObj = {};
        let isMulti = opt.seriesKey.constructor === Array; //是否多个系列
        if (isMulti && opt.sameSeriesKey) {
            //多系列格式3
            opt.seriesKey_ = function (v) {
                //debugger;
                let arr = opt.seriesKey.filter((obj) => {
                    return obj.name === v[opt.sameSeriesKey];
                });
                if (arr[0]) {
                    let key = arr[0].key;
                    return v[key];
                }

                return '';
            };
            //opt.seriesKey = opt.seriesKey_;
        } else if (!isMulti && opt.sameSeriesKey) {
            //多系列格式2
        } else if (isMulti) {
            //多系列格式1
            opt.seriesKey.forEach((obj) => {
                let seriesName = opt.useAirSub
                    ? util.replacePltName(obj.name) //替换空气监测因子名称中的数字为下角标数字
                    : obj.name;
                series.push({
                    name: seriesName,
                    data: []
                });
            });
        } else {
            //格式1
            series.push({ data: [] });
        }

        data.forEach((v) => {
            if (opt.sameSeriesKey) {
                //多系列格式2 和 3
                let x = getValue(v, opt.x);
                let idx = xAxis.indexOf(x);
                if (xAxis.indexOf(x) === -1) {
                    xAxis.push(x);
                    idx = xAxis.length - 1;
                }
                let seriesName = v[opt.sameSeriesKey];
                seriesName = opt.useAirSub
                    ? util.replacePltName(seriesName) //替换空气监测因子名称中的数字为下角标数字
                    : seriesName;
                if (!seriesObj[seriesName]) {
                    seriesObj[seriesName] = [];
                }
                if (isMulti) {
                    //多系列格式3
                    seriesObj[seriesName][idx] = getValue(v, opt.seriesKey_);
                } else {
                    seriesObj[seriesName][idx] = getValue(v, opt.seriesKey);
                }
            } else if (isMulti) {
                //多系列格式1
                xAxis.push(getValue(v, opt.x));
                opt.seriesKey.forEach((obj, j) => {
                    let value = getValue(v, obj.key);
                    series[j].data.push(value);
                });
            } else {
                //单系列基础格式
                xAxis.push(getValue(v, opt.x));
                series[0].data.push(getValue(v, opt.seriesKey));
            }
        });

        if (opt.sameSeriesKey) {
            //多系列格式2 和 3
            for (let key in seriesObj) {
                series.push({
                    name: key,
                    data: seriesObj[key]
                });
            }
        }

        return {
            xAxis,
            series
        };
    }

    // 创建饼图数据转换工具
    createPieOption(data, opt) {
        let res = data.map((item) => {
            return {
                name: item[opt.name],
                value: item[opt.data]
            };
        });
        return res;
    }

    // 雷达图数据转换工具
    createRadarOption(data, opt) {
        let res = this.createLineBarOption(data, opt);
        return {
            indicator: res.xAxis,
            series: res.series.map((datum) => {
                return {
                    name: datum.name,
                    value: datum.data
                };
            })
        };
    }

    // 创建圆柱型数据转换工具
    createCircularOption(data, opt) {
        return {
            xAxis: data.map((datum) => datum[opt.x]),
            data: data.map((datum) => datum[opt.data])
        };
    }

    // 条纹图显示序号
    createPieRankOption(data, opt) {
        let res = data.map((item) => {
            return {
                name: item[opt.name],
                value: item[opt.data],
                rank: item[opt.rank]
            };
        });
        return res;
    }
})();
