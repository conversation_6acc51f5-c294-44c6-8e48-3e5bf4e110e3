<!-- @format -->

<template>
    <div style="width: 250px">
        <div class="pd-modbd">
            <div class="gap"></div>
            <ul class="pd-ulbx5">
                <li>
                    <p-chart
                        style="
                            width: 300;
                            height: 250px;
                            position: relative;
                            top: 20px;
                        "
                        :option="option"
                    ></p-chart>

                    <p>
                        目标值&emsp;{{ data.YLLMBZ }}%

                        <em class="yltb_dbqk" :class="{ cb: !data.YLLSFDB }">{{
                            data.YLLSFDB ? '达标' : '超标'
                        }}</em>
                    </p>
                    <p>同期&emsp;&emsp;{{ data.lastgoodRate }}%</p>
                </li>
            </ul>
        </div>
    </div>
</template>
<script>
export default {
    name: 'airDaysAnalysis',
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    value: 0,
                    YLLMBZ: 0,
                    YLLSFDB: 0,
                    lastgoodRate: 0
                };
            }
        }
    },
    data() {
        return {
            option: {
                tooltip: {
                    formatter: '{a} <br/>{b} : {c}%'
                },

                grid: {
                    top: 20,
                    bottom: 0
                },

                series: [
                    {
                        name: '外部线',
                        type: 'gauge',
                        radius: '100%', // 动态
                        startAngle: 225,
                        endAngle: -45,
                        axisLine: {
                            lineStyle: {
                                color: [
                                    [1, '#31F3FF'] // 动态
                                ],
                                width: 1
                            }
                        },
                        axisLabel: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: false
                        },
                        detail: {
                            show: false
                        },
                        title: {
                            //标题
                            show: false
                        }
                    },
                    {
                        name: '外部刻度',
                        type: 'gauge',
                        radius: '98%',
                        min: 0, //最小刻度
                        max: 100, //最大刻度
                        splitNumber: 10, //刻度数量
                        startAngle: 225,
                        endAngle: -45,
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: [[1, 'rgba(0,0,0,0)']]
                            }
                        }, //仪表盘轴线
                        axisLabel: {
                            show: true,
                            color: '#31F3FF',
                            fontSize: 16, // 动态
                            distance: -20 // 动态
                        }, //刻度标签。
                        axisTick: {
                            show: false
                        }, //刻度样式
                        splitLine: {
                            show: false
                        }
                    },
                    {
                        name: '内部宽线条',
                        type: 'gauge',
                        radius: '80%',
                        startAngle: 225,
                        endAngle: -45,
                        axisLine: {
                            lineStyle: {
                                color: [[1, '#122B3C']],
                                width: 40
                            }
                        },
                        axisLabel: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: false
                        },
                        detail: {
                            show: false
                        },
                        title: {
                            show: false
                        }
                    },
                    {
                        name: '内部细线条',
                        type: 'gauge',
                        radius: '55%',
                        startAngle: 225,
                        endAngle: -45,
                        axisLine: {
                            lineStyle: {
                                color: [[1, '#122B3C']],
                                width: 3
                            }
                        },
                        axisLabel: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: false
                        },
                        detail: {
                            show: false
                        },
                        title: {
                            show: false
                        }
                    },
                    {
                        name: '间隔条形',
                        type: 'gauge',
                        radius: '87%',
                        z: 4,
                        splitNumber: 35,
                        startAngle: 225,
                        endAngle: -45,
                        axisLine: {
                            lineStyle: {
                                opacity: 0
                            }
                        },
                        axisLabel: {
                            show: false
                        },
                        axisTick: {
                            show: true,
                            length: 20,
                            splitNumber: 1,
                            lineStyle: {
                                color: '#122B3C',
                                width: 1
                            }
                        },
                        splitLine: {
                            show: false
                        },
                        detail: {
                            show: false
                        },
                        title: {
                            show: false
                        }
                    },
                    {
                        name: '数据',
                        type: 'gauge',
                        radius: '72%',
                        z: 3,
                        startAngle: 225,
                        max: 100,
                        endAngle: -45,
                        axisLine: {
                            lineStyle: {
                                color: [
                                    [0 / 100, '#31F3FF'], // 动态  dataArr
                                    [1, '#185363']
                                ],
                                width: 20
                            }
                        },
                        tooltip: {
                            show: false
                        },
                        axisLabel: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: false
                        },
                        detail: {
                            color: '#31F3FF',
                            show: true,
                            fontWeight: 'bold',
                            fontSize: 28
                        },
                        pointer: {
                            show: true,
                            width: 3
                        },
                        data: [
                            {
                                name: '',
                                value: 0 // dataArr
                            }
                        ]
                    },
                    // 内圆
                    {
                        name: '内圆环',
                        type: 'pie',
                        radius: ['4%', '2%'],
                        hoverAnimation: false,
                        tooltip: {
                            show: false
                        },
                        cursor: 'default',
                        labelLine: {
                            normal: {
                                show: false
                            }
                        },
                        itemStyle: {
                            color: '#122B3C'
                        },
                        animation: false,
                        data: [1]
                    },
                    // 内圆
                    {
                        name: '内圆环2',
                        type: 'pie',
                        radius: '2%',
                        hoverAnimation: false,
                        cursor: 'default',
                        tooltip: {
                            show: false
                        },
                        labelLine: {
                            normal: {
                                show: false
                            }
                        },
                        itemStyle: {
                            color: '#31F3FF'
                        },
                        animation: false,
                        data: [1]
                    }
                ]
            }
            // option: {
            //     tooltip: {
            //         formatter: '{b} : {c}%'
            //     },
            //     toolbox: { show: false },
            //     grid: {
            //         bottom: 10
            //     },
            //     series: [
            //         {
            //             name: '优良率考核',
            //             type: 'gauge',
            //             radius: '100%',

            //             startAngle: 225,
            //             endAngle: -45,
            //             detail: {
            //                 formatter: '{value}%',
            //                 fontSize: 18,
            //                 offsetCenter: [0, '75%']
            //             },
            //             axisLine: {
            //                 // 坐标轴线
            //                 lineStyle: {
            //                     // 属性lineStyle控制线条样式
            //                     length: 1,
            //                     width: 8,
            //                     color: [
            //                         [0.3, '#DD3F36'],
            //                         [0.7, '#FAE521'],
            //                         [1, '#37B70C']
            //                     ]
            //                 }
            //             },
            //             splitLine: {
            //                 show: false
            //             },
            //             axisLabel: {
            //                 distance: -20,
            //                 textStyle: {
            //                     color: '#FFF',
            //                     fontSize: 10
            //                 },
            //                 formatter: function (e) {
            //                     return e;
            //                 }
            //             },
            //             data: [{ value: 0 }]
            //         }
            //     ]
            // }
        };
    },
    watch: {
        data: 'getData'
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            console.log(this.data.value);
            console.log(this.option);
            this.option.series[5].axisLine.lineStyle.color[0] = [
                this.data.value / 100,
                '#31F3FF'
            ];

            this.option.series[5].data[0].value = this.data.value;
        }
    }
};
</script>
<style scoped>
.yltb_dbqk {
    display: inline-block;
    width: 50px;
    height: 22px;
    line-height: 22px;
    background-color: #25bc5d;
    border-radius: 10px;
    /* color: #000; */
    text-align: center;
}

.yltb_dbqk.cb {
    background-color: #25bc5d;
    /* color: #fff; */
}
.pd-ulbx5 li p {
    font-size: 14px;
    /* color: #fff; */
    padding-top: 5px;
    text-align: justify;
    padding-left: 50px;
}
li {
    list-style: none;
}
ul,
ol {
    padding: 0;
}
</style>
