<!-- @format -->

<template>
    <div>
        <BowoTableView
            :config="tableConfig"
            :styleConfig="styleConfig"
            :columns="columns"
            :tableData="tableData"
            :setOrder="handleSetOrder"
            style="margin-bottom: 20px"
        />
        <!-- 分页 -->
        <!-- eslint-disable-next-line vue/no-v-model-argument -->
        <BowoPagination :total="total" v-model:pagination="pagination" />
    </div>
</template>

<script>
import BowoTableView from './BowoTableView.vue';
import BowoPagination from './BowoPagination.vue';
export default {
    components: {
        BowoTableView,
        BowoPagination
    },
    props: {
        styleConfig: Object,
        // 接口函数(需返回Promise实例)
        api: {
            type: Function,
            require: true
        },
        // 是否自动请求数据
        isAutoLoad: {
            type: Boolean,
            default: true
        },
        // 接口额外请求参数
        extraParams: {
            type: Object,
            default() {
                return {};
            }
        },
        // table配置项
        config: {
            type: Object,
            default() {
                return {};
            }
        },
        // table列配置
        columns: {
            type: Array,
            default() {
                return [];
            }
        },
        // 是否使用自增序号
        useIncreOrder: Boolean,
        // 手动处理返回的数据
        handleRes: Function
    },

    computed: {
        tableConfig() {
            const { useIncreOrder } = this;
            const defaultConfig = {
                maxHeight: '540px', // 最大高度，超过滚动
                showOrder: false, // 是否显示序号
                showCheckbox: false // 是否显示勾选框
            };
            return Object.assign({}, defaultConfig, this.config, {
                showOrder: useIncreOrder // 是否显示序号
            });
        }
    },

    data() {
        return {
            tableData: [],
            pagination: {
                currentPage: 1,
                pageSize: 10
            },
            total: 0,
            loading: false
        };
    },
    watch: {
        pagination: {
            handler(nv) {
                this.getListData();
            },
            deep: true
        }
    },
    mounted() {
        this.initData();
    },
    methods: {
        initData() {
            if (!this.isAutoLoad) {
                return;
            }
            this.getListData();
        },

        // 获取列表数据
        getListData() {
            if (this.loading) return;
            this.loading = true;
            const { api, extraParams, pagination, handleRes } = this;
            api({ ...pagination, ...extraParams }).then((res) => {
                if (handleRes) {
                    handleRes(this, res);
                    return;
                }
                this.loading = false;
                this.tableData = res.data.list;
                this.total = res.data.total;
            });
        },
        //处理序号 前提条件：tableConfig.showOrder为true
        handleSetOrder(val) {
            let order = val;
            const { useIncreOrder } = this;
            const { currentPage, pageSize } = this.pagination;
            if (useIncreOrder) {
                order = (currentPage - 1) * pageSize + val;
            }
            return order;
        },
        reset() {
            this.pagination = {
                pageNum: 1,
                pageSize: 10
            };
            this.total = 0;
            this.tableData = [];
        }
    }
};
</script>

<style></style>
