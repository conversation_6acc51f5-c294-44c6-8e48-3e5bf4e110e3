<!-- @format -->

<!-- 城市空气质量历史数据 -->

<template>
    <div>
        <div class="day-quality-content">
            <div
                class="day-quality-aqi"
                :style="{ 'background-color': data.backgroundColor }"
            >
                <p class="label">
                    <span>-</span>
                    <PollutantName class="label" :name="pollutant" />
                    <span>-</span>
                </p>
                <p class="value">
                    {{ aqi }}
                </p>
                <p class="level">{{ data.data.quality || '-' }}</p>
            </div>
            <div class="day-quality-detail">
                <p class="city-day">
                    <span
                        class="city"
                        :style="{ 'background-image': `url(${iconLocation})` }"
                        >{{ data.data.city || '-' }}</span
                    >
                    <span class="day">{{ data.date }}</span>
                </p>
                <p class="primary">
                    首要污染物:
                    <PollutantName :name="primary" :show="show" />
                </p>
                <div class="pollutant">
                    <div
                        v-for="name in pollutants"
                        :key="name.id"
                        class="pullutan-list"
                    >
                        <PollutantName :name="name" class="name" />
                        <span class="value">{{
                            values[name.replace(/\./g, '_').toLowerCase()]
                        }}</span>
                    </div>
                </div>
                <div class="unit">
                    <div>单位: μg/m³（CO: mg/m³）</div>
                </div>
            </div>
        </div>
        <!-- <PollutantNames class="desc" :name="desc || '-'" /> -->
    </div>
</template>

<script>
import iconLocation from './images/i-location.png';
import PollutantName from './PollutantName.vue';
export default {
    name: 'AirQualityCard',
    components: { PollutantName },
    props: {
        // cityCode: String,
        // date: String,
        pollutant: {
            type: String,
            default: 'AQI'
        },
        show: {
            type: Boolean,
            default: false
        },
        data: {
            type: Object
        }
    },
    data() {
        return {
            iconLocation,
            pollutants: ['PM2.5', 'PM10', 'O3', 'SO2', 'NO2', 'CO'],
            values: this.data || {
                city: null,
                aqi: 0,
                quality: '优',
                pubtime: null,
                pollutions: null,
                pm2_5: 0,
                pm10: 0,
                o3: 0,
                so2: 0,
                no2: 0,
                co: 0
            },
            statistic: null,
            loaded: false
        };
    },
    watch: {
        cityCode(value) {
            if (value) {
                this.loadData();
                this.loadAccumulationData();
            }
        },
        date(value) {
            if (value) {
                this.loadData();
                this.loadAccumulationData();
            }
        },
        data(value) {
            value && Object.assign(this.values, value);
        }
    },
    computed: {
        // eslint-disable-next-line vue/return-in-computed-property
        aqi() {
            switch (this.pollutant) {
                case 'AQI':
                    return this.data.data.aqi;
                case 'PM2.5':
                    return this.data.data.pm2_5;
                case 'PM10':
                    return this.data.data.pm10;
                case 'O3':
                    return this.data.data.o3;
                case 'SO2':
                    return this.data.data.so2;
                case 'NO2':
                    return this.data.data.no2;
                case 'CO':
                    return this.data.data.co;
            }
        },
        primary() {
            let p = this.data.data.pollutions;
            if (p) {
                p = p.replace(/[\u4e00-\u9fa5()]/g, '');
                p = p.replace(/_/g, '.');
            }
            return p || this.data.data.maxItem || '无';
        }
    },
    mounted() {
        this.getList();
        if (!this.data && this.cityCode && this.date && this.autoload) {
            // this.loadData();
        } else {
            this.loaded = true;
        }
        // this.loadAccumulationData();
    },
    methods: {
        getList() {
            this.values = this.data.data;
        }
    }
};
</script>

<style lang="scss" scoped>
.day-quality-content {
    width: 100%;
    display: flex;
}
.day-quality-aqi {
    flex: 1;
    min-width: 104px;
    //   height: 130px;
    position: relative;
    background-color: #24bd5d;
    text-align: center;
    color: white;
    display: inline-flex;
    align-items: center;
    justify-content: space-evenly;
    flex-direction: column;

    .label {
        font-size: 13px;

        span {
            display: inline-block;
            //       width: 10px;
        }
    }
    .value {
        font-size: 33px;
        line-height: 25px;
    }
    .level {
        border-radius: 9px;
        min-width: 26px;
        padding: 3px 6px;
        background: rgba(255, 255, 255, 0.3);
        line-height: 12px;
        font-size: 13px;
    }
}
.day-quality-detail {
    position: relative;
    flex: 3;
    padding: 10px 10px;
    border-bottom: 1px solid rgba(221, 221, 221, 0.3);
    .city-day {
        display: flex;
        align-items: center;
    }
    .city {
        flex: 1;
        color: #333;
        font-size: 16px;
        background-position-y: center;
        background-size: 10px 15px;
        background-repeat: no-repeat;
        padding-left: 17px;
    }
    .day {
        text-align: right;
        color: #999;
        font-size: 13px;
    }
    .primary {
        color: #666;
        font-size: 15px;
        margin: 8px 0 8px 17px;
    }
    .pollutant {
        //     position: absolute;
        left: 18px;
        right: 10px;
        bottom: 10px;
        top: auto;
        display: inline-grid;
        display: -ms-grid;
        grid-template-columns: 100px 100px 80px;
        -ms-grid-columns: 100px 100px 80px;
        line-height: 20px;
        .name {
            color: #999;
            font-size: 14px;
        }
        .value {
            color: #333;
            font-size: 14px;
            text-align: left;
            width: 50px;
            margin-left: auto;
        }
    }
}
.desc {
    padding: 13px 17px 18px 18px;
    p::before {
        content: '\25CF';
        color: #167cf2;
        position: absolute;
        left: 13px;
    }
    p {
        padding-left: 8px;
        font-size: 15px;
        color: #999;
    }
}

.pullutan-list {
    display: flex;
    //   justify-content: space-evenly;
}

.unit {
    position: relative;
    left: 110px;
}
</style>
