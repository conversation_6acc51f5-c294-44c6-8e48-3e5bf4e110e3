<!-- @format -->

<template>
    <div class="align-center-layout">
        <span
            :style="{
                'font-size': dayFontSize > 20 ? 14 + 'px' : dayFontSize + 'px',
                'padding-top': '6px',
                color: notThisMonth
                    ? '#999'
                    : theme === 'light'
                    ? '#333'
                    : '#fff'
            }"
            >{{ day }}</span
        >
        <div class="align-center-layout tile-quality" :style="tileValueStyle">
            <!--  -->
            <div
                ref="buttonRef"
                v-click-outside="onClickOutside"
                class="date-view"
                @click="choseview()"
                :style="{
                    'background-color': backgroundColor,
                    'box-shadow':
                        check && !notThisMonth
                            ? `0 0 0 3px ${rgbaColor()}`
                            : 'none',
                    cursor: notThisMonth ? 'default' : 'pointer',
                    'justify-content': 'center',

                    width: tileWidth + 'px',
                    height: tileHeight + 'px'
                }"
                :class="{ 'not-this-month': notThisMonth }"
            >
                <!-- 'justify-content': pollutants ? 'center' : 'none', -->
                <span
                    class="day"
                    :style="{
                        'margin-top': dayheight
                    }"
                    >{{ notThisMonth ? '-' : aqi }}</span
                >
                <div class="wrapper" v-if="!notThisMonth && pollutants">
                    <PollutantName
                        :name="pollutants"
                        :fontSize="pullutantfonSzie"
                        :newLine="true"
                        class="calendarpollutant"
                    />
                </div>
            </div>
            <el-popover
                style="width: auto"
                v-if="!notThisMonth"
                ref="popoverRef"
                :virtual-ref="buttonRef"
                trigger="click"
                virtual-triggering
                placement="bottom"
                popper-class="canlendar-popover"
            >
                <transition name="bounce">
                    <CalendarDialog
                        :data="showdata"
                        :pollutant="pollutant"
                        :show="show"
                        v-show="show"
                        @hideModal="showDialog"
                    />
                </transition>
            </el-popover>
        </div>
    </div>
</template>

<script setup>
import { ref, unref } from 'vue';
import { ClickOutside as vClickOutside } from 'element-plus';
const buttonRef = ref();
const popoverRef = ref();
const onClickOutside = () => {
    unref(popoverRef).popperRef?.delayHide?.();
};
</script>

<script>
import { hex2rgba } from './utils/color';
import CalendarDialog from './CalendarDialog.vue';
import PollutantName from './PollutantName.vue';
export default {
    data() {
        return {
            show: false,
            pullutantfonSzie: '10px',
            showdata: {},
            theme: window.localStorage.getItem('themeType') || 'light'
        };
    },
    props: {
        day: [Number],
        color: String,
        data: null,
        year: Number,
        month: Number,
        dayFontSize: Number,
        currentMonth: Number,
        pollutant: String,
        tileHeight: Number,
        tileWidth: Number,
        noMonthShow: {
            type: Boolean,
            default: true
        }
    },
    components: {
        PollutantName,
        CalendarDialog
    },

    watch: {
        show(val) {
            if (this.show === true) {
                return !val;
            } else {
                this;
            }
        }
    },

    computed: {
        tileValueStyle: function () {
            return {
                background: this.color
            };
        },

        //非本月的日期块将不显示
        notThisMonth() {
            // console.log(this.month !== this.currentMonth);
            if (this.month != this.currentMonth) {
                // console.log(this.noMonthShow);
                // if (this.noMonthShow == false) {
                //     return true;
                // } else {
                //     return false;
                // }
                return true;
            } else {
                return false;
            }
        },

        //check为点击效果的计算，只有当true的时候才能够正常实现点击效果
        check() {
            if (this.data) {
                let date = `${this.year}-${this.alignNumStr(
                    this.month
                )}-${this.alignNumStr(this.day)}`;
                if (this.data[date] === undefined) {
                    return true;
                } else {
                    return this.data[date].check;
                }
            } else {
                return false;
            }
        },

        //aqi计算，将传入的数据与对应的日期能够对上，这里的year,month,day与this.data中的值将能够对上
        // eslint-disable-next-line vue/return-in-computed-property
        aqi: function () {
            if (this.data) {
                let date = `${this.year}-${this.alignNumStr(
                    this.month
                )}-${this.alignNumStr(this.day)}`;
                if (this.data[date] === undefined) {
                    return '--';
                } else {
                    if (this.data[date].data === undefined) {
                        return '--';
                    } else {
                        //判断进入的污染物是什么？对应的展示数值 ,pollutant指的便是用户选择的污染物
                        switch (this.pollutant) {
                            case 'AQI':
                                return this.data[date].data.aqi;
                            case 'PM2.5':
                                return this.data[date].data.pm2_5;
                            case 'PM10':
                                return this.data[date].data.pm10;
                            case 'O3':
                                return this.data[date].data.o3;
                            case 'SO2':
                                return this.data[date].data.so2;
                            case 'NO2':
                                return this.data[date].data.no2;
                            case 'CO':
                                return this.data[date].data.co;
                        }
                    }
                }
            } else {
                return '--';
            }
        },

        //背景颜色的计算,出现非本月日期时的背景显示
        backgroundColor: function () {
            if (this.data) {
                let date = `${this.year}-${this.alignNumStr(
                    this.month
                )}-${this.alignNumStr(this.day)}`;
                if (this.data[date] === undefined) {
                    return '#d1d5d9';
                } else {
                    return this.data[date].backgroundColor;
                }
            } else {
                return '--';
            }
        },

        //污染物的计算，防止没有数据时正常显示
        pollutants: function () {
            if (this.data) {
                let date = `${this.year}-${this.alignNumStr(
                    this.month
                )}-${this.alignNumStr(this.day)}`;
                if (this.data[date] === undefined) {
                    return '';
                } else {
                    if (this.data[date].pollutants == null) {
                        return '';
                    } else {
                        return this.data[date].pollutants.join('');
                    }
                }
            } else {
                return '--';
            }
        },

        dayheight() {
            if (this.pollutants) {
                if (this.tileHeight > 52) {
                    return '20%';
                } else {
                    return '5px';
                }
            } else {
                return '0px';
            }
        }
    },

    methods: {
        //点击某个日期后，传入当前日期的值，传送至dialog组件
        choseview() {
            let date = `${this.year}-${this.alignNumStr(
                this.month
            )}-${this.alignNumStr(this.day)}`;
            if (this.noMonthShow === false) {
                if (this.month === this.currentMonth) {
                    for (let i in this.data) {
                        this.data[i].check = this.data[i] === this.data[date];
                    }
                    this.showdata = this.data[date];
                    this.show = true;
                }
            } else {
                for (let i in this.data) {
                    this.data[i].check = this.data[i] === this.data[date];
                }
                this.showdata = this.data[date];
                this.show = true;
            }
        },

        //监听弹窗变化，使其完全关闭
        watchPopover() {
            this.show = false;
        },

        showDialog() {
            this.show = false;
        },
        alignNumStr(num) {
            return num < 10 ? '0' + num : `${num}`;
        },

        //点击后阴影效果的变更
        rgbaColor() {
            let value = {};
            let date = `${this.year}-${this.alignNumStr(
                this.month
            )}-${this.alignNumStr(this.day)}`;
            value = this.data[date];
            if (this.data[date] === undefined) {
                //     return '#d1d5d9';
            } else {
                return hex2rgba(value.backgroundColor, 0.4);
            }
        }
    }
};
</script>

<style scoped lang="scss">
.align-center-layout {
    display: flex;
    flex-flow: column;
    justify-content: center;
    align-items: center;
}

.date-view {
    border: none;
    border-radius: 7px;
    box-sizing: border-box;
    color: white;
    align-items: center;
    display: flex;
    //   justify-content: space-evenly;
    flex-direction: column;
    position: relative;
    span {
        display: inline-block;
        line-height: 12px;
    }

    .day {
        font-size: 14px;
    }

    .min-margin-top {
        margin-top: 5px;
    }

    .max-margin-top {
        margin-top: 17px;
    }

    .wrapper {
        max-height: 16px;
        //     max-width: 36px;
        margin-top: 12%;
        //     overflow: hidden;
        text-align: center;
    }

    .min-line-height {
        line-height: 8px;
    }
}

.not-this-month {
    background: #d1d5d9 !important;
}

.bounce-enter-active {
    transform-origin: 50% 0%;
    animation: bounce-in 0.5s;
}
@keyframes bounce-in {
    from {
        transform: scale(0);
    }
    to {
        transform: scale(1);
    }
}
</style>

<style>
.canlendar-popover {
    width: 400px;
    padding: 0px;
    border: none;
    background: transparent;
}
.el-popover {
    width: auto !important;
}
</style>
