<!-- @format -->
<!-- mapbox 标签业务组件 -->
<template>
    <div></div>
</template>

<script>
import { getJosnData } from '@/api/gis/3D/BasicFramework/index';
export default {
    data() {
        return {
            areaMarkers: [],
            pointSize: 'min'
        };
    },
    props: ['map', 'markerParam'],
    components: {},
    mounted() {
        this.initEvent();
    },
    unmounted() {
        this.clear('高低图');
    },
    methods: {
        initEvent() {
            console.log(this.map);
            if (!this.map) {
                setTimeout(() => {
                    this.initEvent();
                }, 200);
                return;
            }
            let self = this;
            this.map.on('zoomend', (evt) => {
                self.zoomHandle();
            });
        },
        //地图缩放事件处理
        zoomHandle() {
            // let zoom = this.map.getZoom();
            // let fjzoom = this.markerParam.FJZOOM || 10;
            // if (
            //     (zoom >= fjzoom && this.pointSize == 'min') ||
            //     (zoom < fjzoom && this.pointSize == 'max')
            // ) {
            //     this.pointSize = this.pointSize == 'max' ? 'min' : 'max';
            //     this.addMarkers();
            // }
        },

        //绘制标签
        addMarkers() {
            this.addFillExtrusion();
        },
        addFillExtrusion() {
            //   let data = this.pointData;
            getJosnData('./gis/data/yc.json').then((res) => {
                let data = res.data.features;

                this.addPolygonSymbol(
                    this.markerParam.pointData,
                    {
                        layerId: '高低图',
                        legendData: [
                            {
                                text: '无数据',
                                color: '#ccc',
                                num: 71.2,
                                id: 'hjzlzs-zh-0',
                                class: 'hjzlzs-zh-0'
                            },
                            {
                                text: '',
                                color: '#E9FFBE',
                                num: 74.2,
                                id: 'hjzlzs-zh-5',
                                class: 'hjzlzs-zh-5'
                            },
                            {
                                text: '',
                                color: '#D0FF73',
                                num: 77.2,
                                id: 'hjzlzs-zh-4',
                                class: 'hjzlzs-zh-4'
                            },
                            {
                                text: '',
                                color: '#AAFF01',
                                num: 80.2,
                                id: 'hjzlzs-zh-3',
                                class: 'hjzlzs-zh-3'
                            },
                            {
                                text: '',
                                color: '#98E500',
                                num: 83.2,
                                id: 'hjzlzs-zh-3',
                                class: 'hjzlzs-zh-3'
                            },
                            {
                                text: '',
                                color: '#70A800',
                                num: 86.2,
                                id: 'hjzlzs-zh-3',
                                class: 'hjzlzs-zh-3'
                            }
                        ]
                    },
                    data
                );
            });
        },

        removeLayerFromName(name) {
            PowerGL.removeLayerFromName(this.map, name);
            // this.removeLayerEvent(name);
        },
        addPolygonSymbol(data, params, xzqhJson) {
            if (!this.map) {
                return;
            }

            this.removeLayerFromName(params.layerId);
            this.removeLayerFromName(params.layerId + '-label');
            params.max = 0;
            let data1 = PowerGL.sortObject(data, 'JCZ', 'ASC');
            let index = 1;
            let jcz = 0;
            data1.forEach((el) => {
                if (jcz != el.JCZ) {
                    index++;
                }

                jcz = el.JCZ;

                el.gd = (parseFloat(el.JCZ) * 30 * index) / 4;
            });
            let geojson = {
                type: 'FeatureCollection',
                features: []
            };

            xzqhJson.forEach((item) => {
                data1.forEach((el) => {
                    if (item.properties.NAME == el.XZQMC) {
                        Object.assign(item.properties, el);

                        item.properties.gd = parseFloat(item.properties.gd);
                        item.properties.label = '';
                        if (item.properties.COUNTY) {
                            item.properties.label =
                                item.properties.COUNTY +
                                ' ' +
                                item.properties.JCZ +
                                item.properties.JCZ_DW;
                        }

                        geojson.features.push({
                            type: 'Feature',
                            geometry: {
                                type: 'Point',
                                coordinates: [
                                    parseFloat(item.properties.INSIDE_X),
                                    parseFloat(item.properties.INSIDE_Y)
                                ]
                            },
                            properties: item.properties
                        });
                    }
                });
            });

            let fillColor = this.getFillColor(params);
            console.log(xzqhJson);

            this.map.addLayer({
                id: params.layerId,
                type: 'fill-extrusion',
                source: {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: xzqhJson
                    }
                },
                layout: {
                    // visibility: 'none'
                },
                paint: {
                    'fill-extrusion-color': fillColor,
                    'fill-extrusion-height': ['get', 'gd'], //获取属性的值
                    'fill-extrusion-opacity': 0.9
                }
            });
            console.log(geojson);

            this.map.addLayer({
                id: params.layerId + '-label',
                type: 'symbol',
                source: {
                    type: 'geojson',
                    data: geojson
                },
                layout: {
                    // visibility: 'none',
                    'text-field': ['get', 'label'],
                    'text-size': 14,
                    'text-font': ['Microsoft YaHei Regular'],
                    'text-allow-overlap': true //显示全部要素
                },
                paint: {
                    'text-color': '#fff',
                    'text-halo-color': '#000',
                    'text-halo-width': 1.5
                }
            });

            // this.setTclxEvent_area(params);
        },
        //设置pait 颜色条件
        getFillColor(params) {
            let typeObj = params.typeObj;
            let matcher = ['case'];
            params.legendData.forEach((item, index) => {
                matcher.push([
                    '<=',
                    ['to-number', ['get', 'JCZ'], 0],
                    item.num
                ]);
                matcher.push(item.color);
                if (index == params.legendData.length - 1) {
                    matcher.push(item.color);
                }
            });

            return matcher;
        },
        clear(layerId) {
            this.removeLayerFromName(layerId);
            this.removeLayerFromName(layerId + '-label');
        }
    },
    watch: {
        markerParam: {
            immediate: true,
            deep: true,
            handler(val) {
                if (this.markerParam && this.markerParam.pointData) {
                    if (this.markerParam.visible) {
                        this.addMarkers();
                    } else {
                        this.clear('高低图');
                    }
                }
            }
        }
    }
};
</script>

<style>
@import '~_as/gis/commom/map3DMarker.css';
</style>
