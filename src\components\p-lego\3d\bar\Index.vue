<!-- @format -->

<template>
    <highcharts
        :options="chartOptions"
        style="width: 100%; height: 100%"
    ></highcharts>
</template>

<script>
// const Highcharts = import.meta('highcharts')(window);
import { Chart } from 'highcharts-vue';
import highcharts from 'highcharts';
import highcharts3d from 'highcharts/highcharts-3d';
highcharts3d(highcharts);

const lightColor = [
    '#6ebffb',
    '#a9df34',
    '#40c057',
    '#5a7fe8',
    '#ffea00',
    '#13c9d9',
    '#00a497',
    '#5363c5',
    '#218ede',
    '#f39800',
    '#4262d9',
    '#9799f3',
    '#0fd0b7',
    '#ffd351'
];
const darkColor = [
    '#2ad9ff',
    '#e9c613',
    '#26d267',
    '#f68b17',
    '#fc4a4a',
    '#4d76eb',
    '#00e1c4',
    '#9465f4',
    '#c0f02f',
    '#06a4ff'
];

export default {
    components: {
        Highcharts: Chart
    },
    name: 'Bar3D',
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    series: [],
                    xAxis: []
                };
            }
        },
        option: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    data() {
        return {
            defaultOption: {
                stack: false, //多个系列数据是否堆叠 默认false
                showLabel: false, //是否显示label
                labelInside: false, //false时label在柱子上方 true时label在柱子内部
                fontColor: '', //字体颜色 默认白底#666 深底#fff
                color: null, // 配色 默认根据底色使用不同色系
                maxPointWidth: 30, //柱子宽度
                showYAxis: false, //是否显示y轴
                showLegend: true, //是否显示图例
                viewDistance: 25, //视距
                dealFn: null //Functio 自定义修改最终配置项
            },
            finalOption: {},
            chartOptions: {}
        };
    },
    watch: {
        data: 'setChartOptions'
    },
    mounted() {
        this.setChartOptions();
    },
    methods: {
        setChartOptions() {
            this.finalOption = {
                ...this.defaultOption,
                fontColor: localStorage.themeType === 'dark' ? '#fff' : '#666',
                color:
                    localStorage.themeType === 'dark' ? darkColor : lightColor,
                ...this.option
            };
            let self = this;
            let data = this.data;
            let series = [];
            if (data.series && data.series.length) {
                series = data.series.map((v) => {
                    let dataArr = v.data.map((d) => {
                        return isNaN(d) ? 0 : Number(d);
                    });
                    return {
                        name: v.name,
                        data: dataArr,
                        maxPointWidth: this.finalOption.maxPointWidth,
                        dataLabels: {
                            enabled: this.finalOption.showLabel,
                            inside: this.finalOption.labelInside,
                            style: {
                                color: this.finalOption.fontColor,
                                fontSize: '16px'
                            }
                        }
                    };
                });
            }
            let chartOptions = {
                colors: this.finalOption.color,
                chart: {
                    backgroundColor: 'transparent',
                    type: 'column',
                    marginTop: 60,
                    marginRight: 20,
                    options3d: {
                        enabled: true,
                        alpha: 5,
                        beta: 0,
                        depth: 50,
                        viewDistance: this.finalOption.viewDistance
                    }
                },
                title: {
                    text: ''
                },
                legend: {
                    enabled:
                        this.finalOption.showLegend &&
                        series[0] &&
                        series[0].name,
                    align: 'center', //水平方向位置
                    verticalAlign: 'top', //垂直方向位置
                    x: 0, //距离x轴的距离
                    y: 0, //距离Y轴的距离
                    symbolPadding: 10,
                    itemStyle: {
                        color: this.finalOption.fontColor,
                        fontSize: '16px',
                        fontWeight: '400'
                    },
                    itemHoverStyle: {
                        color: this.finalOption.fontColor,
                        fontWeight: '700'
                    }
                },
                xAxis: {
                    labels: {
                        enabled: true,
                        style: {
                            color: this.finalOption.fontColor,
                            fontSize: '16px'
                        }
                    },
                    gridLineWidth: 0, //网格
                    lineWidth: 0, //网格
                    categories: data.xAxis || []
                },
                yAxis: [
                    {
                        labels: {
                            enabled: this.finalOption.showYAxis, //是否显示文字
                            format: '{value}', //数字过大避免自动格式化
                            style: {
                                color: this.finalOption.fontColor,
                                fontSize: '16px'
                            }
                        },
                        gridLineWidth: this.finalOption.showYAxis ? 1 : 0, //网格
                        gridLineDashStyle: 'Dash', //网格
                        gridLineColor: 'rgba(255,255,255,0.3)',
                        lineWidth: 0, //网格
                        allowDecimals: false,
                        min: 0,
                        title: {
                            text: ''
                        }
                    }
                ],
                tooltip: {
                    headerFormat: '<b>{point.key}</b><br>',
                    pointFormat:
                        '<span style="color:{series.color}">\u25CF</span> {series.name}: {point.y} / {point.stackTotal}'
                },
                plotOptions: {
                    column: {
                        stacking: this.finalOption.stack ? 'normal' : null,
                        depth: 50
                    }
                },
                series: series
            };

            this.chartOptions = this.finalOption.dealFn
                ? this.finalOption.dealFn(chartOptions)
                : chartOptions;
        }
    }
};
</script>

<style>
.highcharts-point {
    width: 14px;
    height: 14px;
    border-radius: 0;
    rx: 0;
    transform: translate(0, 3px);
}
.highcharts-credits {
    display: none;
}
</style>
