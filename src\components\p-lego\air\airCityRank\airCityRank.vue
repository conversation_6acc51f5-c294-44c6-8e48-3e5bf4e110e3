<!-- @format -->

<template>
    <div style="width: 528px; background: #004280">
        <div class="pd-modhd">
            <span>空气质量排名</span>
            <em class="rank" @click="rankTableShow = !rankTableShow"
                >全国168城市排名<b>{{ data.rankNum }}</b></em
            >
            <ul class="pd-ultbs3">
                <li
                    :class="{ on: item.value == data.dateType }"
                    v-for="(item, index) of data.arrDateType"
                    :key="index"
                    @click="dateTypeChange(item.value)"
                >
                    {{ item.name }}
                </li>
            </ul>
        </div>
        <div class="gap"></div>
        <ul class="pd-ultbs2 otr">
            <li
                :class="{ on: item.value == data.rankType }"
                v-for="(item, index) of data.arrRankType"
                :key="index"
                @click="rankTypeChange(item.value)"
            >
                {{ item.name }}
            </li>
        </ul>

        <em
            style="
                float: right;
                margin-top: -24px;
                color: #fff;
                margin-right: 15px;
                cursor: pointer;
            "
            v-show="data.rankType == 'shi' && data.dateType != 'real'"
            @click="showMore()"
            >更多>></em
        >
        <div class="gap"></div>

        <el-table
            :data="data.rankList"
            style="width: 528px"
            height="365px"
            ref="tableRegion"
            v-show="data.rankType == 'shi' && data.dateType != 'real'"
            highlight-current-row
            :default-sort="{
                prop: 'PM25',
                order: 'ascending' // 'descending'
            }"
        >
            <el-table-column
                label="排名"
                type="index"
                align="center"
                width="50"
            >
            </el-table-column>
            <el-table-column
                prop="SSXZQ"
                label="城市"
                align="center"
                width="80"
            >
            </el-table-column>

            <el-table-column label="优良率(%)" align="center">
                <el-table-column
                    prop="YLL"
                    label="实际值"
                    align="center"
                    sortable
                    width="66"
                >
                    <template v-slot="scope">
                        <span :class="getCls(scope.row, 'YLL')">
                            {{ scope.row.YLL }}
                        </span>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="YLLMBZ"
                    label="目标值"
                    align="center"
                    width="66"
                >
                </el-table-column>
            </el-table-column>

            <el-table-column label="PM25" align="center">
                <el-table-column
                    prop="PM25"
                    label="实际值"
                    align="center"
                    width="66"
                    sortable
                >
                    <template v-slot="scope">
                        <span :class="getCls(scope.row, 'PM25')">
                            {{ scope.row.PM25 }}
                        </span>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="PM25MBZ"
                    label="目标值"
                    align="center"
                    width="66"
                >
                </el-table-column>
            </el-table-column>

            <el-table-column label="O3" align="center">
                <el-table-column
                    prop="O3"
                    label="实际值"
                    align="center"
                    width="66"
                    sortable
                >
                    <template v-slot="scope">
                        <span :class="getCls(scope.row, 'O3')">
                            {{ scope.row.O3 }}
                        </span>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="O3MBZ"
                    label="目标值"
                    align="center"
                    width="66"
                >
                </el-table-column>
            </el-table-column>
        </el-table>

        <el-table
            :data="data.rankList"
            style="width: 528px"
            height="365px"
            ref="tableRegion"
            v-show="!(data.rankType == 'shi' && data.dateType != 'real')"
            highlight-current-row
            :default-sort="{
                prop: data.dateType != 'real' ? 'ZHZS' : 'AQI',
                order: 'ascending'
            }"
        >
            <el-table-column
                label="排名"
                type="index"
                align="center"
                width="50"
            >
            </el-table-column>
            <el-table-column
                prop="SSXZQ"
                label="城市"
                align="center"
                width="80"
            >
            </el-table-column>
            <el-table-column
                v-if="data.dateType == 'real'"
                prop="AQI"
                label="AQI"
                align="center"
                sortable
            >
            </el-table-column>
            <el-table-column
                v-if="data.dateType != 'real' && data.rankType != 'xiangzhen'"
                prop="ZHZS"
                label="综合指数"
                align="center"
                sortable
            >
            </el-table-column>

            <el-table-column
                v-if="data.dateType != 'real' && data.rankType == 'xiangzhen'"
                prop="YLL"
                label="优良率"
                align="center"
                sortable
            >
            </el-table-column>
            <el-table-column prop="PM25" label="PM25" align="center" sortable>
            </el-table-column>

            <el-table-column prop="PM10" label="PM10" align="center" sortable>
            </el-table-column>
        </el-table>

        <div
            class="data.rankTable"
            v-show="rankTableShow"
            style="background: rgba(0, 66, 128, 1)"
        >
            <el-table
                :data="data.rankTable"
                style="width: 528px"
                height="600px"
                ref="rankTableRegion"
                highlight-current-row
            >
                <el-table-column
                    label="排名"
                    prop="rank"
                    align="center"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    prop="SSXZQ"
                    label="城市"
                    align="center"
                    width="100"
                >
                </el-table-column>
                <el-table-column prop="ZHZS" label="综合指数" align="center">
                </el-table-column>
                <el-table-column prop="PM25" label="PM₂.₅" align="center">
                </el-table-column>
                <el-table-column prop="PM10" label="PM10" align="center">
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script>
export default {
    name: 'airCityRank',
    props: {
        data: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    data() {
        return {
            rankTableShow: false,

            wryDialogVisible: false
        };
    },
    watch: {
        rankTableShow: 'setCurrRow'
    },
    mounted() {
        this.getAirIndexRank();
    },
    methods: {
        getAirIndexRank() {
            this.data.rankList.forEach((v, i) => {
                if (v.SSXZQ === this.data.cityName) {
                    // rowNumber = i;
                    this.$refs.tableRegion.setCurrentRow(v);
                }
            });
            this.data.rankTable.forEach((v, i) => {
                if (v.SSXZQ === this.data.cityName) {
                    // rowNumber = i;
                    this.$refs.rankTableRegion.setCurrentRow(v);
                }
            });
        },
        rankTypeChange(val) {
            //   this.data.rankType = val;
            this.$emit('rankChange', val);
            this.getAirIndexRank();
        },

        dateTypeChange(val) {
            //   this.data.dateType = val;
            this.$emit('dateChange', val);

            this.getAirIndexRank();
        },
        getCls(row, type) {
            let cls = '';
            if (type == 'YLL') {
                if (parseFloat(row[type]) < parseFloat(row[type + 'MBZ'])) {
                    cls = 'warning';
                }
            } else {
                if (parseFloat(row[type]) > parseFloat(row[type + 'MBZ'])) {
                    cls = 'warning';
                }
            }

            return cls;
        },
        setCurrRow() {
            if (this.rankTableShow) {
                let rowNumber = 0;
                this.data.rankTable.forEach((v, i) => {
                    if (v.SSXZQ === '衢州市') {
                        rowNumber = i;
                        this.$refs.rankTableRegion.setCurrentRow(v);
                    }
                });
                setTimeout(() => {
                    $('.data.rankTable .el-table__body-wrapper').scrollTop(
                        rowNumber * 40 - 100
                    );
                }, 500);
            }
        },
        showMore() {
            this.wryDialogVisible = true;
        }
    }
};
</script>
<style scoped>
.pd-ultbs2 {
    text-align: center;
    font-size: 0;
}

.pd-ultbs2 li {
    display: inline-block;
    text-align: center;
    cursor: pointer;
    padding: 0 5px;
    margin-bottom: 5px;
    border-radius: 2px;
    /* width: 58px; */
    height: 28px;
    line-height: 28px;
    font-size: 16px;
    color: #fff;
    border: 1px solid #0069ba;
    background: #003161;
    width: 110px;
}

.pd-ultbs2 li + li {
    margin-left: 10px;
    width: 100px;
}

.pd-ultbs2 li.on {
    background: #0181dc;
    color: #fff;
    border-color: #0181dc;
    width: 100px;
}

.pd-ultbs2 li sub {
    font-size: 12px;
    vertical-align: baseline;
}
.pd-ultbs2.otr li {
    width: 78px;
    height: 26px;
    line-height: 26px;
    border-radius: 2px;
}
.pd-ultbs3 {
    float: right;
}

.pd-ultbs3 li {
    float: left;
    font-size: 16px;
    color: #fff;
    padding: 0 10px;
    position: relative;
    cursor: pointer;
}

.pd-ultbs3 li + li:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 1px;
    height: 12px;
    background: #ddd;
    margin-top: -6px;
}

.pd-ultbs3 li.on {
    color: #119cff;
}
.el-table,
.el-table__expanded-cell {
    background-color: rgba(0, 0, 0, 0) !important;
}
.warning {
    color: #ef9913;
    font-weight: bold;
}
</style>
