<!-- @format -->
<!-- 输入框 -->

<template>
    <div class="search-type">
        <div class="title">{{ title }}</div>
        <el-input
            style="flex: 1"
            v-model="keywords"
            :placeholder="placeholder"
            @change="$emit('inputChange', keywords)"
        ></el-input>
    </div>
</template>

<script>
export default {
    name: '',
    props: {
        title: {
            type: String,
            default: () => {
                return '';
            }
        },
        defaultVal: {
            type: String,
            default: () => {
                return '';
            }
        },
        placeholder: {
            type: String,
            default: () => {
                return '请输入';
            }
        }
    },
    data() {
        return {
            keywords: ''
        };
    },
    mounted() {
        this.keywords = this.defaultVal;
    },
    methods: {}
};
</script>

<style lang="scss" scoped>
.search-type {
    margin-right: 20px;
    color: var(--font-color);
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
}
</style>
