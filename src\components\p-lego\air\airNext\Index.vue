<!-- @format -->

<template>
    <div>
        <div class="air-next">
            <div class="air-next-label">
                <p class="air-next-label-item">日期</p>
                <p class="air-next-label-item">AQI最低</p>
                <p class="air-next-label-item">AQI最高</p>

                <p class="air-next-label-item">首要污染物</p>
            </div>
            <ul class="air-next-list">
                <li v-for="(item, idx) in data" :key="idx">
                    <p class="air-next-list-dateName">{{ item.dateName }}</p>
                    <p class="air-next-list-date">{{ item.date }}</p>
                    <p
                        class="air-next-list-value"
                        :style="{ backgroundColor: getColor(item.aqimin) }"
                    >
                        {{ item.aqimin }}
                    </p>
                    <p
                        class="air-next-list-value"
                        :style="{ backgroundColor: getColor(item.aqiMax) }"
                    >
                        {{ item.aqiMax }}
                    </p>

                    <p class="air-next-list-plt">
                        {{ replacePltName(item.maxItem) || '-' }}
                    </p>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
import util from './util';
export default {
    name: 'airNext',
    props: {
        data: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    methods: {
        getColor(value) {
            return util.getLevelPollution('AQI', value).color;
        },
        replacePltName(value) {
            return util.replacePltName(value);
        }
    }
};
</script>

<style scoped lang="less">
.air-next {
    display: flex;

    &-label {
        width: 80px;
        padding-right: 6px;
        box-sizing: border-box;

        &-item {
            font-size: 14px;
            // color: #2083f3;
            text-align: right;
            line-height: 34px;

            &:first-child {
                line-height: 48px;
            }
        }
    }

    &-list {
        display: flex;
        flex: 1;
        padding-left: 20px;

        & > li {
            flex: 1;
            text-align: center;
            padding-top: 6px;
            border-left: 1px dashed var(--el-border-color);
        }

        &-dateName {
            font-size: 14px;
            line-height: 18px;
        }

        &-date {
            font-size: 12px;
            opacity: 0.8;
            line-height: 18px;
            margin-bottom: 12px;
        }

        &-value {
            width: 44px;
            height: 22px;
            line-height: 22px;
            border-radius: 300px;
            font-size: 14px;
            color: #fff;
            margin: 0 auto;
            margin-bottom: 12px;
        }

        &-plt {
            font-size: 14px;
            line-height: 18px;
            padding-top: 2px;
        }
    }
}
</style>
