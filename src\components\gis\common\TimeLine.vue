<!-- @format -->

<!-- 自定义播放轴，用于没有规律的时间序列 -->
<template>
    <div class="gis-timeaxis">
        <img
            @click="playClick"
            v-show="!timeId"
            alt="加载中"
            src="../../../assets/gis/commom/images/dark/gis_plybtn.png"
            class="pd-play"
        />

        <img
            v-show="timeId"
            @click="pauseClick"
            alt="加载中"
            src="../../../assets/gis/commom/images/dark/gis_pausebtn.png"
            class="pd-play"
        />

        <ul class="pd-ulnumlst">
            <li
                v-for="(item, index) of arrDate"
                :key="index"
                :class="[
                    {
                        on: selectDate == item.sj
                    },
                    item.clsName
                ]"
                @click="itemClick(item)"
            >
                {{ item.sj }}<sup>{{ item.tipStr }}</sup>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    data() {
        return {
            selectDate: '2021', //当前选中的日历
            arrDate: [], //
            timeId: '',
            lastDate: '',
            defaultOption: {
                loop: false //是否循环播放
            }
        };
    },
    components: {},
    unmounted() {
        this.clearTime();
    },
    methods: {
        selectDateChanged() {
            this.$emit('dateChange', this.selectDate, false);
        },

        //初始化timeline
        initTimeLine(arr, option) {
            if (arr.length > 0) {
                this.lastDate = arr[arr.length - 1].SJ;
                this.selectDate = arr[arr.length - 1].SJ;
            }

            this.arrDate = [];

            for (let item of arr) {
                this.arrDate.push({
                    sj: item.SJ,
                    tipStr: item.SJ,
                    clsName: item.clsName
                });
            }

            if (option) {
                Object.assign(this.defaultOption, option);
            }

            //第一次请求数据
            this.$emit('dateChange', this.selectDate, false);
        },

        //清除定时器
        clearTime() {
            if (this.timeId) {
                clearInterval(this.timeId);
                this.timeId = null;
            }
        },

        itemClick(item) {
            this.selectDate = item.sj;
            this.clearTime();
            this.$emit('dateChange', item.sj, false);
        },

        //暂停
        pauseClick() {
            this.clearTime();
        },

        //播放按钮点击
        playClick() {
            //如果正在播放，则停止
            if (this.timeId) {
                this.clearTime();
                return;
            }

            this.timeId = setInterval(() => {
                console.log(this.selectDate);

                if (this.selectDate == this.lastDate) {
                    this.selectDate = this.arrDate[0].sj;
                } else {
                    for (let index = 0; index < this.arrDate.length; index++) {
                        if (this.arrDate[index].sj == this.selectDate) {
                            if (index + 1 < this.arrDate.length) {
                                this.selectDate = this.arrDate[index + 1].sj;
                                break;
                            }
                        }
                    }
                }

                this.$emit('dateChange', this.selectDate, true);

                if (!this.defaultOption.loop) {
                    if (this.selectDate == this.lastDate) {
                        this.clearTime();
                    }
                }
            }, 2000);
        }
    }
};
</script>

<style scoped></style>
