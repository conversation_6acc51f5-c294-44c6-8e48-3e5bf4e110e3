{"compilerOptions": {"target": "es5", "module": "esnext", "baseUrl": "./", "moduleResolution": "node", "paths": {"@/*": ["src/*"], "_as/*": ["src/assets/*"], "_u/*": ["src/utils/*"], "_a/*": ["src/api/*"], "_s/*": ["src/store/*"], "_v/*": ["src/views/*"], "_c/*": ["src/components/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "exclude": ["node_modules", "dist"], "include": ["src/**/*"]}