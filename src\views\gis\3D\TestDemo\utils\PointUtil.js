/** @format */
import BasePointUtil from '@/components/gis/3D/utils/BasePointUtil';
class PointUtil extends BasePointUtil {
    constructor(map, callback) {
        super(map, callback);
        let arr = [
            {
                name: 'gyy-01',
                url: './gis/3D/images/WRYZX/FQ-ZC.png' //正常
            }
        ];

        this.loadImages(arr);
    }

    // mapboxgl.Popup参数 可选择是否覆盖的方法
    getPopupOption(id, properties) {
        let option = {};

        switch (id) {
            case '大气站点':
                option = {
                    offset: [0, -75],
                    className: 'mapbox-tooltip-tip-air'
                };
                break;

            case '小圆点':
                option = {
                    offset: [0, -20],
                    className: 'mapbox-tooltip'
                };
                break;
        }

        return option;
    }
    // mapboxgl.Popup.HTML 需要覆盖的方法
    getPopupStr(id, properties) {
        let html = '';

        switch (id) {
            case '工业源':
                html = `<div class="protecttip" > ${properties.QYMC} </div>`;
                break;

            case '小圆点':
            case '立体柱子':
            case '缓冲结果':
                html = `<div class="protecttip" > ${properties.MC} </div>`;
                break;

            // case '面图层':
            case '线图层':
                html = `<div class="protecttip" > ${properties.NAME} </div>`;
                break;
        }

        return html;
    }

    // marker.option 可选择是否覆盖方法
    getMarkerOption(id, properties) {
        let result = { className: 'coustom-marker' };
        return result;
    }

    //生成marker.element 需要覆盖的方法
    getMarkerHtml(properties, params) {
        let el = document.createElement('div');
        el.className = 'air-marker';
        let template = ``;

        switch (params.id) {
            case '大气站点':
                break;
        }
        el.innerHTML = template;
        return el;
    }

    // 获取symbol layout属性  需要覆盖的方法
    getSymbolLayout(params) {
        let layout = {};

        switch (params.id) {
            case '缓冲结果':
                layout = {
                    'icon-image': 'gyy-01',

                    'icon-size': [
                        'step',
                        ['zoom'],
                        0.3,
                        10,
                        0.5,
                        13,
                        0.8,
                        18,
                        1
                    ]

                    // feature-state 不能控制layout 属性
                    // 'icon-size': [
                    //     'case',
                    //     ['boolean', ['feature-state', 'hover'], false],
                    //     1,
                    //     0.5
                    // ]
                };

                // ['step', ['zoom'], 0.3, 10, 0.5, 13, 0.8, 18, 1],
                // ['step', ['zoom'], 0.4, 10, 0.7, 13, 1.2, 18, 1.5]
                break;

            case '文本注记':
            case 'geojson图层注记':
                layout = {
                    visibility: 'visible',
                    'text-size': 15,
                    'text-rotation-alignment': 'viewport',
                    'text-pitch-alignment': 'viewport',
                    'text-font': ['Microsoft YaHei Bold'],
                    'text-anchor': 'center',
                    'text-max-width': 8,
                    'text-field': '{NAME}'
                };
                break;
        }

        layout['icon-allow-overlap'] = true;

        return layout;
    }

    // 获取symbol paint属性
    getSymbolPaint(params) {
        let paint = {};
        switch (params.id) {
            case '小圆点':
                paint = {
                    'circle-color': ['get', 'color'], //获取颜色
                    // 'circle-radius': 10,
                    'circle-stroke-width': 1,
                    'circle-stroke-color': '#fff',

                    //设置移入专题
                    'circle-radius': [
                        'case',
                        ['boolean', ['feature-state', 'hover'], false],
                        15,
                        10
                    ]
                };
                break;

            case '文本注记':
            case 'geojson图层注记':
                paint = {
                    'text-color': 'rgba(146, 217, 255, 1)',
                    'text-halo-color': 'rgb(146, 217, 255)',
                    'text-halo-width': 0
                };
                break;
        }
        return paint;
    }

    /**
     * 定义热力图样式，可覆盖此方法
     * @param { } params
     * @returns
     */
    getHeatPaint(params) {
        return {
            // 根据频率和属性大小增加热图权重
            'heatmap-weight': [
                'interpolate',
                ['linear'],
                ['get', 'mag'],
                0,
                0,
                6,
                1
            ],
            // 按缩放级别增加热图颜色权重
            // 热图强度是热图权重之上的一个乘数
            'heatmap-intensity': [
                'interpolate',
                ['linear'],
                ['zoom'],
                0,
                1,
                9,
                3
            ],
            // 创建一个色带 ,自定义色带
            'heatmap-color': [
                'interpolate',
                ['linear'],
                ['heatmap-density'],
                0,
                'rgba(0,0,255, 0)',
                0.2,
                'rgb(0,0,255)',
                0.4,
                'rgb(0,255,255)',
                0.6,
                'rgb(0,255,0)',
                0.8,
                'rgb(255,255,0)',
                1,
                'rgb(239,56,55)'
            ],
            // 按缩放级别调整热图半径
            'heatmap-radius': [
                'interpolate',
                ['linear'],
                ['zoom'],
                0,
                2,
                9,
                20
            ],
            // 透明度
            'heatmap-opacity': 1

            //   // 透明度
            //   'heatmap-opacity': [
            //     'interpolate',
            //     ['linear'],
            //     ['zoom'],
            //     10,
            //     1,
            //     15,
            //     0
            // ]
        };
    }

    /**
     * Polygon 填充
     * @param {*} params
     * @returns
     */
    getFillPaint(params) {
        let option = {};
        switch (params.id) {
            case '面图层':
                option = {
                    'fill-antialias': true,
                    'fill-color': '#ff0000',
                    'fill-outline-color': '#ff0000',

                    'fill-opacity': [
                        'case',
                        ['boolean', ['feature-state', 'hover'], false],
                        0.8,
                        0.4
                    ]
                    // 'fill-opacity': 0.4
                };
                break;

            case '点缓冲':
            case '线缓冲':
            case '面缓冲':
                option = {
                    'fill-antialias': true,
                    'fill-color': '#0f8cce',
                    'fill-outline-color': '#70cdff',
                    // 'fill-opacity': 0.4,

                    //设置鼠标移入改变透明度
                    'fill-opacity': [
                        'case',
                        ['boolean', ['feature-state', 'hover'], false],
                        0.8,
                        0.4
                    ]
                };
                break;

            case 'geojson图层':
                option = {
                    'fill-antialias': true,
                    'fill-color': '#ff0000',
                    'fill-outline-color': '#70cdff',
                    // 'fill-opacity': 0.4

                    //设置鼠标移入改变透明度
                    'fill-opacity': [
                        'case',
                        ['boolean', ['feature-state', 'hover'], false],
                        0.8,
                        0.4
                    ]
                };
                break;
            default:
                break;
        }
        return option;
    }

    //线数据样式
    getLinePaint(params) {
        let option = {};
        switch (params.id) {
            case '线图层':
                option = {
                    'line-opacity': 1,
                    'line-color': '#ff0000',
                    // 'line-width': 2

                    'line-width': [
                        'case',
                        ['boolean', ['feature-state', 'hover'], false],
                        5,
                        2
                    ]
                };
                break;

            default:
                break;
        }
        return option;
    }

    getExtrusionPaint(params) {
        let paint = {};
        switch (params.id) {
            case '拉升面':
                paint = {
                    'fill-extrusion-color': 'rgba(0, 233, 255, 1)',
                    'fill-extrusion-height': 200,
                    'fill-extrusion-opacity': 1,
                    'fill-extrusion-vertical-gradient': true,
                    'fill-extrusion-base': 0
                };
                break;

            default:
                break;
        }
        return paint;
    }
}
export default PointUtil;
