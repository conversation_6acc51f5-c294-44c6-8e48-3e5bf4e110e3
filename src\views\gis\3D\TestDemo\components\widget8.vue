<!-- @format -->
<!-- 点、线、面缓冲 -->
<template>
    <div></div>
</template>

<script>
import PointUtil from '../utils/PointUtil';
export default {
    props: [],
    data() {
        return {
            map: null,
            pointUtil: null,
            layerID: '点缓冲',
            layerID1: '线缓冲',
            layerID2: '面缓冲',
            layerID3: '缓冲结果'
        };
    },

    unmounted() {
        this.clear();
    },

    mounted() {
        this.initPage();
    },

    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.addPointBuffer();

            this.addLineBuffer();

            this.addPolygonBuffer();
        },

        //新增点缓冲
        addPointBuffer() {
            let pt = [120.5705420917073, 30.91177853901037];

            let data = [
                {
                    coordinates: pt,
                    properties: {}
                }
            ];

            let geojson = this.pointUtil.toGeojson(data, 'Point');

            //传入geojson
            // let geo1 = this.pointUtil.getBuffer(geojson, { radius: 5 });
            // this.pointUtil.addGeojsonData(geo1, {
            //     id: this.layerID,
            //     beforeId: '',
            //     geoType: 'Polygon',
            //     disableClick: true,
            //     disablePopup: true
            // });

            //得到缓冲的面
            let geo1 = this.pointUtil.getBuffer(geojson.features[0], {
                radius: 2
            });

            this.pointUtil.addPolygon([geo1], {
                id: this.layerID,
                beforeId: '',
                disableClick: true,
                disablePopup: true
            });

            let pts = [];

            for (let i = 0; i < 30; i++) {
                let obj = {
                    MC: '点位' + i,
                    JD: pt[0] + (Math.random() - 0.5) * 0.1,
                    WD: pt[1] + (Math.random() - 0.5) * 0.1
                };
                pts.push(obj);
            }

            let result = this.pointUtil.areaFilter(geo1, pts);

            this.pointUtil.addImgPoint(result, {
                id: this.layerID3
            });
        },

        //先缓冲
        addLineBuffer() {
            let pts = [
                [120.49968693243005, 31.011166854588026],
                [120.5784284979772, 31.004342625258346],
                [120.67117117417024, 30.997100499361224],
                [120.75815235130801, 31.003821677137466]
            ];

            let data = [
                {
                    coordinates: pts,
                    properties: {}
                }
            ];

            let geojson = this.pointUtil.toGeojson(data, 'LineString');

            this.pointUtil.addLine(geojson.features, {
                id: '线图层',
                beforeId: '',
                disableClick: false,
                disablePopup: false
            });

            let geo1 = this.pointUtil.getBuffer(geojson.features[0], {
                radius: 2
            });

            this.pointUtil.addPolygon([geo1], {
                id: this.layerID1,
                beforeId: '线图层',
                disableClick: false,
                disablePopup: false
            });
        },

        //面缓冲
        addPolygonBuffer() {
            let pts = [
                [120.66141798360735, 31.12172208528277],
                [120.66470930478357, 31.088996923401794],
                [120.70974392753124, 31.085905400417303],
                [120.70914851924357, 31.121243127894488],
                [120.70914851924357, 31.121243127894488],
                [120.68576352296299, 31.126541298866556],
                [120.66678663215043, 31.120286818091245],
                [120.66141798360735, 31.12172208528277]
            ];

            let data = [
                {
                    coordinates: [pts],
                    properties: {}
                }
            ];

            let geojson = this.pointUtil.toGeojson(data, 'Polygon');

            this.pointUtil.addPolygon(geojson.features, {
                id: '面图层',
                beforeId: '',
                disableClick: false,
                disablePopup: false
            });

            let geo1 = this.pointUtil.getBuffer(geojson.features[0], {
                radius: 2
            });

            this.pointUtil.addPolygon([geo1], {
                id: this.layerID2,
                beforeId: '面图层',
                disableClick: false,
                disablePopup: false
            });
        },

        clear() {
            this.pointUtil.removeLayerByName('线图层');
            this.pointUtil.removeLayerByName('面图层');
            this.pointUtil.removeLayerByName(this.layerID);
            this.pointUtil.removeLayerByName(this.layerID1);
            this.pointUtil.removeLayerByName(this.layerID2);
            this.pointUtil.removeLayerByName(this.layerID3);
        },

        pointClickHandle() {}
    }
};
</script>

<style scoped></style>
