<!-- @format -->

<template>
    <div
        class="sw1212-wrap"
        style="
            background: #fff;
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
        "
    >
        <div class="sw1212-container flx1" style="top: 0">
            <div class="sw1212-rmain" style="margin-left: 0">
                <div class="sw1212-topbar">
                    <dl>
                        <dt>广东省辐射安全监管网络学习平台</dt>
                        <dd>
                            <input
                                type="text"
                                class="sw1212-inptxt1"
                                placeholder="请输入"
                                v-model="searchTxt"
                                @keypress.enter="
                                    showDetailContent = true;
                                    getData();
                                "
                                @input="getContent()"
                            />
                            <button
                                type="button"
                                class="sw1212-btn1"
                                @click="
                                    showDetailContent = true;
                                    getData();
                                "
                            >
                                搜索
                            </button>
                            <div
                                class=""
                                style="cursor: pointer"
                                @click="myFav"
                            >
                                <img
                                    v-if="GRSC == ''"
                                    style="
                                        width: 30px;
                                        height: 30px;
                                        margin-left: 20px;
                                    "
                                    src="@/assets/exam/images/sw1212_ic3a.png"
                                    alt=""
                                />
                                <img
                                    v-else
                                    style="
                                        width: 30px;
                                        height: 30px;
                                        margin-left: 20px;
                                    "
                                    src="@/assets/exam/images/sw1212_ic4a.png"
                                    alt=""
                                />
                            </div>
                            <div
                                style="
                                    position: absolute;
                                    width: 810px;
                                    background-color: #fff;
                                    top: 174px;
                                    border: 1px solid #efefef;
                                    border-radius: 5px;
                                    z-index: 2;
                                    overflow-y: auto;
                                    max-height: 280px;
                                "
                                v-if="searchOptions.length && showOptions"
                            >
                                <div
                                    v-for="(item, index) in searchOptions"
                                    :key="index"
                                    class="search-option"
                                    @click="selectOption(item)"
                                >
                                    {{
                                        item.WJMC.slice(
                                            0,
                                            item.WJMC.indexOf('.')
                                        )
                                    }}
                                </div>
                            </div>
                        </dd>
                    </dl>
                </div>
                <div class="sw1212-botbar" v-show="showDetailContent">
                    <dl class="sw1212-dlbx1">
                        <dt>分类：</dt>
                        <dd>
                            <span
                                v-for="item in knowledgeClassificationList"
                                :key="item.DM"
                                :class="{
                                    on: knowledgeClassification === item.DM
                                }"
                                @click="knowledgeClassificationChange(item)"
                                >{{ item.DMMC }}</span
                            >
                        </dd>

                        <img
                            @click="backToHome"
                            src="./images/back.png"
                            style="width: 20px; cursor: pointer"
                        />
                    </dl>
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <div class="rel">
                        <ul class="sw1212-ultbs1">
                            <li
                                v-for="item in knowledgeFormList"
                                :key="item.DM"
                                :class="{ on: knowledgeForm === item.DM }"
                                @click="knowledgeFormChange(item)"
                            >
                                {{ item.DMMC }}
                            </li>
                        </ul>
                        <ul class="sw1212-ultbs2 abs">
                            <li
                                class="li1"
                                :class="{ on: viewMode === 'card' }"
                                @click="viewModeChange('card')"
                            ></li>
                            <li
                                class="li2"
                                :class="{ on: viewMode === 'table' }"
                                @click="viewModeChange('table')"
                            ></li>
                        </ul>
                    </div>
                    <div
                        class="gap"
                        style="border-bottom: 1px solid #eee"
                    ></div>
                    <div class="gap15"></div>
                    <table class="sw1212-tablelst1" v-if="viewMode === 'table'">
                        <colgroup>
                            <col width="40%" />
                            <col width="30%" />
                            <col width="" />
                            <col width="2%" />
                            <col width="2%" />
                            <col width="2%" />
                        </colgroup>
                        <tr
                            v-for="item in list"
                            :key="item.XH"
                            style="cursor: pointer"
                            @click="previewFile(item)"
                        >
                            <td style="color: #467ddc; font-weight: bold">
                                {{ item.BT || '' }}
                            </td>
                            <td class="gray">
                                <span>上传人：{{ item.SCR || '' }}</span>
                            </td>
                            <td>
                                <i class="type">{{ item.XSMC || '' }}</i>
                            </td>
                            <td>
                                <i
                                    class="share"
                                    :class="{ on: !!item.FXXH }"
                                    @click.stop="
                                        !!item.FXXH
                                            ? cancelShareKnowledge(item)
                                            : shareKnowledge(item)
                                    "
                                ></i>
                            </td>
                            <td>
                                <i
                                    class="star"
                                    :class="{ on: !!item.SCXH }"
                                    @click.stop="
                                        !!item.SCXH
                                            ? cancelCollectKnowledge(item)
                                            : collectKnowledge(item)
                                    "
                                ></i>
                            </td>
                            <td>
                                <el-button
                                    type="primary"
                                    plain
                                    round
                                    @click.stop="openExercise(item)"
                                    style="font-size: 14px"
                                    >模拟练习</el-button
                                >
                            </td>
                        </tr>
                    </table>
                    <div class="sw1212-cardbx1" v-if="viewMode === 'card'">
                        <div class="row">
                            <div
                                class="col"
                                style="cursor: pointer"
                                v-for="item in list.slice(0, 5)"
                                :key="item.XH"
                                @click="previewFile(item)"
                            >
                                <dl class="sw1212-dlbx2">
                                    <dt>
                                        <div class="rt-btn">
                                            <sup
                                                class="share"
                                                :class="{ on: !!item.FXXH }"
                                                @click.stop="
                                                    !!item.FXXH
                                                        ? cancelShareKnowledge(
                                                              item
                                                          )
                                                        : shareKnowledge(item)
                                                "
                                            ></sup>
                                            <sup
                                                class="star"
                                                :class="{ on: !!item.SCXH }"
                                                @click.stop="
                                                    !!item.SCXH
                                                        ? cancelCollectKnowledge(
                                                              item
                                                          )
                                                        : collectKnowledge(item)
                                                "
                                            ></sup>
                                        </div>
                                        <img
                                            :src="getBanner(item)"
                                            class="img"
                                        />
                                    </dt>
                                    <dd>
                                        <h1>{{ item.BT || '' }}</h1>
                                        <p>
                                            <!-- <span
                                                >上传人：{{
                                                    item.SCR || ''
                                                }}</span
                                            > -->

                                            <i
                                                :class="[
                                                    {
                                                        type1:
                                                            item.XSMC ===
                                                            '电子书'
                                                    },
                                                    {
                                                        type2:
                                                            item.XSMC === '视频'
                                                    },
                                                    {
                                                        type3:
                                                            item.XSMC === '文档'
                                                    }
                                                ]"
                                                >{{ item.XSMC || '' }}</i
                                            >
                                            <el-button
                                                type="primary"
                                                plain
                                                round
                                                @click.stop="openExercise(item)"
                                                style="font-size: 14px"
                                                >模拟练习</el-button
                                            >
                                        </p>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                        <div class="gap clear"></div>
                        <div class="gap clear"></div>
                        <div class="gap clear"></div>
                        <div class="row">
                            <div
                                class="col"
                                style="cursor: pointer"
                                v-for="item in list.slice(5, 10)"
                                :key="item.XH"
                                @click="previewFile(item)"
                            >
                                <dl class="sw1212-dlbx2">
                                    <dt>
                                        <div>
                                            <sup
                                                class="share"
                                                :class="{ on: !!item.FXXH }"
                                                @click.stop="
                                                    !!item.FXXH
                                                        ? cancelShareKnowledge(
                                                              item
                                                          )
                                                        : shareKnowledge(item)
                                                "
                                            ></sup>
                                            <sup
                                                class="star"
                                                :class="{ on: !!item.SCXH }"
                                                @click.stop="
                                                    !!item.SCXH
                                                        ? cancelCollectKnowledge(
                                                              item
                                                          )
                                                        : collectKnowledge(item)
                                                "
                                            ></sup>
                                        </div>
                                        <img
                                            :src="getBanner(item)"
                                            class="img"
                                        />
                                    </dt>
                                    <dd>
                                        <h1>{{ item.BT || '' }}</h1>
                                        <p>
                                            <!-- <span
                                                >上传人：{{
                                                    item.SCR || ''
                                                }}</span
                                            > -->
                                            <i
                                                :class="[
                                                    {
                                                        type1:
                                                            item.XSMC ===
                                                            '电子书'
                                                    },
                                                    {
                                                        type2:
                                                            item.XSMC === '视频'
                                                    },
                                                    {
                                                        type3:
                                                            item.XSMC === '文档'
                                                    }
                                                ]"
                                                >{{ item.XSMC || '' }}</i
                                            >
                                            <el-button
                                                type="primary"
                                                plain
                                                round
                                                @click.stop="openExercise(item)"
                                                style="font-size: 14px"
                                                >模拟练习</el-button
                                            >
                                        </p>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                        <div class="gap clear"></div>
                        <div class="gap clear"></div>
                        <div class="gap clear"></div>
                    </div>
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <div class="pagination-block">
                        <el-pagination
                            v-model:current-page="pageNum"
                            :page-size="10"
                            layout="total, prev, pager, next"
                            :total="total"
                            background
                            @current-change="handleCurrentChange"
                        />
                    </div>
                    <div class="gap"></div>
                    <div class="gap"></div>
                </div>

                <!-- 大模块开始 -->
                <div
                    v-show="!showDetailContent"
                    style="
                        width: 100vw;
                        padding: 10px;
                        overflow: hidden;
                        box-sizing: border-box;
                    "
                >
                    <ul class="zy0925-data1" style="margin-top: 10px">
                        <li
                            v-for="(item, index) in knowledgeClassificationList"
                            :key="item.DM"
                            :class="{
                                on: knowledgeClassification === item.DM
                            }"
                            @click="knowledgeClassificationChangeAll(item)"
                            v-show="item.DMMC != '全部'"
                        >
                            <img
                                :src="
                                    require(`./images/zy0925_img${index}.png`)
                                "
                                alt=""
                            />
                            <p>{{ item.DMMC }}</p>
                        </li>
                        <!-- <li>
                            <img src="./images/zy0925_img2.png" alt="" />
                            <p>安全管理要求</p>
                        </li>
                        <li>
                            <img src="./images/zy0925_img3.png" alt="" />
                            <p>执法要点</p>
                        </li>
                        <li>
                            <img src="./images/zy0925_img4.png" alt="" />
                            <p>执法案例</p>
                        </li>
                        <li>
                            <img src="./images/zy0925_img5.png" alt="" />
                            <p>典型案例</p>
                        </li>
                        <li>
                            <img src="./images/zy0925_img6.png" alt="" />
                            <p>核安全文化</p>
                        </li>
                        <li>
                            <img src="./images/zy0925_img7.png" alt="" />
                            <p>监测管理性要求</p>
                        </li>
                        <li>
                            <img src="./images/zy0925_img8.png" alt="" />
                            <p>核技术基本知识</p>
                        </li>
                        <li>
                            <img src="./images/zy0925_img9.png" alt="" />
                            <p>审批许可实务</p>
                        </li> -->
                    </ul>
                </div>

                <!-- 大模块结束 -->
            </div>
        </div>
        <FilePreview
            ref="filePreview"
            :url="frameUrl"
            :fileInfo="currentFile"
            :isVideo="isVideo"
            :watermark="watermark"
            :previewData="previewData"
        ></FilePreview>
    </div>
</template>

<script>
import FilePreview from './FilePreview.vue';
import {
    getKnowledgeList,
    getCommonCodesFromCache,
    cancelCollectKnowledge,
    collectKnowledge,
    cancelShareKnowledge,
    shareKnowledge,
    getWaterMark,
    jcrsStatistics,
    getContent,
    updateDjcs
} from '@/api/knowledge.js';
import CryptoJS from 'crypto-js';
import nobannerImg from '@/assets/images/no_banner_pic.png';
export default {
    name: 'Knowledge',
    components: { FilePreview },
    data() {
        return {
            showDetailContent: false,
            nobannerImg,
            viewMode: 'card', //table表格 card卡片
            searchTxt: '',
            knowledgeClassification: '',
            knowledgeForm: '',
            knowledgeClassificationList: [],
            knowledgeFormList: [],
            pageNum: 1,
            list: [],
            total: 0,
            frameUrl: '',
            currentFile: {},
            previewData: {},
            isVideo: false,
            watermark: '',
            GRSC: '',
            timer: null,
            searchOptions: [],
            showOptions: false,
            knowledgeId: ''
        };
    },
    provide() {
        return {
            closePreview: this.closePreview
        };
    },
    created() {
        this.getCommonCode();
        this.getData();
        this.getWaterMark();
    },
    mounted() {},
    watch: {},
    methods: {
        backToHome() {
            this.showDetailContent = false;
        },

        getWaterMark() {
            getWaterMark({}).then((res) => {
                this.watermark = res.result || '';
            });
        },
        getCommonCode() {
            getCommonCodesFromCache({
                DMJBH: 'ZSFL',
                FDM: ''
            }).then((res) => {
                res.unshift({
                    DM: '',
                    DMMC: '全部'
                });
                this.knowledgeClassificationList = res;
            });
            getCommonCodesFromCache({
                DMJBH: 'ZSXS',
                FDM: ''
            }).then((res) => {
                res.unshift({
                    DM: '',
                    DMMC: '全部'
                });
                this.knowledgeFormList = res;
            });
        },
        getData() {
            this.showOptions = false;

            getKnowledgeList({
                searchTxt: this.searchTxt || '',
                ZSFL: this.knowledgeClassification || '',
                ZSXS: this.knowledgeForm || '',
                pageNum: this.pageNum,
                pageSize: 10,
                GRSC: this.GRSC,
                ZSKBH: this.knowledgeId
            }).then((res) => {
                this.list = res.result.list || [];
                this.total = res.result.total;
            });
        },
        getContent() {
            this.knowledgeId = '';
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                if (this.searchTxt) {
                    getContent({
                        searchTxt: this.searchTxt
                    }).then((res) => {
                        this.searchOptions = res.data_json;
                        this.showOptions = true;
                    });
                }
            }, 500);
        },
        selectOption(item) {
            this.searchTxt = item.WJMC.slice(0, item.WJMC.indexOf('.'));
            this.knowledgeId = item.ZSKBH;
            this.showOptions = false;
            this.getData();
        },
        viewModeChange(value) {
            this.viewMode = value;
        },
        knowledgeClassificationChange(item) {
            this.knowledgeClassification = item.DM;
            this.getData();
        },
        knowledgeClassificationChangeAll(item) {
            this.showDetailContent = true;
            this.knowledgeClassification = item.DM;
            this.getData();
        },
        knowledgeFormChange(item) {
            this.knowledgeForm = item.DM;
            this.getData();
        },
        cancelCollectKnowledge(item) {
            cancelCollectKnowledge({
                SCXH: item.SCXH
            }).then((res) => {
                this.getData();
                this.$message.success('取消收藏成功！');
            });
        },
        collectKnowledge(item) {
            collectKnowledge({
                ZSKBH: item.XH
            }).then((res) => {
                this.getData();
                this.$message.success('收藏成功！');
            });
        },
        cancelShareKnowledge(item) {
            cancelShareKnowledge({
                FXXH: item.FXXH
            }).then((res) => {
                this.getData();
                this.$message.success('取消收藏成功！');
            });
        },
        shareKnowledge(item) {
            shareKnowledge({
                ZSKBH: item.XH
            }).then((res) => {
                this.getData();
                this.$message.success('收藏成功！');
            });
        },
        previewFile(item) {
            console.log(item, 'item');

            updateDjcs({
                XH: item.XH
            }).then((res) => {
                console.log(res);
            });

            this.previewData = item;
            this.currentFile = item.FileInfo;
            this.isVideo = item.XS === 'SP' ? true : false;
            let fileUrl = '';
            if (item.FileInfo && item.FileInfo.WJLX.toLowerCase() === 'pdf') {
                this.frameUrl =
                    ServerGlobalConstant.flipbookPreviewUrl +
                    item.FileInfo.WJID;
                let frameUrl = this.frameUrl;
                top.Common.dialog({
                    type: 'open',
                    title: item.FileInfo.WJMC,
                    width: '100%',
                    height: '100%',
                    url: frameUrl
                });
            } else {
                if (item.XS === 'WD') {
                    fileUrl = `${ServerGlobalConstant.fileDownloadUrl}${item.FileInfo?.WJID}?fullfilename=${item.FileInfo?.WJMC}&page=2`;
                } else {
                    fileUrl = `${ServerGlobalConstant.fileDownloadUrl}${item.FileInfo?.WJID}?fullfilename=${item.FileInfo?.WJMC}`;
                }
                this.frameUrl =
                    ServerGlobalConstant.previewUrl +
                    encodeURIComponent(
                        CryptoJS.enc.Base64.stringify(
                            CryptoJS.enc.Utf8.parse(fileUrl)
                        )
                    );
                +`&watermarkTxt=${encodeURIComponent(this.watermark)}`;
                this.$nextTick(() => {
                    this.$refs.filePreview.showPreviewDialog();
                });
            }
        },
        closePreview() {
            this.currentFile = {};
            this.previewData = {};
            this.frameUrl = '';
        },
        getBanner(item) {
            if (item.FmInfo?.WJID) {
                return ServerGlobalConstant.bannerUrl + item.FmInfo?.WJID;
            } else {
                return nobannerImg;
            }
        },
        handleCurrentChange(value) {
            this.pageNum = value;
            this.getData();
        },
        myFav() {
            if (this.GRSC == '') {
                this.GRSC = '1';
            } else {
                this.GRSC = '';
            }
            this.getData();
        },
        openExercise(item) {
            this.$router.push({
                path: '/exercise/' + item.XH,
                query: {
                    ZSK: '1'
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.sw1212-dlbx2 dd h1 {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.sw1212-ultbs1 li {
    cursor: pointer;
}
.sw1212-ultbs1 {
    justify-content: flex-start;
}
.pagination-block {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.sw1212-tablelst1 tr:hover {
    background: #e4f5ff;
}

.sw1212-dlbx2 dt .img {
    width: 100%;
    height: 100%;
}

.rt-btn {
    background: #fff;
    position: absolute;
    right: 10px;
    top: 12px;
    width: 47px;
    padding: 0 2px;
    height: 20px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-radius: 20px;
}
.sw1212-dlbx2 dt .rt-btn sup.star {
    position: unset;
}
.sw1212-dlbx2 dt .rt-btn sup.share {
    position: unset;
}
.search-option {
    height: 50px;
    border-bottom: 1px solid #eee;
    line-height: 50px;
    font-size: 16px;
    padding-left: 10px;
    cursor: pointer;
}
.search-option:hover {
    background: #efefef;
}

.zy0925-data1 {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -1%;
}
.zy0925-data1 li {
    width: 31%;
    height: 172px;
    margin: 0 1%;
    margin-bottom: 30px;
    background: #eff9ff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a9dfff;
    display: flex;
    align-items: center;
    padding-left: 78px;
    box-sizing: border-box;
}
.zy0925-data1 li > img {
    width: 80px;
    height: 80px;
}
.zy0925-data1 li > p {
    font-size: 24px;
    color: #3d3d3d;
    line-height: 32px;
    margin-left: 30px;
}
</style>
