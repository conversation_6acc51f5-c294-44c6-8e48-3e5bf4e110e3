<!-- @format -->
<!-- 业务主要入口 -->
<template>
    <div style="position: fixed; top: 10px; left: 10px" v-show="mod != 'look'">
        <el-button type="primary" @click="btnPlotClick('POLYGON')"
            >范围标绘</el-button
        >
        <el-button type="primary" @click="btnPlotClick('POINT')"
            >点标绘</el-button
        >
        <el-button type="primary" @click="btnPlotClick('POLYLINE')"
            >线标绘</el-button
        >
        <el-button type="warning" @click="btnClearAllClick">清除全部</el-button>
        <el-button type="warning" @click="btnClearSelectClick"
            >清除选中</el-button
        >
        <el-button type="success" @click="btnConfirmClick">保存</el-button>

        <Map2DPlot ref="childPlot" :map="map"></Map2DPlot>

        <div
            class="pd-srhbx1-gis"
            style="position: fixed; right: 20px; top: 20px; left: auto"
        >
            <input
                type="text"
                v-model="keyStr"
                placeholder="关键字搜索"
                @keydown="keydownHandle"
            />
            <button type="button" @click="searchByKeyWord"></button>
        </div>
    </div>
</template>
<script>
import Map2DPlot from '@/components/gis/2D/Map2DPlot';
import axios from 'axios';
export default {
    name: 'GetPointMain',
    created() {},
    data() {
        return {
            arrLegend: [],
            showPanel: false, //右侧面板是否显示

            selectItem: {},
            mod: '',
            drawTool: null,
            keyStr: ''
        };
    },
    props: ['map'],
    unmounted() {},
    components: { Map2DPlot },
    mounted() {
        // //图例数组
        // this.arrLegend = [
        //     {
        //         title: '点位类型',
        //         data: [
        //             { name: '断面', url: 'tuli1.png' },
        //             { name: '自动站', url: 'tuli2.png' }
        //         ]
        //     }
        // ];

        this.initPage();
    },
    methods: {
        //页面初始化
        initPage() {
            if (!this.map) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.createMap();

            this.initEvent();
        },

        //加载地图相关资源、以及初始化地图操作相关的图层等
        createMap() {
            this.addMask();
            this.addXZQ();
        },

        //定义地图事件
        initEvent() {
            let self = this;
            this.map.on('zoomend', (evt) => {});

            //地图加载完成交互
            window.parent.postMessage(
                {
                    type: 'mapLoadComplete',
                    item: {}
                },
                '*'
            );

            //接受父页面的初始值
            window.addEventListener(
                'message',
                (e) => {
                    if (e.data.type == 'initData') {
                        console.log('接收到父页面消息');
                        console.log(e.data.item);
                        this.selectItem = e.data.item;
                        this.drawInitGra();
                    }
                },
                false
            );

            // //默认调用激活图层的编辑事件
            // this.drawTool = this.$refs.childPlot.drawData('POLYGON', false);

            // setTimeout(() => {
            //     if (this.drawTool) {
            //         this.drawTool.toolBar.deactivate();
            //         this.drawTool.toolBar1.deactivate();
            //     }
            // }, 1000);
        },

        showPanleChangeHandle(showPanel) {
            this.showPanel = showPanel;
        },
        //地图点击事件,触发事件，与前端进行交互
        pointClickHandle(type, item) {
            this.$emit('mapEvent', type, item);
        },

        //绘制遮罩
        addMask() {
            let url = './gis/2D/data/昆山市.json';
            $.getJSON(url, (data) => {
                let item = data.features[0];
                let obj = {};
                obj = item.attributes;
                obj.rings = item.geometry.rings;
                obj.option = {
                    color: [255, 255, 255, 0.7], //填充颜色
                    tcstyle: 'STYLE_SOLID', //填充样式
                    lineColor: [128, 0, 255, 0.7], //设置符号线的颜色
                    style: 'STYLE_SOLID',
                    lineWidth: 1 //线的宽度
                };

                PowerGis.addShadePolygon(
                    this.map,
                    '添加行政区划遮罩',
                    obj,
                    true,
                    (layer) => {}
                );
            });
        },

        //绘制行政区边界线
        addXZQ() {
            let url = './gis/2D/data/昆山镇界.json';
            $.getJSON(url, (data) => {
                let features = data.features;

                let datas = [];
                let datas2 = [];

                for (let feature of features) {
                    let item = {};

                    item = {
                        rings: feature.geometry.rings[0],
                        wkid: 4326,
                        option: {
                            lineColor: [255, 255, 255], //设置符号线的颜色
                            style: 'STYLE_SOLID', //设置线的样式
                            lineWidth: 1.5 //先的宽度
                        }
                    };
                    datas.push(item);

                    let item2 = {
                        name: feature.attributes.NAME,
                        JD: feature.attributes.INSIDE_X,
                        WD: feature.attributes.INSIDE_Y,
                        option: {
                            //该对象是设置不同图片的、没有则使用默认图片
                            size: 10, //大小
                            haloSize: 1, //文字阴影大小
                            angle: 0, //角度
                            haloColor: [0, 0, 0], //文字阴影颜色
                            color: [255, 255, 255], //填充颜色
                            fontSize: '12pt', //文字大小
                            offsety: '0', //y偏差
                            offsetx: '0' //x偏差
                        }
                    };

                    datas2.push(item2);
                }

                PowerGis.addLine(
                    this.map,
                    '行政区界线',
                    datas,
                    false,
                    (layer) => {}
                );

                PowerGis.addTextPoint(
                    this.map,
                    '行政区注记',
                    datas2,
                    false,
                    (layer) => {}
                );
            });
        },

        drawInitGra() {
            if (this.selectItem.JD && this.selectItem.WD) {
                //绘制点位
                let pt = {
                    x: this.selectItem.JD,
                    y: this.selectItem.WD
                };

                this.$refs.childPlot.drawPoint(pt, '定位点');
            }

            this.selectItem.YQFW = JSON.parse(this.selectItem.YQFW);

            if (this.selectItem.YQFW && this.selectItem.YQFW.length) {
                this.addYQFW();
            }

            //默认调用激活图层的编辑事件
            this.drawTool = this.$refs.childPlot.drawData('POLYGON', false);

            setTimeout(() => {
                if (this.drawTool) {
                    this.drawTool.toolBar.deactivate();
                    this.drawTool.toolBar1.deactivate();
                }
            }, 1000);
        },

        //标绘
        btnPlotClick(type) {
            this.$refs.childPlot.drawData(type, false);
        },

        //清除全部
        btnClearAllClick() {
            this.$refs.childPlot.clear();
        },

        //清除选中的要素
        btnClearSelectClick() {
            this.$refs.childPlot.clearSelectGra();
        },

        //获取所有的标绘数据
        btnConfirmClick() {
            this.getZBAll();
        },

        //获取所有的绘制要素，交互给父页面
        getZBAll() {
            let layer = this.map.getLayer('标绘图层');
            if (layer) {
                let gs = layer.graphics;

                let geoJson = gs.map((gra) => {
                    return gra.toJson();
                });

                let item = {
                    YQFW: geoJson,
                    ZXWD: null,
                    ZXJD: null,
                    ZDWD: null,
                    ZDJD: null
                };

                let arr = gs.map((g) => {
                    return g.geometry;
                });

                if (arr.length > 0) {
                    let union = PowerGis.geometryEngine.union(arr);
                    let ext = union.getExtent();

                    item.ZXWD = ext.ymin;
                    item.ZXJD = ext.xmin;
                    item.ZDWD = ext.ymax;
                    item.ZDJD = ext.xmax;
                }

                Object.assign(this.selectItem, item);

                console.log('保存成功');

                let param = JSON.parse(
                    JSON.stringify({
                        type: 'PlotSucess', //标绘成功
                        item: this.selectItem
                    })
                );

                console.log(param);

                window.parent.postMessage(param, '*');
            }
        },

        //绘制园区范围
        addYQFW() {
            this.selectItem.YQFW.forEach((gra) => {
                let rings = gra.geometry.rings;
                let obj = {};
                obj.rings = rings;
                obj.title = gra.attributes.title;

                this.$refs.childPlot.drawPolygon(obj);
            });
        },

        keydownHandle(e) {
            if (e.keyCode == '13' && this.keyStr) {
                this.searchByKeyWord();
            }
        },

        //根据关键字搜索
        searchByKeyWord() {
            if (!this.keyStr) {
                return;
            }
            let url = 'http://api.tianditu.gov.cn/geocoder';

            let _this = this;

            axios
                .get(url, {
                    params: {
                        ds: JSON.stringify({
                            keyWord: '昆山市' + this.keyStr
                        }),
                        tk: 'ace32c3e47b85e181be05e15eb85d48c'
                    }
                })
                .then(function (response) {
                    if (response.data.msg == 'ok') {
                        let item = {
                            JD: response.data.location.lon,
                            WD: response.data.location.lat
                        };

                        PowerGis.pointTo(_this.map, item, true);
                    }
                })
                .catch(function (error) {
                    console.log(error);
                });
        }
    },
    watch: {},
    computed: {}
};
</script>

<style>
.gis-search {
    position: absolute;
    top: 77px;
    left: 172px;
}

.gis-legend {
    position: absolute;
    left: 170px;
    bottom: 80px;
}
</style>
