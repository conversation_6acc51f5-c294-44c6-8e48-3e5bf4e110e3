<!-- @format -->

<template>
    <dl class="air-quality">
        <dt class="air-quality-lf">
            <div>
                <p-air-db
                    style="height: 150px; width: 150px"
                    :data="{ value: data.AQI }"
                    :config="{
                        barWidth: 10
                    }"
                    :option="option"
                    show-option
                ></p-air-db>
            </div>
            <p class="air-quality-lf-plt">
                <em>首要污染物：</em>
                <em class="plt">{{
                    replacePltName(data.firstPollution) || '无'
                }}</em>
            </p>
        </dt>
        <dd class="air-quality-rt">
            <div class="air-quality-rt-box">
                <div
                    class="air-quality-rt-box-item"
                    v-for="item in opts"
                    :key="item.value"
                >
                    <h1 class="air-quality-rt-box-item-label">
                        <em v-html="item.label"></em>
                        <i
                            :style="{
                                color: getPollutionColor(
                                    item.value,
                                    data[item.value]
                                )
                            }"
                            >{{ data[item.value] || '' }}</i
                        >
                    </h1>
                </div>
            </div>
            <p class="air-quality-rt-tip">单位：μg/m³(CO:mg/m³)</p>
        </dd>
    </dl>
</template>
<script>
import util from './util';
export default {
    name: 'airQualityNow',
    props: {
        data: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    watch: {
        data: 'setOpt'
    },
    data() {
        return {
            opts: [
                { label: 'PM<sub>2.5</sub>', value: 'PM25' },
                { label: 'SO<sub>2</sub>', value: 'SO2' },
                { label: 'PM<sub>10</sub>', value: 'PM10' },
                { label: 'NO<sub>2</sub>', value: 'NO2' },
                { label: 'O<sub>3</sub>', value: 'O3' },
                { label: 'CO', value: 'CO' }
            ],
            option: {
                title: {
                    top: '22%',
                    textStyle: {
                        fontWeight: 'normal'
                    }
                },
                polar: {
                    radius: '190%'
                },
                series: [
                    {},
                    {},
                    {
                        detail: {
                            offsetCenter: [0, '50%'],
                            fontWeight: 'normal'
                        },
                        title: {
                            fontWeight: 'normal',
                            fontFamily: 'DIN-Bold'
                        }
                    }
                ]
            }
        };
    },
    mounted() {
        this.setOpt();
    },

    methods: {
        setOpt() {
            if (util.getLevelPollution('AQI', this.data.AQI).txt === '良') {
                this.option.series[2].detail.color = '#333';
            } else {
                this.option.series[2].detail.color = '#fff';
            }
        },
        //替换污染物下标
        replacePltName(code) {
            return util.replacePltName(code);
        },

        getPollutionColor(name, value) {
            return util.getLevelPollution(name, value).color;
        }
    }
};
</script>
<style scoped lang="less">
.air-quality {
    display: flex;
    &-lf {
        margin-right: 20px;
        padding-top: 10px;

        &-plt {
            text-align: center;
            line-height: 24px;
            margin-top: -10px;
        }
    }
    &-rt {
        flex: 1;
        max-width: 350px;
        &-box {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            height: 150px;
            &-item {
                width: 118px;
                height: 50px;
                line-height: 50px;
                box-sizing: border-box;
                max-width: 46%;

                &-label {
                    display: flex;
                    justify-content: space-between;

                    > em {
                        font-size: 18px;
                        font-weight: normal;
                        ::v-deep sub {
                            vertical-align: baseline;
                            font-size: 12px;
                        }
                    }
                    > i {
                        font-family: 'DIN-Medium';
                        font-size: 18px;
                        float: right;
                    }
                }
                &-bar {
                    height: 5px;
                    border-radius: 5px;
                    margin-top: 5px;
                }
            }
        }

        &-tip {
            text-align: right;
            line-height: 24px;
        }
    }
}
</style>
