<!-- @format -->

<template>
    <div>
        <div style="height: 310px; overflow: auto">
            <table class="gm-table1">
                <tr v-for="(item, index) in data" :key="item">
                    <td>
                        <img
                            src="./images/no1.png"
                            alt=""
                            v-show="index == 0"
                        /><img
                            src="./images/no2.png"
                            alt=""
                            v-show="index == 1"
                        /><img
                            src="./images/no3.png"
                            alt=""
                            v-show="index == 2"
                        />
                        <span v-show="parseInt(index) > 2">{{
                            index + 1
                        }}</span>
                    </td>
                    <td>{{ item.name }}</td>
                    <td>
                        <div class="gm-bar1">
                            <div
                                class="bili"
                                :style="{ width: item.percent + '%' }"
                            ></div>
                        </div>
                    </td>
                    <td>{{ item.percent }}%</td>
                </tr>
            </table>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        }
    }
};
</script>

<style scoped>
table {
    border: none;
}
tr {
    border: none;
}
th,
td {
    border: none;
}
.gm-table1 {
    width: 100%;
}

.gm-table1 td {
    height: 52px;
    font-size: 16px;
    /* color: #333; */
    text-align: center;
    padding: 0 5px;
}

.gm-table1 td:nth-of-type(1) {
    /* color: #666; */
}

.gm-bar1 {
    width: 400px;
    height: 15px;
    border-radius: 8px;
    background-color: #f5f7fd;
}

.gm-bar1 .bili {
    width: 80%;
    height: 15px;
    border-radius: 8px;
    background: linear-gradient(to right, #25d8fc, #1c8cdf);
}
</style>
