/** @format */

/** 雷达图 */
class Lidar {
    constructor(userOptions) {
        this.options = [];
        Object.assign(this.options, userOptions);

        this.radius = this.options.radius;

        this.baseCanvas = document.createElement('canvas');
        this.baseCanvas.width = this.radius * 2;
        this.baseCanvas.height = this.radius * 2;

        this.center = {
            x: this.radius,
            y: this.radius
        };

        //全局变量
        this.baseCtx = this.baseCanvas.getContext('2d');

        this.getColor = this.segmentedColorScale([
            [0, [35, 14, 208]],
            [5, [19, 55, 219]],
            [10, [12, 132, 233]],
            [15, [2, 234, 251]],
            [20, [0, 220, 186]],

            [25, [0, 181, 108]],
            [30, [0, 139, 22]],
            [35, [41, 148, 13]],
            [40, [96, 176, 31]],
            [45, [143, 199, 46]],

            [50, [189, 221, 33]],
            [55, [215, 235, 19]],
            [60, [245, 249, 5]],
            [65, [255, 226, 0]],
            [70, [255, 193, 0]],

            [75, [254, 168, 0]],
            [80, [255, 115, 0]],
            [85, [255, 59, 0]],
            [90, [252, 1, 0]],
            [95, [213, 0, 0]],
            [100, [169, 0, 0]]
        ]);

        this.timeStamp = 100;

        this.start();
    }

    getImage() {
        let src = this.baseCanvas.toDataURL();

        // console.log(src);
        return src;
    }

    /**
     * 添加图片资源
     * @param {*} imgUrl
     * @returns
     */
    addImage(map, layerName, imgUrl, bounds, initOpcity) {
        let rectangle = {
            xmin: bounds[0],
            xmax: bounds[2],
            ymin: bounds[1],
            ymax: bounds[3]
        };

        let ext = [
            [rectangle.xmin, rectangle.ymin],
            [rectangle.xmax, rectangle.ymin],
            [rectangle.xmax, rectangle.ymax],
            [rectangle.xmin, rectangle.ymax]
        ];

        let id = layerName;
        let source = map.getSource(id);
        if (source) {
            source.updateImage({
                url: imgUrl,
                coordinates: ext.reverse()
            });
            return;
        }

        map.addSource(id, {
            type: 'image',
            url: imgUrl,
            coordinates: ext.reverse()
        });

        map.addLayer({
            id: id,
            type: 'raster',
            source: id,
            paint: {
                'raster-fade-duration': 0,
                'raster-opacity': initOpcity
            }
        });
    }

    start() {
        this.dataset = this.options.data;

        //画激光雷达图
        this.baseCtx.clearRect(
            0,
            0,
            this.baseCtx.canvas.width,
            this.baseCtx.canvas.height
        );
        //绘制雷达扇形
        this.index = 0;

        this.batchInterpolate();
    }

    batchCompute(index, dataset) {
        let element = dataset[index];
        let grd = this.baseCtx.createRadialGradient(
            this.center.x,
            this.center.y,
            0,
            this.center.x,
            this.center.y,
            this.radius
        );

        for (let i = 0, dataLength = element.length; i < dataLength; i++) {
            let value = parseFloat(element[i]) * 100;

            //point, alpha, min, max
            let rgb = this.getColor(value, 1, 0, this.options.max * 100);
            let opcity = 1;
            let color =
                'rgba(' +
                rgb[0] +
                ', ' +
                rgb[1] +
                ',' +
                rgb[2] +
                ',' +
                opcity +
                ')';

            grd.addColorStop(i / dataLength, color);
        }
        this.baseCtx.save();
        //绘制扇形
        this.baseCtx.beginPath();
        this.baseCtx.moveTo(this.center.x, this.center.y);

        let startAngle =
            this.options.startAngle + index * this.options.gapAngle;
        let stopAngle = startAngle + this.options.gapAngle;

        this.baseCtx.arc(
            this.center.x,
            this.center.y,
            this.radius,
            (startAngle * Math.PI) / 180,
            (stopAngle * Math.PI) / 180
        );
        this.baseCtx.closePath();
        this.baseCtx.fillStyle = grd;
        this.baseCtx.fill();
        this.baseCtx.restore();
    }

    batchInterpolate() {
        let start = Date.now();
        while (this.index < this.dataset.length) {
            this.batchCompute(this.index, this.dataset);
            this.index += 1;
            if (Date.now() - start > self.timeStamp) {
                console.log('index', this.index);
                setTimeout(this.batchInterpolate, 25);
                return;
            }
        }
    }

    segmentedColorScale(segments) {
        let points = [],
            interpolators = [],
            ranges = [];
        for (let i = 0; i < segments.length - 1; i++) {
            points.push(segments[i + 1][0]);
            interpolators.push(
                this.colorInterpolator(segments[i][1], segments[i + 1][1])
            );
            ranges.push([segments[i][0], segments[i + 1][0]]);
        }
        return function (point, alpha, min, max) {
            let i;
            point = (100 * (point - min)) / (max - min);
            for (i = 0; i < points.length - 1; i++) {
                if (point <= points[i]) {
                    break;
                }
            }
            let range = ranges[i];
            return interpolators[i](
                this.proportion(point, range[0], range[1]),
                alpha
            );
        };
    }

    colorInterpolator(start, end) {
        let r = start[0],
            g = start[1],
            b = start[2];
        let Δr = end[0] - r,
            Δg = end[1] - g,
            Δb = end[2] - b;
        return function (i, a) {
            return [
                Math.floor(r + i * Δr),
                Math.floor(g + i * Δg),
                Math.floor(b + i * Δb),
                a
            ];
        };
    }

    proportion(x, low, high) {
        return (this.clamp(x, low, high) - low) / (high - low);
    }

    clamp(x, low, high) {
        return Math.max(low, Math.min(x, high));
    }
}

export default Lidar;
