<!-- @format -->

<template>
    <div>
        <p-bar
            :data="barData"
            :option="barOpt"
            style="width: 400px; height: 200px"
        ></p-bar>
    </div>
</template>

<script>
export default {
    props: {
        data: {
            type: Array
        },
        option: {
            type: Object
        }
    },
    created() {
        this.$pChart.setChartConfig({
            SHOW_TOOLBOX: false
        });
    },
    data() {
        return {
            barData: {},
            barOpt: {
                xAxis: [
                    {
                        type: 'value',
                        inverse: true,
                        splitLine: { show: false },
                        axisLabel: { show: false }
                    },
                    {
                        type: 'value',
                        inverse: false,
                        gridIndex: 1,
                        splitLine: { show: false },
                        axisLabel: { show: false }
                    }
                ],
                yAxis: [
                    {
                        position: 'right',
                        type: 'category',
                        axisLabel: { show: true },
                        axisLine: { show: true },
                        axisTick: { show: false },
                        inverse: true
                    },
                    {
                        type: 'category',
                        axisLabel: { color: 'transparent' },
                        axisLine: { show: true },
                        axisTick: { show: false },
                        inverse: true,
                        gridIndex: 1
                    }
                ],
                grid: [
                    {
                        left: 30,
                        width: '40%',
                        containLabel: true
                    },
                    {
                        left: '37%',
                        width: '40%',
                        containLabel: true
                    }
                ],
                series: [
                    {
                        type: 'bar',
                        label: {
                            show: true,
                            position: 'left',
                            fontSize: 12
                        },
                        itemStyle: {
                            normal: {
                                barBorderRadius: [20, 0, 0, 20]
                            }
                        }
                    },
                    {
                        yAxisIndex: 1,
                        xAxisIndex: 1,
                        type: 'bar',
                        label: {
                            show: true,
                            position: 'right',
                            fontSize: 12
                        },
                        itemStyle: {
                            normal: {
                                barBorderRadius: [0, 20, 20, 0]
                            }
                        }
                    }
                ],

                legend: {
                    top: '15%'
                }
            }
        };
    },
    mounted() {
        this.initBar();
    },
    methods: {
        initBar() {
            // Y轴
            const yAxis = this.data.map((item) => {
                return `${item.year}`;
            });

            let s1 = []; // 处罚金额
            let s2 = []; // 案件数量
            for (let i in this.data) {
                for (let j in this.data[i].data) {
                    if (this.data[i].data[j].name === '处罚金额') {
                        s1.push(this.data[i].data[j].value);
                    }
                    if (this.data[i].data[j].name === '案件数量') {
                        s2.push(this.data[i].data[j].value);
                    }
                }
            }

            this.barData = {
                yAxis: yAxis,
                series: [
                    {
                        name: '处罚金额（万元）',
                        data: s1
                    },
                    {
                        name: '案件数量（件）',
                        data: s2
                    }
                ]
            };

            this.barOpt.yAxis[0].data = yAxis;
            this.barOpt.yAxis[1].data = yAxis;
        }
    }
};
</script>

<style></style>
