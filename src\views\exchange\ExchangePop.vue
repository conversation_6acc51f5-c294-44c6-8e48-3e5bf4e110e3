<!-- @format -->

<template>
    <el-dialog
        class="dialog_custom"
        v-model="show"
        title="查看"
        :width="1600"
        :before-close="closePreviewDialog"
        top="3vh"
    >
        <div style="background: #f1f3f7">
            <div
                class="sw1212-wrap"
                style="background: #f1f3f7; height: 950px; width: 100%"
            >
                <div class="sw1212-dlghd">
                    <strong>{{ clickData.ZXBT || '-' }}</strong>
                    <img
                        src="@/assets/exam/images/sw1212_cls2.png"
                        class="icon"
                        @click="$emit('close')"
                    />
                </div>
                <div class="sw1212-dlgbd" style="overflow-y: scroll">
                    <dl class="sw0130-dlbx1">
                        <dt>
                            {{ clickData.ZXXQ || '-' }}
                        </dt>
                        <dd class="flx1 ac">
                            <span
                                ><em>提问人：</em
                                >{{ clickData.CJR || '-' }}</span
                            >
                            <span
                                ><em>提问时间：</em
                                >{{ clickData.CJSJ || '-' }}</span
                            >
                            <span
                                ><em>问题来源：</em
                                >{{ clickData.GLZSMC || '-' }}</span
                            >
                        </dd>
                        <dd class="flx1 ac jb">
                            <div class="flx1 ac">
                                <button class="sw0130-btn1" @click="send">
                                    写回答
                                </button>
                                <i
                                    class="sw0130-ictxt1"
                                    style="margin-left: 24px"
                                    >共{{ list.length }}条评论</i
                                >
                            </div>
                            <!-- <ul class="sw0130-ulbx1">
                                <li>
                                    <h2>被收藏</h2>
                                    <p>1250</p>
                                </li>
                                <li>
                                    <h2>被浏览</h2>
                                    <p>12546</p>
                                </li>
                            </ul> -->
                        </dd>
                    </dl>
                    <div class="gap16"></div>
                    <ul class="sw0130-ullst2" v-if="list.length">
                        <li v-for="(item, index) in list" :key="index">
                            <dl>
                                <dt>
                                    <img
                                        src="@/assets/exam/images/sw0130_hdpic1.png"
                                        alt=""
                                    />
                                </dt>
                                <dd>
                                    <h1>{{ item.LYRMC || '-' }}</h1>
                                    <p>发布于 {{ item.LYSJ || '-' }}</p>
                                </dd>
                            </dl>
                            <p>
                                {{ item.LYNR || '-' }}
                            </p>
                            <ul class="sw0130-ulbx2">
                                <li
                                    @click="like('1', item, index)"
                                    :class="{ on: item.check == '1' }"
                                >
                                    <i class="i1">赞同 {{ item.ZTZS }}</i>
                                </li>
                                <li
                                    @click="like('0', item, index)"
                                    :class="{ on: item.check == '0' }"
                                >
                                    <i class="i2">不赞同 {{ item.BZTZS }}</i>
                                </li>
                            </ul>
                        </li>
                    </ul>
                    <el-empty description="暂无数据" v-else />
                </div>
            </div>
        </div>
    </el-dialog>

    <el-dialog
        v-model="showTips"
        title="留言"
        width="30%"
        top="30vh"
        :show-close="false"
    >
        <div>
            <div class="destitle">
                <i class="iocn"></i>
                <i>留言内容</i>
            </div>
            <el-input v-model="desc" type="textarea" :rows="6" />
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="saveMessage()">
                    提交
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import { getLynrList, agreeMessage, saveMessage } from '@/api/exchange.js';
import { reactive } from 'vue';
export default {
    name: 'ExchangePop',
    components: {},
    emits: ['close'],
    props: ['clickData'],
    data() {
        return {
            desc: '',
            showTips: false,
            timer: null,
            likeType: '',
            index: 0,
            item: {},
            pageSize: 10,
            pageNum: 1,
            show: false,
            list: []
        };
    },
    created() {},
    unmounted() {},
    watch: {
        likeType() {
            this.agreeMessage();
        },
        item() {
            this.agreeMessage();
        }
    },
    mounted() {
        this.show = true;
        this.getLynrList();
    },
    methods: {
        getLynrList() {
            getLynrList({
                YWXTBH: this.clickData.XH
            }).then((res) => {
                this.list = res.list;
            });
        },
        like(SFZT, item, index) {
            if (SFZT == '1') {
                this.likeType = '1';
            } else {
                this.likeType = '0';
            }
            this.index = index;
            this.item = item;
        },
        agreeMessage() {
            this.timer = setTimeout(() => {
                clearTimeout(this.timer);
                agreeMessage({
                    YWXTBH: this.item.YWXTBH,
                    HDJLXH: this.item.XH,
                    SFZT: this.likeType,
                    CJR: this.item.LYRMC
                }).then((res) => {
                    let obj = reactive(this.item);
                    if (this.likeType == '1') {
                        obj.ZTZS++;
                    } else {
                        obj.BZTZS++;
                    }
                    obj.check = this.likeType;
                    this.list[this.index] = obj;
                });
            }, 200);
        },
        send() {
            this.showTips = true;
        },

        saveMessage() {
            if (this.desc == '') {
                this.$message({
                    message: '请输入留言内容',
                    type: 'warning'
                });
            } else {
                saveMessage({
                    XH: '',
                    YWXTBH: this.clickData.XH,
                    LYNR: this.desc
                }).then((res) => {
                    this.$message({
                        message: '留言成功',
                        type: 'success'
                    });
                    this.showTips = false;
                    this.getLynrList();
                });
            }
        },
        closePreviewDialog() {
            this.show = false;
            this.$emit('close');
        }
    }
};
</script>

<style>
.dialog_custom .el-dialog__body {
    height: calc(100% - 44px);
    width: 100%;
    padding: 0;
}

.destitle {
    position: relative;
    height: 45px;
    line-height: 45px;
    text-align: left;
    height: 100%;
    font-size: 15px;
    font-weight: 700;
    color: #5f5f5f;
    vertical-align: bottom;
    padding-left: 30px;
}

.destitle .iocn {
    margin-top: 10px;
    margin-right: -8px;
    content: '';
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #ff911c;
    position: absolute;
    left: 10px;
    top: 5px;
}
</style>
