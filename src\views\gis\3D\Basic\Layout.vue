<!-- @format -->
<!-- 业务主要入口 -->
<template>
    <div>
        <!-- 搜索框 -->
        <SearchTool
            class="gis-search"
            :arrSearch="arrSearch"
            :map="map"
            dock="left"
        ></SearchTool>

        <!-- 图例 -->
        <LegendTool
            class="gis-legend"
            :arrLegend="arrLegend"
            :expand="true"
        ></LegendTool>
    </div>
</template>
<script>
import { getPointHourMaxDate } from '@/api/gis/3D/AirMap/index';

import SearchTool from '@/components/gis/3D/SearchTool';
import LegendTool from '@/components/gis/3D/LegendTool';
import PointUtil from './utils/PointUtil';

export default {
    data() {
        return {
            showPanel: false, //左右侧面板是否展示
            arrLegend: [], //图例
            arrSearch: [], //搜索框绑定数据
            objSearch: {}, //搜索框中转数据
            pointUtil: null
        };
    },
    provide() {
        return {
            pointClickHandle: this.pointClickHandle
        };
    },
    props: [],
    unmounted() {},
    components: {
        SearchTool,
        LegendTool
    },
    mounted() {
        this.arrLegend = [
            {
                title: '污染源',
                data: [{ name: '扬尘源', url: 'YCY-ZC.png' }]
            }
        ];
        this.initPage();
    },
    methods: {
        //页面初始化
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.initEvent();

            //this.getPointHourMaxDate();
        },

        //请求最新时间
        getPointHourMaxDate() {
            getPointHourMaxDate({}).then((res) => {
                this.curentTime = res.data.JCSJ;
            });
        },

        //定义地图事件
        initEvent() {
            let self = this;
            // this.map.on('zoomend', (evt) => {});
        },

        //地图点位点击事件
        pointClickHandle(type, item) {
            console.log('点击了地图：' + type);
            this.$emit('mapEvent', type, item);
        },

        //前端右侧面板
        showPanleChangeHandle(showPanel) {
            this.showPanel = showPanel;
        },

        //刷新查询框的数据
        refreshSearchData(data) {
            Object.assign(this.objSearch, data);

            this.arrSearch = [];
            for (let key in this.objSearch) {
                for (let item of this.objSearch[key]) {
                    let obj = {
                        name: item.CDMC || item.QYMC || item.NAME,
                        type: key,
                        JD: item.JD,
                        WD: item.WD
                    };

                    this.arrSearch.push(obj);
                }
            }
        }
    }
};
</script>

<style scoped>
.gis-search {
    position: absolute;
    top: 80px;
    left: 240px;
}

.gis-legend {
    position: absolute;
    left: 10px;
    bottom: 10px;
    height: 95px !important;
}
</style>
