<!-- @format -->

<template>
    <div style="background-color: #033b48; width: 1600px; height: 800px">
        <div class="pd-panel1a">
            <div class="pd-treesider" v-if="QYS1.length">
                <div v-for="(item, index) in QYS1" :key="index">
                    <h1
                        :class="QYS1Val == index ? 'on cur' : 'cur'"
                        :title="item.QYMC"
                        style="position: relative; color: #fff"
                        @click="changeQYS1(index)"
                    >
                        <div class="td-text">
                            <i>{{ item.QYMC }}</i>
                        </div>
                    </h1>
                    <ul>
                        <li
                            v-for="(items, indexs) in item.list"
                            :key="indexs"
                            :class="QYS1ZiVal == indexs ? 'on cur' : 'cur'"
                            @click="changeQYS1Zi(indexs, items.PKXH)"
                        >
                            {{ items.PKMC }}
                        </li>
                    </ul>
                </div>
            </div>
            <div class="pd-rtcon1">
                <p class="zy-til1">基本信息</p>
                <div class="gap"></div>

                <table cellpadding="0" class="zy-table1" v-if="JBXX1">
                    <tr>
                        <td class="td-hd">排口名称</td>
                        <td>{{ JBXX1.PKMC }}</td>
                        <td class="td-hd">排口类型名称</td>
                        <td>{{ JBXX1.PKLXMC }}</td>
                    </tr>

                    <tr>
                        <td class="td-hd">经度</td>
                        <td>{{ JBXX1.JD }}</td>
                        <td class="td-hd">纬度</td>
                        <td>{{ JBXX1.WD }}</td>
                    </tr>
                </table>

                <div class="nomsg" v-if="!JBXX1">暂无数据</div>
                <div class="gap"></div>
                <p class="zy-til1">监测数据</p>
                <div class="pd-row1">
                    <div
                        class="pd-slec1a"
                        @click.stop="changeWRW1Style(!WRW1Style)"
                        style="width: 308px"
                    >
                        <p v-if="WRW1.length">
                            <span v-for="(item, index) in WRW1List" :key="index"
                                >{{ item.checked ? item.WRWMC : '' }}
                            </span>
                        </p>
                        <p
                            v-if="!WRW1.length"
                            class="nomsg"
                            style="
                                color: #aaa;
                                font-size: 10px;
                                text-align: left;
                            "
                        >
                            暂无数据
                        </p>
                        <ul
                            @click.stop="changeWRW1Style(true)"
                            :style="WRW1Style ? 'display:block' : ''"
                            v-if="WRW1List.length"
                        >
                            <li v-for="(item, index) in WRW1List" :key="index">
                                <label class="pd-label"
                                    ><input
                                        type="checkbox"
                                        :value="item.WRWBH"
                                        name="1"
                                        :label="item.WRWMC"
                                        @change="changeWRW1"
                                        v-model="item.checked"
                                    /><i></i
                                    ><span>{{ item.WRWMC }}</span></label
                                >
                            </li>
                        </ul>
                    </div>
                    <div class="pd-rdobx1">
                        <label
                            class="pd-label cur"
                            v-for="(item, index) in DayList"
                            @click="changeDay1(item.value)"
                            :key="index"
                            ><input
                                type="radio"
                                :name="item.value + '1'"
                                :checked="item.value == Day1"
                            /><i></i><span>{{ item.label }}</span></label
                        >
                    </div>
                    <div class="pd-datesrh1">
                        <input
                            type="text"
                            :value="getDateByStyle(value1['DayList' + Day1][0])"
                        />
                        <i></i>
                        <input
                            type="text"
                            :value="getDateByStyle(value1['DayList' + Day1][1])"
                        />
                        <div class="datePicker">
                            <el-date-picker
                                v-model="value1['DayList' + Day1]"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                            <!-- <el-date-picker
                                style="width: 100%"
                                v-model="value1['DayList' + Day1]"
                                type="datetimerange"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker> -->
                        </div>
                    </div>
                </div>
                <div class="gap"></div>
                <div class="gap"></div>
                <LineP
                    :arr="PchartsData1"
                    v-if="PchartsData1.length"
                    :day="Day1"
                    :key="Nums1"
                    :WRW="WRW1"
                    :WRWMC="WRWMC1"
                ></LineP>
                <div class="nomsg" v-if="!PchartsData1.length">暂无数据</div>
                <div class="gap"></div>
                <div class="gap"></div>
                <table cellpadding="0" class="pd-tablelst1 otr">
                    <colgroup>
                        <col width="10%" />
                        <col width="25%" />
                        <col width="20%" />
                        <col width="20%" />
                        <col width="20%" />
                        <col width="25%" />
                    </colgroup>
                    <thead>
                        <tr>
                            <td>序号</td>
                            <td>监测时间</td>
                            <td>监测因子</td>
                            <td>检测值</td>
                            <td>单位</td>
                            <td>排放标准</td>
                        </tr>
                    </thead>
                </table>
                <div class="tablelist" style="height: 200px">
                    <table
                        cellpadding="0"
                        class="pd-tablelst1 otr"
                        v-if="PchartsData1.length"
                    >
                        <colgroup>
                            <col width="10%" />
                            <col width="25%" />
                            <col width="20%" />
                            <col width="20%" />
                            <col width="20%" />
                            <col width="25%" />
                        </colgroup>
                        <tbody>
                            <tr
                                v-for="(item, index) in PchartsData1"
                                :key="index"
                            >
                                <td>{{ index + 1 }}</td>
                                <td>{{ item.jcsj }}</td>
                                <td>{{ item.WRWMC }}</td>
                                <td>{{ item.JCZ }}</td>
                                <td>{{ item.DW ? item.DW : '-' }}</td>
                                <td>{{ item.PFBZ }}</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="nomsg" v-if="!PchartsData1.length">
                        暂无数据
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LineP from './LineP.vue';
export default {
    components: {
        LineP
    },
    data() {
        return {
            sj: '',
            PchartsData1: '',
            JBXX1: '',
            Nums1: 1,
            QYS1: [],
            QYS1Val: 0,
            QYS1ZiVal: 0,
            WRW1List: [],
            WRW1: [],
            WRW1Style: false,
            Day1: 'R',
            DayList: [
                { value: 'R', label: '日数据' },
                { value: 'S', label: '小时数据' }
            ],
            value1: {
                DayListR: [
                    this.$dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
                    this.$dayjs().format('YYYY-MM-DD')
                ],
                DayListS: [
                    this.$dayjs().subtract(1, 'day').format('YYYY-MM-DD HH'),
                    this.$dayjs().format('YYYY-MM-DD HH')
                ]
            }
        };
    },
    computed: {
        getDateByStyle() {
            let that = this;
            return function (value) {
                if (that.Day1 == 'R') {
                    console.log('时间：', value);
                    return that.$dayjs(value).format('YYYY-MM-DD');
                } else {
                    return that.$dayjs(value).format('YYYY-MM-DD HH');
                }
            };
        }
    },
    mounted() {
        // 左侧企业树
        this.getQYS();
        // 基本信息
        this.getJBXX();
        // 污染因子
        this.getWRYYZ();
        // 监测数据-图表
        this.getJCSJ();
    },
    methods: {
        getQYS() {
            this.QYS1 = [
                {
                    QYMC: '中石化宁波镇海炼化有限公司',
                    list: [
                        {
                            PKMC: '2#乙烯污水排放口',
                            PKXH: '33330211011561'
                        }
                    ]
                }
            ];
        },
        getJBXX() {
            this.JBXX1 = {
                JD: 112.456,
                PKLX: 'FS',
                PKLXMC: '废水',
                PKMC: '2#乙烯污水排放口',
                WD: 36.839
            };
        },
        getWRYYZ() {
            this.WRW1List = [
                {
                    WRWBH: '001',
                    WRWMC: 'pH值'
                },
                {
                    WRWBH: '011',
                    WRWMC: '化学需氧量'
                },
                {
                    WRWBH: '060',
                    WRWMC: '氨氮'
                }
            ];
        },
        getJCSJ() {
            this.PchartsData1 = [
                {
                    JCZ: 8.449,
                    PFBZ: '6.0000-9.0000',
                    WRWMC: 'pH值',
                    jcsj: '2022-04-13 00:00:00'
                },
                {
                    JCZ: 8.52,
                    PFBZ: '6.0000-9.0000',
                    WRWMC: '化学需氧量',
                    jcsj: '2022-04-14 00:00:00'
                },
                {
                    JCZ: 20.52,
                    PFBZ: '6.0000-9.0000',
                    WRWMC: '化学需氧量',
                    jcsj: '2022-04-14 00:00:00'
                },
                {
                    JCZ: 1.52,
                    PFBZ: '6.0000-9.0000',
                    WRWMC: 'pH值',
                    jcsj: '2022-04-14 00:00:00'
                },
                {
                    JCZ: 24.52,
                    PFBZ: '6.0000-9.0000',
                    WRWMC: '氨氮',
                    jcsj: '2022-04-14 00:00:00'
                },
                {
                    JCZ: 8.52,
                    PFBZ: '6.0000-9.0000',
                    WRWMC: '氨氮',
                    jcsj: '2022-04-14 00:00:00'
                }
            ];
        },
        changeQYS1(e) {
            if (this.QYS1Val != e) {
                this.QYS1Val = e;
            } else {
                this.QYS1Val = null;
            }
        },
        // 监测数据 污染因子
        changeWRW1(e) {
            let WRW1 = [];
            let WRWMC1 = [];
            this.WRW1List.forEach((item) => {
                if (item.checked) {
                    WRW1.push(item.WRWBH);
                    WRWMC1.push(item.WRWMC);
                }
            });
            this.WRW1 = [...WRW1];
            this.WRWMC1 = [...WRWMC1];
        },
        // 污染源在线监控 排口
        changeQYS1Zi() {},
        changeWRW1Style(e) {
            this.WRW1Style = e;
        },
        changeDay1(e) {
            this.Day1 = e;
            this[e]++;
        }
    }
};
</script>

<style scoped>
.pd-treesider {
    width: 275px;
    border: 1px solid #0b5f72;
    float: left;
    height: 777px;
}
.pd-treesider h1 {
    padding: 13px 18px;
}
.pd-treesider h1 i {
    font-size: 16px;
    color: #fff;
    position: relative;
    padding-left: 15px;
}
.pd-treesider h1 i:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-color: transparent transparent transparent #fff;
    margin-top: -5px;
}
.pd-treesider ul {
    padding: 0 18px;
    display: none;
}
.pd-treesider ul li + li {
    padding-top: 15px;
}
.pd-treesider ul li.on {
    color: #20beee;
}
.pd-treesider ul li {
    padding-left: 30px;
    padding-top: 15px;
    font-size: 16px;
    color: #fff;
}
.pd-treesider h1.on i:before {
    content: '';
    border-width: 5px 5px 0 5px;
    border-color: #fff transparent transparent transparent;
    margin-top: 0;
}
.pd-treesider h1.on + ul {
    display: list-item;
}
.pd-rdobx1 .pd-label span {
    font-size: 16px;
    color: #fff;
}
.pd-rdobx1 {
    display: flex;
    align-items: center;
    margin-left: 40px;
}
.pd-rdobx1 .pd-label + .pd-label {
    margin-left: 40px;
}
.pd-rdobx1 .pd-label input[type='radio'] ~ i {
    width: 18px;
    height: 18px;
    margin-right: 10px;
}
.pd-label span {
    display: inline-block;
    vertical-align: middle;
}
.zy-til1 {
    font-size: 18px;
    color: #fff;
    line-height: 40px;
    padding-left: 12px;
    position: relative;
}
.zy-til1::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #0dc8ef;
}
.pd-row1 {
    display: flex;
}
.zy-table1 {
    width: 100%;
    border: 1px solid #0b5f72;
}
.zy-table1 td.td-hd {
    background-color: #064959;
    width: 178px;
    padding-left: 15px;
    color: #21bbe0;
}
.zy-table1 td {
    font-size: 16px;
    color: #fff;
    height: 48px;
    border: 1px solid #0b5f72;
    padding-left: 15px;
}
table {
    table-layout: fixed;
}
.tablelist {
    overflow-y: scroll;
}
.pd-tablelst1 {
    width: 100%;
}
.pd-tablelst1 tr td {
    font-size: 14px;
    color: #fff;
    text-align: center;
    height: 40px;
}
.pd-tablelst1 thead tr td {
    background: rgba(39, 117, 155, 0.35);
}
.pd-tablelst1 tr td .prog {
    height: 16px;
    border-radius: 300px;
    background: #11578b;
    position: relative;
}
.pd-tablelst1 tr td .prog b {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background: #34dbff;
    border-radius: 300px 0 0 300px;
}
.pd-tablelst1 tbody tr:first-child td {
    padding-top: 13px;
}
.pd-tablelst1.otr tbody tr:first-child td {
    padding-top: 0;
}
.pd-tablelst1.thd thead tr td {
    font-size: 16px;
}
.pd-tablelst1 tr td.tal {
    text-align: left;
}
.pd-tablelst1.tbd tbody tr td {
    height: 60px;
}
.pd-tablelst1.tbd tbody tr td:last-child {
    font-size: 14px;
}
.pd-tablelst1.otr.htn tbody tr td {
    height: auto;
    padding: 5px 0;
    border-bottom-color: #35626d;
    line-height: 2;
}
.pd-tablelst1.otr.htn tr td:first-child {
    text-align: right;
}
.pd-tablelst1.otr tr td.tal {
    text-align: left;
}
.pd-datesrh1 {
    position: relative;
    display: flex;
    align-items: center;
    margin-left: 40px;
}
.datePicker {
    position: absolute;
    width: 314px;
    left: 0;
    top: 1px;
    opacity: 0;
}

.pd-rtcon1 {
    overflow: hidden;
    padding-left: 40px;
    height: 777px;
}
.cur {
    cursor: pointer;
}

.pd-datesrh1 input {
    border: 1px solid #0b5f72;
    width: 128px;
    line-height: 32px;
    font-size: 14px;
    color: #fff;
    background: url(./images/dateic1.png) no-repeat 95% center;
    text-indent: 10px;
    outline: none;
}
.pd-datesrh1 button {
    width: 80px;
    height: 34px;
    box-sizing: border-box;
    font-size: 16px;
    color: #023f4e;
    background: #20beee;
    border: none;
    outline: none;
    margin-left: 16px;
}
.pd-datesrh1 i {
    width: 10px;
    height: 1px;
    background: #fff;
    margin: 0 10px;
}
.nomsg {
    font-size: 16px;
    color: #aaa;
    text-align: center;
    line-height: 40px;
}
.pd-slec1a {
    border: 1px solid #27c8e9;
    height: 32px;
    background: url(./images/arwbt1.png) no-repeat 95% center;
    position: relative;
}
.pd-slec1a p {
    white-space: nowrap;
    font-size: 16px;
    color: #fff;
    line-height: 32px;
    text-indent: 10px;
}
.pd-slec1a ul {
    position: absolute;
    left: -1px;
    right: -1px;
    top: 100%;
    border: 1px solid #0b5f72;
    background: #064959;
    margin-top: 4px;
    z-index: 1;
    padding: 5px 0;
    display: none;
}
.pd-slec1a ul li {
    padding: 6px 20px;
}
.pd-slec1a ul li .pd-label input[type='checkbox'] ~ i {
    width: 16px;
    height: 16px;
    margin-right: 10px;
}
.pd-slec1a ul li .pd-label span {
    font-size: 16px;
    color: #fff;
}
.pd-slec1a.on ul {
    display: list-item;
}

/* 单选和复选 */
.pd-label {
    display: inline-block;
    vertical-align: middle;
}
.pd-label input {
    display: none;
}
.pd-label i {
    display: inline-block;
    vertical-align: middle;
    background-repeat: no-repeat;
    background-position: center;
}
.pd-label input[type='checkbox'] ~ i {
    background-image: url(./images/checkic.png);
}
.pd-label input[type='checkbox']:checked ~ i {
    background-image: url(./images/checkicon.png);
}
.pd-label input[type='radio'] ~ i {
    background-image: url(./images/radioic.png);
}
.pd-label input[type='radio']:checked ~ i {
    background-image: url(./images/radioicon.png);
}
.pd-label span {
    display: inline-block;
    vertical-align: middle;
}
.pd-slec1a p span {
    padding-right: 10px;
}
</style>
