<!-- @format -->

<!-- 空气质量日历（组件组合） -->

<template>
    <div class="aqi-calendar-main">
        <div
            class="allcalendar"
            :style="{
                'grid-template-columns': pageWidthIndex
            }"
        >
            <div
                class="aqi-calendar"
                ref="cardWidth"
                v-for="month in 12"
                :key="month"
            >
                <CalendarMonthCard
                    :dayFontSize="dayFontSize"
                    :data="fullYearData"
                    :year="year"
                    :tileWidth="tileWidth"
                    :tileHeight="tileHeight"
                    :month="month"
                    :pollutant="pollutant"
                />
            </div>
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs';
import { getAqiColor, getAqiLevel } from './utils/aqi';
import { LEVEL_TEXTS } from './utils/constants';
import CalendarMonthCard from './CalendarMonthCard.vue';
// import { getCityHistoryDataByDate } from '_a/AirQualityServer.js'; //获取数据接口，历史空气质量指数
export default {
    name: 'AqiCalendarIndex',
    components: {
        CalendarMonthCard
    },
    props: {
        calData: {
            type: Array,
            default: function () {
                return [];
            }
        },
        cityCode: {
            type: String,
            required: true
        },
        year: {
            type: String,
            required: true
        },
        pollutantIndex: {
            type: Number,
            default: 0
        },
        dayFontSize: {
            type: Number,
            default: 14
        },
        tileWidth: {
            type: Number,
            default: 42
        },
        tileHeight: {
            type: Number,
            default: 42
        },
        column: {
            type: Number,
            default: ''
        }
    },

    data() {
        return {
            data: [], //接入的数据
            pollutants: ['AQI', 'PM2.5', 'PM10', 'O3', 'SO2', 'NO2', 'CO'],
            //       year: '', //年份
            pollutant: 0,
            month: '', //月份
            fullYearData: null, //格式化后的数据
            pageWidth: 0, //日历换行的情况
            screenWidth: 0 //实时获取的日历宽度
        };
    },

    computed: {
        pageWidthIndex() {
            switch (this.pageWidth) {
                case 1:
                    return 'repeat(1, 100%)';
                case 2:
                    return 'repeat(2, 50%)';
                case 3:
                    return 'repeat(3, 33%)';
                case 4:
                    return 'repeat(4, 24%)';
                case 5:
                    return 'repeat(6, 15.6%)';
            }
        }
    },

    watch: {
        calData: function () {
            this.loadCalender();
        },
        //年份发生变化时，重新加载
        year: function () {
            this.loadCalender();
        },
        //城市发生变化时，重新加载
        cityCode() {
            this.loadCalender();
        },
        //污染物发生变化时，重新加载
        pollutant() {
            for (let i in this.fullYearData) {
                this.fullYearData[i].backgroundColor = this.getLevelColor(
                    this.fullYearData[i].data
                );
                this.fullYearData[i].pollutants = this.getPrimaryPollutant(
                    this.fullYearData[i].data
                );
            }
        },
        pollutantIndex() {
            this.loadCalender();
        },
        screenWidth(val) {
            this.getChangePageWidth(val);
        }
    },

    created() {
        this.loadCalender();
    },

    mounted() {
        this.getPageWidth();
        window.onresize = () => {
            return (() => {
                this.screenWidth = this.$el.clientWidth;
                this.getChangePageWidth(this.pageWidth);
            })();
        };
    },
    methods: {
        loadCalender() {
            this.pollutant = this.pollutants[this.pollutantIndex];
            //获取传入的年份，载入后获取当年的所有数据
            let startDate = this.year + '-01-01';
            let end = new Date(this.year);
            end.setMonth(end.getMonth() + 12);
            end.setDate(0);
            let endDate = this.year + '-12-31';
            // getCityHistoryDataByDate({
            //     startDate: startDate,
            //     endDate: endDate,
            //     cityCode: this.cityCode
            // })
            // .then(data => {
            let data = this.calData;
            if (data instanceof Array) {
                //数据格式化
                let pollutants = ['PM2.5', 'PM10', 'O3', 'SO2', 'NO2', 'CO'];
                this.data = data.map((e) => {
                    let aqi = e.aqi === '' ? '--' : parseInt(e.aqi);
                    let pollutions = [];
                    if (aqi > 50) {
                        pollutants.forEach((p) => {
                            let key =
                                p.replace(/\./g, '').toLowerCase() + 'Iaqi';
                            let value = parseInt(e[key]);
                            if (value === aqi) {
                                pollutions.push(p);
                            }
                        });
                    }

                    //判断当前选择的污染物，并用getAqiLevel方法返回对应污染物的污染等级
                    let pollutantValue = 0;
                    switch (this.pollutant) {
                        case 'AQI':
                            pollutantValue = e.aqi;
                            break;
                        case 'PM2.5':
                            pollutantValue = e.pm25;
                            break;
                        case 'PM10':
                            pollutantValue = e.pm10;
                            break;
                        case 'O3':
                            pollutantValue = e.o3;
                            break;
                        case 'SO2':
                            pollutantValue = e.so2;
                            break;
                        case 'NO2':
                            pollutantValue = e.no2;
                            break;
                        case 'CO':
                            pollutantValue = e.co;
                            break;
                    }
                    let quality =
                        LEVEL_TEXTS[
                            parseInt(
                                getAqiLevel(
                                    pollutantValue,
                                    this.pollutant,
                                    false
                                )
                            ) - 1
                        ];
                    return {
                        city: e.areaName,
                        quality,
                        pubtime: e.monitorDate,
                        pollutions:
                            pollutions.length > 0
                                ? pollutions.join('\n')
                                : null,
                        aqi,
                        pm2_5: e.pm25 === '' ? '--' : parseInt(e.pm25),
                        pm10: e.pm10 === '' ? '--' : parseInt(e.pm10),
                        o3: e.o3 === '' ? '--' : parseInt(e.o3),
                        so2: e.so2 === '' ? '--' : parseInt(e.so2),
                        no2: e.no2 === '' ? '--' : parseInt(e.no2),
                        co: e.co === '' ? '--' : parseFloat(e.co)
                    };
                });
            }
            // })
            // .catch()
            // .finally(() => {
            this.buildCalendarData();
            // });
        },

        buildCalendarData() {
            //截取一整年时间的参数，处理完成后传入Card组件
            let start = new Date(this.year);
            let endtime = new Date(start.getFullYear(), 12, 0);

            start.setDate(1);
            let firstDay = dayjs(start).format('YYYY-MM-DD');
            endtime.setDate(1 + endtime.getDate());
            let lastDay = endtime.getTime();
            let array = [];
            let today = dayjs().format('YYYY-MM-DD');
            let item = this.data.find((e) => e.pubtime === today);
            // 默认选中
            let matchDay = item
                ? today
                : this.data.length > 0
                ? this.data[0].pubtime
                : firstDay;

            let selectedItem =
                item ||
                (this.data.length > 0
                    ? this.data[0]
                    : {
                          city: null,
                          quality: '优',
                          pubtime: matchDay,
                          pollutions: null,
                          aqi: 0,
                          pm2_5: 0,
                          pm10: 0,
                          o3: 0,
                          so2: 0,
                          no2: 0,
                          co: 0
                      });
            this.$emit('select', selectedItem);
            let date = new Date(start);
            while (date.getTime() <= lastDay) {
                let day = date.getDate();
                let dateStr = dayjs(date).format('YYYY-MM-DD');
                let data = this.data.find((e) => e.pubtime === dateStr);
                let backgroundColor = this.getLevelColor(data);
                let pollutants = this.getPrimaryPollutant(data);
                array.push({
                    date: dateStr,
                    day,
                    check: dateStr === matchDay,
                    html: true,
                    data,
                    backgroundColor,
                    pollutants
                });
                date.setDate(day + 1);
            }
            //按照日历顺序排列为每天想要的数据
            let mappedData = {};
            let keyOfTime = 'date';
            array.forEach((item) => {
                let monitorDate = item[keyOfTime];
                mappedData[monitorDate] = item;
            });
            this.fullYearData = mappedData;
        },

        //格式化污染物内容
        getPrimaryPollutant(item) {
            if (this.pollutant === 'AQI') {
                if (item) {
                    let primary = item.pollutions;
                    if (primary instanceof Array) {
                        return primary;
                    } else {
                        if (primary) {
                            primary = primary.replace(/[\u4e00-\u9fa5()]/g, '');
                            primary = primary.replace(/_/g, '.').toUpperCase();
                        }
                        return [primary || '--'];
                    }
                }
            }
            return null;
        },

        //格式化污染背景颜色
        getLevelColor(data) {
            let name = this.pollutant.replace(/\./g, '_');
            let value = data && data[name.toLowerCase()];
            if (value >= 0) {
                return getAqiColor(value, name);
            }
            // 无数据
            return '#d1d5d9';
        },

        getPageWidth() {
            this.$nextTick(function () {
                this.getChangePageWidth(this.pageWidth);
            });
        },

        getChangePageWidth(type) {
            if (this.column === 0) {
                this.screenWidth = this.$el.clientWidth;
                let nowPageWidth = this.$refs.cardWidth[0].clientWidth; //当前判断出的日历宽度
                let changePageWidth = this.tileWidth * 7 + 66;
                let value = this.screenWidth / changePageWidth;
                if (value < 2) {
                    this.pageWidth = 1;
                } else if (value < 3) {
                    this.pageWidth = 2;
                } else if (value < 4) {
                    this.pageWidth = 3;
                } else if (value < 10000) {
                    this.pageWidth = 4;
                }
            } else {
                this.pageWidth = this.column;
            }
        }
    }
};
</script>

<style scoped>
.aqi-calendar {
    display: inline-block;
}

.allcalendar {
    display: grid;
    display: -ms-grid;
    /* grid-template-columns: repeat(4, 24%);
  -ms-grid-columns: repeat(4, 24%); */
    grid-template-rows: auto;
    -ms-grid-rows: auto;
    grid-row-gap: 35px;
    -ms-grid-gap: 35px;
    grid-column-gap: 22px;
    -ms-grid-column-gap: 22px;
}

.aqi-calendar-main p {
    margin: 0;
    padding: 0;
    line-height: normal;
}
</style>
