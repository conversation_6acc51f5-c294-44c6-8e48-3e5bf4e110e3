<!-- @format -->
<!-- 水质类别  -->

<template>
    <div class="">
        <p-stack-bar
            :data="data"
            :option="optionData"
            :show-option="mOption.showOption"
            :config="{
                unit: '%',
                color: [
                    '#44c5fd',
                    '#51a5fd',
                    '#73bb31',
                    '#eebd15',
                    '#f88e17',
                    '#ee3b5b',
                    '#BD28B6'
                ]
            }"
            :style="{
                width: mOption.width,
                height: mOption.height,
                background: mOption.bgColor
            }"
        ></p-stack-bar>
    </div>
</template>

<script>
export default {
    name: 'water-quality',
    props: {
        data: {
            //堆叠图数据
            type: Object,
            default: function () {
                return {};
            }
        },
        option: {
            type: Object,
            default: function () {
                return {
                    showOption: false, //是否显示工具栏
                    width: '100%', //宽
                    height: '100%', //高
                    bgColor: '#fff', //背景色
                    xRotate: '45', //x轴字体旋转角度
                    stack: false //优良率是否跟水质类别堆叠
                };
            }
        }
    },
    computed: {
        mOption() {
            return Object.assign(
                {
                    showOption: false, //是否显示工具栏
                    width: '100%', //宽
                    height: '100%', //高
                    bgColor: '#fff', //背景色
                    xRotate: '45', //x轴字体旋转角度
                    stack: false //优良率是否跟水质类别堆叠
                },
                this.option
            );
        }
    },
    data() {
        return {
            dataSet: {},
            optionData: {
                legend: {
                    left: 'center'
                },
                xAxis: {
                    type: 'category',
                    axisLabel: {
                        rotate: ''
                    }
                },
                series: []
            }
        };
    },

    created() {
        this.dataSet = JSON.parse(JSON.stringify(this.data));
        this.setOption();
    },
    methods: {
        setOption() {
            // x轴字体旋转角度
            this.optionData.xAxis.axisLabel.rotate = this.mOption.xRotate;

            this.optionData.series = this.dataSet.series.map((item) => {
                if (item.name == '优良率') {
                    item.type = 'line';
                    if (!this.mOption.stack) {
                        item.stack = 'total';
                    }
                } else {
                    item.type = 'bar';
                    if (!this.mOption.stack) {
                        item.stack = 'one';
                    }
                }

                return item;
            });
        }
    }
};
</script>

<style lang="less" scoped></style>
