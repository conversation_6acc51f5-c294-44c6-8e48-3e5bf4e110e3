/** @format */

import { LEVEL_COLORS, LEVEL_TEXTS, LEVEL_AQI } from './constants';

/**
 * AQI最大等级
 */
const AQI_MAX_LEVEL = 6;

export function getAqiLevel(value, pollutant = 'AQI', hour) {
    let p = pollutant.replace(/\./g, '_').toUpperCase();
    let pollutantLevelLimitValues = LEVEL_AQI[p];
    let arr = hour
        ? pollutantLevelLimitValues[1] || pollutantLevelLimitValues[24]
        : pollutantLevelLimitValues[24] ||
          pollutantLevelLimitValues[8] ||
          pollutantLevelLimitValues[1];
    let level = 0;
    if (
        (pollutant === 'O3' && value > 800) ||
        (pollutant === 'SO2' && value > 1600)
    ) {
        // 已达到最大值
        level = AQI_MAX_LEVEL;
    } else {
        while (level < arr.length) {
            let levelLimitValue = arr[level];
            // 小于等于继续执行
            if (value <= levelLimitValue) {
                break;
            }
            level++;
        }
    }
    if (level === 0) {
        // 超出范围
        return 1;
    } else if (level > AQI_MAX_LEVEL) {
        // 超出范围
        return AQI_MAX_LEVEL;
    } else {
        return level;
    }
}

export function getAqiColor(value, pollutant = 'AQI', hour = true) {
    let level = getAqiLevel(value, pollutant, hour);
    return level > 0 && LEVEL_COLORS[level - 1];
}

export function getAqiInfo(value, pollutant = 'AQI', hour) {
    let level = getAqiLevel(value, pollutant, hour);
    return (
        level > 0 && {
            level,
            color: LEVEL_COLORS[level - 1],
            text: LEVEL_TEXTS[level - 1]
        }
    );
}

const pollutants = ['PM2_5', 'PM10', 'O3', 'SO2', 'NO2', 'CO'];

export function calculateAqi(item, hour = true) {
    let ps = [];
    let max = 0;
    pollutants.forEach(function(p) {
        let value = item[p];
        let iaqi = calculate_iaqi(p, value, hour);
        if (iaqi > max) {
            max = iaqi;
            ps = [p];
        } else if (iaqi > 0 && iaqi === max) {
            ps.push(p);
        }
    });
    return {
        aqi: max,
        // aqi 小于 50 认为空气质量优，无污染
        primary: max <= 50 ? [] : ps
    };
}

export function calculate_iaqi(pollutant, value, hour = true) {
    let level = LEVEL_AQI[pollutant];
    let arr = hour ? level[1] || level[24] : level[24] || level[8] || level[1];
    if ((pollutant == 'O3' || pollutant == 'SO2') && value > 800) return -1;
    let i = 0;
    while (i < arr.length) {
        let level = arr[i];
        // 小于等于继续执行
        if (level >= value) {
            break;
        }
        i++;
    }
    let bp_low, bp_high, iaqi_low, iaqi_high;
    if (i == 0) {
        // 超出范围
        return 0;
    } else if (i == arr.length) {
        // 超出范围
        return 500;
    } else {
        bp_low = arr[i - 1];
        bp_high = arr[i];
        let aqiLevel = LEVEL_AQI['AQI'];
        iaqi_low = aqiLevel[i - 1];
        iaqi_high = aqiLevel[i];
        // 计算
        let iaqi =
            ((iaqi_high - iaqi_low) / (bp_high - bp_low)) * (value - bp_low) +
            iaqi_low;
        // 取整
        return Math.round(iaqi);
    }
}
