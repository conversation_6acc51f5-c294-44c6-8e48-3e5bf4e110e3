<!-- @format -->

<template>
    <div class="">
        <div id="waterGauge" style="width: 100%; height: 100%"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
export default {
    name: '',
    props: {
        data: {
            type: Number || String,
            default: 0
        },
        option: {
            type: Object,
            default: function () {
                return {
                    startAngle: 180, //开始角度
                    endAngle: 0 //结束角度
                };
            }
        }
    },
    computed: {
        mOption() {
            return Object.assign(
                {
                    startAngle: 180, //开始角度
                    endAngle: 0 //结束角度
                },
                this.option
            );
        }
    },
    data() {
        return {};
    },
    mounted() {
        this.setChart(this.data, 'waterGauge');
    },
    methods: {
        setChart(val, id) {
            let myChart = echarts.init(document.getElementById(id));
            let theme = window.localStorage.getItem('themeType') || 'dark';
            let option = {
                series: [
                    {
                        type: 'gauge',
                        radius: '100%',
                        center: ['50%', '60%'],
                        startAngle: this.mOption.startAngle, //开始角度
                        endAngle: this.mOption.endAngle, //结束角度
                        min: 0,
                        max: 100,
                        splitNumber: 6,
                        itemStyle: {
                            color: '#559ffb'
                            // shadowColor: 'rgba(0,138,255,0.45)',
                            // shadowBlur: 10,
                            // shadowOffsetX: 2,
                            // shadowOffsetY: 2
                        },
                        progress: {
                            show: true,
                            roundCap: true,
                            width: 8
                        },
                        pointer: {
                            icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
                            length: '60%',
                            width: 8,
                            offsetCenter: [0, '5%']
                        },
                        axisLine: {
                            roundCap: true,
                            lineStyle: {
                                width: 8
                                // color: [
                                //     [
                                //         1,
                                //         new echarts.graphic.LinearGradient(
                                //             0,
                                //             0,
                                //             1,
                                //             0,
                                //             [
                                //                 {
                                //                     offset: 0.1,
                                //                     color: '#37c4f2'
                                //                 },
                                //                 {
                                //                     offset: 1,
                                //                     color: '#559ffb'
                                //                 }
                                //             ]
                                //         )
                                //     ]
                                // ]
                            }
                        },
                        axisTick: {
                            distance: 7,
                            splitNumber: 5,
                            lineStyle: {
                                width: 2,
                                color: theme === 'light' ? '#999' : '#fff'
                            }
                        },
                        splitLine: {
                            distance: 7,
                            length: 8,
                            lineStyle: {
                                width: 1,
                                color: theme === 'light' ? '#999' : '#fff'
                            }
                        },
                        axisLabel: {
                            distance: 13,
                            color: theme === 'light' ? '#999' : '#fff',
                            fontSize: 12,
                            formatter: (value) => {
                                return value.toFixed(0);
                            }
                        },
                        title: {
                            show: false
                        },

                        detail: {
                            valueAnimation: true,
                            // padding: [40, 0, 0, 0],
                            formatter: (value) => {
                                return (
                                    '{unit|达标率：}' +
                                    '{value|' +
                                    value +
                                    '}{unit|%}'
                                );
                            },
                            rich: {
                                value: {
                                    fontSize: 20,
                                    fontWeight: 'bolder',
                                    color: theme === 'light' ? '#333' : '#fff',
                                    fontFamily: 'DINPro-Bold'
                                },
                                unit: {
                                    fontSize: 18,
                                    color: theme === 'light' ? '#333' : '#fff'
                                }
                            }
                        },
                        data: [
                            {
                                value: val
                            }
                        ]
                    }
                ]
            };

            myChart.setOption(option);
            console.log(myChart);
        }
    }
};
</script>

<style lang="less" scoped></style>
