<!-- @format -->
<!--全屏 -->
<template>
    <ul class="zy-tools-gis">
        <li>
            <i
                class="tool-fullScreen"
                @click="fullscreenClick"
                title="全屏"
            ></i>
        </li>
    </ul>
</template>

<script>
export default {
    name: 'FullScreen',
    props: ['map', 'mapCls'],
    data() {
        return {};
    },
    components: {},
    computed: {},
    mounted() {},
    methods: {
        fullscreenClick() {
            this.$emit('fullScreenClick');

            if ($.support.fullscreen) {
                $(this.mapCls).fullScreen({
                    callback: (isFullScreen) => {
                        console.log(isFullScreen);
                        setTimeout(() => {}, 500);
                    }
                });
            }
        }
    },
    watch: {}
};
</script>

<style>
#fullscreenelm:fullscreen {
    background-color: #fff;
    width: 100%;
    height: 100%;
}

:not(:root):fullscreen::backdrop {
    position: fixed;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
    background: rgba(0, 0, 0, 0);
}
</style>
