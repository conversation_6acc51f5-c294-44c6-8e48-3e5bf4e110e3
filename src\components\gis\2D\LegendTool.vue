<!-- @format -->
<!-- 图例 -->
<template>
    <div class="gis-tuli" style="height: 123px">
        <div
            class="gis-tuli-toggle"
            :class="{ on: showTL }"
            @click="btnFolderClick()"
        >
            <i>图例</i>
        </div>
        <div class="gis-tuli-img">
            <div class="item" v-for="(item, index) of arrLegend" :key="index">
                <div class="lp">{{ item.title }}：</div>
                <div
                    class="rp"
                    v-for="(child, index1) of item.data"
                    :key="child.name + index1"
                >
                    <p>
                        <img
                            :src="'./gis/2D/images/lengend/' + child.url"
                            alt=""
                        />{{ child.name }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'LegendTool',
    props: ['map', 'arrLegend', 'expand'],
    data() {
        return {
            showTL: true
        };
    },
    components: {},
    computed: {},
    mounted() {
        if (this.expand != undefined) {
            this.showTL = this.expand;
        }
    },
    methods: {
        btnFolderClick() {
            this.showTL = !this.showTL;
        }
    },
    watch: {
        expand(newVal, oldVal) {
            this.showTL = newVal;
        }
    }
};
</script>

<style scoped></style>
