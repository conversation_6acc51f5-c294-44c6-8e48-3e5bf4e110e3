<!-- @format -->

<!--
 * @Author: your name
 * @Date: 2022-04-13 16:25:07
 * @LastEditTime: 2022-04-13 16:51:23
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /Front_PC_COMPONENTS/src/components/business/mobile/remoteSensing/remoteSensing.vue
-->
<!-- @format -->

<template>
    <div class="inversion">
        <p class="zy-til2 mt-20 mb-20">{{ componentsTitle }}</p>

        <div class="zy-line jb" style="padding: 0 60px">
            <dl class="pd-dlbx1a" v-for="item in data" :key="item.id">
                <dt><img src="./images/aic1a.png" alt="" /></dt>
                <dd>
                    <h1>{{ item.name }}</h1>
                    <p>{{ item.value || '--' }} <sub></sub></p>
                </dd>
            </dl>
            <!-- <dl class="pd-dlbx1a">
                <dt><img src="./images/aic2a.png" alt="" /></dt>
                <dd>
                    <h1>疑似堆场面积</h1>
                    <p>
                        {{ results.YGJCQYMJ.replace('平方公里', '') }}
                        <sub>平方公里</sub>
                    </p>
                </dd>
            </dl> -->
        </div>
    </div>
</template>

<script>
export default {
    components: {},
    props: {
        data: {
            type: Array
        },
        componentsTitle: {
            type: String,
            defult: '组件标题'
        }
    },
    data() {
        return {
            results: null
        };
    },

    mounted() {}
};
</script>

<style scoped>
.inversion {
    overflow: hidden;
}

.zy-til2 {
    font-size: 18px;
    color: #1ae1ff;
    line-height: 18px;
    padding-left: 32px;
    padding-bottom: 6px;
    background: url(./images/til-bg2.png) left bottom no-repeat;
    font-weight: bold;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.zy-line {
    display: flex;
}
.zy-line.ac {
    align-items: center;
}
.zy-line.jb {
    justify-content: space-between;
}
.zy-line.jc {
    justify-content: center;
}

.pd-dlbx1a {
    display: flex;
    align-items: center;
}
.pd-dlbx1a dd {
    flex: 1;
    padding-left: 20px;
}

.pd-dlbx1a dd h1 {
    font-size: 16px;
    color: #fff;
}
.pd-dlbx1a dd p {
    font-size: 30px;
    color: #2ad8f2;
    font-family: 'DIN-Medium';
}
.pd-dlbx1a dd p sub {
    font-size: 16px;
    color: #fff;
    vertical-align: baseline;
}
</style>
