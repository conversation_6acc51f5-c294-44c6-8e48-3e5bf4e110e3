<!-- @format -->
<!-- 单个时间选择 -->

<template>
    <div class="search-type">
        <div class="title">{{ title }}</div>
        <el-date-picker
            style="flex: 1"
            v-model="time"
            :type="type"
            :placeholder="placeholder"
            :clearable="clearable"
            :value-format="valueFormat"
            :format="format"
            @change="$emit('singleTimeChange', time)"
        >
        </el-date-picker>
    </div>
</template>

<script>
export default {
    name: '',
    props: {
        title: {
            type: String,
            default: () => {
                return '';
            }
        },
        defaultVal: {
            type: String,
            default: () => {
                return '';
            }
        },
        type: {
            type: String,
            default: () => {
                return 'date';
            }
        }, //可选值：year/month/date/dates/week/ datetime
        placeholder: {
            type: String,
            default: () => {
                return '请选择';
            }
        },
        clearable: {
            type: Boolean,
            default: () => {
                return true;
            }
        },
        format: {
            type: String,
            default: () => {
                return 'YYYY-MM-DD';
            }
        },
        valueFormat: {
            type: String,
            default: () => {
                return 'YYYY-MM-DD';
            }
        }
    },
    data() {
        return {
            time: ''
        };
    },
    mounted() {
        this.time = this.defaultVal;
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
.search-type {
    margin-right: 20px;
    color: var(--font-color);
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
}
</style>
