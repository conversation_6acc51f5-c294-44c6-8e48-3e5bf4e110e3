<!-- @format -->

<template>
    <ul class="xx-data8">
        <li class="cur">
            <div class="line">
                <p class="p1">优良天数占比</p>
                <div :class="'type ' + changeMB(data.YLL, data.YLLMBZ).cls">
                    {{ changeMB(data.YLL, data.YLLMBZ).label }}
                </div>
            </div>
            <div class="line">
                <p class="p2">{{ data.YLL }}<span>%</span></p>
                <div
                    v-if="
                        data.YLLTB != 0 &&
                        data.YLLTB != undefined &&
                        data.YLLTB != ''
                    "
                    :class="data.YLLTB > 0 ? 'tong-add' : 'tong-reduce2'"
                >
                    {{ Math.abs(data.YLLTB) }}%
                </div>
            </div>
            <div class="line">
                <p class="p3">
                    目标：<span class="zy-num">{{ data.YLLMBZ }}%</span>
                </p>
                <p class="p3">
                    同期：<span class="zy-num">{{ data.YLLTQ }}%</span>
                </p>
            </div>
            <p class="p3">
                污染控制天数：<span class="zy-num">{{ data.SYCBTS }}</span>
            </p>
        </li>
        <li>
            <div class="line">
                <p class="p1">PM₂.₅</p>
                <div
                    :class="'type ' + changeMB(data.PM25MBZ, data.PM25LJZ).cls"
                >
                    {{ changeMB(data.PM25MBZ, data.PM25LJZ).label }}
                </div>
            </div>
            <div class="line">
                <p class="p2">{{ data.PM25LJZ }}<span>μg/m³</span></p>
                <div
                    v-if="
                        data.PM25TB != 0 &&
                        data.PM25TB != undefined &&
                        data.PM25TB != ''
                    "
                    :class="data.PM25TB > 0 ? 'tong-add2' : 'tong-reduce'"
                >
                    {{ Math.abs(data.PM25TB) }}%
                </div>
            </div>
            <div class="line">
                <p class="p3">
                    目标：<span class="zy-num">{{ data.PM25MBZ }}</span>
                </p>
                <p class="p3">
                    同期：<span class="zy-num">{{ data.PM25TQ }}</span>
                </p>
            </div>
            <p class="p3">
                控制值：<span class="zy-num">{{ data.PM25KZZ }}</span>
            </p>
        </li>
        <li>
            <div class="line">
                <p class="p1">PM₁₀</p>
                <div
                    :class="'type ' + changeMB(data.PM10MBZ, data.PM10LJZ).cls"
                >
                    {{ changeMB(data.PM10MBZ, data.PM10LJZ).label }}
                </div>
            </div>
            <div class="line">
                <p class="p2">{{ data.PM10LJZ }}<span>μg/m³</span></p>
                <div
                    v-if="
                        data.PM10TB != 0 &&
                        data.PM10TB != undefined &&
                        data.PM10TB != ''
                    "
                    :class="data.PM10TB > 0 ? 'tong-add2' : 'tong-reduce'"
                >
                    {{ Math.abs(data.PM10TB) }}%
                </div>
            </div>
            <p class="p3">
                同期：<span class="zy-num">{{ data.PM10TQ }}</span>
            </p>
        </li>
        <li>
            <div class="line">
                <p class="p1">O₃</p>
                <div :class="'type ' + changeMB(data.O3MBZ, data.O3LJZ).cls">
                    {{ changeMB(data.O3MBZ, data.O3LJZ).label }}
                </div>
            </div>
            <div class="line">
                <p class="p2">{{ data.O3LJZ }}<span>μg/m³</span></p>
                <div
                    v-if="
                        data.O3TB != 0 &&
                        data.O3TB != undefined &&
                        data.O3TB != ''
                    "
                    :class="data.O3TB > 0 ? 'tong-add2' : 'tong-reduce'"
                >
                    {{ Math.abs(data.O3TB) }}%
                </div>
            </div>
            <p class="p3">
                同期：<span class="zy-num">{{ data.O3TQ }}</span>
            </p>
        </li>
    </ul>
</template>

<script>
export default {
    props: {
        data: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    computed: {
        //计算同比大小 -- 优良上升
        changeTQRise() {
            return function (val, valTB) {
                if (valTB > val) {
                    return 'reduce2';
                } else if (valTB == val) {
                    return '';
                } else if (valTB < val) {
                    return 'add2';
                }
            };
        },
        //计算同比大小 -- 空气质量
        changeTQ() {
            return function (val, valTB) {
                if (valTB > val) {
                    return 'reduce3';
                } else if (valTB == val) {
                    return '';
                } else if (valTB < val) {
                    return 'add3';
                }
            };
        },
        //计算目标值大小
        changeMB() {
            return function (val, valMB) {
                if (val >= valMB) {
                    return { cls: '', label: '达标' };
                } else {
                    return { cls: 'red', label: '不达标' };
                }
            };
        }
    }
};
</script>

<style scoped>
.xx-data8 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.xx-data8 li {
    width: 214px;
    border: 1px solid #134669;
    border-radius: 5px;
    background-color: rgba(19, 70, 105, 0.15);
    margin-bottom: 15px;
    box-sizing: border-box;
    padding-left: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
}

.xx-data8 li.cur {
    background-color: rgba(238, 238, 238, 0.14);
    border-color: #13aeff;
}

.xx-data8 li .line {
    display: flex;
    align-items: center;
}

.xx-data8 li .p1 {
    font-size: 16px;
    color: #fff;
    line-height: 26px;
}

.xx-data8 li .type {
    padding: 0 8px;
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    color: #fff;
    background-color: #4db727;
    border-radius: 10px;
    margin-left: 10px;
}

.xx-data8 li .type.chaobiao {
    background-color: #ea4545;
}

.xx-data8 li .p2 {
    color: #00c7f7;
    font-size: 24px;
    line-height: 38px;
    font-family: 'DIN-Medium';
    margin-right: 10px;
}

.xx-data8 li .p2 span {
    font-size: 14px;
    vertical-align: baseline;
}

.tong-add {
    padding-left: 45px;
    font-size: 18px;
    color: #fff;
    line-height: 38px;
    background: url(./images/tong-add1.png) 0 center no-repeat;
    font-family: 'DIN-Medium';
}

.tong-add2 {
    padding-left: 45px;
    font-size: 18px;
    color: #fff;
    line-height: 38px;
    background: url(./images/tong-add2.png) 0 center no-repeat;
    font-family: 'DIN-Medium';
}

.tong-reduce {
    padding-left: 45px;
    font-size: 18px;
    color: #fff;
    line-height: 38px;
    background: url(./images/tong-reduce1.png) 0 center no-repeat;
    font-family: 'DIN-Medium';
}

.tong-reduce2 {
    padding-left: 45px;
    font-size: 18px;
    color: #fff;
    line-height: 38px;
    background: url(./images/tong-reduce2.png) 0 center no-repeat;
    font-family: 'DIN-Medium';
}

.xx-data8 li .p3 {
    font-size: 16px;
    color: #fff;
    line-height: 26px;
    display: flex;
    align-items: center;
    margin-right: 10px;
}

.xx-data8 li .p4 {
    font-size: 14px;
    color: #fff;
    line-height: 26px;
    display: flex;
    align-items: center;
    margin-right: 10px;
}

.xx-data8 li .c-red {
    color: #ea4545 !important;
}

.xx-data8 li .p3:last-child {
    margin-right: 0;
}

.xx-data8 li .p3 span {
    font-family: 'DIN-Medium';
}
</style>
