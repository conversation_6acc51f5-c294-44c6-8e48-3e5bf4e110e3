<!-- @format -->
<!-- 污染物余量控制 -->

<template>
    <div class="pollutant-margin">
        <div style="width: 100%; height: max-content" class="main">
            <div class="title">
                截止{{ data.textData.DEADDATE }}，已过<span>{{
                    data.textData.PASSDAY
                }}</span
                >天，剩余<span>{{ data.textData.RESIDUEDAY }}</span
                >天。
            </div>

            <el-table
                :data="data.tableData"
                border
                :cell-style="rowClass"
                style="width: 100%"
                :row-style="{ height: '70px' }"
                :header-cell-style="{
                    textAlign: 'center',
                    height: '70px !important',
                    background: '#e1e9fa !important',
                    color: '#333333 !important',
                    fontSize: '16px !important'
                }"
            >
                <el-table-column prop="WRW" label="污染物">
                    <template v-slot="scope">
                        <div>
                            <div
                                v-if="
                                    scope.row.WRW !== 'CO' &&
                                    scope.row.WRW !== 'YLTS'
                                "
                            >
                                {{ setSubscript(scope.row.WRW) }}(μg/m³)
                            </div>
                            <div v-else-if="scope.row.WRW == 'CO'">
                                {{ setSubscript(scope.row.WRW) }}(mg/m³)
                            </div>
                            <div v-else>
                                {{ '优良天数' }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="WRWLJZ" width="180" label="累计值">
                    <template v-slot="scope">
                        <div class="value">
                            {{ scope.row.WRWLJZ }}
                        </div>
                    </template>
                </el-table-column>

                <el-table-column prop="WRWKZZ" label="控制值">
                    <template v-slot="scope">
                        <div class="value">
                            {{ scope.row.WRWKZZ }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="WRWMBZ" label="目标值">
                    <template v-slot="scope">
                        <div class="value">
                            <el-input
                                v-model="scope.row.WRWMBZ"
                                placeholder="请输入目标值"
                            ></el-input>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <div class="button">
                <el-button type="primary" @click="handleCalculate"
                    >计算</el-button
                >
            </div>
        </div>
    </div>
</template>

<script>
import util from './util';
export default {
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    textData: {},
                    tableData: []
                };
            }
        }
    },

    methods: {
        // 计算
        //计算公式：控制值=（目标值*365-累计值*300）/65
        handleCalculate() {
            this.data.tableData.forEach((item) => {
                if (item.WRWMBZ !== '') {
                    if (item.WRW === 'CO') {
                        item.WRWKZZ = (
                            (365 * item.WRWMBZ - 300 * item.WRWLJZ) /
                            65
                        ).toFixed(1);
                    } else {
                        item.WRWKZZ = (
                            (365 * item.WRWMBZ - 300 * item.WRWLJZ) /
                            65
                        ).toFixed(0);
                    }
                }
            });
        },

        // 表格样式设置
        rowClass() {
            return 'text-align: center;color:#333!important;fontSize: 16!important';
        },

        // 转换污染物下标
        setSubscript(val) {
            if (!val) {
                return '';
            } else {
                console.log(util.replacePltName(val), 111);
                return util.replacePltName(val);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
/* -- Reset -- */
body,
ul,
ol,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
p,
form,
input,
textarea,
select,
button {
    margin: 0;
    padding: 0;
    font: 12px 'Microsoft YaHei', Arial, Helvetica, sans-serif;
}
ul,
ol {
    list-style-type: none;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
img {
    border: 0 none;
}
em,
i {
    font-style: normal;
}
a:link {
    color: #4f14f7;
    text-decoration: none;
}
a:visited {
    color: #551a8b;
}
a:hover {
    color: #ff9900;
    text-decoration: underline;
}
a:active {
    color: #cc0000;
}

/* -- Common style -- */
.dn {
    display: none;
}
.db {
    display: block;
}
.fl {
    float: left;
}
.fr {
    float: right;
}
.rel {
    position: relative;
}
.abs {
    position: absolute;
}
.gap {
    height: 10px;
    width: 100%;
}
.auto {
    margin: 0 auto;
}
.clear {
    clear: both;
}
.clearfix:after {
    content: '\200B';
    display: block;
    height: 0;
    clear: both;
}
.clearfix {
    *zoom: 1;
}

/*栅格系统*/
.raster {
    padding: 0 10px;
    margin: 0 auto;
}
.row {
    margin: 0 -5px;
}
.row:before,
.row:after {
    content: '';
    display: table;
    clear: both;
}
.col {
    min-height: 1px;
    float: left;
    position: relative;
    padding: 0 5px;
    box-sizing: border-box;
}
.col-1 {
    width: 8.333333%;
}
.col-2 {
    width: 16.666666%;
}
.col-3 {
    width: 25%;
}
.col-4 {
    width: 33.333333%;
}
.col-5 {
    width: 41.666666%;
}
.col-6 {
    width: 50%;
}
.col-7 {
    width: 58.333333%;
}
.col-8 {
    width: 66.666666%;
}
.col-9 {
    width: 75%;
}
.col-10 {
    width: 83.333333%;
}
.col-11 {
    width: 91.666666%;
}
.col-12 {
    width: 100%;
}

/* 表单输入框 */
.input-block {
    display: inline-block;
    position: relative;
    vertical-align: middle;
}
.input-block > .input-label {
    display: inline-block;
    max-width: 100%;
}
.input-block > .input-txt {
    border: none;
    padding: 6px;
    outline: none;
}

/* 按钮 */
.btn {
    display: inline-block;
    border: none;
    outline: none;
    cursor: pointer;
}

/* 图标 */
.icon {
    display: inline-block;
    cursor: pointer;
}

/* 遮罩层 */
.mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: black;
    opacity: 0.5;
    filter: alpha(opacity=50);
    z-index: 999;
    display: none;
}

/* tab栏 */
.select-con {
    /* margin-left: 50px; */
}
.select-con > ul {
    /* width: 150px; */
    /* height: 30px; */
    box-sizing: border-box;
    display: flex;
    /* margin-top: 5px; */
    justify-content: center;
}
.select-con li {
    border: 1px #dcdfe6 solid;
    align-items: center;
    /* width: 50%; */
    height: 40px;
    line-height: 40px;
    color: #333;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
    margin-left: -1px;
    padding: 5px 25px;
}
.select-con li:hover {
    background: #367592;
    color: #fff !important;
    border-color: #367592 !important;
}
.select-con li:first-of-type {
    margin-left: 0;
}
.select-active {
    background: #367592;
    color: #fff !important;
    border-color: #367592 !important;
}

.line {
    height: 10px;
    margin-right: 20px;
    background-color: rgba(241, 244, 248, 1);
}

.pollutant-margin {
    height: 100%;
    display: flex;
    .city-data {
        width: 225px;
        height: 100%;
        border-right: 1px solid #d3dfe6;
        padding: 10px;
    }
    .main {
        padding: 10px 30px;
        .title {
            margin-bottom: 16px;
            font-size: 16px;
            color: #333333;
            span {
                font-size: 20px;
                font-weight: 700;
                color: #0099ff;
            }
        }
        .value {
            font-weight: 700;
        }
        .button {
            text-align: center;
            margin: 20px 0;
        }
    }
}

.bg-white {
    width: 100%;
    height: 50px;
    box-sizing: border-box;
    background: #fff;
    font-size: 16px;
    padding: 0 30px;
    padding-top: 5px;
}
</style>
<style>
.main .el-table {
    font-size: 16px;
}
.main .el-input__inner {
    height: 50px !important;
    border: 1px solid rgb(118, 118, 118);
    font-size: 16px;
    color: #000;
    font-weight: 700;
    text-align: center;
}

.main .el-button {
    font-size: 18px;
    padding: 15px 40px !important;
}
</style>
<style>
.tpc .el-time-spinner__wrapper {
    width: 100% !important;
}
.tpc .el-scrollbar:nth-of-type(2) {
    display: none !important;
}
</style>
