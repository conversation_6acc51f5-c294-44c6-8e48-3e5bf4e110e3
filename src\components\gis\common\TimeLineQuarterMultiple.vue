<!-- @format -->
<!-- 小时进度条，支持多天、间隔小时等，一般用于水环境 -->
<template>
    <div>
        <div class="gis-timeline">
            <img
                @click="playClick"
                v-show="!timeId && showPlay"
                src="../../../assets/gis/commom/images/dark/gis_plybtn.png"
                class="pd-play"
                alt="渲染失败"
            />

            <img
                v-show="timeId && showPlay"
                @click="pauseClick"
                src="../../../assets/gis/commom/images/dark/gis_pausebtn.png"
                class="pd-play"
                alt="渲染失败"
            />
            <div class="slide">
                <div
                    class="item"
                    style="width: 100%"
                    v-for="(item, index) of arrDate"
                    :key="index"
                >
                    <ol class="colorBar">
                        <li
                            v-for="(hour, ii) of item.hours"
                            :key="index + ii"
                            :sj="item.day + ' ' + hour.data"
                            :class="[
                                {
                                    on: dateStr == item.day + '-' + hour.data
                                },
                                hour.clsName
                            ]"
                            @click="itemClick(item, hour.data, index, ii)"
                        >
                            <sup>{{
                                item.day + formatName(parseInt(hour.data))
                            }}</sup>
                        </li>
                    </ol>
                    <ol class="calendar">
                        <li
                            v-for="(hour, ii) of item.hours"
                            :key="index + ii"
                            v-show="labelShow"
                        >
                            {{ formatName(parseInt(hour.data)) }}
                        </li>
                    </ol>
                    <p>{{ item.day }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'TimeLine',
    props: [],
    data() {
        return {
            timeId: null,
            selectDate: '', //时期组件选择时间
            arrDate: [], // 进度条上的时间
            dateStr: '',
            lastDate: '', //最大时间
            startDate: '', //最小时间
            days: 5, //天数
            gap: 1, // 小时间隔
            labelShow: true, // 是否显示小时
            showDatePick: false, // 是否显示日期控件
            showPlay: false, // 是否显示播放控件
            selectIndex: 0, //当前选中的序号

            maxIndex: 0, //有效的最大序号
            minIndex: 0, //有效的最小序号
            interVal: 1 //播放间隔时间
        };
    },
    components: {},
    computed: {},
    mounted() {},
    unmounted() {
        this.clearTime();
    },
    methods: {
        /**
         *时间轴的初始化
         */
        initTimeLine(option) {
            this.lastDate = option.lastDate;
            this.dateStr = option.selectDate;
            this.selectDate = option.selectDate;
            this.startDate = option.startDate;
            this.days = option.nums;
            this.gap = option.gap;
            this.interVal = option.interVal;

            if (option.labelShow != undefined) {
                this.labelShow = option.labelShow;
            }

            if (option.showDatePick != undefined) {
                this.showDatePick = option.showDatePick;
            }

            if (option.showPlay != undefined) {
                this.showPlay = option.showPlay;
            }

            this.initData();
        },

        initData() {
            let result = [];

            let dd = Math.abs(this.days);

            let index = 0;

            //为正，表示未来的时间， 为负表示过去的时间
            if (this.days > 0) {
                for (let i = 0; i < dd; i++) {
                    let day = this.$dayjs(new Date(this.lastDate + '-01'))
                        .add(i, 'year')
                        .format('YYYY');

                    let hours = [];
                    for (let j = 1; j < 4; j++) {
                        if (j % this.gap == 0) {
                            let hour = j >= 10 ? j : '0' + j;

                            let sj = day + '-' + hour;

                            hours.push({
                                data: hour,
                                clsName: 'init'
                            });

                            if (sj == this.lastDate) {
                                index = (i * 4) / this.gap + hours.length - 1;
                            }
                        }
                    }

                    result.push({
                        day: day,
                        hours: hours
                    });
                }

                this.dateStr = result[0].day + ' ' + result[0].hours[0].data;

                //最后一个
                this.selectIndex = (dd * 12) / this.gap - 1;
            } else {
                for (let i = dd - 1; i >= 0; i--) {
                    let day = this.$dayjs(new Date(this.lastDate + '-01'))
                        .add(-i, 'year')
                        .format('YYYY');

                    let hours = [];
                    for (let j = 1; j <= 4; j++) {
                        if (j % this.gap == 0) {
                            let hour = '0' + j;

                            let sj = day + '-' + hour + '-01';

                            //在最新时间（lastDate）之后
                            let flag1 = this.$dayjs(sj).isAfter(
                                this.$dayjs(this.lastDate + '-01')
                            );

                            let flag2 = this.$dayjs(sj).isBefore(
                                this.$dayjs(this.startDate + '-01')
                            );

                            let flag = flag1 || flag2;

                            hours.push({
                                data: hour,
                                clsName: flag ? 'gray' : 'init'
                            });

                            if (sj == this.selectDate + '-01') {
                                index =
                                    ((dd - 1 - i) * 4) / this.gap +
                                    hours.length -
                                    1;
                            }

                            if (sj == this.lastDate + '-01') {
                                this.maxIndex =
                                    ((dd - 1 - i) * 4) / this.gap +
                                    hours.length -
                                    1;
                            }

                            if (sj == this.startDate + '-01') {
                                this.minIndex =
                                    ((dd - 1 - i) * 4) / this.gap +
                                    hours.length -
                                    1;
                            }
                        }
                    }

                    result.push({
                        day: day,
                        hours: hours
                    });
                }

                this.selectIndex = index;
            }

            this.arrDate = result;

            this.$emit('dateChange', this.dateStr, false);
        },

        //设置进度条的状态
        setCls(arr) {
            // let arr = [{ date: '2022-01-07 12', clsName: 'cb' }];

            for (let item of this.arrDate) {
                for (let obj of item.hours) {
                    let sj = item.day + ' ' + obj.data;

                    let result = arr.filter((oo) => {
                        return oo.date == sj;
                    });

                    if (result && result[0]) {
                        obj.clsName = result[0].clsName;
                    } else {
                        obj.clsName = 'gray';
                    }
                }
            }
        },

        itemClick(item, hour, index, ii) {
            let temp = (index * 4) / this.gap + ii;

            if (temp < this.minIndex || temp > this.maxIndex) {
                return;
            }

            this.selectIndex = temp;

            //手动点击后，播放暂停
            this.clearTime();

            this.dateStr = item.day + '-' + hour;
            this.$emit('dateChange', this.dateStr, false);
        },

        //暂停
        pauseClick() {
            this.clearTime();
        },

        //播放按钮点击
        playClick() {
            //如果正在播放，则停止
            if (this.timeId) {
                this.clearTime();
                return;
            }

            if (this.selectIndex == this.maxIndex) {
                this.selectIndex = this.minIndex;
                this.getDateStr();
                this.$emit('dateChange', this.dateStr, true);
            }

            // let max = (Math.abs(this.days) * 12) / this.gap;

            this.timeId = setInterval(() => {
                if (this.selectIndex >= this.maxIndex) {
                    this.clearTime();

                    return;
                }

                this.selectIndex++;
                this.getDateStr();
                this.$emit('dateChange', this.dateStr, true);
            }, this.interVal * 1000);
        },

        getDateStr() {
            let index = Math.ceil(this.selectIndex / (4 / this.gap));
            let ii = this.selectIndex % (4 / this.gap);

            if (ii != 0) {
                index = Math.ceil(this.selectIndex / (4 / this.gap)) - 1;
            }

            this.dateStr =
                this.arrDate[index].day +
                '-' +
                this.arrDate[index].hours[ii].data;

            console.log(this.dateStr);
        },

        //格式化名称
        formatName(num) {
            let str = '';
            switch (num) {
                case 1:
                    str = '一季度';
                    break;
                case 2:
                    str = '二季度';
                    break;
                case 3:
                    str = '三季度';
                    break;
                case 4:
                    str = '四季度';
                    break;
            }
            return str;
        },
        //清除定时器
        clearTime() {
            if (this.timeId) {
                clearInterval(this.timeId);
                this.timeId = null;
            }
        }
    },
    watch: {}
};
</script>

<style scoped></style>
