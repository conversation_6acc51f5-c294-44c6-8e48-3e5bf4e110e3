<!-- @format -->

<template>
    <el-submenu>
        <template v-slot:title>
            <!-- <i class="el-icon-location"></i> -->
            <router-link :to="m.LJDZ">{{ m.CDMC }}</router-link>
        </template>
        <template v-for="l in m.children">
            <el-menu-item v-if="l.children" :index="l.LJDZ" :key="l.LJDZ">{{
                l.CDMC
            }}</el-menu-item>
            <ReSub v-else :key="l" :m="l"></ReSub>
        </template>
    </el-submenu>
</template>
<script>
export default {
    name: 'ReSub',
    props: ['m']
};
</script>
