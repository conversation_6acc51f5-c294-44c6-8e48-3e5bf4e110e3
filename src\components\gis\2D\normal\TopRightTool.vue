<!-- @format -->

<template>
    <div>
        <!-- 导航 -->
        <Navigation :map="map" :mapOption="mapOption"></Navigation>

        <!-- 地图测量 -->
        <MapMeasure :map="map"></MapMeasure>

        <!-- 地图全屏 -->
        <FullScreen :map="map" :htmlRoot="htmlRoot"></FullScreen>

        <!-- 导出图片 -->
        <ExportMap :map="map"></ExportMap>

        <!-- 图层管理 -->
        <!-- <LayerManagement :dock="dock" :map="map"></LayerManagement> -->
    </div>
</template>

<script>
import Navigation from '../Navigation';
import MapMeasure from '../MapMeasure';
import FullScreen from '../FullScreen';
import ExportMap from '../ExportMap';
import LayerManagement from '../LayerManagement';

export default {
    name: 'TopRightTool',
    props: ['map', 'mapOption', 'dock', 'htmlRoot'],
    data() {
        return {};
    },
    components: {
        Navigation,
        MapMeasure,
        FullScreen,
        ExportMap,
        LayerManagement
    },
    computed: {},
    mounted() {},
    methods: {},
    watch: {}
};
</script>

<style scoped></style>
