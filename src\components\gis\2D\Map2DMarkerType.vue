<!-- @format -->
<!-- mapbox 标签业务组件 -->
<template>
    <div></div>
</template>

<script>
export default {
    data() {
        return {
            areaMarkers: [],
            pointSize: 'min'
        };
    },
    props: ['map', 'markerParam'],
    inject: ['callback'],
    components: {},
    mounted() {
        this.initEvent();
    },
    unmounted() {
        this.clear();
    },
    methods: {
        initEvent() {
            // console.log(this.map);
            if (!this.map) {
                setTimeout(() => {
                    this.initEvent();
                }, 200);
                return;
            }
            let self = this;
            // this.map.on('zoomend', (evt) => {
            //     self.zoomHandle();
            // });
            this.map.on('zoom-end', (evt) => {
                self.zoomHandle();
            });
        },
        //地图缩放事件处理
        zoomHandle() {
            let zoom = this.map.getZoom();
            let fjzoom = this.markerParam.FJZOOM || 10;
            if (
                (zoom >= fjzoom && this.pointSize == 'min') ||
                (zoom < fjzoom && this.pointSize == 'max')
            ) {
                this.pointSize = this.pointSize == 'max' ? 'min' : 'max';
                this.addMarkers();
            }
        },

        //绘制标签
        addMarkers() {
            switch (this.markerParam.type) {
                case '大气站点':
                case '大气站点2':
                case '微站':
                    this.addAirPoint();
                    break;
                case '水质站点':
                    this.addWaterPoint();
                    break;
            }
        },
        addAirPoint() {
            let result = [];

            PowerGis.removeCustomHtmlLayer('空气站点图层');

            for (let item of this.markerParam.pointData) {
                item.JD = item.longitude || item.JD;
                item.WD = item.latitude || item.WD;

                //需要显示设置宽高，否则会出现offset
                item.htmlTemplate = this.getContent(item);
                result.push(item);
            }

            PowerGis.addCustomHtmlLayer(
                this.map,
                '空气站点图层',
                result,
                (obj) => {
                    let item = obj.item;

                    // if (this.callback) {
                    if (obj.type == 'click') {
                        this.callback('空气站', obj.item);
                    }
                    // }
                    if (obj.type == 'mouseout') {
                        this.map.setMapCursor('default');
                        PowerGis.removeMapTip();
                    }
                    if (obj.type == 'mouseover') {
                        this.map.setMapCursor('pointer');
                        PowerGis.removeMapTip();
                        let contentStr = '';
                        let cssObj = {
                            background: 'rgba(0,0,0,0)'
                        };
                        // contentStr = this.getContentStr('空气站', item);
                        // let pt = PowerGis.getPoint(this.map, item);
                        // PowerGis.showMapTip(pt, this.map, cssObj, contentStr);
                    }
                }
            );
        },

        getContent(item) {
            let level = PowerGis.getLevelByWrw(this.markerParam.yz, item, 1);
            let dj = PowerGis.getAirDJByLevel(level);
            let data = item[this.markerParam.yz]
                ? item[this.markerParam.yz]
                : '--';
            let wid = 18 * item.CDMC.length;
            let htmlTemplate = ``;
            let width = 80 + wid + 'px';

            if (this.markerParam.type == '微站') {
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        htmlTemplate = `
               <div class="location">   <div class=" airLevel dj${dj}">${data}</div></div>
              `;
                    } else {
                        let marginLeft = (45 - wid) / 2;
                        htmlTemplate = `<div class="location">   <div class="airLevel dj${dj}">${data}</div><div class="mc" style="width:${wid}px;margin-left:${marginLeft}px">${item.CDMC}</div></div>`;
                    }
                } else {
                    htmlTemplate = `
                 <div class="location">  <div class=" airLevel dj${dj}">${data}</div></div>
              `;
                    if (this.markerParam.showAno) {
                        let marginLeft = (45 - wid) / 2;
                        htmlTemplate = `<div class="location">   <div class="airLevel dj${dj}">${data}</div><div class="mc" style="width:${wid}px;margin-left:${marginLeft}px">${item.CDMC}</div></div>`;
                    }
                }
            } else if (this.markerParam.type == '大气站点') {
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        htmlTemplate = `<dl class="pd-dlpin-air dj${dj}"><dt>${data}</dt><dd></dd></dl>`;
                    } else {
                        htmlTemplate = `<dl style="width:${width}; height:103px" class="pd-dlpin-air dj${dj}"><dt>${item.CDMC}&emsp;${data}</dt><dd></dd></dl>`;
                    }
                } else {
                    htmlTemplate = `<dl style="width:${width}; height:103px" class="pd-dlpin-air dj${dj}"><dt>${item.CDMC}&emsp;${data}</dt><dd></dd></dl>`;

                    if (!this.markerParam.showAno) {
                        htmlTemplate = `<dl class="pd-dlpin-air dj${dj}"><dt>${data}</dt><dd></dd></dl>`;
                    }
                }
            } else if (this.markerParam.type == '大气站点2') {
                let djtext = this.getDJTextByDJ(dj);
                let color = PowerGL.getAirColorByLevel(level);
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        htmlTemplate = `<div class="sw19-dwsite type${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    } else {
                        htmlTemplate = `<div class="sw19-dwsite type${dj}" >
				<h1><p>${item.CDMC}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                } else {
                    htmlTemplate = `<div class="sw19-dwsite type${dj}" >
				<h1><p>${item.CDMC}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    if (!this.markerParam.showAno) {
                        htmlTemplate = `<div class="sw19-dwsite type${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                }
            }
            return htmlTemplate;
        },
        getDJTextByDJ(val) {
            let str = '';
            switch (val) {
                case '0':
                    str = '无';
                    break;
                case '1':
                    str = '优';
                    break;
                case '2':
                    str = '良';
                    break;
                case '3':
                    str = '轻度';
                    break;
                case '4':
                    str = '中度';
                    break;
                case '5':
                    str = '重度';
                    break;
                case '6':
                    str = '严重';
                    break;
            }
            return str;
        },

        // 水点位
        addWaterPoint() {
            let result = [];

            PowerGis.removeCustomHtmlLayer('水质站点图层');

            for (let item of this.markerParam.pointData) {
                item.JD = item.longitude || item.JD;
                item.WD = item.latitude || item.WD;

                //需要显示设置宽高，否则会出现offset
                item.htmlTemplate = this.getWaterContent(item);
                result.push(item);
            }

            PowerGis.addCustomHtmlLayer(
                this.map,
                '水质站点图层',
                result,
                (obj) => {
                    let item = obj.item;

                    // if (this.callback) {
                    if (obj.type == 'click') {
                        this.callback('水质站点图层', obj.item);
                    }
                    // }
                    if (obj.type == 'mouseout') {
                        this.map.setMapCursor('default');
                        PowerGis.removeMapTip();
                    }
                    if (obj.type == 'mouseover') {
                        this.map.setMapCursor('pointer');
                        PowerGis.removeMapTip();
                        let contentStr = '';
                        let cssObj = {
                            background: 'rgba(0,0,0,0)'
                        };
                        // contentStr = this.getContentStr('空气站', item);
                        // let pt = PowerGis.getPoint(this.map, item);
                        // PowerGis.showMapTip(pt, this.map, cssObj, contentStr);
                    }
                }
            );
        },
        getWaterContent(item) {
            let htmlTemplate = ``;

            let dj = item.szlbbs;
            let waterQuality = item.waterQuality || '--';
            let color = PowerGis.getWaterColorByLevel(waterQuality);
            let djtext = PowerGis.getDJMCByLevel(dj + '');

            if (this.markerParam.type == '水质站点') {
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        htmlTemplate = `<div class="sw19-dwsite type1${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    } else {
                        htmlTemplate = `<div class="sw19-dwsite type1${dj}" >
				<h1><p>${item.pointName}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                } else {
                    htmlTemplate = `<div class="sw19-dwsite type1${dj}" >
				<h1><p>${item.pointName}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    if (!this.markerParam.showAno) {
                        htmlTemplate = `<div class="sw19-dwsite type1${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                }
            }

            return htmlTemplate;
        },
        //清除
        clear() {
            for (let marker of this.areaMarkers) {
                if (marker) {
                    marker.remove();
                    marker = null;
                }
            }

            this.areaMarkers = [];
        }
    },
    watch: {
        markerParam: {
            immediate: true,
            deep: true,
            handler(val) {
                if (this.markerParam && this.markerParam.pointData) {
                    if (this.markerParam.visible) {
                        this.addMarkers();
                    } else {
                        this.clear();
                    }
                }
            }
        }
    }
};
</script>

<style>
@import '~_as/gis/commom/map3DMarker.css';
</style>
