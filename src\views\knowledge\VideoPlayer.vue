<!-- @format -->

<template>
    <video ref="videoPlayer" class="video-js"></video>
    <el-dialog
        v-model="showTips"
        title="提示"
        width="30%"
        top="30vh"
        :show-close="false"
    >
        <span class="v-tips">{{
            isSwitchOut
                ? '检测到页面切换到后台，已暂停播放'
                : '您还在屏幕前吗？'
        }}</span>
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    @click="
                        play();
                        showTips = false;
                        this.isConfirmed = true;
                    "
                >
                    {{ isSwitchOut ? '继续播放' : '是的' }}
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import videojs from 'video.js';
export default {
    name: 'VideoPlayer',
    props: {
        options: {
            type: Object,
            default() {
                return {};
            }
        },
        watermark: {
            type: String,
            default() {
                return '';
            }
        },
        videoProgress: {
            type: Number,
            default() {
                return 0;
            }
        }
    },
    inject: [
        'startRecord',
        'stopRecord',
        'progressChange',
        'recordVideoWatchTimes'
    ],
    data() {
        return {
            player: null,
            isSwitchOut: false, //true 切换至后台暂停提示 false定时暂停提示
            showTips: false,
            setTimeFlag: 1, //currentTime属性赋值次数
            watchMaxTime: 0,
            isConfirmed: true
        };
    },
    created() {
        this.installListener();
    },
    mounted() {
        this.player = videojs(
            this.$refs.videoPlayer,
            { ...this.options, width: 1024, height: 576 },
            () => {
                this.player.log('onPlayerReady', this);
                this.addWaterMark();
            }
        );
        this.player.on('fullscreenchange', (params) => {
            this.addWaterMark();
        });

        this.player.on('play', (params) => {
            this.startRecord();
        });

        this.player.on('pause', (params) => {
            this.stopRecord();
        });
        this.player.on('ended', (params) => {
            this.endPlayer();
        });

        //当目前的播放位置已更改时触发
        this.player.on('timeupdate', () => {
            let currentTime = this.player.currentTime();
            let duration = this.player.duration();

            // console.log('this.player.currentTime', this.player.currentTime());
            if (this.setTimeFlag == 1) {
                this.watchMaxTime = parseInt(this.videoProgress);
                this.player.currentTime(parseInt(this.videoProgress));
                this.setTimeFlag = 2;
            }
            //如果当前播放进度等于总时长，复位
            if (parseInt(currentTime) == parseInt(duration)) {
                this.endPlayer();
                this.recordVideoWatchTimes(duration);
            }

            //获取当前播放位置的秒数，并设置localStorage
            if (currentTime > this.watchMaxTime) {
                //但如果相差1秒就证明是往后拖时间了 正常来说是每次时长更新相差0.3s
                let num =
                    parseFloat(currentTime) - parseFloat(this.watchMaxTime);
                if (num < 1) {
                    //正常播放时，记录当前播放时间
                    this.watchMaxTime = currentTime;
                    this.progressChange(currentTime);
                } else {
                    // 差值大于1s时，视为拖动，将上一次记录的播放时间赋值给当前播放器的播放时间
                    this.player.currentTime(this.watchMaxTime);
                    this.progressChange(this.watchMaxTime);
                }

                //30% 50% 80% 暂停提示检测是否在看
                let watchRate =
                    (parseFloat(this.watchMaxTime) * 100) /
                    parseFloat(duration);
                // console.log('currentTime', parseInt(currentTime));
                // console.log('duration', parseInt(duration));
                // console.log('watchRate', parseInt(watchRate));

                //如果百分比不等于30%50%80%，重置确认状态为false
                if (
                    parseInt(watchRate) != 30 &&
                    parseInt(watchRate) != 50 &&
                    parseInt(watchRate) != 80
                ) {
                    this.isConfirmed = false;
                }
                //如果百分比等于30%50%80%，暂停提示确认
                if (
                    (parseInt(watchRate) == 30 ||
                        parseInt(watchRate) == 50 ||
                        parseInt(watchRate) == 80) &&
                    !this.isConfirmed
                ) {
                    this.pause();
                    this.isSwitchOut = false;
                    this.showTips = true;
                }
            }
        });
        //监听键盘方向右键
        this.player.on('keydown', (event) => {
            let currentTime = this.player.currentTime();
            if (event.keyCode === 39) {
                //不允许通过方向键快进至超过当前播放的最大视频时间，超过时，将当前播放的最大视频时间赋值给当前播放器播放时间，小于时，不做操作
                if (currentTime > this.watchMaxTime) {
                    currentTime = this.watchMaxTime;
                }
            }
        });
    },
    beforeUnmount() {
        if (this.player) {
            this.player.dispose();
        }
    },
    unmounted() {
        this.uninstallListener();
    },
    methods: {
        onVisibilityChange() {
            if (document.hidden) {
                this.pause();
                this.isSwitchOut = true;
                this.showTips = true;
            }
        },
        installListener() {
            // 监听document visibilityState变化事件（用于监听页面是否被隐藏）
            document.addEventListener(
                'visibilitychange',
                this.onVisibilityChange
            );
        },
        uninstallListener() {
            // 解除监听事件
            document.removeEventListener(
                'visibilitychange',
                this.onVisibilityChange
            );
        },

        play() {
            this.player.play();
        },
        pause() {
            this.player.pause();
        },
        endPlayer() {
            this.player.pause();
            this.player.currentTime(0);
        },
        addWaterMark() {
            let container = document.getElementById(this.player.id_); // 注意水印盒子必须和video同级
            //清除水印
            if (document.getElementById('vieoWatermark')) {
                container.removeChild(document.getElementById('vieoWatermark'));
            }
            let containerWidth = container.offsetWidth; // 获取父容器宽
            let containerHeight = container.offsetHeight; // 获取父容器高
            // container.style.position = 'relative'; // 设置布局为相对布局
            // 创建canvas元素(先制作一块背景图)
            const can = document.createElement('canvas');
            can.width = 250; // 设置每一块的宽度
            can.height = 200; // 高度
            const cans = can.getContext('2d'); // 获取canvas画布
            cans.rotate((-20 * Math.PI) / 180); // 逆时针旋转π/9
            cans.font = '20px Vedana'; // 设置字体
            cans.fillStyle = 'rgba(200, 200, 200, 0.30)'; // 设置字体的颜色
            cans.textAlign = 'left'; // 文本对齐方式
            cans.textBaseline = 'Middle'; // 文本基线
            cans.fillText(this.watermark, 0, (4 * can.height) / 5); // 绘制文字
            // 创建一个div元素
            const div = document.createElement('div');
            div.id = 'vieoWatermark'; // 设置id
            div.style.pointerEvents = 'none'; // 取消所有事件
            div.style.top = '0px';
            div.style.left = '0px';
            div.style.position = 'absolute';
            div.style.zIndex = '100000';
            div.style.width = containerWidth + 'px';
            div.style.height = containerHeight + 'px';
            div.style.background =
                'url(' + can.toDataURL('image/png') + ') left top repeat';
            container.appendChild(div); // 追加到页面
        }
    }
};
</script>

<style lang="scss" scoped>
.v-tips {
    font-size: 18px;
    margin: 15px;
    display: block;
}
</style>
