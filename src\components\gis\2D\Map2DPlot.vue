<!-- @format -->

<template>
    <div></div>
</template>

<script>
export default {
    name: 'Map2DPlot', // arcgis 标绘
    created() {},
    data() {
        return {
            drawTool: null
        };
    },
    props: ['map'],
    components: {},
    unmounted() {},
    mounted() {},
    methods: {
        //绘制点位
        drawPoint(evt, layerName, callback) {
            let data = [];
            let item = {};
            item = {
                JD: evt.x,
                WD: evt.y,
                wkid: 4326,
                option: {
                    //该对象是设置不同图片的、没有则使用默认图片
                    url: './gis/2D/images/common/L3.png',
                    width: 20,
                    height: 27
                }
            };
            data.push(item);
            PowerGis.addImgPoint(this.map, layerName, data, false, (layer) => {
                // let graphics = layer.graphics;
                // graphics.forEach((gra) => {
                //     // if (gra.attributes.title == '定位点坐标') {
                //     PowerGis.centerToPointByZoom(
                //         this.map,
                //         gra.geometry,
                //         true,
                //         17
                //     );
                //     // }
                // });

                if (callback) {
                    callback(layer);
                }
            });
        },

        //绘制多边形
        drawPolygon(evt) {
            let data = [];
            let item = {};

            item = {
                title: evt.title,
                rings: evt.rings,
                wkid: 4326,
                option: {
                    color: [255, 125, 0, 0.2], //填充颜色
                    tcstyle: 'STYLE_SOLID', //填充样式
                    lineColor: [255, 20, 0], //设置符号线的颜色
                    style: 'STYLE_SOLID', //设置线的样式
                    lineWidth: 1 //先的宽度
                }
            };
            data.push(item);
            PowerGis.addPolygon(this.map, '标绘图层', data, false, (layer) => {
                let graphics = layer.graphics;
                graphics.forEach((gra) => {
                    this.map.setExtent(gra.geometry.getExtent().expand(1.5));
                    return;
                });

                layer.on('mouse-over', (evt) => {
                    //鼠标移入事件
                    this.map.setMapCursor('pointer');
                    PowerGis.removeMapTip();
                    let item = evt.graphic.attributes;
                    let contentStr = item.title;
                    if (!contentStr) {
                        return;
                    }
                    let cssObj = {};
                    PowerGis.showMapTip(
                        evt.mapPoint,
                        this.map,
                        cssObj,
                        contentStr
                    );
                });
                layer.on('mouse-out', (evt) => {
                    //鼠标移出事件
                    this.map.setMapCursor('default');
                    PowerGis.removeMapTip();
                });
            });
        },

        //绘制线
        drawPolyline(evt) {
            let data = [];
            let item = {};

            item = {
                title: evt.title,
                rings: evt.paths[0],
                wkid: 4326,
                option: {
                    lineColor: [255, 20, 0], //设置符号线的颜色
                    style: 'STYLE_SOLID', //设置线的样式
                    lineWidth: 3 //先的宽度
                }
            };
            data.push(item);

            PowerGis.addLine(this.map, '标绘图层', data, false, (layer) => {
                layer.on('mouse-over', (evt) => {
                    //鼠标移入事件
                    this.map.setMapCursor('pointer');
                    PowerGis.removeMapTip();
                    let item = evt.graphic.attributes;
                    let contentStr = item.title;
                    if (!contentStr) {
                        return;
                    }
                    let cssObj = {};
                    PowerGis.showMapTip(
                        evt.mapPoint,
                        this.map,
                        cssObj,
                        contentStr
                    );
                });
                layer.on('mouse-out', (evt) => {
                    //鼠标移出事件
                    this.map.setMapCursor('default');
                    PowerGis.removeMapTip();
                });
            });
        },

        /**
         * 开启绘制功能
         * isClear: 是否清理
         */
        drawData(type, isClear, callback) {
            if (isClear) {
                this.clear();
            }
            this.drawTool = PowerGis.activeDraw(
                this.map,
                '标绘图层',
                type,
                true,
                (type, evt) => {
                    if (type == 'POINT') {
                        this.drawPoint(evt, '标绘图层', callback);
                    } else if (type == 'POLYLINE') {
                        this.drawPolyline(evt);
                    } else if (type == 'POLYGON') {
                        this.drawPolygon(evt);
                    }

                    // this.$prompt('请输入名称', '提示', {
                    //     confirmButtonText: '确定',
                    //     cancelButtonText: '取消'
                    // })
                    //     .then(({ value }) => {
                    //         evt.title = value || this.selectItem.YQMC;
                    //         this.drawPolygon(evt);
                    //     })
                    //     .catch(() => {
                    //         // evt.title = '没有填';
                    //         // this.drawPolygon(evt);
                    //     });
                }
            );
        },

        //清除
        clear() {
            PowerGis.clearLayer(this.map, '标绘图层');
            this.map.graphics.clear();
        },

        //清除选中要素
        clearSelectGra() {
            if (this.drawTool) {
                this.drawTool.clearSelectGra();
            }
        }
    }
};
</script>

<style></style>
