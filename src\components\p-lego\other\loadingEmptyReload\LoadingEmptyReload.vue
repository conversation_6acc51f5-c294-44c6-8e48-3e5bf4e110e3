<!-- @format -->
<!--
 * @Author: zhang<PERSON><PERSON>
 * @Date: 2022-04-29 10:05:59
 * @LastEditTime: 2022-04-29 18:02:27
 * @LastEditors: zhanglei
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /Front_PC_COMPONENTS/src/components/business/other/LoadingEmptyReload/LoadingEmptyReload.vue
-->
<template>
    <div
        class="status-box"
        :style="areaStyle"
        :class="{
            'mask-mode': mOption.mode === 'mask',
            'show-mask':
                mOption.status === 'loading' || mOption.status === 'loadFail'
        }"
    >
        <!-- 无数据 -->
        <div v-if="mOption.status === 'empty'" class="status-box-empty">
            <img
                class="status-box-empty-img"
                src="./images/empty.png"
                alt="暂无数据"
            />
            <span class="status-box-tip">暂无数据</span>
        </div>

        <!-- 加载状态 -->
        <div v-if="mOption.status === 'loading'" class="status-box-loading">
            <div class="status-box-loading-icon"></div>
            <span class="status-box-tip">加载中...</span>
        </div>

        <!-- 重新请求 -->
        <div
            v-if="mOption.status === 'loadFail'"
            class="status-box-fail"
            @click="$emit('reload')"
        >
            <div class="status-box-fail-icon"></div>
            <span class="status-box-tip"> 重新加载 </span>
        </div>
    </div>
</template>

<script>
export default {
    // 说明
    // areaWidth 占位区域宽度
    // areaHeight 占位区域高度
    // status 状态 loading 加载中 empty 空数据 loadSuccess 加载完成 loadFail 加载失败
    // mode  nomal 占位   mask遮罩模式
    //@reload="getData"  getData为接口请求方法
    props: {
        option: {
            type: Object,
            default: () => {
                return {
                    areaWidth: '',
                    areaHeight: '',
                    status: 'empty',
                    mode: 'nomal'
                };
            }
        }
    },
    computed: {
        mOption() {
            return Object.assign(
                {
                    areaWidth: '',
                    areaHeight: '',
                    status: 'empty',
                    mode: 'nomal'
                },
                this.option
            );
        },
        areaStyle() {
            return {
                width: this.mOption.areaWidth,
                height: this.mOption.areaHeight
            };
        }
    },
    data() {
        return {};
    },
    mounted() {
        if (this.mOption.status !== 'empty') {
            this.$el.parentNode.style.position =
                this.$el.parentNode.style.position || 'relative';
        }
    }
};
</script>

<style scoped lang="less">
.status-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    // 加载中和重新加载添加占位
    &.show-mask {
        position: absolute;
        width: auto;
        height: auto;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;

        // 显示半透明背景色
        &.mask-mode {
            background: rgba(0, 0, 0, 0.1);
        }
    }

    &-tip {
        font-size: 16px;
        opacity: 0.8;
    }

    &-empty {
        max-height: 100%;
        text-align: center;
        &-img {
            height: 134px;
            max-height: 70%;
            display: block;
            margin: auto;
        }
    }

    &-fail {
        cursor: pointer;
    }

    &-loading-icon,
    &-fail-icon {
        position: relative;
        width: 64px;
        height: 64px;
        margin-bottom: 20px;
    }
    &-loading-icon::before,
    &-loading-icon::after,
    &-fail-icon::before {
        content: '';
        display: block;
        width: 64px;
        height: 64px;
        border: 4px solid #c0c0c0;
        border-radius: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        animation: ball-clip-rotate-multiple-rotate 1s linear infinite;
    }
    &-loading-icon::before,
    &-fail-icon::before {
        border-right-color: transparent;
        border-left-color: transparent;
    }
    &-loading-icon::after {
        width: 32px;
        height: 32px;
        border-top-color: transparent;
        border-bottom-color: transparent;
        animation-direction: reverse;
    }

    &-fail-icon::after {
        content: '';
        display: block;
        width: 32px;
        height: 32px;
        position: absolute;
        top: 50%;
        left: 50%;
        background-color: #c0c0c0;
        border-radius: 100%;
        animation: ball-clip-rotate-pulse-scale 1s
            cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
    }

    &-fail-icon::before,
    &-fail-icon::after {
        animation-play-state: paused;
    }

    &-fail-icon:hover::before,
    &-fail-icon:hover::after {
        animation-play-state: running;
    }
}

.darkTheme {
    .status-box-loading-icon::before,
    .status-box-fail-icon::before {
        border-top-color: #fff;
        border-bottom-color: #fff;
    }
    .status-box-loading-icon::after {
        border-right-color: #fff;
        border-left-color: #fff;
    }

    .status-box-fail-icon::after {
        background-color: #fff;
    }
}

@keyframes ball-clip-rotate-multiple-rotate {
    0% {
        -webkit-transform: translate(-50%, -50%) rotate(0deg);
        -moz-transform: translate(-50%, -50%) rotate(0deg);
        -o-transform: translate(-50%, -50%) rotate(0deg);
        transform: translate(-50%, -50%) rotate(0deg);
    }

    50% {
        -webkit-transform: translate(-50%, -50%) rotate(180deg);
        -moz-transform: translate(-50%, -50%) rotate(180deg);
        -o-transform: translate(-50%, -50%) rotate(180deg);
        transform: translate(-50%, -50%) rotate(180deg);
    }

    100% {
        -webkit-transform: translate(-50%, -50%) rotate(360deg);
        -moz-transform: translate(-50%, -50%) rotate(360deg);
        -o-transform: translate(-50%, -50%) rotate(360deg);
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes ball-clip-rotate-pulse-scale {
    0%,
    100% {
        opacity: 1;
        -webkit-transform: translate(-50%, -50%) scale(1);
        -moz-transform: translate(-50%, -50%) scale(1);
        -o-transform: translate(-50%, -50%) scale(1);
        transform: translate(-50%, -50%) scale(1);
    }

    30% {
        opacity: 0.3;
        -webkit-transform: translate(-50%, -50%) scale(0.15);
        -moz-transform: translate(-50%, -50%) scale(0.15);
        -o-transform: translate(-50%, -50%) scale(0.15);
        transform: translate(-50%, -50%) scale(0.15);
    }
}
</style>
