/** @format */

import AirCityRank from './air/airCityRank/airCityRank.vue';
import airClean from './air/airClean/airClean.vue';
import airQualityNow from './air/airQualityNow/airQualityNow.vue';
import AirQualityForecast from './air/AirQualityForecast/AirQualityForecast.vue';
import airDaysAnalysis from './air/airDaysAnalysis/airDaysAnalysis.vue';
import airOverDay from './air/airOverDay/airOverDay.vue';
import airCheck from './air/airCheck/Index.vue';
import airAndWindCharts from './air/airAndWindCharts/Index.vue';
import AirQuqlityChange from './air/qualityChange/Index.vue';
// import waterCalendar from './water/waterCalendar/WaterCalendar.vue';
import AdministrativeSanction from './mobile/administrativeSanction/AdministrativeSanction.vue';
import CapacityBuilding from './mobile/capacityBuilding/CapacityBuilding.vue';
import RegulatoryObject from './mobile/regulatoryObject/RegulatoryObject.vue';
import MobileLawEnforcement from './mobile/mobileLawEnforcement/MobileLawEnforcement.vue';
import AdministrativePunishmentRank from './mobile/administrativePunishmentRank/AdministrativePunishmentRank.vue';

import { installPlugins } from './utils/installPlugins';

let firPlugins = [
    airCheck,
    airAndWindCharts,
    airOverDay,
    airDaysAnalysis,
    AirCityRank,
    airClean,
    airQualityNow,
    AirQualityForecast,
    AirQuqlityChange,
    // waterCalendar,
    AdministrativeSanction,
    CapacityBuilding,
    RegulatoryObject,
    MobileLawEnforcement,
    AdministrativePunishmentRank
];

let plugins = [];

firPlugins.forEach((item) => {
    let i = installPlugins(item);
    plugins.push(i);
});

function install(app) {
    plugins.forEach(app.use);
}

export default {
    install
};

export { install };
