@font-face {
    font-family: "DIN-Medium", sans-serif;
    src: url("../../fonts/DIN-RegularAlternate.otf") format("otf");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "DIN-Bold", sans-serif;
    src: url("../../fonts/DIN-BoldAlternate.otf") format("otf");
    font-weight: normal;
    font-style: normal;
}

.el-table {
    /* 表头 */
    /* height: 50px; */
}
.el-table .cell{
    font-size: 16px;
    font-weight: 400;
    color: #000;
}
.el-dialog .el-dialog__header{
    margin-right: 0px!important;
    text-align: left;
    padding-top: 10px;
}
.el-dialog .el-dialog__headerbtn{
    font-size: 23px;
    top: -2px;
}
.el-dialog .el-dialog__headerbtn:hover .el-dialog__close{
    color: #fff;
}
.el-checkbox {
    --el-checkbox-font-size: 16px;
}
.el-button{
    height: 36px;
}
.el-select{
    height: 36px;
}
.el-input{
    height: 36px;
}
.el-input__inner{
    height: 36px;
}
.el-date-table{
    font-size: 14px;
}
.el-time-spinner__item{
    font-size: 14px;
}
.el-table thead{
    height: 50px;
}
.el-form-item{
    margin-bottom:22px;
}
.el-form-item__error{
    font-size: 14px;
}