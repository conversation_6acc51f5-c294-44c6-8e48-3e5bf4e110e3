<!-- @format -->
<!--月进度条，支持某一年 -->
<template>
    <div class="gis-timeaxis">
        <img
            @click="playClick"
            v-show="!timeId"
            :src="iconPlay"
            class="pd-play"
            alt="渲染失败"
            v-if="showPlay"
        />

        <img
            v-show="timeId"
            @click="pauseClick"
            :src="iconPause"
            class="pd-play"
            v-if="showPlay"
            alt="渲染失败"
        />
        <el-date-picker
            v-model="selectDate"
            type="year"
            placeholder="选择日期"
            :clearable="false"
            value-format="YYYY"
            format="YYYY"
            @change="selectDateChanged"
            v-if="showDatePick"
            style="width: 140px; margin-top: 10px"
        >
        </el-date-picker>
        <ul class="pd-ulnumlst">
            <li
                v-for="(item, index) of arrDate"
                :key="index"
                :class="[
                    {
                        on: selectMonth == item.month
                    },
                    item.clsName
                ]"
                @click="itemClick(item)"
            >
                {{ item.month }}<sup>{{ item.tipStr }}</sup>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    data() {
        return {
            selectDate: '2021', //当前选中的日历
            arrDate: [],
            selectMonth: '', //当前选中的月份
            resultData: null,
            lastDate: '', //最新时间
            timeId: '',
            showDatePick: true,
            showPlay: true,
            iconPlay: require('@/assets/gis/commom/images/dark/gis_plybtn.png'),
            iconPause: require('@/assets/gis/commom/images/dark/gis_pausebtn.png')
        };
    },
    components: {},
    unmounted() {
        this.clearTime();
    },
    mounted() {
        let theme = window.localStorage.getItem('themeType');
        if (theme == 'light') {
            this.iconPlay = require('@/assets/gis/commom/images/light/gis_light_plybtn.png');
            this.iconPause = require('@/assets/gis/commom/images/light/gis_light_pausebtn.png');
        }
    },
    methods: {
        selectDateChanged() {
            if (this.selectDate == this.lastDate.substring(0, 4)) {
                this.selectMonth = parseInt(this.lastDate.substring(5, 7));
            } else {
                this.selectMonth = '12';
            }

            this.$emit('datePickChange', this.selectDate);
            this.intData();
        },

        //初始化timeline
        initTimeLine(option) {
            this.lastDate = option.initDate;
            this.selectDate = option.selectDate.substring(0, 4);
            this.selectMonth = parseInt(option.selectDate.substring(5, 7));

            if (option.showDatePick != undefined) {
                this.showDatePick = option.showDatePick;
            }

            if (option.showPlay != undefined) {
                this.showPlay = option.showPlay;
            }

            this.intData();
        },

        intData() {
            this.arrDate = [];

            for (let i = 1; i <= 12; i++) {
                let sj = this.selectDate + (i >= 10 ? '-' + i : '-0' + i);

                //在最新时间（initDate）之后
                let flag = this.$dayjs(sj + '-01').isAfter(
                    this.$dayjs(this.lastDate + '-01')
                );

                this.arrDate.push({
                    month: i,
                    sj: sj,
                    tipStr: sj,
                    clsName: flag ? '' : 'init'
                });
            }

            let mm =
                this.selectMonth >= 10
                    ? this.selectMonth
                    : '0' + this.selectMonth;

            this.$emit('dateChange', this.selectDate + '-' + mm, false);
        },

        itemClick(item) {
            this.clearTime();
            this.selectMonth = item.month;

            this.$emit('dateChange', item.sj, false);
        },

        //暂停
        pauseClick() {
            this.clearTime();
        },

        //播放按钮点击
        playClick() {
            //如果正在播放，则停止
            if (this.timeId) {
                this.clearTime();
                return;
            }

            let sj = this.selectDate + '-' + this.selectMonth;
            let flag1 = this.$dayjs(sj + '-01').isAfter(
                this.$dayjs(this.lastDate + '-01')
            );

            let flag2 = this.$dayjs(sj + '-01').isSame(
                this.$dayjs(this.lastDate + '-01')
            );

            //如果当前时间在最新时间的后面，则从第一个开始播放
            if (flag1 || flag2 || this.selectMonth == '12') {
                this.selectMonth = '01';
            }

            this.timeId = setInterval(() => {
                let i = parseInt(this.selectMonth);
                i++;
                this.selectMonth = i >= 10 ? i : '0' + i;

                let sj = this.selectDate + '-' + this.selectMonth;

                this.$emit('dateChange', sj, true);

                let flag = this.$dayjs(sj + '-01').isSame(
                    this.$dayjs(this.lastDate + '-01')
                );

                console.log(i);

                if (i == 12 || flag) {
                    this.clearTime();
                }
            }, 1000);
        },

        //设置进度条的状态
        setCls(arr) {
            // arr = [{ date: '2021-01', clsName: 'cb' }];
            for (let item of this.arrDate) {
                let result = arr.filter((oo) => {
                    return oo.date == item.sj;
                });

                if (result && result[0]) {
                    item.clsName = result[0].clsName;
                } else {
                    item.clsName = 'gray';
                }
            }
        },

        //清除定时器
        clearTime() {
            if (this.timeId) {
                clearInterval(this.timeId);
                this.timeId = null;
            }
        }
    }
};
</script>

<style scoped></style>
