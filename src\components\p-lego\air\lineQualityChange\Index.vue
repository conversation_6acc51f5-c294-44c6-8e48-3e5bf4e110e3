<!-- @format -->

<template>
    <div style="background: #064959">
        <ul class="pd-ultbs2 zy-item4">
            <li
                v-for="item in pollutionArr"
                :key="item.value"
                :class="{ on: item.value == itemCode }"
                @click="changeType(item.value)"
            >
                {{ item.name }}
            </li>
        </ul>
        <div class="gap"></div>
        <div class="zy-tu">
            <p-chart
                :option="barOpt"
                class="chart"
                style="width: 100%; height: 400px"
            ></p-chart>
            <!-- <img src="~@/assets/airGovernment/images/ecimg1.png" alt="" /> -->
        </div>
    </div>
</template>

<script>
import util from './util';
import echarts from 'echarts';
export default {
    name: 'air-quality-change',
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    dataSource: [], //图表数据源，二维数组
                    legend: []
                };
            }
        }
    },
    data() {
        return {
            pollutionArr: [
                { name: 'AQI', value: 'AQI' },
                { name: 'PM₂.₅', value: 'PM25' },
                { name: 'PM₁₀', value: 'PM10' },
                { name: 'SO₂', value: 'SO2' },
                { name: 'NO₂', value: 'NO2' },
                { name: 'O₃', value: 'O3' },
                { name: 'CO', value: 'CO' }
            ],
            itemCode: 'AQI',
            chartData_: {},
            barOpt: {}
        };
    },
    watch: {
        data: 'showChart'
    },
    mounted() {
        // console.log(this.data);
        this.showChart();
    },
    methods: {
        showChart() {
            let fontColor = '#30eee9';

            let xAxis = this.getXAxisData();
            let series = this.getSeries();

            this.barOpt = {
                grid: {
                    left: '2%',
                    right: '5%',
                    top: '25%',
                    bottom: '10%',
                    containLabel: true
                },
                color: [
                    '#0070BA',
                    '#00DB70',
                    '#00D0F5',
                    '#F7BB00',
                    '#FF4B4C',
                    '#9465F3',
                    '#FF5000'
                ],
                tooltip: {
                    show: true,
                    trigger: 'axis'
                },
                toolbox: {
                    show: false
                },
                legend: {
                    show: true,
                    x: 'center',
                    top: '5%',
                    itemWidth: 10,
                    itemHeight: 10,
                    textStyle: {
                        color: '#fff',
                        fontSize: 16
                    },
                    width: 360,
                    data: this.data.legend
                },
                // dataZoom: {
                //     show: true,
                //     bottom: '0',
                //     height: '20',
                //     start: 60,
                //     end: 100
                // },
                xAxis: [
                    {
                        type: 'category',
                        boundaryGap: false,
                        axisLabel: {
                            color: '#fff',
                            textStyle: {
                                fontSize: 16
                            }
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#397cbc'
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: false,
                            lineStyle: {
                                color: '#195384'
                            }
                        },
                        data: xAxis
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}',
                            textStyle: {
                                color: '#fff',
                                fontSize: 16
                            }
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#27b4c2'
                            }
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#11366e'
                            }
                        }
                    }
                ],
                series: series
            };
        },
        getXAxisData() {
            if (!this.data.dataSource || this.data.dataSource.length == 0) {
                return [];
            }
            let set = new Set();
            this.data.dataSource.forEach((item) => {
                item.sort((a, b) => {
                    return new Date(a).getTime() - new Date(b).getTime();
                }).forEach((data) => {
                    set.add(this.$dayjs(data.JCSJ).format('MM-DD HH'));
                });
            });

            return Array.from(set);
        },
        getSeries() {
            if (!this.data.dataSource || this.data.dataSource.length == 0) {
                return [];
            }

            let series = [];
            this.data.dataSource.forEach((item, index) => {
                let data = item
                    .sort((a, b) => {
                        return new Date(a).getTime() - new Date(b).getTime();
                    })
                    .map((item) => {
                        return item[this.itemCode];
                    });
                let name = this.data.legend[index];
                let sery = {
                    name: name,
                    type: 'line',
                    symbol: 'none',
                    symbolSize: 8,
                    data: data
                };
                if (name == '光明区') {
                    sery.areaStyle = {
                        color: 'rgba(0, 123, 204,0.4)'
                    };
                }
                series.push(sery);
            });
            // console.log(series);
            return series;
        },
        changeType(type) {
            this.itemCode = type;
            this.showChart();
        }
    }
};
</script>

<style scoped>
.pd-ultbs2 {
    text-align: center;
    font-size: 0;
}
.pd-ultbs2 li {
    display: inline-block;
    width: 58px;
    height: 23px;
    line-height: 23px;
    text-align: center;
    font-size: 14px;
    color: #fff;
    border: 1px solid #bcbcbc;
    cursor: pointer;
}
.pd-ultbs2 li + li {
    cursor: pointer;
    margin-left: 10px;
}
.pd-ultbs2 li.on {
    background: #0181dc;
    color: #fff;
    border-color: #0181dc;
}
.pd-ultbs2 li sub {
    font-size: 12px;
    vertical-align: baseline;
}
.pd-ultbs2.zy-item4 li {
    border-color: #2083f3;
    cursor: pointer;
}
</style>
