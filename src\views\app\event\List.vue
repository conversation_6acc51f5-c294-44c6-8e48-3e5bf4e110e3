<!-- @format -->

<template>
    <div style="width: 100%; height: 50%">
        <el-table
            :data="tableObj.tableData"
            :height="$parent.height"
            border
            style="width: 100%"
        >
            <el-table-column label="事件名称">
                <template v-slot="scope">
                    <i class="el-icon-time"></i>
                    <span style="margin-left: 10px">{{
                        scope.row.eventName
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column label="事件地址">
                <template v-slot="scope">
                    <i class="el-icon-time"></i>
                    <span style="margin-left: 10px">{{
                        scope.row.address
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column label="发生时间">
                <template v-slot="scope">
                    <i class="el-icon-time"></i>
                    <span style="margin-left: 10px">{{ scope.row.date }}</span>
                </template>
            </el-table-column>
            <el-table-column label="接警时间">
                <template v-slot="scope">
                    <i class="el-icon-time"></i>
                    <span style="margin-left: 10px">{{
                        scope.row.warmingDate
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column label="事件类型">
                <template v-slot="scope">
                    <i class="el-icon-time"></i>
                    <span style="margin-left: 10px">{{
                        scope.row.eventTypeName
                    }}</span>
                </template>
            </el-table-column>

            <el-table-column label="操作">
                <template v-slot="scope">
                    <el-button
                        size="mini"
                        @click="handleEdit(scope.$index, scope.row)"
                        >编辑</el-button
                    >
                    <el-button
                        size="mini"
                        type="danger"
                        @click="handleDelete(scope.$index, scope.row)"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <div class="block" style="position: relative; top: -80px">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="tableObj.currentPage"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="10"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableObj.total"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
import { delUser } from '_a/user';
export default {
    data() {
        return {
            tableObj: {
                total: 0,
                currentPage: 0,
                tableData: []
            },
            name: 'user'
        };
    },
    mounted() {
        //this.$store.dispatch('user/fetchList');
    },
    methods: {
        // 点击编辑的时候。通过$parent 调用编辑组件（Edit）的方法，来回选编辑
        handleEdit(index, row) {
            this.$parent.$refs.editRegion.dialogFormVisible = true;
            this.$parent.$refs.editRegion.setData(row);
        },
        handleDelete(index, row) {
            this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    delUser(row._id).then(() => {
                        this.$parent.fetchList();
                        this.$message({
                            showClose: true,
                            message: '删除成功',
                            type: 'success'
                        });
                    });
                })
                .catch(() => {});
        },
        // 分页个数改变，调用父组件的搜索改变方法，然后重新加载数据
        handleSizeChange(val) {
            this.$parent.setSearch({ pageSize: val });
            this.$parent.fetchList();
        },
        // 分页数改变，调用父组件的搜索改变方法，然后重新加载数据
        handleCurrentChange(val) {
            this.$parent.setSearch({ pageNum: val });
            this.$parent.fetchList();
        }
    }
};
</script>

<style scoped></style>
