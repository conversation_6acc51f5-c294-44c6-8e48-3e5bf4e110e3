<!-- @format -->

<template>
    <div class="flx1" style="height: 100%">
        <AirJudgmentMap
            :paramsData="paramsData"
            ref="childMap"
            @resultChange="resultChangeHandle"
        >
        </AirJudgmentMap>
        <div class="sw0725-rarea1">
            <div class="gap15"></div>
            <div class="flx1 ac">
                <ul class="sw0726-ultbs1" style="width: 195px">
                    <li
                        :class="{ on: fxlx == 'SFX' }"
                        @click="fxlxChange('SFX')"
                    >
                        上风向
                    </li>
                    <li :class="{ on: fxlx == 'ZB' }" @click="fxlxChange('ZB')">
                        周边
                    </li>
                </ul>
                <p class="sw0725-txt1" style="margin-left: 40px">范围：</p>
                <input
                    type="text"
                    class="sw0725-inptxt1"
                    v-model="fw"
                    style="width: 90px"
                />
                <p class="sw0725-txt1" style="margin-left: 10px">KM</p>
                <button
                    type="button"
                    class="sw0726-btn1"
                    style="width: 90px; margin-left: auto"
                    @click="searchData"
                >
                    查询
                </button>
            </div>
            <div class="gap"></div>
            <div class="gap"></div>

            <ul class="sw0725-ultbs3">
                <li
                    v-for="(item, index) of arrTab"
                    :key="index"
                    :class="{ on: curTab === item.type }"
                    @click="curTab = item.type"
                >
                    {{ item.name }}<br />（{{ objResult[item.type].length }}）
                </li>
            </ul>
            <div class="gap"></div>
            <div class="gap"></div>
            <ul class="sw0725-ullst1" v-show="curTab === 'zbzd'">
                <li
                    v-for="(item, index) of objResult['zbzd']"
                    :key="index"
                    @click="itemClick(item)"
                >
                    <div class="flx1 ac jb">
                        <h1>
                            <strong>{{ item.CDMC }}</strong
                            ><i class="grn" style="margin-left: 15px">{{
                                item.JGJBNAME
                            }}</i>
                        </h1>
                        <h1>
                            <span>首要污染物：{{ item.SYWRW }}</span
                            ><small>单位：μg/m³(CO:mg/m³)</small>
                        </h1>
                    </div>
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <div class="sw0725-grd2 flx1">
                        <dl class="sw0726-dlaqi" style="width: 200px">
                            <dt>-AQI-</dt>
                            <dd
                                :style="{
                                    color: colors[item.aqiLevel]
                                }"
                            >
                                {{ item.AQI }}
                            </dd>
                        </dl>
                        <div class="row" style="flex: 1">
                            <div class="col col-4">
                                <div class="sw0725-datatxt1">
                                    <p class="p1">PM₂.₅</p>
                                    <p
                                        class="p2"
                                        :style="{
                                            color: colors[item.pm25Level]
                                        }"
                                    >
                                        {{ item.PM25 }}
                                    </p>
                                </div>
                            </div>
                            <div class="col col-4">
                                <div class="sw0725-datatxt1">
                                    <p class="p1">PM₁₀</p>
                                    <p
                                        class="p2"
                                        :style="{
                                            color: colors[item.pm10Level]
                                        }"
                                    >
                                        {{ item.PM10 }}
                                    </p>
                                </div>
                            </div>
                            <div class="col col-4">
                                <div class="sw0725-datatxt1">
                                    <p class="p1">O₃</p>
                                    <p
                                        class="p2"
                                        :style="{
                                            color: colors[item.o3Level]
                                        }"
                                    >
                                        {{ item.O3 }}
                                    </p>
                                </div>
                            </div>
                            <div class="gap15 clear"></div>
                            <div class="col col-4">
                                <div class="sw0725-datatxt1">
                                    <p class="p1">SO₂</p>
                                    <p
                                        class="p2"
                                        :style="{
                                            color: colors[item.so2Level]
                                        }"
                                    >
                                        {{ item.SO2 }}
                                    </p>
                                </div>
                            </div>
                            <div class="col col-4">
                                <div class="sw0725-datatxt1">
                                    <p class="p1">NO₂</p>
                                    <p
                                        class="p2"
                                        :style="{
                                            color: colors[item.no2Level]
                                        }"
                                    >
                                        {{ item.NO2 }}
                                    </p>
                                </div>
                            </div>
                            <div class="col col-4">
                                <div class="sw0725-datatxt1">
                                    <p class="p1">CO</p>
                                    <p
                                        class="p2"
                                        :style="{
                                            color: colors[item.coLevel]
                                        }"
                                    >
                                        {{ item.CO }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

            <ul class="sw0725-ullst1 sw0725-content" v-show="curTab != 'zbzd'">
                <li
                    v-for="(item, index) of objResult[curTab]"
                    :key="index"
                    @click="itemClick(item)"
                >
                    <div class="flx1 ac jb">
                        <h1>
                            <strong class="pointer" @click="openDetail(item)">{{
                                item.QYMC
                            }}</strong>
                        </h1>
                    </div>
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <div class="flx1 ac jb">
                        <span>行业类型：{{ item.HYLX }}</span>
                        <span>距离：{{ item.distance }} km</span>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
import AirJudgmentMap from './AirJudgmentMap.vue';
export default {
    // props: ['paramsData'],
    data() {
        return {
            curTab: 'zbzd',
            objResult: {
                zbzd: [],
                zxjc: [],
                sqzdy: []
            },

            colors: [
                '#CCCCCC',
                '#2DB62D',
                '#CBCC33',
                '#FF7E00',
                '#FF0000',
                '#99004C',
                '#7E0023'
            ],

            fxlx: 'ZB',
            fw: 5,
            fxjd: 0,

            paramsData: {}, //正式环境不用这个

            arrTab: [
                {
                    name: '周边站点',
                    type: 'zbzd',
                    sum: 0
                },
                {
                    name: '在线监测企业',
                    type: 'zxjc',
                    sum: 0
                },
                {
                    name: '涉气重点源',
                    type: 'sqzdy',
                    sum: 0
                }
            ]
        };
    },
    mounted() {
        this.paramsData = {
            JD: 120.6144750928243,
            WD: 30.975579857498687,
            CDDM: '',
            YJYZ: 'PM25',
            FXJD: 45,
            YJSJ: '2023-11-14 10:00:00'
        };

        this.fxjd = this.paramsData.FXJD;
        this.getData();
    },
    components: {
        AirJudgmentMap
    },
    methods: {
        handleClose() {
            this.$emit('close');
        },
        fxlxChange(type) {
            this.fxlx = type;
        },

        getData() {
            this.$refs.childMap.initData(this.fxlx, this.fw, this.fxjd);
        },
        resultChangeHandle(type, data) {
            this.objResult[type] = data;
        },

        itemClick(item) {
            PowerGL.pointTo(this.$refs.childMap.map, item, 14, true);
        },

        // 显示污染源详情框
        openDetail(v) {
            console.log(v);
        },
        searchData() {
            this.getData();
        }
    }
};
</script>

<style>
@import '~_as/gis/commom/mapCommon.css';
@import '~_as/gis/commom/mapTool.css';
@import '~_as/gis/commom/map3DMarker.css';

@import './css/style.css';
</style>
