<!-- @format -->
<!-- 鹰眼 -->
<template>
    <div
        class="small-map-gis"
        :class="{ small: type == 'small' }"
        id="id_overviewMap"
    >
        <i class="suoxiao" @click="btnExpand()"></i>
    </div>
</template>

<script>
export default {
    name: 'EagleEye',
    props: ['map'],
    data() {
        return {
            type: 'big',

            arrMap: []
        };
    },
    components: {},
    computed: {},
    mounted() {
        this.initPage();
    },
    methods: {
        initPage() {
            if (!this.map) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.loadMap();
        },

        loadMap() {
            this.arrMap = GisServerGlobalConstant.arcgis.basemaps.filter(
                (item) => {
                    return item.layerControl == true;
                }
            );

            let mapObj = {};
            this.arrMap.map((item) => {
                if (item.visible) {
                    mapObj = item;
                }
            });

            $('#id_overviewMap_root').remove();

            PowerGis.overViewMap(
                this.map,
                mapObj,
                'id_overviewMap',
                GisServerGlobalConstant.arcgis.mapOptions
            );
        },

        btnExpand() {
            if (this.type == 'big') {
                this.type = 'small';
            } else {
                this.type = 'big';
            }

            this.$emit('sizeChange', this.type);
        }
    },
    watch: {}
};
</script>

<style scoped></style>
