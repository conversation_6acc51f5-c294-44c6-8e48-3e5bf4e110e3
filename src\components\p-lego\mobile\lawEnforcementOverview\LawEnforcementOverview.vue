<!-- @format -->

<!--
 * @Author: 姚进玺 <EMAIL>
 * @Date: 2022-05-05 11:54:24
 * @LastEditors: 姚进玺 <EMAIL>
 * @LastEditTime: 2022-05-05 12:13:54
 * @FilePath: /Front_PC_COMPONENTS/src/components/business/mobile/lawEnforcementOverview/LawEnforcementOverview.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- @format -->

<template>
    <div>
        <p class="bs-til1">{{ componentsTitle }}</p>
        <div class="gap"></div>
        <div class="xzhj-zfwrap">
            <ul>
                <li v-for="item in overviewData" :key="item.id">
                    <h1>{{ item.name }}</h1>
                    <h2 :style="{ color: item.color }">
                        {{ item.value || '0' }}
                    </h2>
                    <div>
                        <b :class="item.bfb >= 0 ? 'up' : 'dwn'"
                            >{{ item.bfb || '0' }}%</b
                        >
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        overviewData: {
            type: Array
        },

        componentsTitle: {
            type: String
        }
    },
    data() {
        return {};
    },
    computed: {},
    watch: {},
    methods: {}
};
</script>

<style scoped>
.xzhj-zfwrap ul {
    display: flex;
    justify-content: space-around;
}
.xzhj-zfwrap ul li {
    padding: 0 24px;
    position: relative;
    box-sizing: border-box;
}
.xzhj-zfwrap ul li::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 100%;
    width: 1px;
    height: 100%;
    background: url(./images/dash.png) no-repeat center center;
}
.xzhj-zfwrap ul li:last-child::after {
    display: none;
}
.xzhj-zfwrap ul li h1 {
    color: #fff;
    font-size: 16px;
}

.xzhj-zfwrap ul li h2 {
    color: #00b0ff;
    font-size: 28px;
    margin: 3px 0;
    font-family: 'DINPro-Bold';
}

.xzhj-zfwrap ul li div b {
    font-size: 16px;

    padding-left: 46px;
}

.xzhj-zfwrap li div b.dwn {
    background: url(./images/down2.png) no-repeat 0 center;
    color: #ee3b5b;
}

.xzhj-zfwrap li div b.up {
    background: url(./images/up2.png) no-repeat 0 center;
    color: #73bb31;
}

.bs-til1 {
    font-size: 18px;
    color: #fff;
    line-height: 36px;
    position: relative;
    padding-left: 15px;
}

.bs-til1::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #00b4ff;
}
</style>
