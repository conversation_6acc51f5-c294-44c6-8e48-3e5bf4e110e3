/**
 * 污染物名称转义
 *
 * @format
 * @param {String} name
 */

export function hex2rgba(value, alpha) {
    let arr = value.split('');
    if (arr[0] === '#') {
        arr = arr.slice(1);
    }
    let length = arr.length;
    let red = 0;
    let green = 0;
    let blue = 0;
    let a = alpha || 1;
    if (length === 3 || length === 4) {
        red = parseInt(`0x${arr[0]}`);
        green = parseInt(`0x${arr[1]}`);
        blue = parseInt(`0x${arr[2]}`);
        if (length === 4) {
            a = parseInt(`0x${arr[3]}`);
        }
    } else if (length === 6 || length === 7 || length === 8) {
        red = parseInt(`0x${arr[0]}${arr[1]}`);
        green = parseInt(`0x${arr[2]}${arr[3]}`);
        blue = parseInt(`0x${arr[4]}${arr[5]}`);
        if (length === 7) {
            a = parseInt(`0x${arr[6]}`);
        } else if (length === 8) {
            a = parseInt(`0x${arr[6]}${arr[7]}`);
        }
    }
    return `rgba(${red}, ${green}, ${blue}, ${a})`;
}
