<!-- @format -->

<!-- 实时空气质量圆环图 -->

<template>
    <div :style="circleStyle" class="air-real-aqi">
        <circle-progress
            class="air-real-aqi-chart"
            :ringWidth="6"
            :max="500"
            :progress="value"
            :ringColor="lineColor"
        />
        <div>
            <div class="air-real-aqi-label">-AQI-</div>
            <div :style="{ color }" class="air-real-aqi-value">{{ value }}</div>
            <div :style="{ color }" class="air-real-aqi-level">{{ level }}</div>
        </div>
    </div>
</template>

<script>
import util from './util';

import CircleProgress from './CircleProgress.vue';

export default {
    name: 'AqiRealtime',

    components: {
        CircleProgress
    },

    data() {
        return {
            lineColor: '#fff',
            level: '-'
        };
    },

    props: {
        value: {
            type: Number,
            default: 0
        },
        circleStyle: Object
    },

    computed: {
        color() {
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            this.level = util.getLevelPollution('AQI', this.value).txt;
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            this.lineColor = util.getLevelPollution('AQI', this.value).color;
            return util.getLevelPollution('AQI', this.value).color;
        }
    }
};
</script>

<style scoped lang="less">
.air-real-aqi {
    position: relative;
    width: 170px;
    height: 170px;
    text-align: center;
    overflow: hidden;

    &-chart {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 50%;
        transform: translate(-50%);
    }

    &-label {
        position: absolute;
        top: 18%;
        left: 0;
        right: 0;
        margin: auto;
    }

    &-level {
        position: absolute;
        bottom: 16%;
        left: 0;
        right: 0;
        margin: auto;
        font-size: 22px;
    }

    &-value {
        position: absolute;
        height: 40px;
        line-height: 40px;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        margin: auto;
        font-size: 50px;
        font-family: 'DIN-Bold';
    }
}
</style>
