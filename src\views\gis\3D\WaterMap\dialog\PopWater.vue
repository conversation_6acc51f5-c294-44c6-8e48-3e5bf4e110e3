<!-- @format -->

<template>
    <div
        class="yy-mapalert1"
        style="
            height: 280px;
            width: 580px;
            margin-bottom: 0;
            transform: translate(-50%, 0);
        "
        :style="{ height: height + 'px' }"
        :class="{ zjs: selectItem.DMSX == '重金属' }"
    >
        <div class="flx1 ac jb hdbox" style="margin-bottom: -10px">
            <h1 class="t1" style="width: 310px">
                {{ selectItem.CDMC
                }}<i class="yygkbtn" v-show="selectItem.JGJBMC">{{
                    selectItem.JGJBMC
                }}</i>
            </h1>
            <p class="time1">
                {{ jcsj }}
            </p>
        </div>
        <img
            src="../css/images/yy-alinebg.png"
            alt=""
            style="width: 540px"
            class="yy-alinebg"
        />
        <div class="bdbox" v-show="selectItem.DMSX != '重金属'">
            <div class="flx1 ac mbkind">
                <p class="f1" :class="bgCls">
                    {{ selectItem.SZLB ? selectItem.SZLB : '--' }}
                </p>
                <div class="rfont">
                    <p class="p1">
                        目标：{{ selectItem.ZXBZMC ? selectItem.ZXBZMC : '--' }}
                    </p>
                    <p class="p1">
                        超标因子及超标倍数：
                        {{ selectItem.CBXM ? selectItem.CBXM : '--' }}
                    </p>
                </div>
            </div>
        </div>

        <dl
            class="sw0630-dlbx2"
            style="margin-top: 5px"
            v-show="selectItem.DMSX != '重金属'"
        >
            <dd>
                <ul>
                    <li
                        style="width: 18%; margin-top: 10px"
                        v-for="(item, index) of arrYZ"
                        :key="index"
                        :class="item.borderCls"
                    >
                        <h1
                            :class="[
                                item.bgCls,
                                { small: item.name.length > 4 }
                            ]"
                            style="padding: 0 5px"
                        >
                            {{ item.name }}
                        </h1>
                        <p :class="item.colorCls">
                            {{ item.value }} / {{ item.SZLB }}
                        </p>
                    </li>
                </ul>
            </dd>
        </dl>

        <dl
            class="sw0630-dlbx2"
            style="margin-top: 5px"
            v-show="selectItem.DMSX == '重金属'"
        >
            <dd>
                <ul>
                    <li
                        style="width: 18%; margin-top: 10px"
                        v-for="(item, index) of arrYZ2"
                        :key="index"
                        :class="item.borderCls"
                    >
                        <h1 :class="item.bgCls" style="padding: 0 5px">
                            {{ item.name }}
                        </h1>
                        <p :class="item.colorCls">
                            {{ item.value }}
                        </p>
                    </li>
                </ul>
            </dd>
        </dl>
    </div>
</template>

<script>
import {
    getZdStationSzxx,
    getZdStationSzxx2
} from '@/api/gis/3D/WaterMap/index';
export default {
    props: ['selectItem', 'layerID'],
    data() {
        return {
            arrYZ: [],
            arrYZ2: [],
            jcsj: '',
            height: 280
        };
    },
    mounted() {
        setTimeout(() => {
            this.getData();
        }, 200);
    },
    methods: {
        getData() {
            this.jcsj = this.selectItem.JCSJ
                ? this.selectItem.JCSJ.substring(0, 13)
                : '';
            let param = {
                pointCode: this.selectItem.CDDM
            };

            if (this.selectItem.DMSX == '重金属') {
                getZdStationSzxx2(param).then((res) => {
                    if (res.data) {
                        // for (let obj of res.data) {
                        //     for (let item of this.arrYZ2) {
                        //         if (obj.FXXMMC == item.name) {
                        //             item.value = obj.BCJGBS;
                        //             item.SZLB = '-';
                        //             item.SZLBBS = 1;
                        //             item.bgCls = 'water' + item.SZLBBS;
                        //             item.colorCls = 'water-color' + item.SZLBBS;
                        //             item.borderCls =
                        //                 'water-border' + item.SZLBBS;
                        //         }
                        //     }
                        // }

                        let arrTemp = res.data.filter((item) => {
                            return !!item.BCJGBS;
                        });

                        this.arrYZ2 = arrTemp.map((obj) => {
                            let szlbbs = '1';
                            if (!this.jcsj && obj.JCSJ) {
                                this.jcsj = obj.JCSJ.substring(0, 13);
                            }
                            return {
                                name: obj.FXXMMC,
                                value: obj.BCJGBS,
                                SZLB: '-',
                                SZLBBS: szlbbs,
                                bgCls: 'water' + szlbbs,
                                colorCls: 'water-color' + szlbbs,
                                borderCls: 'water-border' + szlbbs
                            };
                        });

                        // if (this.arrYZ2.length > 6) {
                        //     this.arrYZ2 = this.arrYZ2.slice(0, 6);
                        // }

                        if (this.arrYZ2.length > 10) {
                            this.height =
                                280 +
                                Math.ceil((this.arrYZ2.length - 10) / 5) * 60;
                        } else {
                            this.height = 280;
                        }
                    }
                });
            } else {
                getZdStationSzxx(param).then((res) => {
                    if (res.data) {
                        let arrTemp = res.data.filter((item) => {
                            return !!item.BCJGBS;
                        });

                        arrTemp.sort((a, b) => {
                            return a.SZLBBS ? -1 : 1;
                        });

                        this.arrYZ = arrTemp.map((obj) => {
                            if (!this.jcsj && obj.JCSJ) {
                                this.jcsj = obj.JCSJ.substring(0, 13);
                            }
                            let szlbbs = obj.SZLBBS || '1';
                            return {
                                name: obj.FXXMMC,
                                value: obj.BCJGBS,
                                SZLB: obj.SZLB || '-',
                                SZLBBS: szlbbs,
                                bgCls: 'water' + szlbbs,
                                colorCls: 'water-color' + szlbbs,
                                borderCls: 'water-border' + szlbbs
                            };
                        });

                        // if (this.arrYZ.length > 6) {
                        //     this.arrYZ = this.arrYZ.slice(0, 6);
                        // }

                        if (this.arrYZ.length > 10) {
                            this.height =
                                280 +
                                Math.ceil((this.arrYZ.length - 10) / 5) * 60;
                        } else {
                            this.height = 280;
                        }

                        // console.log('高度' + this.height);
                        // for (let obj of res.data) {
                        //     for (let item of this.arrYZ) {
                        //         if (obj.FXXMMC == item.name) {
                        //             item.value = obj.BCJGBS;
                        //             item.SZLB = obj.SZLB;
                        //             item.SZLBBS = obj.SZLBBS || '0';
                        //             item.bgCls = 'water' + item.SZLBBS;
                        //             item.colorCls = 'water-color' + item.SZLBBS;
                        //             item.borderCls =
                        //                 'water-border' + item.SZLBBS;
                        //         }
                        //     }
                        // }
                    }
                });
            }
        },

        closeClick() {
            this.$emit('close', '自动站');
        }
    },
    computed: {
        bgCls() {
            let obj = {};
            obj['water' + this.selectItem.SZLBBS] = true;
            return obj;
        }
    }
};
</script>

<style scoped>
.zjs {
    height: 230px !important;
}

.small {
    font-size: 13px;
}
/* .sw0630-dlbx2 ul li + li {
    margin-right: 10px;
} */
</style>
