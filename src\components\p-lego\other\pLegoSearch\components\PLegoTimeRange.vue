<!-- @format -->
<!-- 时间范围选择 -->

<template>
    <div class="search-type">
        <div class="title">{{ title }}</div>
        <el-date-picker
            style="flex: 1"
            v-model="time"
            :type="type"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="clearable"
            :format="format"
            :value-format="valueFormat"
            @change="$emit('timeRangeChange', time)"
        >
        </el-date-picker>
    </div>
</template>

<script>
export default {
    name: '',
    props: {
        title: {
            type: String,
            default: () => {
                return '';
            }
        },
        defaultVal: {
            type: Array,
            default: () => {
                return [];
            }
        },
        type: {
            type: String,
            default: () => {
                return 'datetimerange';
            }
        }, //可选值：datetimerange、daterange、monthrange

        clearable: {
            type: Boolean,
            default: () => {
                return true;
            }
        },
        format: {
            type: String,
            default: () => {
                return 'YYYY-MM-DD HH:mm:ss';
            }
        },
        valueFormat: {
            type: String,
            default: () => {
                return 'YYYY-MM-DD HH:mm:ss';
            }
        }
    },
    data() {
        return {
            time: []
        };
    },
    mounted() {
        this.time = this.defaultVal;
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
.search-type {
    margin-right: 20px;
    color: var(--font-color);
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
}
</style>
