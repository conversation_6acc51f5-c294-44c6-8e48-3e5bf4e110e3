<!-- @format -->

<template>
    <div
        class="sw1212-wrap"
        style="background: #fff; width: 100%; height: 100%"
    >
        <div>
            <div style="padding: 20px; top: 0">
                <div>
                    <div style="display: flex; align-items: center">
                        <p style="font-size: 16px">监测类型：</p>
                        <el-select
                            v-model="value"
                            placeholder="请选择"
                            @change="typeChange"
                        >
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                        <p style="font-size: 16px; margin-left: 10px">时间：</p>
                        <el-date-picker
                            @change="timeChange"
                            v-model="time"
                            type="year"
                            placeholder="选择年"
                            style="height: 35px"
                            value-format="YYYY"
                        >
                        </el-date-picker>
                        <p style="font-size: 16px; margin-left: 10px">地市：</p>
                        <el-select
                            v-model="SSDS"
                            placeholder="请选择"
                            @change="SSDSChange"
                        >
                            <el-option
                                v-for="item in SSDSOptions"
                                :key="item.XZQHDM"
                                :label="item.XZQH"
                                :value="item.XZQHDM"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div class="gap"></div>
                    <el-table
                        :data="listData"
                        style="width: 100%; height: 500px"
                    >
                        <el-table-column
                            type="index"
                            width="100"
                            label="序号"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="XZQH"
                            label="区域"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="JCRS"
                            label="监测人数"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="JCCS"
                            label="监测次数"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="CYRS"
                            label="从业人数"
                            align="center"
                        >
                        </el-table-column>
                    </el-table>
                    <div class="gap"></div>
                    <p-line
                        v-if="showChart"
                        :data="lineData"
                        :config="{
                            color: ['#20cb44'],
                            showFillArea: true,
                            smooth: false
                        }"
                        style="width: 100%; height: 400px"
                    ></p-line>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    jcrsStatistics,
    jcryQsStatistics,
    queryAdministrativeRegionByCondition
} from '@/api/knowledge.js';

export default {
    name: 'Annual',
    components: {},
    provide() {
        return {};
    },
    data() {
        return {
            options: [
                {
                    value: '1',
                    label: '常规剂量数据采集'
                },
                {
                    value: '2',
                    label: '特殊剂量数据采集'
                }
            ],
            value: '1',
            time: this.$dayjs().format('YYYY'),
            timer: null,
            listData: [],
            lineData: {},
            showChart: true,
            SSDS: '',
            SSDSOptions: [],
            XZQHDM: ''
        };
    },
    created() {
        this.getSSDSOptions();
    },
    computed: {},
    mounted() {
        window.addEventListener('resize', this.refreshChart);

        this.getData();

        this.$nextTick(() => {
            this.refreshChart();
        });
    },
    methods: {
        SSDSChange(e) {
            this.XZQHDM = e;
            this.getData();
        },

        getSSDSOptions() {
            queryAdministrativeRegionByCondition({
                LEVEL: '2',
                FDM: '440000',
                XZJB: '2'
            }).then((res) => {
                this.SSDSOptions = res;
                this.SSDSOptions.unshift({
                    XZQH: '全部',
                    XZQHDM: ''
                });
            });
        },

        getData() {
            jcrsStatistics({
                ND: this.time,
                JCLX: this.value,
                XZQH: this.XZQHDM
            }).then((res) => {
                this.listData = res.data_json;
            });
            jcryQsStatistics({
                ND: this.time,
                JCLX: this.value,
                XZQH: this.XZQHDM
            }).then((res) => {
                this.lineData = {
                    xAxis: [],
                    series: [
                        {
                            name: '监测人员趋势',
                            data: []
                        }
                    ]
                };
                if (res.data_json.length) {
                    res.data_json.map((e) => {
                        this.lineData.xAxis.push(e.MONTH);
                        this.lineData.series[0].data.push(e.RS);
                    });
                }
                console.log(this.lineData);
            });
        },

        refreshChart() {
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                this.showChart = false;
                let unsync = setTimeout(() => {
                    this.showChart = true;
                    clearTimeout(unsync);
                }, 0);
            }, 200);
        },
        typeChange(e) {
            this.value = e;
            this.getData();
        },
        timeChange(e) {
            this.time = e;
            this.getData();
        }
    }
};
</script>

<style lang="scss" scoped></style>
