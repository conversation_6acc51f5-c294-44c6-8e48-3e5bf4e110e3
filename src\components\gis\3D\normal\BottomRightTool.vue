<!-- @format -->

<template>
    <div>
        <!-- 图层切换 -->
        <MapSwitchTool
            :map="map"
            @baseMapChange="baseMapChangeHandle"
        ></MapSwitchTool>
    </div>
</template>

<script>
import MapSwitchTool from '../MapSwitchTool';

export default {
    name: 'BottomRightTool',
    props: ['map'],
    data() {
        return {};
    },
    components: { MapSwitchTool },
    computed: {},
    mounted() {},
    methods: {
        baseMapChangeHandle() {}
    },
    watch: {}
};
</script>

<style scoped>
.MapSwitchTool-small {
    bottom: 70px;
}

.right-small {
    margin-right: -70px;
}
</style>
