.sw0715-typetxt1 {
  text-align: center;
  height: 30px;
  line-height: 30px;
  border-radius: 300px;
  border: 1px solid #0069ff;
  font-size: 14px;
  color: #0069ff;
  padding: 0 14px;
  cursor: pointer;
}
.sw0715-typetxt1.on {
  background: #0069ff;
  color: #fff;
}

.sw0715-dlbx1 dt {
  padding: 20px 0 10px;
}
.sw0715-dlbx1 dt strong {
  font-size: 16px;
  color: #3d3d3d;
}
.sw0715-dlbx1 dd {
  padding: 16px 0;
}
.sw0715-dlbx1 dd span {
  font-size: 16px;
  color: #3d3d3d;
}
.sw0715-dlbx1 dd + dd {
  border-top: 1px solid #eaeaea;
}

.sw0715-ictxt1 {
  font-size: 16px;
  color: #3d3d3d;
  background: url(../images/sw0715_ic2.png) no-repeat left center;
  padding-left: 28px;
}

.sw0715-ulbtn1 {
  display: flex;
  align-items: center;
}
.sw0715-ulbtn1 li {
  font-size: 14px;
  color: #fff;
  padding: 0 26px;
  height: 30px;
  line-height: 30px;
  background: #5098ff;
  border-radius: 300px;
  cursor: pointer;
}
.sw0715-ulbtn1 li.on {
  background: #0069ff;
}
.sw0715-ulbtn1 li + li {
  margin-left: 20px;
}

.sw0715-txt1 {
  font-size: 16px;
  color: #3d3d3d;
}

.sw0715-ulbtn2 {
  margin-left: auto;
  display: flex;
  align-items: center;
}
.sw0715-ulbtn2 li {
  font-size: 16px;
  color: #666;
  cursor: pointer;
}
.sw0715-ulbtn2 li.li1 {
  background: url(../images/sw0715_arwic1.png) no-repeat right center;
  padding-right: 21px;
}
.sw0715-ulbtn2 li.li2 {
  background: url(../images/sw0715_ic3.png) no-repeat right center;
  padding-right: 24px;
  color: #0069ff;
}
.sw0715-ulbtn2 li + li {
  margin-left: 55px;
}

.sw0715-txtarea1 {
  width: 100%;
  height: 125px;
  background: #f8faff;
  border-radius: 10px;
}