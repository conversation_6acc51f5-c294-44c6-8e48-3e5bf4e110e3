<!-- @format -->

<template>
    <div ref="wcqk" class="chart"></div>
</template>
<script>
import * as echarts from 'echarts';

export default {
    props: {
        data: {
            type: Object,
            default: () => {
                return {
                    BFB: '20%'
                };
            }
        }
    },
    data() {
        return {};
    },

    mounted() {
        this.setCharts();
    },

    methods: {
        setCharts() {
            let that = this;
            let myChart = echarts.init(this.$refs.wcqk);

            let option = {
                series: [
                    {
                        name: '背景圈',
                        type: 'gauge',
                        radius: '150%', // 设置圆环的大小，内环与外环保持一致
                        center: ['50%', '90%'], // 设置圆心的位置
                        startAngle: 180, // 开始角度
                        endAngle: 0, // 结束角度

                        axisLine: {
                            // 坐标轴线
                            roundCap: true,
                            lineStyle: {
                                // 属性lineStyle控制线条样式
                                color: [[1, '#EEEEEE']],
                                width: 17
                            }
                        },
                        splitLine: {
                            //分隔线样式
                            show: false
                        },
                        axisLabel: {
                            //刻度标签
                            show: false
                        },
                        pointer: {
                            show: false
                        },
                        axisTick: {
                            //刻度样式
                            show: false
                        }
                    },

                    // 最外层含中间数据
                    {
                        type: 'gauge',
                        radius: '150%',
                        startAngle: 180,
                        endAngle: 0,
                        center: ['50%', '90%'],

                        axisLine: {
                            show: true,
                            roundCap: true,
                            lineStyle: {
                                width: 17,
                                color: [
                                    [
                                        Number(that.data.BFB.replace('%', '')) /
                                            100, // 内圈的进度，如果传入的 BFB 是 '20%'这样的数据就使用 replace替换'%'
                                        new echarts.graphic.LinearGradient(
                                            0,
                                            1,
                                            1,
                                            0,
                                            [
                                                {
                                                    offset: 0,
                                                    color:
                                                        window.localStorage
                                                            .themeType ===
                                                        'dark'
                                                            ? 'rgba(110, 191, 251,1)'
                                                            : 'rgba(42, 217, 255,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color:
                                                        window.localStorage
                                                            .themeType ===
                                                        'dark'
                                                            ? 'rgba(110, 191, 251,0.3)'
                                                            : 'rgba(42, 217, 255,0.3)'
                                                }
                                            ]
                                        ) // 内圈进度的渐变色
                                    ]
                                ]
                            }
                        },
                        axisTick: {
                            show: 0
                        },
                        splitLine: {
                            show: 0
                        },
                        axisLabel: {
                            show: 0
                        },
                        pointer: {
                            show: false
                        },
                        detail: {
                            show: true,
                            offsetCenter: [0, -20],
                            valueAnimation: true, // 动画效果
                            fontSize: 32,
                            color:
                                window.localStorage.themeType === 'dark'
                                    ? '#fff'
                                    : '#000',
                            fontWeight: '700',
                            formatter: function (params) {
                                return `${params}%`;
                            } // 显示的数据格式
                        },

                        data: [
                            {
                                value: Number(that.data.BFB.replace('%', '')) // 显示的数据，如果传入的 BFB 是 '20%'这样的数据就使用 replace替换'%'
                            }
                        ]
                    }
                ]
            };
            myChart.setOption(option);
        }
    }
};
</script>
<style scoped>
.chart {
    width: 100%;
    height: 100%;
}
</style>
