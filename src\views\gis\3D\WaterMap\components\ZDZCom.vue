<!-- @format -->

<template>
    <div class="sw0811-dlbx5">
        <p class="yy0812-tctit">自动监测</p>

        <ZDZJGJBCom
            :tjData="tjData"
            ref="childJGJB"
            @jgjbChange="jgjbChangeHandle"
        ></ZDZJGJBCom>

        <ZDZZDLXCom
            :tjData="tjData"
            ref="childZDLX"
            @jgjbChange="jgjbChangeHandle"
        ></ZDZZDLXCom>

        <STLXCom :tjData="tjData" @stlxChange="stlxChangeHandle"></STLXCom>
    </div>
</template>

<script>
import ZDZJGJBCom from './ZDZJGJBCom';
import ZDZZDLXCom from './ZDZZDLXCom';
import STLXCom from './STLXCom';
import PointUtil from '../utils/PointUtil';
import { getZdjcStatiion, getZDZPointCount } from '@/api/gis/3D/WaterMap';
export default {
    data() {
        return {
            arrResult: null,
            layerName: '自动站',
            map: null,
            tjData: null,
            style: '1',
            arrSTLX: ['河流', '湖库'] //水体类型
        };
    },
    props: ['showName'],
    components: { ZDZJGJBCom, ZDZZDLXCom, STLXCom },
    inject: ['pointClickHandle', 'refreshSearchData'],
    unmounted() {
        this.clear();
    },

    mounted() {
        this.getPointCount();
        this.initPage();
    },
    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.getData();
        },

        getPointCount() {
            let param = {};

            getZDZPointCount(param).then((res) => {
                this.tjData = res.data;
            });
        },

        getData() {
            if (this.arrResult) {
                this.addPointToMap();
                return;
            }
            getZdjcStatiion({}).then((res) => {
                this.arrResult = res.data.map((item) => {
                    item.SZLBBS = PowerGL.getWaterDJByLevel(item.SZLB);

                    // if (item.SZZBBS == 0 && item.SFZX == 1) {
                    //     item.SZLBBS = 1;
                    // }

                    return item;
                });

                this.$emit('dataChange', this.arrResult);

                this.addPointToMap();
            });
        },

        //地图上绘制点位
        addPointToMap() {
            if (!this.arrResult) {
                return;
            }

            this.clear();

            let arrTemp = this.filerData();
            if (this.style == '0') {
                this.pointUtil.addImgPoint(arrTemp, { id: this.layerName });
            } else {
                let params = {
                    id: this.layerName,
                    showAno: this.showName,
                    style: this.style, //样式
                    disablePopup: false,
                    disableClick: false
                };

                this.pointUtil.addCustomHtmlLayer(arrTemp, params);
            }

            this.setSearchData(this.layerName, arrTemp);
        },

        //设置搜索框数据
        setSearchData(layerId, arr) {
            let data = {};
            data[layerId] = arr;
            this.refreshSearchData(data);
        },

        clear() {
            if (this.pointUtil) {
                this.pointUtil.removeLayerByName(this.layerName);
                this.pointUtil.clear(this.layerName);
            }
        },

        jgjbChangeHandle() {
            if (this.map && this.pointUtil) {
                this.addPointToMap();
            }
        },

        filerData() {
            let jgjbArr = this.$refs.childJGJB.jgjbArr;
            let zdlxArr = this.$refs.childZDLX.zdlxArr;

            let arrTemp = this.arrResult.filter((item) => {
                return (
                    jgjbArr.includes(item.JGJB) &&
                    this.arrSTLX.includes(item.JCFLMC) &&
                    zdlxArr.includes(item.DMSX)
                );
            });

            return arrTemp;
        },

        stlxChangeHandle(data) {
            this.arrSTLX = data;
        },
        test(style) {
            this.style = style;

            this.addPointToMap();
        }
    },
    watch: {
        arrSTLX(val) {
            console.log('水体类型改变-自动');
            this.addPointToMap();
        },
        showName(val) {
            this.addPointToMap();
        }
    }
};
</script>

<style></style>
