/** @format */

const WEEK_DAY_CURSOR = [0, 1, 2, 3, 4, 5, 6];
const MS_OF_DAY = 24 * 60 * 60 * 1000;

/**
 * 计算日期加上指定天数
 */
function add(date, num) {
    let msOfDate = date.getTime();
    let addedDateInMilliSec = msOfDate + MS_OF_DAY * num;
    return new Date(addedDateInMilliSec);
}

/**
 * 计算日期减去指定天数
 */
function subtract(date, num) {
    let msOfDate = date.getTime();
    let subtractDateInMilliSec = msOfDate - MS_OF_DAY * num;
    return new Date(subtractDateInMilliSec);
}

/**
 * 格式化日期
 */
function format(date) {
    return `${date.getFullYear()}-${doubleBitNumStr(
        date.getMonth() + 1
    )}-${doubleBitNumStr(date.getDate())}`;
}

/**
 * 补全小于10的数前面的零
 */
function doubleBitNumStr(num) {
    return num < 10 ? `0${num}` : `${num}`;
}

/**
 * 获取一个月在日历上对齐星期的所有日期，因为日历上需要对齐星期，需要做日期对齐补全
 */
export const getMonthDays = (year, month) => {
    let monthDays = [];

    //月份第一天
    let startDay = new Date(year, month - 1, 1);
    //月份最后一天
    let endDay = new Date(year, month, 0);

    //第一天的星期
    let startDayWeek = startDay.getDay();
    //最后一天的星期
    let endDayWeek = endDay.getDay();

    //对齐星期需要补的天数
    let alignStartDays = startDayWeek;
    let alignEndDays = 6 - endDayWeek;

    //对齐星期的第一天和最后一天
    let alignStartDay = subtract(startDay, alignStartDays);
    let alignEndDay = add(endDay, alignEndDays);

    //遍历获取所有日期
    let alignEndDayStr = format(alignEndDay);
    let currentDayStr = format(alignStartDay);
    let tempDay = alignStartDay;
    while (currentDayStr !== alignEndDayStr) {
        monthDays.push(currentDayStr);
        tempDay = add(tempDay, 1);
        currentDayStr = format(tempDay);
    }
    monthDays.push(alignEndDayStr);
    return monthDays;
};
