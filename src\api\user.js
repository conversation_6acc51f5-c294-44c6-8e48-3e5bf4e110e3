/** @format */

import axios from '_u/ajaxRequest';

// 放置接口
// export const getUser = ()=>{
//     return axios.request({
//         url:'/api4f/DrinkWaterController/drinkWaterPoints',
//         method:'get'
//     });
// };
// 编辑用户
export const editUser = (id, data) => {
    return axios.request({
        method: 'put',
        url: '/users/' + id,
        data: data
    });
};

//新增用户
export const addUser = (data) => {
    return axios.request({
        method: 'post',
        url: '/users',
        data: data
    });
};
//删除用户
export const delUser = (id) => {
    return axios.request({
        method: 'delete',
        url: '/users/' + id
    });
};
//获取用户
export const getUsers = (data) => {
    return axios.request({
        url: '/users',
        method: 'get',
        params: data
    });
};

export const getUser = () => {
    return axios.request({
        url: '/user',
        method: 'get'
    });
};

export const login = (username) => {
    return axios.request({
        method: 'post',
        url: '/login',
        data: {
            username
        }
    });
};

export const validate = () => {
    return axios.request({
        method: 'get',
        url: '/validate'
    });
};

export const getMenu = () => {
    return axios.request({
        method: 'get',
        url: 'http://localhost:8080/menu.json'
    });
};

// 空气日历
export const getAqiCalendar = (data) => {
    return axios.request({
        method: 'get',
        url: 'http://**************:31665/mock/92/bigScreen/air/aqiCalendar',
        params: data
    });
};
