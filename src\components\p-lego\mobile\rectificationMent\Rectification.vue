<!-- @format -->

<!--
 * @Author: your name
 * @Date: 2022-04-14 20:24:18
 * @LastEditTime: 2022-05-05 10:42:14
 * @LastEditors: 姚进玺 <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /Front_PC_COMPONENTS/src/components/business/mobile/rectificationMent/rectification.vue
-->
<!-- @format -->

<template>
    <div class="rectification">
        <p class="zy-til2 mt-20 mb-20">{{ title || '组件标题' }}</p>
        <div class="zy-line jb" style="padding: 0 30px">
            <dl class="pd-dlbx3a" v-for="(item, index) in data" :key="index">
                <dt>
                    <img src="./images/char1a.png" alt="" />
                    <h1>
                        <i>{{ item.value }}</i>
                    </h1>
                    <p>{{ item.unit }}</p>
                </dt>
                <dd>{{ item.name }}</dd>
            </dl>
        </div>
    </div>
</template>

<script>
export default {
    components: {},
    props: {
        data: {
            type: Array,
            default: null
        }
    }
};
</script>

<style scoped lang="less" >
.rectification {
    .mt-20 {
        margin-top: 20px;
    }

    .mb-20 {
        margin-bottom: 20px;
    }

    .rectification {
        overflow: hidden;
    }
    
    .zy-til2 {
        font-size: 18px;
        color: #1ae1ff;
        line-height: 18px;
        padding-left: 32px;
        padding-bottom: 6px;
        background: url(./images/til-bg2.png) left bottom no-repeat;
        font-weight: bold;
    }

    .zy-line {
        display: flex;
        &.ac {
            align-items: center;
        }
        &.jb {
            justify-content: space-between;
        }

        &.jc {
            justify-content: center;
        }
    }

    .pd-dlbx3a {
        width: 133px;
        text-align: center;
        dt {
            height: 133px;
            position: relative;
            img {
                position: absolute;
                left: 0;
                top: 0;
            }

            h1 {
                font-size: 24px;
                color: #fff;
                font-family: 'DIN-Medium';
                position: relative;
                padding-top: 40px;
                i {
                    color: #0fc54b;
                }
            }

            p {
                font-size: 14px;
                color: #fff;
                position: relative;
                padding-top: 5px;
            }
        }

        dd {
            font-size: 14px;
            color: #fff;
            padding-top: 10px;
        }
    }

}

</style>
