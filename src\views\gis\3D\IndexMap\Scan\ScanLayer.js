/** @format */

import {
  createShader,
  createProgram,
  createTexture,
  bindTexture,
  createBuffer,
  bindAttribute,
  bindAttribute2,
  bindFramebuffer
} from './ShaderUtil';
import { glMatrix, mat4, vec3, vec4 } from 'gl-matrix';
export default function LayerTemplate(option) {
  this.id = option.id;
  this.type = 'custom';
  this.renderingMode = '3d';
  this.position = option.position;
  this.radius = option.radius || 10000; //meter
  this.color = option.color || [1.0, 0.0, 0.0, 0];
  this.opacity = option.opacity || 1.0;
  this.timeGap = option.timeGap || 1;
  this.time = 0;
  this.map = null;
  this.gl = null;
  this.scanWrapper = null;
  this.flashBuffer = null;
  /* eslint-disable */
  this.vertices = [//x,y,z,u,v
    -0.5, -0.5, 0, 0, 0, 0.5, -0.5, 0, 1, 0, -0.5, 0.5, 0, 0, 1,
    -0.5, 0.5, 0, 0, 1, 0.5, -0.5, 0, 1, 0, 0.5, 0.5, 0, 1, 1
  ];
  /* eslint-enable */
}

LayerTemplate.prototype = {
  constructor: LayerTemplate,
  // 初始化绘制程序
  init() {
    let rectFrag = `
        precision highp float; 
        
        uniform vec4 u_color;
        uniform float u_opacity;
        
        float pi = 3.1415926;
        vec2 center = vec2(0.0, 0.0);
        vec2 zeroRadian = vec2(1.0, 0.0);
        
        varying vec3 v_pos;
        varying float v_range;
        varying float v_arrRadian[2];
        
        float getRadian(vec2 xy, vec2 zeroRadian);
        float getOpacity(float radian, float arrRadian[2], float range);
        
        void main(){ 
          float radius = length(vec2(v_pos.xy));
          if(radius > 0.5){
            discard;
          }
          float radian = getRadian(vec2(v_pos.xy), zeroRadian);
          if(v_arrRadian[0] > v_arrRadian[1]){
            if(radian < v_arrRadian[0] && radian>v_arrRadian[1]){
              discard;
            }
          }else if(radian < v_arrRadian[0] || radian>v_arrRadian[1]){
              discard;
          }
          float opacity = getOpacity(radian, v_arrRadian, v_range);
          gl_FragColor = vec4(u_color.rgb, opacity * u_opacity);
        }
        // 当前点的弧度
        float getRadian(vec2 xy, vec2 zeroRadian){
          vec2 zero = normalize(zeroRadian);
          vec2 unitVec2 = normalize(xy);
          float cosRadian = dot(zero, unitVec2);
          float radian = acos(cosRadian);
          if(xy.y<0.0){
            radian = 2.0 * pi - radian;
          }
          return radian;
        }
        // 不透明度
        float getOpacity(float radian, float arrRadian[2], float range){
          float opacity = 0.0;
          if(arrRadian[1] < arrRadian[0]){
            if(radian>arrRadian[0] && radian <=2.0*pi){
              opacity = (radian-arrRadian[0])/range;
            }else if(radian<arrRadian[1] && radian >=0.0){
              opacity = 1.0-(arrRadian[1] - radian)/range;
            }
          }else if(radian>=arrRadian[0] && radian<arrRadian[1]){
            opacity = (radian-arrRadian[0])/range;
          }
          return opacity;
        }`;
    let rectVert = `
        precision mediump float;
        
        attribute vec3 a_pos;
        
        uniform float u_time;
        uniform mat4 u_matrix;
        uniform mat4 u_modelMatrix;
        
        float pi = 3.1415926;
        float range = 0.7;
        
        varying vec3 v_pos;
        varying float v_range;
        varying float v_arrRadian[2];
        
        void main(){
            v_pos = a_pos;
            v_range = range;
            v_arrRadian[0] = fract(u_time/360.0)*2.0*pi;
            v_arrRadian[1] = v_arrRadian[0] + range;
            if(v_arrRadian[1]>2.0*pi){
                v_arrRadian[1] = v_arrRadian[1] - 2.0*pi;
            }
            gl_Position = u_matrix * u_modelMatrix * vec4(a_pos, 1.0);
        }`;
    this.scanWrapper = createProgram(this.gl, rectVert, rectFrag);
    this.flashBuffer = createBuffer(
      this.gl,
      new Float32Array(this.vertices)
    );
  },
  // 地图事件绑定与移除
  toggleEvent(removeEvent) {
    if (removeEvent) {
      this.map.off('zoom', this.handleZoom);
    } else {
      this.map.on('zoom', this.handleZoom);
    }
  },
  // 地图缩放事件
  handleZoom(e) {
    let zoom = e.target.getZoom();
    console.log(zoom);
  },
  // 像素尺寸对应到地图中真实大小
  getScale(mercatorCoordinate) {
    let units = mercatorCoordinate.meterInMercatorCoordinateUnits();
    let pixelsPerMeter = this.map.transform.pixelsPerMeter;
    let scale = this.radius * units;
    return scale;
  },
  // 添加图层
  onAdd: function (map, gl) {
    this.map = map;
    this.gl = gl;
    this.init(gl);
    // this.toggleEvent();
  },
  // 移除图层
  onRemove: function () {
    this.toggleEvent(true);
  },
  // 渲染图层
  render: function (gl, matrix) {
    gl.disable(gl.DEPTH_TEST);
    this.drawScan(gl, matrix);
    this.map.triggerRepaint();
  },
  // 绘制扫描
  drawScan(gl, matrix) {
    let wrapper = this.scanWrapper;
    let buffer = this.flashBuffer;
    this.time += this.timeGap;
    this.time %= 360;
    gl.useProgram(wrapper.program);
    gl.uniform1f(wrapper.u_time, this.time);
    gl.uniform1f(wrapper.u_opacity, this.opacity);
    let temp = [wrapper.u_color, ...this.color];
    gl.uniform4f.apply(gl, temp);
    gl.uniformMatrix4fv(wrapper.u_matrix, false, matrix);
    // 模型处理
    let modelMatrix = mat4.create();
    mat4.fromTranslation(
      modelMatrix,
      vec4.fromValues(this.position.x, this.position.y, 0, 1.0)
    );
    let scale = this.getScale(this.position);
    mat4.scale(modelMatrix, modelMatrix, vec3.fromValues(scale, scale, 1));
    gl.uniformMatrix4fv(wrapper.u_modelMatrix, false, modelMatrix);
    bindAttribute2(
      gl,
      buffer,
      wrapper.a_pos,
      3,
      5,
      0,
      Float32Array.BYTES_PER_ELEMENT
    );
    gl.enable(gl.BLEND);

    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
    gl.drawArrays(gl.TRIANGLES, 0, 18);
  }
};
