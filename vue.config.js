/** @format */
let path = require('path');
const { defineConfig } = require('@vue/cli-service');

let ElementPlus = require('unplugin-element-plus/webpack');

module.exports = defineConfig({
    transpileDependencies: true,
    lintOnSave: false,
    publicPath: process.env.NODE_ENV === 'production' ? '' : '/',
    assetsDir: 'asserts',
    //outputDir: 'D:/newSpace/YN3/src/main/webapp/frontal',
    outputDir: './dist',
    // 使用模版方式 一般不使用
    runtimeCompiler: false,
    // 打包 不再使用sourcemap
    productionSourceMap: false,

    chainWebpack: (config) => {
        // 移除 prefetch 插件
        config.plugins.delete('prefetch');

        // 可以获取到webpack的配置 在增加一些自己的逻辑
        // 配置目录别名 别名叫+
        config.resolve.alias.set(
            '_c',
            path.resolve(__dirname, 'src/components')
        );
        config.resolve.alias.set('_v', path.resolve(__dirname, 'src/views'));
        config.resolve.alias.set('_s', path.resolve(__dirname, 'src/store'));
        config.resolve.alias.set('_a', path.resolve(__dirname, 'src/api'));
        config.resolve.alias.set('_u', path.resolve(__dirname, 'src/utils'));
        config.resolve.alias.set('_as', path.resolve(__dirname, 'src/assets'));
        config.resolve.alias.set(
            '_const',
            path.resolve(__dirname, 'src/constants')
        );
        //
        config.splitChunks = {
            // include all types of chunks
            chunks: 'all',
            // 重复打包问题
            cacheGroups: {
                vendors: {
                    // node_modules里的代码
                    test: /[\\/]node_modules[\\/]/,
                    chunks: 'all',
                    // name: 'vendors', 一定不要定义固定的name
                    priority: 10, // 优先级
                    enforce: true
                }
            }
        };
    },
    configureWebpack: {
        cache: {
            type: 'filesystem' // 使用文件缓存
        },
        // webpack-merge
        plugins: [
            ElementPlus({
                useSource: true
            })
        ],

        module: {}
    },
    devServer: {
        // contentBase: './dist',
        // 启动压缩
        compress: true,
        // 端口号
        port: 8099,
        // 自动打开浏览器
        // open: true,
        // 开启HMR功能
        hot: true,
        // 开发 服务时使用
        proxy: {
            '/mock/1211': {
                target: process.env.VUE_APP_BASE_URL,
                changeOrigin: true,
                headers: {
                    // 与target一致
                    Referer: process.env.VUE_APP_BASE_URL
                },
                pathRewrite: {
                    //'/api':''
                }
            },
            '/stfs_gd': {
                // target: 'http://localhost:8077',
                target: 'http://************:8077',
                // target: 'https://m.gdeei.cn',
                changeOrigin: true,
                headers: {
                    // 与target一致
                    // Referer: 'http://localhost:8077/'
                    Referer: 'http://************:8077'
                    // Referer: 'https://m.gdeei.cn'
                },
                pathRewrite: {}
            },
            '/onlinePreview': {
                target: 'http://*************:32008',
                changeOrigin: true,
                headers: {
                    // 与target一致
                    Referer: 'http://*************:32008'
                },
                pathRewrite: {}
            },
            '/js': {
                target: 'http://*************:32008',
                changeOrigin: true,
                headers: {
                    // 与target一致
                    Referer: 'http://*************:32008'
                },
                pathRewrite: {}
            },
            '/images': {
                target: 'http://*************:32008',
                changeOrigin: true,
                headers: {
                    // 与target一致
                    Referer: 'http://*************:32008'
                },
                pathRewrite: {}
            }
        }
    },
    pluginOptions: {
        css: {
            loaderOptions: {
                scss: {
                    additionalData: `@use "src/assets/css/dark/index.scss" as *;`
                }
            }
        }
    }
});
