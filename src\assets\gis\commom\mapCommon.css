/* 地图上展示一些常用的效果*/


.sysmbol_Flashing {
    position: absolute;
    z-index: 101;
    background-color: rgba(255, 255, 255, 0);
}

.swfcontainer {
    position: relative;
    width: 45px;
    height: 45px;
}

@keyframes warn {
    0% {
        transform: scale(0.3);
        -webkit-transform: scale(0.3);
        opacity: 0.0;
    }
    25% {
        transform: scale(0.3);
        -webkit-transform: scale(0.3);
        opacity: 0.1;
    }
    50% {
        transform: scale(0.5);
        -webkit-transform: scale(0.5);
        opacity: 0.3;
    }
    75% {
        transform: scale(0.8);
        -webkit-transform: scale(0.8);
        opacity: 0.5;
    }
    100% {
        transform: scale(1);
        -webkit-transform: scale(1);
        opacity: 0.0;
    }
}

@keyframes warn1 {
    0% {
        transform: scale(0.3);
        -webkit-transform: scale(0.3);
        opacity: 0.0;
    }
    25% {
        transform: scale(0.3);
        -webkit-transform: scale(0.3);
        opacity: 0.1;
    }
    50% {
        transform: scale(0.3);
        -webkit-transform: scale(0.3);
        opacity: 0.3;
    }
    75% {
        transform: scale(0.5);
        -webkit-transform: scale(0.5);
        opacity: 0.5;
    }
    100% {
        transform: scale(0.8);
        -webkit-transform: scale(0.8);
        opacity: 0.0;
    }
}


/* 产生动画（向外扩散变大）的圆圈  */

.pulse {
    position: absolute;
    width: 45px;
    height: 45px;
    left: 0px;
    top: 0px;
    border: 8px solid red;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    z-index: 1;
    opacity: 0;
    -webkit-animation: warn 2s ease-out;
    -moz-animation: warn 2s ease-out;
    animation: warn 2s ease-out;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    box-shadow: 1px 1px 30px red;
}

.pulse1 {
    position: absolute;
    width: 45px;
    height: 45px;
    left: 0px;
    top: 0px;
    border: 8px solid red;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    z-index: 1;
    opacity: 0;
    -webkit-animation: warn1 2s ease-out;
    -moz-animation: warn1 2s ease-out;
    animation: warn1 2s ease-out;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    box-shadow: 1px 1px 30px red;
}




#Animation_CQ_layer path {
    stroke-dasharray: 10;
    stroke-width: 2pt;
    animation: dash 50s linear infinite;
    animation-direction: reverse;
    -webkit-animation-direction: reverse;
    -ms-animation: dash 50s linear infinite;
    -ms-animation-direction: reverse;
}

@keyframes dash {
    to {
        stroke-dashoffset: 1000;
    }
}

@-ms-keyframes dash {
    0% {
        stroke-dashoffset: -200;
    }
    100% {
        stroke-dashoffset: 1000;
    }
}


.sysmbol_InfoWindow_pie {
    position: absolute;
    z-index: 100;
    box-shadow: 0 0 1em #26393d;
    font-family: sans-serif;
    font-size: 12px;
    background-color: rgba(255, 255, 255, 0);
    /*** pointer-events: none;  去掉div的事件，为css 样式， IE9 不支持***/
}

.sysmbol_InfoWindow_pie .content {
    position: relative;
    /*  background-color:#EFECCA;*/
    color: #002f2f;
    /*  overflow: hide;*/
    padding: 0px 0px 0px 0px;
    /**上,右，下，左**/
    background-color: rgba(255, 255, 255, 0);
    left: auto;
}



/* 水质类别 */

.szlb-color-0 {
    color: #d1cfcf!important;
}

.szlb-color-1 {
    color: #8FE6FF!important;
}

.szlb-color-2 {
    color: #51A5FD!important;
}

.szlb-color-3 {
    color: #73bb31!important;
}

.szlb-color-4 {
    color: #EEBE16!important;
}

.szlb-color-5 {
    color: #F88D18!important;
}

.szlb-color-6 {
    color: #EE3B5B!important;
}

.szlb-bg-1 {
    color: #FFF!important;
    background: #8FE6FF!important;
}

.szlb-bg-2 {
    color: #FFF!important;
    background: #51A5FD!important;
}

.szlb-bg-3 {
    color: #FFF!important;
    background: #73bb31!important;
}

.szlb-bg-4 {
    color: #FFF!important;
    background: #EEBE16!important;
}

.szlb-bg-5 {
    color: #FFF!important;
    background: #F88D18!important;
}

.szlb-bg-6 {
    color: #FFF!important;
    background: #EE3B5B!important;
}


/* 富营养状态 */

.fyyzt-bg-1 {
    color: #FFF!important;
    background: #2AC2EC!important;
}

.fyyzt-bg-2 {
    color: #FFF!important;
    background: #70BE46!important;
}

.fyyzt-bg-3 {
    color: #FFF!important;
    background: #EAE84C!important;
}

.fyyzt-bg-4 {
    color: #FFF!important;
    background: #EE8D52!important;
}


/* 达标情况 */

.dbqk-bg-cb {
    background-color: #ee3b5b;
    color: #fff!important;
}

.dbqk-bg-db {
    background-color: #73bb31;
    color: #fff!important;
}

.dbqk-color-cb {
    color: #ee3b5b!important;
}

.dbqk-color-db {
    color: #73bb31!important;
}

/* 大气样式 */

/* 无数据 */
.air-back-dj0 {
    background: #cccccc !important;
}

.air-border-dj0 {
    border-color: #e2dede !important;
}

.air-border-dj0  p {
   color: #cccccc !important;
}

/* 优 */

.air-back-dj1 {
    background: #2db62d !important;
}

.air-border-dj1 {
    border-color: #65e765 !important;
}

.air-border-dj1  p {
    color: #2db62d !important;
 }

/* 良 */

.air-back-dj2 {
    background: #cbcc33 !important;
}

.air-border-dj2 {
    border-color: #eeee68 !important;
}

.air-border-dj2  p {
    color: #cbcc33 !important;
 }

/* 轻度 */

.air-back-dj3 {
    background: #ff7e00 !important;
}

.air-border-dj3 {
    border-color: #f1a355 !important;
}

.air-border-dj3  p {
    color: #ff7e00 !important;
 }

/* 中度 */

.air-back-dj4 {
    background: #ff0000 !important;
}

.air-border-dj4 {
    border-color: #e95959 !important;
}

.air-border-dj4  p {
    color: #ff0000 !important;
 }

/* 重度 */
.air-back-dj5 {
    background: #99004c !important;
}

.air-border-dj5 {
    border-color: #ec3591 !important;
}

.air-border-dj5  p {
    color: #99004c !important;
 }

/* 严重 */
.air-back-dj6 {
    background: #7e0023 !important;
}

.air-border-dj6 {
    border-color: #cf2354 !important;
}

.air-border-dj6  p {
    color: #7e0023 !important;
 }


html,body{
    width:100%;
    height:100%; 
    margin: 0;
    padding: 0; 
} 


/**设置滚动条的样式**/

::-webkit-scrollbar {
    width: 5px;
    height: 15px;
}


/**滚动槽**/

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px #d1cfcf;
    border-radius: 10px;
}


/**滚动条滑块**/

::-webkit-scrollbar-thumb {
    border-radius: 12px;
    background: #a8a8a8;
    box-shadow: inset 0 0 6px #d1cfcf;
}

::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(245, 245, 245, 0.4);
}

.fullScreen {
    width: 100%;
    height: 100%;
}


