<!-- @format -->

<template>
  <div>
    <ul class="water-status-qsBackground">
      <li>
        <p class="k1">省控及以上断面达标率</p>
        <p class="k2">{{ data.SKDBL }}<em class="xz">%</em></p>
        <p
          class="k3"
          :class="{
            nodb2: data.CJDMDB == 'WDB',
          }"
        >
          月均值
          {{ data.CJDMDB == "DB" ? "达标" : "未达标" }}
        </p>
        <p class="k4">
          目标：<em class="nff">{{ data.SKDMB }}%</em>
        </p>
        <p class="k5">
          同比：<em class="nff">{{ data.SKTB }}%</em>
          <b
            :class="{
              'r-down': data.SKTB <= 0,
              'g-up': data.SKTB > 0,
            }"
          ></b>
        </p>
        <p class="k5">
          日均：<em class="nff">{{ data.SKRJ }}%</em>
        </p>
      </li>
      <li>
        <p class="k1">省控及以上断面Ⅰ类水占比</p>
        <p class="k2" style="color: #ef4e4a">
          {{ data.YLDBL }}<em class="xz">%</em>
        </p>
        <p
          class="k3"
          :class="{
            nodb2: data.YLDB == 'WDB',
          }"
        >
          月均值{{ data.YLDB == "DB" ? "达标" : "未达标" }}
        </p>
        <p class="k4">
          目标：<em class="nff">{{ data.YLMB }}</em>
        </p>
        <p class="k5">
          同比：<em class="nff">{{ data.YLTB }}</em>
          <b
            :class="{
              'r-down': data.YLTB <= 0,
              'g-up': data.YLTB > 0,
            }"
          ></b>
        </p>
        <p class="k5">
          日均：<em class="nff">{{ data.YLRJ }}%</em>
        </p>
      </li>
      <li>
        <p class="k1">省控及以上断面Ⅱ类水占比</p>
        <p class="k2" style="color: #ef4e4a">
          {{ data.ELDBL }}
          <em class="xz">%</em>
        </p>
        <p class="k3">月均值{{ data.ELDB == "DB" ? "达标" : "未达标" }}</p>
        <p class="k4">
          目标：<em class="nff">{{ data.ELMB }}</em>
        </p>
        <p class="k5">
          同比：<em class="nff">{{ data.ELTB }}%</em>
          <b
            :class="{
              'r-down': data.ELTB <= 0,
              'g-up': data.ELTB > 0,
            }"
          ></b>
        </p>
        <p class="k5">
          日均：<em class="nff">{{ data.ELRJ }}%</em>
        </p>
      </li>
      <li>
        <p class="k1">市级出境断面考核</p>
        <p
          class="k2"
          style="color: #4db727; font-weight: bold; font-size: 26px"
        >
          {{ data.SJCJDM }}
        </p>
        <p class="k3 db">
          {{ data.CJDMDB == "DB" ? "达标" : "未达标" }}
        </p>
        <p class="k4">
          目标：<em class="nff">{{ data.SJCJDMMB }}</em>
        </p>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
};
</script>

<style scoped>
.water-status-qsBackground {
  background-color: rgba(240, 247, 254, 0.9);
  position: relative;
  display: flex;
  justify-content: space-around;
}
.water-status-qsBackground::before {
  content: "";
  position: absolute;
  top: 4px;
  left: 4px;
  width: 10px;
  height: 10px;
  background: url(./images/lj.png) no-repeat center center;
}
.water-status-qsBackground::after {
  content: "";
  position: absolute;
  top: 4px;
  right: 4px;
  width: 10px;
  height: 10px;
  background: url(./images/rj.png) no-repeat center center;
}
.water-status-qsBackground li {
  flex: 1;
  padding: 0 10px;
  position: relative;
}
.water-status-qsBackground li::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 1px;
  background: url(./images/linebg.png) no-repeat center center;
}

.water-status-qsBackground li:last-of-type:after {
  display: none;
}

.water-status-qsBackground li .k1 {
  font-size: 16px;
  margin-bottom: 6px;
  width: 100px;
  padding-top: 10px;
}

.water-status-qsBackground li .k2 {
  font-size: 30px;
  color: #4db727;
  margin-bottom: 6px;
  font-family: "DIN-Medium";
}

.water-status-qsBackground li .k3 {
  font-size: 14px;
  height: 20px;
  line-height: 18px;
  text-align: center;
  background-color: #4db727;
  border-radius: 10px;
  color: #fff;
  margin-bottom: 10px;
}

.water-status-qsBackground li .k3.nodb {
  background-color: #ef4e4a;
  width: 60px;
}

.water-status-qsBackground li .k3.nodb2 {
  background-color: #ef4e4a;
}

.water-status-qsBackground li .k3.db {
  background-color: #4db727;
  width: 60px;
}

.water-status-qsBackground li .k4 {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}

.water-status-qsBackground li .k5 {
  font-size: 16px;
  color: #333;
  padding-bottom: 10px;
}
.g-up {
  width: 8px;
  height: 11px;
  display: inline-block;
  background: url(./images/gup.png) no-repeat center center;
}

.r-down {
  width: 8px;
  height: 11px;
  display: inline-block;
  background: url(./images/rdown.png) no-repeat center center;
}

/* .g-down {
  width: 8px;
  height: 11px;
  display: inline-block;
  background: url(../images/gDown.png) no-repeat center center;
} */

/* .r-up {
  width: 8px;
  height: 11px;
  display: inline-block;
  background: url(../images/rUp.png) no-repeat center center;
} */
</style>
