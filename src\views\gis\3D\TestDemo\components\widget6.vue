<!-- @format -->
<!--立体柱子 -->
<template>
    <div></div>
</template>

<script>
import PointUtil from '../utils/PointUtil';
export default {
    props: [],
    data() {
        return {
            map: null,
            pointUtil: null,
            layerID: '立体柱子'
        };
    },

    unmounted() {
        this.clear();
    },

    mounted() {
        this.initPage();
    },

    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.addLayer();
        },

        addLayer() {
            let datas = [
                {
                    MC: '数据1',
                    JD: 120.53152482474525,
                    WD: 31.00840190006781,
                    base_height: 0,
                    height: 100,
                    color: '#f00'
                },
                {
                    MC: '数据2',
                    JD: 120.58142277194906,
                    WD: 31.003184579,
                    base_height: 0,
                    height: 100,
                    color: '#ff0'
                },
                {
                    MC: '数据3',
                    JD: 120.64262326688737,
                    WD: 30.995319248221875,
                    base_height: 0,
                    height: 200,
                    color: '#f0f'
                }
            ];

            let params = {
                id: this.layerID,
                weight: 10,
                breadth: 0.003 //0.3公里
            };

            this.pointUtil.addCylinderData(datas, params);
        },

        clear() {
            this.pointUtil.removeLayerByName(this.layerID);
        },

        pointClickHandle() {}
    }
};
</script>

<style scoped></style>
