<!-- @format -->
<!-- 插值分析: 河流，湖库 差值分析 -->

<!--  1、明确turf.js 差值的生成的网格排列顺序  2、四角坐标动态读取，写定，  3、需要提供的文件 -->
<template>
    <li :class="{ on: showCZ }" @click="layerContorlClick()">沿程分析</li>
</template>

<script>
import * as turf from '@turf/turf';
export default {
    name: 'InterpolateAnalysis',
    data() {
        return {
            arrResult: [],
            showCZ: true,

            gridResult: [],
            pointCollection: null,
            arrRiver: null
        };
    },
    props: ['map', 'waterPoint', 'selectYZ'],
    unmounted() {
        this.clear(this.map);
    },
    components: {},
    mounted() {},
    methods: {
        layerContorlClick() {
            this.showCZ = !this.showCZ;
            this.showWaterCZLayer();
        },

        // 差值图层处理
        showWaterCZLayer() {
            if (this.showCZ) {
                this.initData();
            } else {
                this.clear(this.map);
            }
        },

        //清除差值图层
        clear(map, id) {
            if (map) {
                if (id) {
                    PowerGL.removeLayerFromName(map, id);
                } else {
                    PowerGL.removeLayerFromName(map, 'river插值');
                    PowerGL.removeLayerFromName(map, 'hk插值');
                }
            }
        },

        initData() {
            let values = [],
                lngs = [],
                lats = [];

            let ext = GisServerGlobalConstant.mapbox.czExt;

            let features = [];

            for (let item of this.waterPoint) {
                let val = item[this.selectYZ + 'SZLBBS'];
                if (
                    item.JD &&
                    item.WD &&
                    !isNaN(parseFloat(item.JD)) &&
                    !isNaN(parseFloat(item.WD)) &&
                    val
                ) {
                    // console.log(item.JD);
                    // console.log(item.WD);
                    let flag =
                        parseFloat(item.JD) > ext.xmin &&
                        parseFloat(item.JD) < ext.xmax &&
                        parseFloat(item.WD) > ext.ymin &&
                        parseFloat(item.WD) < ext.ymax;

                    if (flag) {
                        values.push(val);
                        lngs.push(parseFloat(item.JD));
                        lats.push(parseFloat(item.WD));

                        let g = {
                            geometry: {
                                coordinates: [
                                    parseFloat(item.JD),
                                    parseFloat(item.WD)
                                ],
                                type: 'Point'
                            },
                            properties: {
                                value: val
                            },

                            type: 'Feature'
                        };

                        features.push(g);
                    }
                }
            }

            //虚拟两个顶点
            let extPoint = [
                {
                    JD: ext.xmin,
                    WD: ext.ymin,
                    value: 1
                },
                {
                    JD: ext.xmax,
                    WD: ext.ymax,
                    value: 1
                }
            ];

            for (let i = 0; i < extPoint.length; i++) {
                let g = {
                    geometry: {
                        coordinates: [extPoint[i].JD, extPoint[i].WD],
                        type: 'Point'
                    },
                    properties: {
                        value: extPoint[i].value
                    },

                    type: 'Feature'
                };

                features.push(g);
            }

            let points = {
                type: 'FeatureCollection',
                features: features
            };

            let interpolate_options = {
                gridType: 'points',
                property: 'value',
                // units: 'kilometers',
                units: 'degrees',
                weight: 3
            };
            this.gridResult = turf.interpolate(
                points,
                // 0.5,
                0.002,
                interpolate_options
            );

            console.log(this.gridResult);

            //  this.addGeoJson();
            this.addGeoJson2();
        },

        addGeoJson2() {
            if (this.arrRiver) {
                this.addFeatures2();
            } else {
                fetch('./gis/3D/data/riverCZ.json')
                    .then((res) => res.json())
                    .then((json) => {
                        this.arrRiver = json.features;
                        this.addFeatures2();
                    });
            }
        },

        addFeatures2() {
            let ext = GisServerGlobalConstant.mapbox.czExt;
            let percent =
                this.gridResult.features.length / (ext.xNum * ext.yNum);
            for (let feature of this.arrRiver) {
                let attr = feature.properties;

                // let rows = Math.floor(attr.GRID / ext.xNum);
                // let cols = attr.GRID % ext.xNum;

                // //因为turf 差值出来的每行元素的个数比网格数据多1个
                // let xh = rows * (ext.xNum + 1) + cols;

                let xh = Math.floor(attr.R * percent) * ext.yNum + attr.C;

                if (this.gridResult.features[xh]) {
                    let value = this.gridResult.features[xh].properties.value;
                    feature.properties.R_VALUE = value * 10;
                    // console.log(value);
                } else {
                    delete feature.properties.R_VALUE;
                }
            }

            this.addInterpolateLayer2();
        },

        //湖库面差值
        addInterpolateLayer2() {
            let id = 'hk插值';
            this.clear(this.map, id);
            this.map.addSource(id, {
                type: 'geojson',
                // lineMetrics: true,
                data: {
                    type: 'FeatureCollection',
                    features: this.arrRiver
                }
            });

            this.map.addLayer(
                {
                    type: 'fill',
                    source: id,
                    id: id,
                    paint: {
                        //线条颜色渐变
                        'fill-color': [
                            'interpolate',
                            ['linear'],
                            ['get', 'R_VALUE'],
                            10,
                            '#44C5FF',
                            20,
                            '#4FA6FE',
                            30,
                            '#73BB31',
                            40,
                            '#EEBE16',
                            50,
                            '#F49012',
                            60,
                            '#EE3B5B'
                        ],
                        'fill-opacity': 0.8
                    },
                    layout: {}
                },
                '水系标注_河流_L1'
            );
        }
    },
    watch: {
        waterPoint: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.showWaterCZLayer();
                }
            }
        },

        selectYZ() {
            this.showWaterCZLayer();
        }
    }
};
</script>

<style></style>
