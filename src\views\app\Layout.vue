<!-- @format -->

<template>
    <div>
        <div class="zy-hd">
            <div class="logo" @click="goUrl('/')" style="cursor: pointer">
                <img :src="logoUrl" alt="" id="logo" />
            </div>
            <div class="fr">
                <ul class="nav">
                    <router-link
                        v-for="(item, i) of menuArr"
                        :key="i"
                        :to="item.url"
                        custom
                        v-slot="{ href, route, navigate, isActive }"
                    >
                        <li
                            :class="{ cur: isActive }"
                            :href="href"
                            @click="navigate"
                        >
                            <span>{{ item.title }}</span>
                        </li>
                    </router-link>
                    <!-- <li class="cur"><span>首页</span></li>
                    <li><span>应急准备</span></li>
                    <li><span>应急处置</span></li>
                    <li><span>事后恢复</span></li>
                    <li><span>统计分析</span></li> -->
                </ul>

                <div class="user-wrap">
                    <div class="user" id="user">
                        <img class="avatar" :src="avatarUrl" alt="" />
                        <span>管理员</span>
                    </div>

                    <div
                        class="user"
                        style="cursor: pointer"
                        @click="changeTheme"
                    >
                        <span>换肤</span>
                    </div>

                    <div class="line"></div>
                    <div class="signout">
                        <img :src="signoutUrl" alt="" />
                    </div>
                </div>
            </div>

            <canvas id="J_dotLine"></canvas>
        </div>
        <div class="zy-container">
            <router-view v-if="themeShow"></router-view>
            <!-- <div class="zy-lside">
                <Aside :menu_data="menuList"></Aside>

                <div class="waves"></div>
            </div>
            <div class="zy-content" style="padding: 10px">
                <router-view></router-view>
            </div> -->
        </div>
    </div>
    <!-- <el-aside width="180">
            <Aside :menu_data="menuList"></Aside>
        </el-aside>
        <el-container>
            
        </el-container> -->
</template>

<script>
import Aside from '_c/menu/Aside.vue';
import { reactive, toRefs } from 'vue';
let logoUrl = require('_as/app/images/logo.png');
let avatarUrl = require('_as/app/images/avatar.png');
let signoutUrl = require('_as/app/images/signout.png');
import Driver from 'driver.js';
import 'driver.js/dist/driver.min.css';

export default {
    components: {
        Aside
    },
    setup() {
        const state = reactive({
            themeShow: true,
            logoUrl,
            avatarUrl,
            signoutUrl,
            value: '',
            menuArr: [
                {
                    title: '表单',
                    selected: true,
                    url: '/app/event'
                },
                {
                    title: 'p-charts',
                    selected: false,
                    url: '/app/echartComponent'
                }
            ],
            menuList: [
                {
                    menu_icon: 'el-icon-monitor',
                    menu_name: '表单列表',
                    menu_show_status: 0,
                    path: '/app/event'
                },
                {
                    menu_icon: 'el-icon-monitor',
                    menu_name: 'p-charts',
                    menu_show_status: 1,
                    path: '/app/echartComponent'
                }
            ]
        });

        return {
            ...toRefs(state)
        };
    },

    mounted() {
        this.$nextTick(() => {
            const driver = new Driver({
                doneBtnText: '完成', // Text on the final button
                closeBtnText: '关闭', // Text on the close button for this step
                nextBtnText: '下一步', // Next button text for this step
                prevBtnText: '上一步', // Previous button text for this step
                stageBackground: '#7F7F7F'
            });
            driver.defineSteps([
                {
                    element: '#logo',
                    popover: {
                        className: 'first-step-popover-class',
                        title: 'logo',
                        description: '这是logo',
                        position: 'right'
                    }
                },
                {
                    element: '#user',
                    popover: {
                        title: '用户信息',
                        description: '这是用户信息',
                        position: 'left'
                    }
                }
            ]);

            // Start the introduction
            driver.start();
        });
    },
    methods: {
        changeTheme() {
            let theme =
                ServerGlobalConstant.eleTheme === 'dark' ? 'light' : 'dark';
            localStorage.setItem('themeType', theme);
            ServerGlobalConstant.eleTheme = theme;
            this.themeShow = false;

            document.getElementsByTagName('body')[0].className =
                theme + 'Theme';
            this.$pChart.setChartConfig({
                THEME_COLOR: theme
            });

            this.$nextTick(() => {
                this.themeShow = true;
            });
        },
        goUrl(url) {
            this.$router.push({
                path: url
            });
        }
    }
};
</script>

<style lang="scss">
@import '_as/app/css/a.css';

.my-scroll-bar {
    height: 600px;
}
</style>
