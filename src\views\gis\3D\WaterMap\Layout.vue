<!-- @format -->

<template>
    <div>
        <!-- 图层 -->
        <div class="abs" style="left: 25px; top: 90px; width: 200px">
            <dl
                class="sw0811-dlbx5 yy0812-dlbx5"
                v-show="tab == 'SGZ'"
                @click="changTab('ZDZ')"
            >
                <dt class="flx1 ac jc"><strong>自动监测</strong></dt>
            </dl>

            <ZDZCom
                v-show="tab == 'ZDZ'"
                ref="childZDZ"
                :showName="showName"
                @dataChange="dataChangeHandle"
            ></ZDZCom>

            <div class="gap"></div>
            <dl
                class="sw0811-dlbx5 yy0812-dlbx5"
                v-show="tab == 'ZDZ'"
                @click="changTab('SGZ')"
            >
                <dt class="flx1 ac jc"><strong>手工监测</strong></dt>
            </dl>

            <SGZCom
                v-show="tab == 'SGZ'"
                ref="childSGZ"
                :showName="showName"
            ></SGZCom>

            <div class="gap"></div>
            <WRYCom></WRYCom>
            <div class="gap"></div>
            <OtherCom :map="map"></OtherCom>
            <div class="gap"></div>
            <div class="sw0811-dlbx5">
                <dt class="flx1 ac jc"><strong>控制图层</strong></dt>
                <ul class="yy-tckzbox">
                    <li :class="{ on: showName }" @click="showNameFunc">
                        点位名称
                    </li>
                    <InterpolateAnalysis
                        :map="map"
                        :waterPoint="waterPoint"
                        :selectYZ="selectYZ"
                    ></InterpolateAnalysis>
                </ul>
            </div>
        </div>

        <!-- 图例 -->
        <LegendTool
            class="gis-legend"
            :arrLegend="arrLegend"
            :expand="true"
            :class="{ off: !fold }"
        ></LegendTool>

        <changeStyle></changeStyle>
    </div>
</template>

<script>
import emitter from '@/utils/eventBus.js';
import SGZCom from './components/SGZCom';
import ZDZCom from './components/ZDZCom';
import WRYCom from './components/WRYCom';
import STLXCom from './components/STLXCom'; //水体类型
import LegendTool from '@/components/gis/3D/LegendTool';
import InterpolateAnalysis from './components/InterpolateAnalysis';

import changeStyle from './components/changeStyle.vue';

import DataMock from './utils/DataMock';

export default {
    data() {
        return {
            fold: true,
            tab: 'ZDZ',
            map: null,

            lengendData: {},

            showName: true,
            arrLegend: [],
            waterPoint: null,
            selectYZ: ''
        };
    },

    provide() {
        return {
            pointClickHandle: this.pointClickHandle,
            refreshSearchData: this.refreshSearchData
        };
    },

    unmounted() {
        emitter.off('foldChange');
    },
    components: {
        SGZCom,
        ZDZCom,
        WRYCom,
        STLXCom,
        LegendTool,
        InterpolateAnalysis,
        changeStyle
    },
    mounted() {
        this.initEvent();
        this.initPage();

        this.arrLegend = [
            {
                title: '自动监测',
                data: [
                    { name: 'Ⅰ类', url: 'ZDZ-1.png' },
                    { name: 'Ⅱ类', url: 'ZDZ-2.png' },
                    { name: 'Ⅲ类', url: 'ZDZ-3.png' },
                    { name: 'Ⅳ类', url: 'ZDZ-4.png' },
                    { name: 'Ⅴ类', url: 'ZDZ-5.png' },
                    { name: '劣Ⅴ类', url: 'ZDZ-6.png' },
                    { name: '未评价', url: 'ZDZ-0.png' }
                ]
            },
            {
                title: '手工监测',
                data: [
                    { name: 'Ⅰ类', url: 'DM-1.png' },
                    { name: 'Ⅱ类', url: 'DM-2.png' },
                    { name: 'Ⅲ类', url: 'DM-3.png' },
                    { name: 'Ⅳ类', url: 'DM-4.png' },
                    { name: 'Ⅴ类', url: 'DM-5.png' },
                    { name: '劣Ⅴ类', url: 'DM-6.png' },
                    { name: '未评价', url: 'DM-0.png' }
                ]
            },
            {
                title: '污染源',
                data: [
                    { name: '涉水企业', url: 'SSZDY.png' },
                    { name: '废水在线监控', url: 'WSCLC.png' }
                ]
            }
        ];
    },
    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 300);
                return;
            }

            this.map = window.glMap;
        },

        changTab(type) {
            this.tab = type;
        },

        initEvent() {
            emitter.on('foldChange', (flag) => {
                this.fold = flag;
            });
        },

        pointClickHandle(type, item) {
            this.$emit('mapEvent', type, item);
        },

        refreshSearchData(data) {
            Object.assign(this.lengendData, data);

            let arrTemp = [];

            for (let key in this.lengendData) {
                for (let item of this.lengendData[key]) {
                    arrTemp.push({
                        name:
                            item.QYMC ||
                            item.NAME ||
                            item.PKMC ||
                            item.CDMC ||
                            item.address ||
                            item.KMJZWMC,
                        type: key,
                        JD: item.JD,
                        WD: item.WD
                    });
                }
            }

            emitter.emit('lengendDataChange', arrTemp);
        },

        dataChangeHandle(data) {
            this.waterPoint = data;
        },

        showNameFunc() {
            this.showName = !this.showName;
        }
    },
    watch: {
        fold(val, oldVal) {
            if (val) {
                this.map.panBy([250, 0], { duration: 1500 });
            } else {
                this.map.panBy([-250, 0], { duration: 1500 });
            }
        }
    }
};
</script>

<style scoped>
.gis-legend {
    position: absolute;
    left: 20px;
    bottom: 10px;
    z-index: 999;
    flex-direction: row-reverse;
    height: 100px !important;
}

.gis-legend.off {
}
</style>

<style>
@import './css/style.css';
</style>
