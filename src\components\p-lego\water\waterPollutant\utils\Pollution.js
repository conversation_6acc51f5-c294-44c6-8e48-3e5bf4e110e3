/**
 * 各个因子对应指标线
 *
 * @format
 */
// type用来判断是河流水还是湖库
export const getPollutionmarkLine = (cons, type) => {
    //cons 污染物名称
    switch (cons) {
        case 'w21011_DBS': //总磷
            if (type === 'lake') {
                //湖库
                return [
                    { name: 'Ⅰ类', value: 0.01, color: '#44c5fd' },
                    { name: 'Ⅱ类', value: 0.025, color: '#51a5fd' },
                    { name: 'Ⅲ类', value: 0.05, color: '#73bb31' },
                    { name: 'Ⅳ类', value: 0.1, color: '#eebd15' },
                    { name: 'Ⅴ类', value: 0.2, color: '#f88e17' }
                ];
            } else {
                //自动站、河流
                return [
                    { name: 'Ⅰ类', value: 0.02, color: '#44c5fd' },
                    { name: 'Ⅱ类', value: 0.1, color: '#51a5fd' },
                    { name: 'Ⅲ类', value: 0.2, color: '#73bb31' },
                    { name: 'Ⅳ类', value: 0.3, color: '#eebd15' },
                    { name: 'Ⅴ类', value: 0.4, color: '#f88e17' }
                ];
            }
        case 'w21001_DBS': //总氮
            return [
                { name: 'Ⅰ类', value: 0.2, color: '#44c5fd' },
                { name: 'Ⅱ类', value: 0.5, color: '#51a5fd' },
                { name: 'Ⅲ类', value: 1.0, color: '#73bb31' },
                { name: 'Ⅳ类', value: 1.5, color: '#eebd15' },
                { name: 'Ⅴ类', value: 2.0, color: '#f88e17' }
            ];
        case 'w21003_DBS': //氨氮
            return [
                { name: 'Ⅰ类', value: 0.15, color: '#44c5fd' },
                { name: 'Ⅱ类', value: 0.5, color: '#51a5fd' },
                { name: 'Ⅲ类', value: 1.0, color: '#73bb31' },
                { name: 'Ⅳ类', value: 1.5, color: '#eebd15' },
                { name: 'Ⅴ类', value: 2.0, color: '#f88e17' }
            ];
        case 'w01019_DBS': //高锰酸盐指数
            return [
                { name: 'Ⅰ类', value: 2, color: '#44c5fd' },
                { name: 'Ⅱ类', value: 4, color: '#51a5fd' },
                { name: 'Ⅲ类', value: 6, color: '#73bb31' },
                { name: 'Ⅳ类', value: 10, color: '#eebd15' },
                { name: 'Ⅴ类', value: 15, color: '#f88e17' }
            ];
        case 'w01009_DBS': //溶解氧
            return [
                { name: 'Ⅰ类', value: 7.5, color: '#44c5fd' },
                { name: 'Ⅱ类', value: 6, color: '#51a5fd' },
                { name: 'Ⅲ类', value: 5, color: '#73bb31' },
                { name: 'Ⅳ类', value: 3, color: '#eebd15' },
                { name: 'Ⅴ类', value: 2, color: '#f88e17' }
            ];
        case 'w01001_DBS': //ph
            return [
                { name: '下限值', value: 6, color: '#ee3b5b' },
                { name: '上限值', value: 9, color: '#ee3b5b' }
            ];
        default:
            return [];
    }
};
