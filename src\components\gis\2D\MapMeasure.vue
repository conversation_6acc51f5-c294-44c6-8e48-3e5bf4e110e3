<!-- @format -->

<!-- 地图测量 -->
<template>
    <div style="position: relative">
        <ul class="zy-tools-gis">
            <li @click="btnClick" :class="{ on: show }">
                <i class="tool-measure"></i>
            </li>
        </ul>
        <div class="map-measure" v-show="show">
            <div
                class="point DTool"
                title="绘制点"
                @click="toolbtnClick('POINT')"
            ></div>
            <div
                class="line DTool"
                title="绘制线"
                @click="toolbtnClick('POLYLINE')"
            ></div>
            <div
                class="freeline DTool"
                title="绘制手绘线"
                @click="toolbtnClick('FREEHAND_POLYLINE')"
            ></div>
            <div
                class="extent DTool"
                title="绘制矩形"
                @click="toolbtnClick('EXTENT')"
            ></div>
            <div
                class="circle DTool"
                title="绘制圆"
                @click="toolbtnClick('CIRCLE')"
            ></div>
            <div
                class="polygon DTool"
                title="绘制多边形"
                @click="toolbtnClick('POLYGON')"
            ></div>
            <!-- <div class="txt DTool" title="文本" value="6"></div> -->
            <div
                class="clear DTool"
                @click="toolbtnClick('clear')"
                title="清除绘图"
                value="7"
            ></div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            show: false
        };
    },
    props: ['map'],
    components: {},
    mounted() {},
    methods: {
        btnClick() {
            this.show = !this.show;
        },
        toolbtnClick(type) {
            PowerGis.addDrawAndMesureLayer(this.map, type);
        }
    },
    watch: {}
};
</script>

<style>
.sysmbol_InfoWindow_DTCL {
    position: absolute;
    border: 1px solid #43bafa;
    text-align: center;
    z-index: 100;
    box-shadow: 0 0 1em #26393d;
    font-family: sans-serif, sans-serif;
    font-size: 12px;
    background-color: rgba(255, 255, 255, 0.7);
    /*** pointer-events: none;  去掉div的事件，为css 样式， IE9 不支持***/
}
</style>

<style scoped>
.map-measure {
    position: absolute;
    width: 249px;
    top: 0px;
    left: -250px;
}
</style>
