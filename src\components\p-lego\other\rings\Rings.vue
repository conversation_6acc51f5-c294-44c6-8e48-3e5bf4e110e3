<!-- @format -->

<template>
    <div>
        <p-chart
            :option="opt"
            :style="`width:${option.width}; height:${option.height}`"
        ></p-chart>
    </div>
</template>
<script>
export default {
    name: 'rings',
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        },
        option: {
            type: Object,
            default: function () {
                return {
                    width: '600px',
                    height: '200px',
                    color: [
                        '#ee3b5b',
                        '#44c5fd',
                        '#73bb31',
                        '#eebd15',
                        '#f88e17'
                    ], //圆环颜色
                    color_empty: '#072338', //补全的圆环颜色
                    color_text: '#000' //图例文字颜色
                };
            }
        }
    },
    data() {
        return {
            opt: {},
            ringSize: 100 //最外层圆环大小
        };
    },
    watch: {
        data: 'ringChart',
        option: 'ringChart'
    },
    mounted() {
        this.ringChart();
    },
    methods: {
        ringChart() {
            let self = this;
            let _series = [];
            if (this.data.length) {
                let ringSize = this.ringSize;
                this.data.forEach((item) => {
                    ringSize = ringSize - 20;
                    let _data = {
                        type: 'pie',
                        clockWise: false,
                        radius: [ringSize + 10, ringSize + 20],
                        center: ['22%', '50%'],
                        itemStyle: {
                            normal: {
                                label: {
                                    show: false
                                },
                                labelLine: {
                                    show: false
                                }
                            }
                        }, //伸出来的线
                        hoverAnimation: false, //鼠标放上去圆环放大
                        startAngle: 90,
                        data: [
                            {
                                value: item.bfb,
                                numData: item.value,
                                name: item.name
                            },
                            {
                                value: 100 - item.bfb,
                                name: item.name,
                                tooltip: {
                                    show: false
                                },
                                itemStyle: {
                                    normal: {
                                        color: this.option.color_empty,
                                        label: {
                                            show: false
                                        },
                                        labelLine: {
                                            show: false
                                        }
                                    }
                                }
                            }
                        ]
                    };
                    _series.push(_data);
                });
            }
            this.opt = {
                color: this.option.color,
                toolbox: { show: false },
                tooltip: {
                    trigger: 'item',
                    formatter: function (param) {
                        return (
                            param.data.name +
                            ':' +
                            param.data.numData +
                            '家' +
                            '(' +
                            param.data.value +
                            '%' +
                            ')'
                        );
                    },
                    padding: [8, 10] //内边距
                },
                legend: {
                    orient: 'vertical',
                    icon: 'circle',
                    left: '45%',
                    top: 'center',
                    itemGap: 20, //图例的间距
                    itemHeight: 16,
                    itemWidth: 16,
                    textStyle: {
                        fontSize: 16,
                        // color: this.option.color_text,
                        rich: {
                            a: {
                                fontSize: 17,
                                padding: [0, 0, 0, 6],
                                width: 60,
                                color: this.option.color_text
                            },
                            b: {
                                fontSize: 17,
                                padding: [0, 0, 0, 30],
                                width: 40,
                                color: this.option.color_text
                            },
                            c: {
                                fontSize: 17,
                                padding: [0, 0, 0, 40],
                                width: 40,
                                color: this.option.color_text
                            }
                        }
                    },
                    formatter: function (param) {
                        let _data = self.data;
                        let _item = '';
                        for (let item of _data) {
                            if (item.name === param) {
                                _item = item;
                                break;
                            }
                        }
                        return `{a|${_item.name}} {b|${_item.value} 家} {c|${_item.bfb} %}`;
                    }
                },
                series: _series
            };
        }
    }
};
</script>
<style scoped></style>
