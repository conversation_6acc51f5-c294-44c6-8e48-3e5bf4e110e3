/**
 * /*
 *
 * @format
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @Description: 前端导出工具
 * @Date: 2023-01-29 11:12:22
 * @LastEditTime: 2023-01-29 11:12:22
 */

//导出页面内容为图片
//el：必传，需要导出的dom元素
//name：可选，导出后的文件名称，不传时默认取时间戳；
//done：可选，执行之后的回调
//scale：可选，缩放比例 默认为1
//backgroundColor：可选 背景颜色 默认无
export const saveImage = ({
    name,
    el,
    done,
    scale = 1,
    backgroundColor = '#fff'
}) => {
    try {
        //生成的图片名称
        let imgName = (name || new Date() - 0) + '.jpg';
        let width = el.offsetWidth;
        let height = el.offsetHeight;
        let canvas = document.createElement('canvas');
        let context = canvas.getContext('2d');
        //let scale = getPixelRatio(context); //将canvas的容器扩大PixelRatio倍，再将画布缩放，将图像放大PixelRatio倍。

        canvas.width = width * scale;
        canvas.height = height * scale;
        canvas.style.width = width * scale + 'px';
        canvas.style.height = width * scale + 'px';
        context.scale(scale, scale);

        let opts = {
            scale: 1,
            canvas: canvas,
            // dpi: window.devicePixelRatio,
            backgroundColor: backgroundColor, //背景颜色
            useCORS: true, // 【重要】开启跨域配置
            allowTaint: true, // 允许跨域图片
            scrollY: 0 // 纵向偏移量 写死0 可以避免滚动造成偶尔偏移的现象
        };
        let dataURIToBlob = function (imgName, dataURI, callback) {
            let binStr = atob(dataURI.split(',')[1]),
                len = binStr.length,
                arr = new Uint8Array(len);

            for (let i = 0; i < len; i++) {
                arr[i] = binStr.charCodeAt(i);
            }

            callback(imgName, new Blob([arr]));
        };

        let callback = function (imgName, blob) {
            let link = document.createElement('a');
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.setAttribute('download', imgName);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            done && done('success');
        };

        let start = function () {
            window.html2canvas(el, opts).then(function (canvas) {
                context.imageSmoothingEnabled = false;
                context.webkitImageSmoothingEnabled = false;
                context.msImageSmoothingEnabled = false;
                context.imageSmoothingEnabled = false;
                let dataUrl = canvas.toDataURL('image/jpeg', 1.0);
                dataURIToBlob(imgName, dataUrl, callback);
            });
        };

        if (!window.html2canvas) {
            let script = document.createElement('script');
            script.type = 'text/javascript';
            script.src =
                'http://html2canvas.hertzen.com/dist/html2canvas.min.js';

            document.body.appendChild(script);

            script.onload = function () {
                start();
            };
        } else {
            start();
        }
    } catch (e) {
        console.log(e);
        done && done('success');
    }
};

//导出table内容为表格
//data 和 el 二选一
//el：需要导出的dom元素
//data：需要导出的列表数据
//name：可选，导出后的文件名称，不传时默认取时间戳；
//style 可选，自定义css样式
export const saveExcel = ({ el, data, columns, name, style = '' }) => {
    name = name || new Date() - 0;
    let html = '';

    if (el && el.tagName === 'TABLE') {
        html = el.innerHTML;
    } else if (el) {
        let els = el.getElementsByTagName('table');
        let elsArr = [...els];
        elsArr.forEach((val) => {
            html += val.innerHTML;
        });
    } else if (
        data &&
        data.constructor === Array &&
        columns &&
        columns.constructor === Array
    ) {
        let hd = '<tr>';
        columns.forEach((col) => {
            hd += `<th>${col.label}</th>`;
        });
        hd += '</tr>';

        let bd = '';

        let getValue = (prop, row, index) => {
            if (prop.constructor === Function) {
                return prop(row, index);
            } else if (prop === '$index') {
                return index + 1;
            } else {
                return row[prop];
            }
        };
        data.forEach((row, index) => {
            let tr = '<tr>';
            columns.forEach((col) => {
                tr += `<td>${getValue(col.prop, row, index)}</td>`;
            });
            tr += '</tr>';
            bd += tr;
        });

        html = hd + bd;
    }

    //Worksheet名
    let worksheet = 'Sheet1';
    let uri = 'data:application/vnd.ms-excel;base64,';

    //下载的表格模板数据
    let template = `<html xmlns:o="urn:schemas-microsoft-com:office:office"
      xmlns:x="urn:schemas-microsoft-com:office:excel"
      xmlns="http://www.w3.org/TR/REC-html40">
      <head><meta charset="UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet>
        <x:Name>${worksheet}</x:Name>
        <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet>
        </x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->
        <style>
            th, td{
                text-align: center;
            }
            ${style}
        </style>
        </head><body><table>${html}</table></body></html>`;

    //下载模板
    function base64(s) {
        return window.btoa(unescape(encodeURIComponent(s)));
    }

    let link = document.createElement('a');
    link.style.display = 'none';
    link.href = uri + base64(template);
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    // window.location.href = uri + base64(template);
};
