<!-- @format -->
<!-- mapbox 标签业务组件 -->
<template>
    <div></div>
</template>

<script>
export default {
    data() {
        return {
            areaMarkers: [],
            pointSize: 'min'
        };
    },
    props: ['map', 'markerParam'],
    inject: ['callback'],
    components: {},
    mounted() {
        this.initEvent();
    },
    unmounted() {
        this.clear();
    },
    methods: {
        initEvent() {
            if (!this.map) {
                setTimeout(() => {
                    this.initEvent();
                }, 200);
                return;
            }
            let self = this;
            this.map.on('zoomend', (evt) => {
                self.zoomHandle();
            });
        },
        //地图缩放事件处理
        zoomHandle() {
            let zoom = this.map.getZoom();
            let fjzoom = this.markerParam.FJZOOM || 10;
            if (
                (zoom >= fjzoom && this.pointSize == 'min') ||
                (zoom < fjzoom && this.pointSize == 'max')
            ) {
                this.pointSize = this.pointSize == 'max' ? 'min' : 'max';
                this.addMarkers();
            }
        },

        //绘制标签
        addMarkers() {
            switch (this.markerParam.type) {
                case '大气站点':
                case '大气站点2':
                case '微站':
                    this.addAirPoint();
                    break;
                case '水质站点':
                    this.addWaterPoint();
                    break;
            }
        },

        addAirPoint() {
            let _this = this;
            this.clear();
            for (let item of this.markerParam.pointData) {
                if (
                    !item.JD ||
                    isNaN(parseFloat(item.JD)) ||
                    !item.WD ||
                    isNaN(parseFloat(item.WD))
                ) {
                    return;
                }

                let lnglat = [parseFloat(item.JD), parseFloat(item.WD)];

                let el = this.getContent(item);
                // el.innerHTML = `<dl style="width:${width}; height:103px" class="pd-dlpin-air dj${dj}"><dt>${item.CDMC}&emsp;${data}</dt><dd></dd></dl>`;
                // if (!this.markerParam.showAno) {
                //     el.innerHTML = `<dl class="pd-dlpin-air dj${dj}"><dt>${data}</dt><dd></dd></dl>`;
                // }

                el.addEventListener('mouseenter', function (e) {
                    if (window.glTooltip) {
                        window.glTooltip.remove();
                    }

                    let description =
                        `<span style="font: normal normal bold 20px/26px Microsoft YaHei">` +
                        item.CDMC +
                        `</span>`;

                    window.glTooltip = new mapboxgl.Popup({
                        className: 'mapbox-tooltip',
                        closeOnClick: false,
                        closeButton: false
                    })
                        .setOffset([0, -30])
                        .setLngLat(lnglat)
                        .setHTML(description)
                        .setMaxWidth('none')
                        .addTo(_this.map);
                    _this.map.triggerRepaint();
                });
                el.addEventListener('mouseleave', function (e) {
                    window.glTooltip.remove();
                });

                el.addEventListener('click', function (e) {
                    // _this.$emit(this.type, item);
                    _this.callback(_this.markerParam.type, item);
                    e.preventDefault();
                });

                let marker = new mapboxgl.Marker(el)
                    .setLngLat(lnglat)
                    .addTo(this.map);

                this.areaMarkers.push(marker);
            }
        },
        getContent(item) {
            let level = PowerGL.getLevelByWrw(this.markerParam.yz, item, 1);
            let dj = PowerGL.getAirDJByLevel(level);
            let data = item[this.markerParam.yz]
                ? item[this.markerParam.yz]
                : '--';
            let wid = 18 * item.CDMC.length;
            let el = document.createElement('div');
            el.className = 'area-marker';
            let width = 80 + wid + 'px';
            el.innerHTML = ``;
            if (this.markerParam.type == '微站') {
                if (this.markerParam.FJZS) {
                    el.className = 'location';
                    if (this.pointSize == 'min') {
                        el.innerHTML = `
                  <div class="airLevel dj${dj}">${data}</div>
              `;
                    } else {
                        let marginLeft = (45 - wid) / 2;
                        el.innerHTML = ` <div class="airLevel dj${dj}">${data}</div><div class="mc" style="width:${wid}px;margin-left:${marginLeft}px">${item.CDMC}</div>`;
                    }
                } else {
                    el.className = 'location';
                    el.innerHTML = `
                  <div class="airLevel dj${dj}">${data}</div>
              `;
                    if (this.markerParam.showAno) {
                        let marginLeft = (45 - wid) / 2;
                        el.innerHTML = ` <div class="airLevel dj${dj}">${data}</div><div class="mc" style="width:${wid}px;margin-left:${marginLeft}px">${item.CDMC}</div>`;
                    }
                }
            } else if (this.markerParam.type == '大气站点') {
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        el.innerHTML = `<dl class="pd-dlpin-air dj${dj}"><dt>${data}</dt><dd></dd></dl>`;
                    } else {
                        el.innerHTML = `<dl style="width:${width}; height:103px" class="pd-dlpin-air dj${dj}"><dt>${item.CDMC}&emsp;${data}</dt><dd></dd></dl>`;
                    }
                } else {
                    el.innerHTML = `<dl style="width:${width}; height:103px" class="pd-dlpin-air dj${dj}"><dt>${item.CDMC}&emsp;${data}</dt><dd></dd></dl>`;

                    if (!this.markerParam.showAno) {
                        el.innerHTML = `<dl class="pd-dlpin-air dj${dj}"><dt>${data}</dt><dd></dd></dl>`;
                    }
                }
            } else if (this.markerParam.type == '大气站点2') {
                let djtext = this.getDJTextByDJ(dj);
                let color = PowerGL.getAirColorByLevel(level);
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        el.innerHTML = `<div class="sw19-dwsite type${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    } else {
                        el.innerHTML = `<div class="sw19-dwsite type${dj}" >
				<h1><p>${item.CDMC}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                } else {
                    el.innerHTML = `<div class="sw19-dwsite type${dj}" >
				<h1><p>${item.CDMC}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    if (!this.markerParam.showAno) {
                        el.innerHTML = `<div class="sw19-dwsite type${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                }
            }
            return el;
        },
        getDJTextByDJ(val) {
            let str = '';
            switch (val) {
                case '0':
                    str = '无';
                    break;
                case '1':
                    str = '优';
                    break;
                case '2':
                    str = '良';
                    break;
                case '3':
                    str = '轻度';
                    break;
                case '4':
                    str = '中度';
                    break;
                case '5':
                    str = '重度';
                    break;
                case '6':
                    str = '严重';
                    break;
            }
            return str;
        },

        // 水点位
        addWaterPoint() {
            let _this = this;
            this.clear();
            this.markerParam.pointData.map((item, index) => {
                if (
                    !item.JD ||
                    isNaN(parseFloat(item.JD)) ||
                    !item.WD ||
                    isNaN(parseFloat(item.WD))
                ) {
                    return;
                }

                let lnglat = [parseFloat(item.JD), parseFloat(item.WD)];

                let el = this.getWaterContent(item);

                el.addEventListener('mouseenter', function (e) {
                    let name = item.pointName;
                    let description = `<div style="color:#000" > ${name} </div>`;

                    window.glTooltip = new mapboxgl.Popup({
                        className: 'mapbox-tooltip',
                        closeOnClick: false,
                        closeButton: false
                    })
                        .setOffset([0, -30])
                        .setLngLat(lnglat)
                        .setHTML(description)
                        .setMaxWidth('none')
                        .addTo(this.map);
                    this.map.triggerRepaint();
                });
                el.addEventListener('mouseleave', function (e) {
                    window.glTooltip.remove();
                });

                el.addEventListener('click', function (e) {});

                let marker = new mapboxgl.Marker(el)
                    .setLngLat(lnglat)
                    .addTo(this.map);

                this.areaMarkers.push(marker);
            });
        },
        getWaterContent(item) {
            let el = document.createElement('div');

            let dj = item.szlbbs;
            let waterQuality = item.waterQuality || '--';
            let color = PowerGL.getWaterColorByLevel(waterQuality);
            let djtext = PowerGL.getDJMCByLevel(dj + '');

            if (this.markerParam.type == '水质站点') {
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        el.innerHTML = `<div class="sw19-dwsite type1${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    } else {
                        el.innerHTML = `<div class="sw19-dwsite type1${dj}" >
				<h1><p>${item.pointName}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                } else {
                    el.innerHTML = `<div class="sw19-dwsite type1${dj}" >
				<h1><p>${item.pointName}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    if (!this.markerParam.showAno) {
                        el.innerHTML = `<div class="sw19-dwsite type1${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                }
            }

            return el;
        },
        //清除
        clear() {
            for (let marker of this.areaMarkers) {
                if (marker) {
                    marker.remove();
                    marker = null;
                }
            }

            this.areaMarkers = [];
        }
    },
    watch: {
        markerParam: {
            immediate: true,
            deep: true,
            handler(val) {
                if (this.markerParam && this.markerParam.pointData) {
                    if (this.markerParam.visible) {
                        this.addMarkers();
                    } else {
                        this.clear();
                    }
                }
            }
        }
    }
};
</script>

<style>
@import '~_as/gis/commom/map3DMarker.css';
</style>
