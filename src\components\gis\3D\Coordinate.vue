<!-- @format -->
<!-- 坐标显示 -->
<template>
    <div class="jingwei-gis">{{ jwdStr }}</div>
</template>

<script>
export default {
    name: 'Coordinate',
    props: [],
    data() {
        return {
            map: null,
            jwdStr: ''
        };
    },
    components: {},
    computed: {},
    mounted() {
        this.initPage();
    },
    methods: {
        initPage() {
            // this.initEvent();
        },

        initEvent() {
            this.map.on('mouse-move', (evt) => {
                let pt = evt.mapPoint;
                if (pt.spatialReference.wkid == 102100) {
                    // pt = MapUtil.webMercatorToGeographic(pt);
                }
                this.jwdStr = pt.x.toFixed(6) + ',' + pt.y.toFixed(6);
            });
        }
    },
    watch: {}
};
</script>

<style scoped></style>
