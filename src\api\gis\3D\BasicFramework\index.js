/** @format */

import axios from '_u/ajaxRequest';
import axios2 from 'axios';

// eslint-disable-next-line no-undef
const BASE_URL = ServerGlobalConstant.BASE_URL;

//空气站最新时间
export const getPointHourMaxDate = (data) => {
    return axios.request({
        url: BASE_URL + '/dqwrfsHs/aics_combatgis/getPointHourMaxDate',
        method: 'get',
        params: data
    });
};

//获取服务器json数据
export const getJosnData = (url) => {
    return axios2.get(url, {
        params: {}
    });
};
