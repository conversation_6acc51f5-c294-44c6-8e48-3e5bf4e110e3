<!-- @format -->

<template>
    <div class="zy-cell" style="width: 580px; height: 320px">
        <div class="zy-cell-hd">
            <p class="til til1 re-ic2">行政处罚</p>
            <span class="more"></span>
        </div>

        <div>
            <div class="yg-data5">
                <div class="gap"></div>

                <div class="mod-bd xzcf-bd">
                    <div class="partlf bor">
                        <div class="modtop">
                            <h4 class="zfrank fkbg wfont">案件数排名</h4>
                            <div class="zfcsrankbox">
                                <div class="ajrankbox">
                                    <ul class="anul">
                                        <li
                                            v-for="(item, index) in data.slice(
                                                0,
                                                3
                                            )"
                                            :key="index"
                                            :title="
                                                item.XZQHMC +
                                                ' ' +
                                                item.CFAJ_TOTAL +
                                                '件'
                                            "
                                        >
                                            <h6>
                                                {{ index + 1 }}.{{
                                                    item.XZQHMC.slice(0, 3)
                                                }}
                                            </h6>
                                            <p class="ajnum">
                                                {{ item.CFJE_TOTAL
                                                }}<label>件</label>
                                            </p>
                                        </li>
                                    </ul>
                                </div>
                                <ul class="botul">
                                    <li
                                        v-for="(item, index) in data.slice(
                                            3,
                                            10
                                        )"
                                        :key="index"
                                        :title="
                                            item.XZQHMC +
                                            ' ' +
                                            item.CFAJ_TOTAL +
                                            '件'
                                        "
                                    >
                                        <p
                                            class="jp blueborder"
                                            :class="{ redfont: index > 3 }"
                                        >
                                            {{ index + 4 }}
                                        </p>
                                        <p class="zsf">
                                            {{ item.XZQHMC.slice(0, 3) }}
                                        </p>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="partlf">
                        <div class="modtop">
                            <h4 class="zfrank fkbg wfont">处罚金额排名</h4>
                            <div class="zfcsrankbox">
                                <div class="ajrankbox">
                                    <ul class="anul">
                                        <li
                                            v-for="(item, index) in data.slice(
                                                0,
                                                3
                                            )"
                                            :key="index"
                                            :title="
                                                item.XZQHMC +
                                                ' ' +
                                                item.CFJE_TOTAL +
                                                '万'
                                            "
                                        >
                                            <h6>
                                                {{ index + 1 }}.{{
                                                    item.XZQHMC.slice(0, 3)
                                                }}
                                            </h6>
                                            <p class="ajnum">
                                                {{ item.CFJE_TOTAL
                                                }}<label>万</label>
                                            </p>
                                        </li>
                                    </ul>
                                </div>
                                <ul class="botul">
                                    <li
                                        v-for="(item, index) in data.slice(
                                            3,
                                            10
                                        )"
                                        :key="index"
                                        :title="
                                            item.XZQHMC +
                                            ' ' +
                                            item.CFJE_TOTAL +
                                            '万'
                                        "
                                    >
                                        <p
                                            class="jp blueborder"
                                            :class="{ redfont: index > 3 }"
                                        >
                                            {{ index + 4 }}
                                        </p>
                                        <p class="zsf">
                                            {{ item.XZQHMC.slice(0, 3) }}
                                        </p>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'administrativePunishmentRank',
    props: {
        data: {
            type: Array,
            default: function () {
                return [
                    {
                        XZQHDM: '420382',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '武当山'
                    },
                    {
                        XZQHDM: '420322',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '郧西'
                    },
                    {
                        XZQHDM: '420300',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '十堰市'
                    },
                    {
                        XZQHDM: '420323',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '竹山'
                    },
                    {
                        XZQHDM: '420302',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '茅箭'
                    },
                    {
                        XZQHDM: '420324',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '竹溪'
                    },
                    {
                        XZQHDM: '420303',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '张湾'
                    },
                    {
                        XZQHDM: '420325',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '房县'
                    },
                    {
                        XZQHDM: '420304',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '郧阳'
                    },
                    {
                        XZQHDM: '420381',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '丹江口'
                    },
                    {
                        XZQHDM: '420305',
                        CFJE_TOTAL: '0',
                        CFAJ_TOTAL: '0',
                        XZQHMC: '开发'
                    }
                ];
            }
        }
    },
    data() {
        return {};
    },
    watch: {},
    mounted() {},
    methods: {}
};
</script>

<style lang="scss" scoped>
/* -- Reset -- */
body,
ul,
ol,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
p,
form,
input,
textarea,
select,
button {
    margin: 0;
    padding: 0;
    font: 12px 'Microsoft YaHei', SimSun, Arial, Helvetica, sans-serif;
}

.zy-cell {
    background-color: rgba(16, 37, 58, 1);
    position: relative;
    margin-bottom: 10px;
    padding-bottom: 1px;
}
.zy-cell::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    background-image: url(./images/cell-bg.png);
}
.zy-cell-hd {
    display: flex;
    height: 46px;
    padding: 0 10px;
    padding-bottom: 6px;
    background: url(./images/hd-bg.png) center bottom no-repeat;
    justify-content: space-between;
    align-items: center;
}

.zy-cell-hd .til {
    padding-left: 38px;
    font-size: 22px;
    color: #fff;
    line-height: 46px;
    background-position: 0 10px;
    background-repeat: no-repeat;
    font-family: 'TTTGB';
    text-shadow: 1px 1px 5px rgba(255, 255, 255, 0.8);
}

.zy-cell-hd .til1 {
    background-image: url(./images/zxcfic3.png);
}

.zy-cell-hd .more {
    width: 20px;
    height: 20px;
    background: url(./images/ic-more.png) no-repeat;
    cursor: pointer;
    margin-right: 20px;
}

.gap {
    height: 10px;
    width: 100%;
}

.partlf {
    width: 50%;
}

.xzcf-bd {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.zfrank.fkbg.wfont {
    text-align: left;
    color: #fff;
}
.zsf {
    font-size: 16px;
}
.anul {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.zfcsrankbox {
    margin: 10px 12px;
}
.ajrankbox {
    margin-bottom: 15px;
}
.anul > li {
    width: 80px;
    height: 66px;
    color: #fff;
    background: url(./images/xzcfbg.png) no-repeat center center;
    background-size: 100%;
}
.anul > li > h6 {
    font-size: 18px;
    height: 30px;
    line-height: 30px;
    text-align: center;
}
.anul > li .ajnum {
    font-size: 18px;
    height: 38px;
    line-height: 38px;
    text-align: center;
}
.anul > li .ajnum label {
    font-size: 14px;
}
.bor {
    border-right: dashed 1px #0d5086;
}
.mod-bd .zfrank {
    padding-left: 30px;
    font-size: 18px;
    color: #fff;
    height: 38px;
    line-height: 38px;
}

.mod-bd .zfrank.fkbg {
    color: #137dcf;
    font-size: 18px;
    background: url(./images/fkbg.png) no-repeat 0 center;
    margin: 5px 17px;
}

.mod-bd .zfrank.fkbg label {
    color: #909090;
    margin-left: 5px;
}

.zfcsrankbox > ul {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
}

.zfcsrankbox > ul > li {
    color: #fff;
    width: 22px;
    height: 83px;
    margin: 0 auto;
    text-align: center;
    display: flex;
    align-items: center;
    flex-direction: column;
    /* margin: 0 2px; */
}

.jp {
    width: 22px;
    height: 29px;
    margin-bottom: 6px;
}

.jp.no1 {
    background: url(./images/jp1.png) no-repeat center center;
}

.jp.no2 {
    background: url(./images/jp2.png) no-repeat center center;
}

.jp.no3 {
    background: url(./images/jp3.png) no-repeat center center;
}

.jp.blueborder {
    width: 27px;
    height: 27px;
    line-height: 27px;
    text-align: center;
    border: solid 1px #137dcf;
    font-size: 14px;
}

.hidefont {
    text-indent: -9999px;
    border: none !important;
}

.redfont {
    color: #e42a2a;
}
</style>
