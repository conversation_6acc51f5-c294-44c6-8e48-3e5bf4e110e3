<!-- @format -->
<!-- mapbox 标签业务组件 -->
<template>
    <div></div>
</template>

<script>
import * as turf from '@turf/turf';
export default {
    data() {
        return {
            areaMarkers: [],
            markers: [],
            pointSize: 'min'
        };
    },
    props: ['map', 'markerParam'],
    components: {},
    mounted() {
        this.initEvent();
    },
    unmounted() {
        this.clear();
        for (let marker of this.markers) {
            if (marker) {
                marker.remove();
                marker = null;
            }
        }

        this.markers = [];

        this.removeLayerFromName('污染源');
    },
    methods: {
        initEvent() {
            console.log(this.map);
            if (!this.map) {
                setTimeout(() => {
                    this.initEvent();
                }, 200);
                return;
            }
            let arr = [
                {
                    name: '污染源',
                    url: './gis/3D/images/iconLibrary/wry/其他污染源.png'
                }
            ];
            for (let item of arr) {
                this.map.loadImage(item.url, (error, image) => {
                    if (error) throw error;
                    if (!this.map.hasImage(item.name)) {
                        this.map.addImage(item.name, image);
                    }
                });
            }

            let self = this;
            this.map.on('zoomend', (evt) => {
                self.zoomHandle();
            });
        },
        //地图缩放事件处理
        zoomHandle() {
            let zoom = this.map.getZoom();
            let fjzoom = this.markerParam.FJZOOM || 10;
            if (
                (zoom >= fjzoom && this.pointSize == 'min') ||
                (zoom < fjzoom && this.pointSize == 'max')
            ) {
                this.pointSize = this.pointSize == 'max' ? 'min' : 'max';
                this.addMarkers();
            }
        },

        //绘制标签
        addMarkers() {
            switch (this.markerParam.centraltype) {
                case '大气站点1':
                case '大气站点2':
                case '大气站点3':
                    this.addAirPoint();
                    break;
                case '水质站点':
                    this.addWaterPoint();
                    break;
            }
        },
        addSFXfw() {
            let center = turf.point([
                parseFloat(this.markerParam.centralPoint[0].JD),
                parseFloat(this.markerParam.centralPoint[0].WD)
            ]);
            let bearing1 = this.markerParam.angle - 45;
            let bearing2 = this.markerParam.angle + 45;
            let geoarr = [];
            let geoarrpoint = [];
            let getColor = PowerGL.segmentedColorScale([
                [0, [191, 136, 0]],
                [50, [201, 141, 12]],
                [100, [204, 159, 90]]
            ]);

            let getColor2 = PowerGL.segmentedColorScale([
                [0, [247, 8, 3]],
                [50, [247, 123, 18]],
                [100, [215, 187, 55]]
            ]);

            this.markerParam.distanc.forEach((radius) => {
                let color = getColor(
                    radius,
                    0.5,
                    this.markerParam.distanc[0],
                    this.markerParam.distanc[
                        this.markerParam.distanc.length - 1
                    ]
                );
                console.log(color);
                let sector = turf.sector(center, radius, bearing1, bearing2, {
                    properties: { color: color }
                });
                geoarr.push(sector);
                let color2 = getColor2(
                    radius,
                    1,
                    this.markerParam.distanc[0],
                    this.markerParam.distanc[
                        this.markerParam.distanc.length - 1
                    ]
                );
                let options = {
                    units: 'kilometers',
                    properties: { name: radius + '公里', color: color2 }
                };
                let destination = turf.destination(
                    center,
                    radius,
                    bearing2,
                    options
                );
                geoarrpoint.push(destination);
            });

            this.addPolygonSymbol(geoarr);

            // 定位到该范围
            const bound = turf.bbox(geoarr[geoarr.length - 1]);
            let option = {
                pitch: 0,
                padding: {
                    top: 100,
                    bottom: 100,
                    left: 200,
                    right: 100
                },
                // offset: [40, 0],
                duration: 2000, //飞行总时长，单位ms
                easing: (t) => {
                    return t;
                }
            };
            this.map.fitBounds(bound, option);

            this.addGisData({ layerId: '距离文字' }, geoarrpoint);

            let arr = [];
            this.markerParam.filterData.forEach((el) => {
                let po = turf.point([el.JD, el.WD], el);
                arr.push(po);
            });
            let points = turf.featureCollection(arr);
            let filterData = turf.pointsWithinPolygon(
                points,
                geoarr[geoarr.length - 1]
            );
            console.log(filterData);
            this.addwry({ layerId: '污染源' }, filterData);
        },
        addZBFXfw() {
            let center = turf.point([
                parseFloat(this.markerParam.centralPoint[0].JD),
                parseFloat(this.markerParam.centralPoint[0].WD)
            ]);
            let bearing1 = 0;
            let geoarr = [];
            let geoarrpoint = [];
            let getColor = PowerGL.segmentedColorScale([
                [0, [191, 136, 0]],
                [50, [201, 141, 12]],
                [100, [204, 159, 90]]
            ]);

            let getColor2 = PowerGL.segmentedColorScale([
                [0, [247, 8, 3]],
                [50, [247, 123, 18]],
                [100, [215, 187, 55]]
            ]);

            this.markerParam.distanc.forEach((radius) => {
                let color = getColor(
                    radius,
                    0.5,
                    this.markerParam.distanc[0],
                    this.markerParam.distanc[
                        this.markerParam.distanc.length - 1
                    ]
                );

                // let sector = turf.sector(center, radius, bearing1, bearing2, {
                //     properties: { color: color }
                // });
                let buffer = turf.buffer(center, radius, {
                    properties: { color: color }
                });
                buffer.properties.color = color;

                // let buffer = turf.sector(center, radius, bearing1, bearing2, {
                //     properties: { color: color }
                // });
                geoarr.push(buffer);
                let color2 = getColor2(
                    radius,
                    1,
                    this.markerParam.distanc[0],
                    this.markerParam.distanc[
                        this.markerParam.distanc.length - 1
                    ]
                );
                let options = {
                    units: 'kilometers',
                    properties: { name: radius + '公里', color: color2 }
                };
                let destination = turf.destination(
                    center,
                    radius,
                    bearing1,
                    options
                );
                geoarrpoint.push(destination);
            });

            this.addPolygonSymbol(geoarr);

            // 定位到该范围
            const bound = turf.bbox(geoarr[geoarr.length - 1]);
            let option = {
                pitch: 0,
                padding: {
                    top: 100,
                    bottom: 100,
                    left: 200,
                    right: 100
                },
                // offset: [40, 0],
                duration: 2000, //飞行总时长，单位ms
                easing: (t) => {
                    return t;
                }
            };
            this.map.fitBounds(bound, option);

            this.addGisData({ layerId: '距离文字' }, geoarrpoint);

            let arr = [];
            this.markerParam.filterData.forEach((el) => {
                let po = turf.point([el.JD, el.WD], el);
                arr.push(po);
            });
            let points = turf.featureCollection(arr);
            let filterData = turf.pointsWithinPolygon(
                points,
                geoarr[geoarr.length - 1]
            );

            this.addwry({ layerId: '污染源' }, filterData);
        },
        addwry(params, data) {
            this.removeLayerFromName(params.layerId);
            this.map.addLayer({
                id: params.layerId,
                type: 'symbol',
                source: {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: data.features
                    }
                },
                layout: {
                    'icon-image': '污染源',
                    'icon-size': 1,
                    'icon-allow-overlap': true
                }
            });

            this.map.on('mouseenter', params.layerId, (evt) => {
                window.glTooltip && window.glTooltip.remove();
                let item = evt.features[0];
                let attr = item.properties;
                let lnglat = evt.lngLat;
                let title = attr.WRYMC || attr.Name || attr.CDMC;

                let content = `<span id="gis-marker" style="font: normal normal bold 20px/26px Microsoft YaHei">${title}</span>`;
                window.glTooltip = new mapboxgl.Popup({
                    className: 'mapbox-tooltip',
                    closeOnClick: false,
                    closeButton: false
                })
                    .setOffset([0, -20])
                    .setLngLat(lnglat)
                    .setHTML(content)
                    .setMaxWidth('none')
                    .addTo(this.map);
            });
            this.map.on('mouseleave', params.layerId, (e) => {
                window.glTooltip && window.glTooltip.remove();
                let canvas = this.map.getCanvas();
                canvas.style.cursor = 'default';
            });
        },
        removeLayerFromName(name) {
            PowerGL.removeLayerFromName(this.map, name);
        },
        addGisData(params, data) {
            let _this = this;

            this.removeMarkerByLayerId(params.layerId);

            let effectArr = [];
            data.map((item, index) => {
                item.JD = item.geometry.coordinates[0];
                item.WD = item.geometry.coordinates[1];

                let lnglat = [item.JD, item.WD];
                if (parseFloat(item.JD) < parseFloat(item.WD)) {
                    lnglat = [item.WD, item.JD];
                }

                let el = document.createElement('div');
                el.className = 'area-marker';

                el.innerHTML = `<p style='    background: rgba(${item.properties.color});
    color: #fff;
    padding: 2px 5px;'>${item.properties.name}</p>`;

                el.style.cursor = 'pointer';

                let marker = new mapboxgl.Marker(el).setLngLat(lnglat);
                marker.layerId = params.layerId;
                this.markers.push(marker);

                marker.addTo(_this.map);
            });
        },
        removeMarkerByLayerId(layerId) {
            this.markers = this.markers.filter((marker) => {
                if (layerId == marker.layerId) {
                    marker.remove();
                    return false;
                }
                return true;
            });
        },
        addPolygonSymbol(data) {
            let layerId = '缓冲范围';
            this.removeLayerFromName(layerId);
            this.map.addLayer({
                id: layerId,
                type: 'fill',
                source: {
                    type: 'geojson',
                    data: {
                        type: 'FeatureCollection',
                        features: data
                    }
                },
                layout: {},
                paint: {
                    'fill-color': ['get', 'color'], //直接赋值颜色
                    'fill-opacity': 0.5,
                    'fill-outline-color': '#ffffff'
                }
            });
        },

        addAirPoint() {
            let _this = this;
            this.clear();
            for (let item of this.markerParam.centralPoint) {
                if (
                    !item.JD ||
                    isNaN(parseFloat(item.JD)) ||
                    !item.WD ||
                    isNaN(parseFloat(item.WD))
                ) {
                    return;
                }

                let lnglat = [parseFloat(item.JD), parseFloat(item.WD)];

                let el = this.getContent(item);
                // el.innerHTML = `<dl style="width:${width}; height:103px" class="pd-dlpin-air dj${dj}"><dt>${item.CDMC}&emsp;${data}</dt><dd></dd></dl>`;
                // if (!this.markerParam.showAno) {
                //     el.innerHTML = `<dl class="pd-dlpin-air dj${dj}"><dt>${data}</dt><dd></dd></dl>`;
                // }

                el.addEventListener('mouseenter', function (e) {
                    if (window.glTooltip) {
                        window.glTooltip.remove();
                    }

                    let description =
                        `<span style="font: normal normal bold 20px/26px Microsoft YaHei">` +
                        item.CDMC +
                        `</span>`;

                    window.glTooltip = new mapboxgl.Popup({
                        className: 'mapbox-tooltip',
                        closeOnClick: false,
                        closeButton: false
                    })
                        .setOffset([0, -30])
                        .setLngLat(lnglat)
                        .setHTML(description)
                        .setMaxWidth('none')
                        .addTo(_this.map);
                    _this.map.triggerRepaint();
                });
                el.addEventListener('mouseleave', function (e) {
                    window.glTooltip.remove();
                });

                el.addEventListener('click', function (e) {
                    _this.$emit(this.type, item);
                    e.preventDefault();
                });

                let marker = new mapboxgl.Marker(el)
                    .setLngLat(lnglat)
                    .addTo(this.map);

                this.areaMarkers.push(marker);
            }
        },
        getContent(item) {
            let level = PowerGL.getLevelByWrw(this.markerParam.yz, item, 1);
            let dj = PowerGL.getAirDJByLevel(level);
            let data = item[this.markerParam.yz]
                ? item[this.markerParam.yz]
                : '--';
            let wid = 18 * item.CDMC.length;
            let el = document.createElement('div');
            el.className = 'area-marker';
            let width = 80 + wid + 'px';
            el.innerHTML = ``;
            if (this.markerParam.centraltype == '大气站点1') {
                if (this.markerParam.FJZS) {
                    el.className = 'location';
                    if (this.pointSize == 'min') {
                        el.innerHTML = `
                  <div class="airLevel dj${dj}">${data}</div>
              `;
                    } else {
                        let marginLeft = (45 - wid) / 2;
                        el.innerHTML = ` <div class="airLevel dj${dj}">${data}</div><div class="mc" style="width:${wid}px;margin-left:${marginLeft}px">${item.CDMC}</div>`;
                    }
                } else {
                    el.className = 'location';
                    el.innerHTML = `
                  <div class="airLevel dj${dj}">${data}</div>
              `;
                    if (this.markerParam.showAno) {
                        let marginLeft = (45 - wid) / 2;
                        el.innerHTML = ` <div class="airLevel dj${dj}">${data}</div><div class="mc" style="width:${wid}px;margin-left:${marginLeft}px">${item.CDMC}</div>`;
                    }
                }
            } else if (this.markerParam.centraltype == '大气站点2') {
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        el.innerHTML = `<dl class="pd-dlpin-air dj${dj}"><dt>${data}</dt><dd></dd></dl>`;
                    } else {
                        el.innerHTML = `<dl style="width:${width}; height:103px" class="pd-dlpin-air dj${dj}"><dt>${item.CDMC}&emsp;${data}</dt><dd></dd></dl>`;
                    }
                } else {
                    el.innerHTML = `<dl style="width:${width}; height:103px" class="pd-dlpin-air dj${dj}"><dt>${item.CDMC}&emsp;${data}</dt><dd></dd></dl>`;

                    if (!this.markerParam.showAno) {
                        el.innerHTML = `<dl class="pd-dlpin-air dj${dj}"><dt>${data}</dt><dd></dd></dl>`;
                    }
                }
            } else if (this.markerParam.centraltype == '大气站点3') {
                let djtext = this.getDJTextByDJ(dj);
                let color = PowerGL.getAirColorByLevel(level);
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        el.innerHTML = `<div class="sw19-dwsite type${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    } else {
                        el.innerHTML = `<div class="sw19-dwsite type${dj}" >
				<h1><p>${item.CDMC}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                } else {
                    el.innerHTML = `<div class="sw19-dwsite type${dj}" >
				<h1><p>${item.CDMC}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    if (!this.markerParam.showAno) {
                        el.innerHTML = `<div class="sw19-dwsite type${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                }
            }
            return el;
        },
        getDJTextByDJ(val) {
            let str = '';
            switch (val) {
                case '0':
                    str = '无';
                    break;
                case '1':
                    str = '优';
                    break;
                case '2':
                    str = '良';
                    break;
                case '3':
                    str = '轻度';
                    break;
                case '4':
                    str = '中度';
                    break;
                case '5':
                    str = '重度';
                    break;
                case '6':
                    str = '严重';
                    break;
            }
            return str;
        },

        // 水点位
        addWaterPoint() {
            let _this = this;
            this.clear();
            this.markerParam.centralPoint.map((item, index) => {
                if (
                    !item.JD ||
                    isNaN(parseFloat(item.JD)) ||
                    !item.WD ||
                    isNaN(parseFloat(item.WD))
                ) {
                    return;
                }

                let lnglat = [parseFloat(item.JD), parseFloat(item.WD)];

                let el = this.getWaterContent(item);

                el.addEventListener('mouseenter', function (e) {
                    let name = item.pointName;
                    let description = `<div style="color:#000" > ${name} </div>`;

                    window.glTooltip = new mapboxgl.Popup({
                        className: 'mapbox-tooltip',
                        closeOnClick: false,
                        closeButton: false
                    })
                        .setOffset([0, -30])
                        .setLngLat(lnglat)
                        .setHTML(description)
                        .setMaxWidth('none')
                        .addTo(this.map);
                    this.map.triggerRepaint();
                });
                el.addEventListener('mouseleave', function (e) {
                    window.glTooltip.remove();
                });

                el.addEventListener('click', function (e) {});

                let marker = new mapboxgl.Marker(el)
                    .setLngLat(lnglat)
                    .addTo(this.map);

                this.areaMarkers.push(marker);
            });
        },
        getWaterContent(item) {
            let el = document.createElement('div');

            let dj = item.szlbbs;
            let waterQuality = item.waterQuality || '--';
            let color = PowerGL.getWaterColorByLevel(waterQuality);
            let djtext = PowerGL.getDJMCByLevel(dj + '');

            if (this.markerParam.centraltype == '水质站点') {
                if (this.markerParam.FJZS) {
                    if (this.pointSize == 'min') {
                        el.innerHTML = `<div class="sw19-dwsite type1${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    } else {
                        el.innerHTML = `<div class="sw19-dwsite type1${dj}" >
				<h1><p>${item.pointName}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                } else {
                    el.innerHTML = `<div class="sw19-dwsite type1${dj}" >
				<h1><p>${item.pointName}</p><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    if (!this.markerParam.showAno) {
                        el.innerHTML = `<div class="sw19-dwsite type1${dj}" >
				<h1><i>${djtext}</i></h1>
				<div class="dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                    }
                }
            }

            return el;
        },
        //清除
        clear() {
            for (let marker of this.areaMarkers) {
                if (marker) {
                    marker.remove();
                    marker = null;
                }
            }

            this.areaMarkers = [];
        }
    },
    watch: {
        markerParam: {
            immediate: true,
            deep: true,
            handler(val) {
                if (this.markerParam && this.markerParam.centralPoint) {
                    if (this.markerParam.visible) {
                        this.addMarkers();
                        if (this.markerParam.type == 'SFX') {
                            this.addSFXfw();
                        } else {
                            this.addZBFXfw();
                        }
                    } else {
                        this.clear();
                        for (let marker of this.markers) {
                            if (marker) {
                                marker.remove();
                                marker = null;
                            }
                        }
                        this.markers = [];
                        this.removeLayerFromName('污染源');
                        this.removeLayerFromName('缓冲范围');
                    }
                }
            }
        }
    }
};
</script>

<style>
@import '~_as/gis/commom/map3DMarker.css';
</style>
