<!-- @format -->

<template>
    <div ref="childMap" id="map" class="mapContainer"></div>
</template>

<script>
import axios from '_u/ajaxRequest';
export default {
    name: 'EsriMap',
    props: ['mapOption'],
    data() {
        return {
            map: null
        };
    },
    mounted() {
        // console.log('esri-map');

        if (GisServerGlobalConstant.needToken) {
            // "YHID": window.queryObject.YHID
            this.getToken({ YHID: 'SYSTEM' }).then((res) => {
                window.tokenKey = res.data.token;
                PowerGis.initLoad(this.createMap);
            });
        } else {
            PowerGis.initLoad(this.createMap);
        }
    },
    methods: {
        getToken(data) {
            return axios.request({
                url: GisServerGlobalConstant.tokenUrl,
                method: 'get',
                params: data
            });
        },

        //创建地图
        createMap() {
            let ext = GisServerGlobalConstant.arcgis.mapOptions.extent;
            if (this.mapOption && this.mapOption.extent) {
                Object.assign(ext, this.mapOption.extent);
            }

            let option = {
                extent: {
                    xmin: ext.xmin,
                    ymin: ext.ymin,
                    xmax: ext.xmax,
                    ymax: ext.ymax,
                    spatialReference: { wkid: ext.spatialReference.wkid }
                },
                logo: false,
                slider: false
            };

            option.minZoom =
                this.mapOption.minZoom ||
                GisServerGlobalConstant.arcgis.mapOptions.minZoom;

            option.maxZoom =
                this.mapOption.maxZoom ||
                GisServerGlobalConstant.arcgis.mapOptions.maxZoom;

            this.map = PowerGis.addMap(this.$refs.childMap, option);

            let basemaps =
                this.mapOption.basemaps ||
                GisServerGlobalConstant.arcgis.basemaps;

            for (let i = 0; i < basemaps.length; i++) {
                let mapObj = basemaps[i];
                switch (mapObj.type) {
                    case 'tiled':
                    case 'dynamic':
                        mapObj.token = window.tokenKey;
                        break;

                    default:
                        break;
                }

                PowerGis.addBasLayer(this.map, mapObj);
            }

            setTimeout(() => {
                this.$emit('onMapLoaded', this.map);
            }, 100);

            this.map.on('load', function () {
                // console.log('地图加载完成');
            });

            let This = this;
            this.map.on('click', function (evt) {
                console.log(This.map.extent);
                // console.log('地图点击事件');
            });
        }
    }
};
</script>

<style scoped>
.mapContainer {
    width: 100%;
    height: 100%;
    position: relative;
}
</style>
