/**
 * Minified by jsDelivr using Terser v5.3.5.
 * Original file: /npm/geotiff@1.0.4/dist-browser/geotiff.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
parcelRequire=function(e,t,r,n){var i,o="function"==typeof parcelRequire&&parcelRequire,a="function"==typeof require&&require;function s(r,n){if(!t[r]){if(!e[r]){var i="function"==typeof parcelRequire&&parcelRequire;if(!n&&i)return i(r,!0);if(o)return o(r,!0);if(a&&"string"==typeof r)return a(r);var u=new Error("Cannot find module '"+r+"'");throw u.code="MODULE_NOT_FOUND",u}f.resolve=function(t){return e[r][1][t]||t},f.cache={};var l=t[r]=new s.Module(r);e[r][0].call(l.exports,f,l,l.exports,this)}return t[r].exports;function f(e){return s(f.resolve(e))}}s.isParcelRequire=!0,s.Module=function(e){this.id=e,this.bundle=s,this.exports={}},s.modules=e,s.cache=t,s.parent=o,s.register=function(t,r){e[t]=[function(e,t){t.exports=r},{}]};for(var u=0;u<r.length;u++)try{s(r[u])}catch(e){i||(i=e)}if(r.length){var l=s(r[r.length-1]);"object"==typeof exports&&"undefined"!=typeof module?module.exports=l:"function"==typeof define&&define.amd?define((function(){return l})):this[n]=l}if(parcelRequire=s,i)throw i;return s}({QVnC:[function(e,t,r){var n=function(e){"use strict";var t,r=Object.prototype,n=r.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function u(e,t,r,n){var i=t&&t.prototype instanceof b?t:b,o=Object.create(i.prototype),a=new E(n||[]);return o._invoke=function(e,t,r){var n=f;return function(i,o){if(n===h)throw new Error("Generator is already running");if(n===d){if("throw"===i)throw o;return P()}for(r.method=i,r.arg=o;;){var a=r.delegate;if(a){var s=S(a,r);if(s){if(s===p)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===f)throw n=d,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=h;var u=l(e,t,r);if("normal"===u.type){if(n=r.done?d:c,u.arg===p)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n=d,r.method="throw",r.arg=u.arg)}}}(e,r,a),o}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var f="suspendedStart",c="suspendedYield",h="executing",d="completed",p={};function b(){}function y(){}function v(){}var g={};g[o]=function(){return this};var m=Object.getPrototypeOf,w=m&&m(m(C([])));w&&w!==r&&n.call(w,o)&&(g=w);var _=v.prototype=b.prototype=Object.create(g);function k(e){["next","throw","return"].forEach((function(t){e[t]=function(e){return this._invoke(t,e)}}))}function x(e,t){var r;this._invoke=function(i,o){function a(){return new t((function(r,a){!function r(i,o,a,s){var u=l(e[i],e,o);if("throw"!==u.type){var f=u.arg,c=f.value;return c&&"object"==typeof c&&n.call(c,"__await")?t.resolve(c.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(c).then((function(e){f.value=e,a(f)}),(function(e){return r("throw",e,a,s)}))}s(u.arg)}(i,o,r,a)}))}return r=r?r.then(a,a):a()}}function S(e,r){var n=e.iterator[r.method];if(n===t){if(r.delegate=null,"throw"===r.method){if(e.iterator.return&&(r.method="return",r.arg=t,S(e,r),"throw"===r.method))return p;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var i=l(n,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,p;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,p):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,p)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function C(e){if(e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,a=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}return{next:P}}function P(){return{value:t,done:!0}}return y.prototype=_.constructor=v,v.constructor=y,v[s]=y.displayName="GeneratorFunction",e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,s in e||(e[s]="GeneratorFunction")),e.prototype=Object.create(_),e},e.awrap=function(e){return{__await:e}},k(x.prototype),x.prototype[a]=function(){return this},e.AsyncIterator=x,e.async=function(t,r,n,i,o){void 0===o&&(o=Promise);var a=new x(u(t,r,n,i),o);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(_),_[s]="Generator",_[o]=function(){return this},_.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=C,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function i(n,i){return s.type="throw",s.arg=e,r.next=n,i&&(r.method="next",r.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(u&&l){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),T(r),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;T(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:C(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),p}},e}("object"==typeof t?t.exports:{});try{regeneratorRuntime=n}catch(e){Function("r","regeneratorRuntime = r")(n)}},{}],PMvg:[function(e,t,r){t.exports=e("regenerator-runtime")},{"regenerator-runtime":"QVnC"}],IxO8:[function(e,t,r){t.exports=function(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}},{}],OUZ9:[function(e,t,r){t.exports=function(e){if(Array.isArray(e))return e}},{}],vKPt:[function(e,t,r){t.exports=function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(n=(a=s.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){i=!0,o=e}finally{try{n||null==s.return||s.return()}finally{if(i)throw o}}return r}}},{}],NVR6:[function(e,t,r){t.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}},{}],UyFj:[function(e,t,r){var n=e("./arrayLikeToArray");t.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}}},{"./arrayLikeToArray":"NVR6"}],Rom6:[function(e,t,r){t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}},{}],HETk:[function(e,t,r){var n=e("./arrayWithHoles"),i=e("./iterableToArrayLimit"),o=e("./unsupportedIterableToArray"),a=e("./nonIterableRest");t.exports=function(e,t){return n(e)||i(e,t)||o(e,t)||a()}},{"./arrayWithHoles":"OUZ9","./iterableToArrayLimit":"vKPt","./unsupportedIterableToArray":"UyFj","./nonIterableRest":"Rom6"}],agGE:[function(e,t,r){function n(e,t,r,n,i,o,a){try{var s=e[o](a),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}t.exports=function(e){return function(){var t=this,r=arguments;return new Promise((function(i,o){var a=e.apply(t,r);function s(e){n(a,i,o,s,u,"next",e)}function u(e){n(a,i,o,s,u,"throw",e)}s(void 0)}))}}},{}],P8NW:[function(e,t,r){function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}t.exports=function(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}},{}],AkAO:[function(e,t,r){function n(e,r){return t.exports=n=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},n(e,r)}t.exports=n},{}],d4H2:[function(e,t,r){var n=e("./setPrototypeOf");t.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&n(e,t)}},{"./setPrototypeOf":"AkAO"}],b9XL:[function(e,t,r){function n(e){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?t.exports=n=function(e){return typeof e}:t.exports=n=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}t.exports=n},{}],E7HD:[function(e,t,r){t.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}},{}],pxk2:[function(e,t,r){var n=e("../helpers/typeof"),i=e("./assertThisInitialized");t.exports=function(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?i(e):t}},{"../helpers/typeof":"b9XL","./assertThisInitialized":"E7HD"}],UJE0:[function(e,t,r){function n(e){return t.exports=n=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},n(e)}t.exports=n},{}],vNP7:[function(e,t,r){t.exports=function(e){return-1!==Function.toString.call(e).indexOf("[native code]")}},{}],QFb6:[function(e,t,r){t.exports=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}},{}],dJhE:[function(e,t,r){var n=e("./setPrototypeOf"),i=e("./isNativeReflectConstruct");function o(e,r,a){return i()?t.exports=o=Reflect.construct:t.exports=o=function(e,t,r){var i=[null];i.push.apply(i,t);var o=new(Function.bind.apply(e,i));return r&&n(o,r.prototype),o},o.apply(null,arguments)}t.exports=o},{"./setPrototypeOf":"AkAO","./isNativeReflectConstruct":"QFb6"}],ozpi:[function(e,t,r){var n=e("./getPrototypeOf"),i=e("./setPrototypeOf"),o=e("./isNativeFunction"),a=e("./construct");function s(e){var r="function"==typeof Map?new Map:void 0;return t.exports=s=function(e){if(null===e||!o(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return a(e,arguments,n(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),i(t,e)},s(e)}t.exports=s},{"./getPrototypeOf":"UJE0","./setPrototypeOf":"AkAO","./isNativeFunction":"vNP7","./construct":"dJhE"}],fcMS:[function(e,t,r){t.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},{}],GG94:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.roundToFloat16Bits=function(e){i[0]=e;const t=o[0],r=t>>23&511;return a[r]+((8388607&t)>>s[r])},r.convertToNumber=function(e){const t=e>>10;return o[0]=u[f[t]+(1023&e)]+l[t],i[0]};const n=new ArrayBuffer(4),i=new Float32Array(n),o=new Uint32Array(n),a=new Uint32Array(512),s=new Uint32Array(512);for(let e=0;e<256;++e){const t=e-127;t<-27?(a[0|e]=0,a[256|e]=32768,s[0|e]=24,s[256|e]=24):t<-14?(a[0|e]=1024>>-t-14,a[256|e]=1024>>-t-14|32768,s[0|e]=-t-1,s[256|e]=-t-1):t<=15?(a[0|e]=t+15<<10,a[256|e]=t+15<<10|32768,s[0|e]=13,s[256|e]=13):t<128?(a[0|e]=31744,a[256|e]=64512,s[0|e]=24,s[256|e]=24):(a[0|e]=31744,a[256|e]=64512,s[0|e]=13,s[256|e]=13)}const u=new Uint32Array(2048),l=new Uint32Array(64),f=new Uint32Array(64);u[0]=0;for(let e=1;e<1024;++e){let t=e<<13,r=0;for(;0==(8388608&t);)r-=8388608,t<<=1;t&=-8388609,r+=947912704,u[e]=t|r}for(let e=1024;e<2048;++e)u[e]=939524096+(e-1024<<13);l[0]=0;for(let e=1;e<31;++e)l[e]=e<<23;l[31]=1199570944,l[32]=2147483648;for(let e=33;e<63;++e)l[e]=2147483648+(e-32<<23);l[63]=3347054592,f[0]=0;for(let e=1;e<64;++e)f[e]=32===e?0:1024},{}],G4sf:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){if(e=Number(e),!Number.isFinite(e)||0===e)return e;const t=(0,n.roundToFloat16Bits)(e);return(0,n.convertToNumber)(t)};var n=e("./lib")},{"./lib":"GG94"}],xQUP:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.ToInteger=function(e){return"number"!=typeof e&&(e=Number(e)),Number.isNaN(e)&&(e=0),Math.trunc(e)},r.defaultCompareFunction=function(e,t){const[r,n]=[Number.isNaN(e),Number.isNaN(t)];if(r&&n)return 0;if(r)return 1;if(n)return-1;if(e<t)return-1;if(e>t)return 1;if(0===e&&0===t){const[r,n]=[Object.is(e,0),Object.is(t,0)];if(!r&&n)return-1;if(r&&!n)return 1}return 0}},{}],iJ7y:[function(e,t,r){var n=arguments[3];Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var i="object"==typeof n&&n&&n.Object===Object&&n,o=i;r.default=o},{}],ygki:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_freeGlobal.js"));var i="object"==typeof self&&self&&self.Object===Object&&self,o=n.default||i||Function("return this")();r.default=o},{"./_freeGlobal.js":"iJ7y"}],Oi8n:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_root.js")).default.Symbol;r.default=n},{"./_root.js":"ygki"}],InRc:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_Symbol.js"));var i=Object.prototype,o=i.hasOwnProperty,a=i.toString,s=n.default?n.default.toStringTag:void 0;var u=function(e){var t=o.call(e,s),r=e[s];try{e[s]=void 0;var n=!0}catch(e){}var i=a.call(e);return n&&(t?e[s]=r:delete e[s]),i};r.default=u},{"./_Symbol.js":"Oi8n"}],iwg4:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=Object.prototype.toString;var i=function(e){return n.call(e)};r.default=i},{}],svpX:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=a(e("./_Symbol.js")),i=a(e("./_getRawTag.js")),o=a(e("./_objectToString.js"));function a(e){return e&&e.__esModule?e:{default:e}}var s=n.default?n.default.toStringTag:void 0;var u=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":s&&s in Object(e)?(0,i.default)(e):(0,o.default)(e)};r.default=u},{"./_Symbol.js":"Oi8n","./_getRawTag.js":"InRc","./_objectToString.js":"iwg4"}],UKR9:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return null!=e&&"object"==typeof e};r.default=n},{}],MYZg:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=o(e("./_baseGetTag.js")),i=o(e("./isObjectLike.js"));function o(e){return e&&e.__esModule?e:{default:e}}var a=function(e){return(0,i.default)(e)&&"[object ArrayBuffer]"==(0,n.default)(e)};r.default=a},{"./_baseGetTag.js":"svpX","./isObjectLike.js":"UKR9"}],pmj6:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return function(t){return e(t)}};r.default=n},{}],oPFz:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_freeGlobal.js"));var i="object"==typeof r&&r&&!r.nodeType&&r,o=i&&"object"==typeof t&&t&&!t.nodeType&&t,a=o&&o.exports===i&&n.default.process,s=function(){try{return o&&o.require&&o.require("util").types||a&&a.binding&&a.binding("util")}catch(e){}}();r.default=s},{"./_freeGlobal.js":"iJ7y"}],rXXV:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=a(e("./_baseIsArrayBuffer.js")),i=a(e("./_baseUnary.js")),o=a(e("./_nodeUtil.js"));function a(e){return e&&e.__esModule?e:{default:e}}var s=o.default&&o.default.isArrayBuffer,u=s?(0,i.default)(s):n.default;r.default=u},{"./_baseIsArrayBuffer.js":"MYZg","./_baseUnary.js":"pmj6","./_nodeUtil.js":"oPFz"}],XYHx:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.isDataView=function(e){return e instanceof DataView},r.isStringNumberKey=function(e){return"string"==typeof e&&e===(0,n.ToInteger)(e)+""},Object.defineProperty(r,"isArrayBuffer",{enumerable:!0,get:function(){return i.default}});var n=e("./spec"),i=function(e){return e&&e.__esModule?e:{default:e}}(e("lodash-es/isArrayBuffer"))},{"./spec":"xQUP","lodash-es/isArrayBuffer":"rXXV"}],EEXG:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.createPrivateStorage=function(){const e=new WeakMap;return t=>{let r=e.get(t);return r||(r=Object.create(null),e.set(t,r),r)}}},{}],DTZk:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};r.default=n},{}],tWvT:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=o(e("./_baseGetTag.js")),i=o(e("./isObject.js"));function o(e){return e&&e.__esModule?e:{default:e}}var a=function(e){if(!(0,i.default)(e))return!1;var t=(0,n.default)(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t};r.default=a},{"./_baseGetTag.js":"svpX","./isObject.js":"DTZk"}],xFkS:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_root.js")).default["__core-js_shared__"];r.default=n},{"./_root.js":"ygki"}],pULC:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_coreJsData.js"));var i=function(){var e=/[^.]+$/.exec(n.default&&n.default.keys&&n.default.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();var o=function(e){return!!i&&i in e};r.default=o},{"./_coreJsData.js":"xFkS"}],UKJK:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=Function.prototype.toString;var i=function(e){if(null!=e){try{return n.call(e)}catch(e){}try{return e+""}catch(e){}}return""};r.default=i},{}],Zczf:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=s(e("./isFunction.js")),i=s(e("./_isMasked.js")),o=s(e("./isObject.js")),a=s(e("./_toSource.js"));function s(e){return e&&e.__esModule?e:{default:e}}var u=/^\[object .+?Constructor\]$/,l=Function.prototype,f=Object.prototype,c=l.toString,h=f.hasOwnProperty,d=RegExp("^"+c.call(h).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var p=function(e){return!(!(0,o.default)(e)||(0,i.default)(e))&&((0,n.default)(e)?d:u).test((0,a.default)(e))};r.default=p},{"./isFunction.js":"tWvT","./_isMasked.js":"pULC","./isObject.js":"DTZk","./_toSource.js":"UKJK"}],ZVQn:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e,t){return null==e?void 0:e[t]};r.default=n},{}],tx41:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=o(e("./_baseIsNative.js")),i=o(e("./_getValue.js"));function o(e){return e&&e.__esModule?e:{default:e}}var a=function(e,t){var r=(0,i.default)(e,t);return(0,n.default)(r)?r:void 0};r.default=a},{"./_baseIsNative.js":"Zczf","./_getValue.js":"ZVQn"}],YsDH:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=(0,function(e){return e&&e.__esModule?e:{default:e}}(e("./_getNative.js")).default)(Object,"create");r.default=n},{"./_getNative.js":"tx41"}],sJnX:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_nativeCreate.js"));var i=function(){this.__data__=n.default?(0,n.default)(null):{},this.size=0};r.default=i},{"./_nativeCreate.js":"YsDH"}],c9sB:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t};r.default=n},{}],zYTU:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_nativeCreate.js"));var i=Object.prototype.hasOwnProperty;var o=function(e){var t=this.__data__;if(n.default){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return i.call(t,e)?t[e]:void 0};r.default=o},{"./_nativeCreate.js":"YsDH"}],ZFwF:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_nativeCreate.js"));var i=Object.prototype.hasOwnProperty;var o=function(e){var t=this.__data__;return n.default?void 0!==t[e]:i.call(t,e)};r.default=o},{"./_nativeCreate.js":"YsDH"}],i3LK:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_nativeCreate.js"));var i=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=n.default&&void 0===t?"__lodash_hash_undefined__":t,this};r.default=i},{"./_nativeCreate.js":"YsDH"}],tE4I:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=u(e("./_hashClear.js")),i=u(e("./_hashDelete.js")),o=u(e("./_hashGet.js")),a=u(e("./_hashHas.js")),s=u(e("./_hashSet.js"));function u(e){return e&&e.__esModule?e:{default:e}}function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n.default,l.prototype.delete=i.default,l.prototype.get=o.default,l.prototype.has=a.default,l.prototype.set=s.default;var f=l;r.default=f},{"./_hashClear.js":"sJnX","./_hashDelete.js":"c9sB","./_hashGet.js":"zYTU","./_hashHas.js":"ZFwF","./_hashSet.js":"i3LK"}],cHCQ:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(){this.__data__=[],this.size=0};r.default=n},{}],SRq7:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e,t){return e===t||e!=e&&t!=t};r.default=n},{}],Hp84:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./eq.js"));var i=function(e,t){for(var r=e.length;r--;)if((0,n.default)(e[r][0],t))return r;return-1};r.default=i},{"./eq.js":"SRq7"}],NMSb:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_assocIndexOf.js"));var i=Array.prototype.splice;var o=function(e){var t=this.__data__,r=(0,n.default)(t,e);return!(r<0||(r==t.length-1?t.pop():i.call(t,r,1),--this.size,0))};r.default=o},{"./_assocIndexOf.js":"Hp84"}],P9vD:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_assocIndexOf.js"));var i=function(e){var t=this.__data__,r=(0,n.default)(t,e);return r<0?void 0:t[r][1]};r.default=i},{"./_assocIndexOf.js":"Hp84"}],mbsC:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_assocIndexOf.js"));var i=function(e){return(0,n.default)(this.__data__,e)>-1};r.default=i},{"./_assocIndexOf.js":"Hp84"}],GGNI:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_assocIndexOf.js"));var i=function(e,t){var r=this.__data__,i=(0,n.default)(r,e);return i<0?(++this.size,r.push([e,t])):r[i][1]=t,this};r.default=i},{"./_assocIndexOf.js":"Hp84"}],UlXa:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=u(e("./_listCacheClear.js")),i=u(e("./_listCacheDelete.js")),o=u(e("./_listCacheGet.js")),a=u(e("./_listCacheHas.js")),s=u(e("./_listCacheSet.js"));function u(e){return e&&e.__esModule?e:{default:e}}function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n.default,l.prototype.delete=i.default,l.prototype.get=o.default,l.prototype.has=a.default,l.prototype.set=s.default;var f=l;r.default=f},{"./_listCacheClear.js":"cHCQ","./_listCacheDelete.js":"NMSb","./_listCacheGet.js":"P9vD","./_listCacheHas.js":"mbsC","./_listCacheSet.js":"GGNI"}],hhFe:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=o(e("./_getNative.js")),i=o(e("./_root.js"));function o(e){return e&&e.__esModule?e:{default:e}}var a=(0,n.default)(i.default,"Map");r.default=a},{"./_getNative.js":"tx41","./_root.js":"ygki"}],S4lD:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=a(e("./_Hash.js")),i=a(e("./_ListCache.js")),o=a(e("./_Map.js"));function a(e){return e&&e.__esModule?e:{default:e}}var s=function(){this.size=0,this.__data__={hash:new n.default,map:new(o.default||i.default),string:new n.default}};r.default=s},{"./_Hash.js":"tE4I","./_ListCache.js":"UlXa","./_Map.js":"hhFe"}],yCDP:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};r.default=n},{}],QOd1:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_isKeyable.js"));var i=function(e,t){var r=e.__data__;return(0,n.default)(t)?r["string"==typeof t?"string":"hash"]:r.map};r.default=i},{"./_isKeyable.js":"yCDP"}],kigP:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_getMapData.js"));var i=function(e){var t=(0,n.default)(this,e).delete(e);return this.size-=t?1:0,t};r.default=i},{"./_getMapData.js":"QOd1"}],uJAD:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_getMapData.js"));var i=function(e){return(0,n.default)(this,e).get(e)};r.default=i},{"./_getMapData.js":"QOd1"}],h8gm:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_getMapData.js"));var i=function(e){return(0,n.default)(this,e).has(e)};r.default=i},{"./_getMapData.js":"QOd1"}],VMmx:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_getMapData.js"));var i=function(e,t){var r=(0,n.default)(this,e),i=r.size;return r.set(e,t),this.size+=r.size==i?0:1,this};r.default=i},{"./_getMapData.js":"QOd1"}],Ov7L:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=u(e("./_mapCacheClear.js")),i=u(e("./_mapCacheDelete.js")),o=u(e("./_mapCacheGet.js")),a=u(e("./_mapCacheHas.js")),s=u(e("./_mapCacheSet.js"));function u(e){return e&&e.__esModule?e:{default:e}}function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n.default,l.prototype.delete=i.default,l.prototype.get=o.default,l.prototype.has=a.default,l.prototype.set=s.default;var f=l;r.default=f},{"./_mapCacheClear.js":"S4lD","./_mapCacheDelete.js":"kigP","./_mapCacheGet.js":"uJAD","./_mapCacheHas.js":"h8gm","./_mapCacheSet.js":"VMmx"}],mNqw:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./_MapCache.js"));function i(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var a=e.apply(this,n);return r.cache=o.set(i,a)||o,a};return r.cache=new(i.Cache||n.default),r}i.Cache=n.default;var o=i;r.default=o},{"./_MapCache.js":"Ov7L"}],cSEU:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.isTypedArrayIndexedPropertyWritable=void 0;const n=Object.getOwnPropertyDescriptor(new Uint8Array(1),0).writable;r.isTypedArrayIndexedPropertyWritable=n},{}],ZpG8:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=e("./spec"),i=e("./is"),o=e("./private"),a=function(e){return e&&e.__esModule?e:{default:e}}(e("lodash-es/memoize")),s=e("./lib"),u=e("./bug");const l=(0,o.createPrivateStorage)();function f(e){return e instanceof b}function c(e){if(!f(e))throw new TypeError("This is not a Float16Array")}function h(e){const t=e.length,r=new Array(t);for(let n=0;n<t;++n)r[n]=(0,s.convertToNumber)(e[n]);return r}const d={apply:(e,t,r)=>f(t)&&function(e){return"function"==typeof e&&v.has(e)}(e)?Reflect.apply(e,l(t).target,r):Reflect.apply(e,t,r)},p={get(e,t){let r=null;if(u.isTypedArrayIndexedPropertyWritable||(e=l(r=e).target),(0,i.isStringNumberKey)(t))return Reflect.has(e,t)?(0,s.convertToNumber)(Reflect.get(e,t)):void 0;{const n=null!==r&&Reflect.has(r,t)?Reflect.get(r,t):Reflect.get(e,t);if("function"!=typeof n)return n;let i=l(n).proxy;return void 0===i&&(i=l(n).proxy=new Proxy(n,d)),i}},set(e,t,r){let n=null;return u.isTypedArrayIndexedPropertyWritable||(e=l(n=e).target),(0,i.isStringNumberKey)(t)?Reflect.set(e,t,(0,s.roundToFloat16Bits)(r)):null===n||Reflect.has(e,t)&&!Object.isFrozen(n)?Reflect.set(e,t,r):Reflect.set(n,t,r)}};u.isTypedArrayIndexedPropertyWritable||(p.getPrototypeOf=e=>Reflect.getPrototypeOf(l(e).target),p.setPrototypeOf=(e,t)=>Reflect.setPrototypeOf(l(e).target,t),p.defineProperty=(e,t,r)=>{const n=l(e).target;return!Reflect.has(n,t)||Object.isFrozen(e)?Reflect.defineProperty(e,t,r):Reflect.defineProperty(n,t,r)},p.deleteProperty=(e,t)=>{const r=l(e).target;return Reflect.has(e,t)?Reflect.deleteProperty(e,t):Reflect.deleteProperty(r,t)},p.has=(e,t)=>Reflect.has(e,t)||Reflect.has(l(e).target,t),p.isExtensible=e=>Reflect.isExtensible(e),p.preventExtensions=e=>Reflect.preventExtensions(e),p.getOwnPropertyDescriptor=(e,t)=>Reflect.getOwnPropertyDescriptor(e,t),p.ownKeys=e=>Reflect.ownKeys(e));class b extends Uint16Array{constructor(e,t,r){if(f(e))super(l(e).target);else if(null===e||"object"!=typeof e||(0,i.isArrayBuffer)(e))switch(arguments.length){case 0:super();break;case 1:super(e);break;case 2:super(e,t);break;case 3:super(e,t,r);break;default:super(...arguments)}else{const t=Reflect.has(e,"length")||void 0===e[Symbol.iterator]?e:[...e],r=t.length;super(r);for(let e=0;e<r;++e)this[e]=(0,s.roundToFloat16Bits)(t[e])}let n;if(u.isTypedArrayIndexedPropertyWritable)n=new Proxy(this,p);else{const e=Object.create(null);l(e).target=this,n=new Proxy(e,p)}return l(n).target=this,l(this).proxy=n,n}static from(e,...t){if(0===t.length)return new b(Uint16Array.from(e,s.roundToFloat16Bits).buffer);const r=t[0],n=t[1];return new b(Uint16Array.from(e,(function(e,...t){return(0,s.roundToFloat16Bits)(r.call(this,e,...t))}),n).buffer)}static of(...e){return new b(e)}*[Symbol.iterator](){for(const e of super[Symbol.iterator]())yield(0,s.convertToNumber)(e)}keys(){return super.keys()}*values(){for(const e of super.values())yield(0,s.convertToNumber)(e)}*entries(){for(const[e,t]of super.entries())yield[e,(0,s.convertToNumber)(t)]}map(e,...t){c(this);const r=t[0],n=[];for(let t=0,i=this.length;t<i;++t){const i=(0,s.convertToNumber)(this[t]);n.push(e.call(r,i,t,l(this).proxy))}return new b(n)}filter(e,...t){c(this);const r=t[0],n=[];for(let t=0,i=this.length;t<i;++t){const i=(0,s.convertToNumber)(this[t]);e.call(r,i,t,l(this).proxy)&&n.push(i)}return new b(n)}reduce(e,...t){let r,n;c(this),0===t.length?(r=(0,s.convertToNumber)(this[0]),n=1):(r=t[0],n=0);for(let t=n,i=this.length;t<i;++t)r=e(r,(0,s.convertToNumber)(this[t]),t,l(this).proxy);return r}reduceRight(e,...t){let r,n;c(this);const i=this.length;0===t.length?(r=(0,s.convertToNumber)(this[i-1]),n=i-1):(r=t[0],n=i);for(let t=n;t--;)r=e(r,(0,s.convertToNumber)(this[t]),t,l(this).proxy);return r}forEach(e,...t){c(this);const r=t[0];for(let t=0,n=this.length;t<n;++t)e.call(r,(0,s.convertToNumber)(this[t]),t,l(this).proxy)}find(e,...t){c(this);const r=t[0];for(let t=0,n=this.length;t<n;++t){const n=(0,s.convertToNumber)(this[t]);if(e.call(r,n,t,l(this).proxy))return n}}findIndex(e,...t){c(this);const r=t[0];for(let t=0,n=this.length;t<n;++t){const n=(0,s.convertToNumber)(this[t]);if(e.call(r,n,t,l(this).proxy))return t}return-1}every(e,...t){c(this);const r=t[0];for(let t=0,n=this.length;t<n;++t)if(!e.call(r,(0,s.convertToNumber)(this[t]),t,l(this).proxy))return!1;return!0}some(e,...t){c(this);const r=t[0];for(let t=0,n=this.length;t<n;++t)if(e.call(r,(0,s.convertToNumber)(this[t]),t,l(this).proxy))return!0;return!1}set(e,...t){c(this);const r=t[0];let n;if(f(e))n=l(e).target;else{const t=Reflect.has(e,"length")||void 0===e[Symbol.iterator]?e:[...e],r=t.length;n=new Uint16Array(r);for(let e=0,r=t.length;e<r;++e)n[e]=(0,s.roundToFloat16Bits)(t[e])}super.set(n,r)}reverse(){return c(this),super.reverse(),l(this).proxy}fill(e,...t){return c(this),super.fill((0,s.roundToFloat16Bits)(e),...t),l(this).proxy}copyWithin(e,t,...r){return c(this),super.copyWithin(e,t,...r),l(this).proxy}sort(...e){c(this);let t=e[0];void 0===t&&(t=n.defaultCompareFunction);const r=(0,a.default)(s.convertToNumber);return super.sort(((e,n)=>t(r(e),r(n)))),l(this).proxy}slice(...e){let t;c(this);try{t=super.slice(...e)}catch(r){if(!(r instanceof TypeError))throw r;t=new Uint16Array(this.buffer,this.byteOffset,this.length).slice(...e)}return new b(t.buffer)}subarray(...e){let t;c(this);try{t=super.subarray(...e)}catch(r){if(!(r instanceof TypeError))throw r;t=new Uint16Array(this.buffer,this.byteOffset,this.length).subarray(...e)}return new b(t.buffer,t.byteOffset,t.length)}indexOf(e,...t){c(this);const r=this.length;let i=(0,n.ToInteger)(t[0]);i<0&&(i+=r)<0&&(i=0);for(let t=i,n=r;t<n;++t)if((0,s.convertToNumber)(this[t])===e)return t;return-1}lastIndexOf(e,...t){c(this);const r=this.length;let i=(0,n.ToInteger)(t[0]);(i=0===i?r:i+1)>=0?i=i<r?i:r:i+=r;for(let t=i;t--;)if((0,s.convertToNumber)(this[t])===e)return t;return-1}includes(e,...t){c(this);const r=this.length;let i=(0,n.ToInteger)(t[0]);i<0&&(i+=r)<0&&(i=0);const o=Number.isNaN(e);for(let t=i,n=r;t<n;++t){const r=(0,s.convertToNumber)(this[t]);if(o&&Number.isNaN(r))return!0;if(r===e)return!0}return!1}join(...e){return c(this),h(this).join(...e)}toLocaleString(...e){return c(this),h(this).toLocaleString(...e)}get[Symbol.toStringTag](){if(f(this))return"Float16Array"}}r.default=b;const y=b.prototype,v=new WeakSet;for(const e of Reflect.ownKeys(y)){const t=y[e];"function"==typeof t&&v.add(t)}},{"./spec":"xQUP","./is":"XYHx","./private":"EEXG","lodash-es/memoize":"mNqw","./lib":"GG94","./bug":"cSEU"}],q489:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.getFloat16=function(e,t,...r){if(!(0,n.isDataView)(e))throw new TypeError("First argument to getFloat16 function must be a DataView");return(0,i.convertToNumber)(e.getUint16(t,...r))},r.setFloat16=function(e,t,r,...o){if(!(0,n.isDataView)(e))throw new TypeError("First argument to setFloat16 function must be a DataView");e.setUint16(t,(0,i.roundToFloat16Bits)(r),...o)};var n=e("./is"),i=e("./lib")},{"./is":"XYHx","./lib":"GG94"}],Stm8:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"hfround",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(r,"Float16Array",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(r,"getFloat16",{enumerable:!0,get:function(){return o.getFloat16}}),Object.defineProperty(r,"setFloat16",{enumerable:!0,get:function(){return o.setFloat16}});var n=a(e("./hfround")),i=a(e("./Float16Array")),o=e("./dataView.js");function a(e){return e&&e.__esModule?e:{default:e}}},{"./hfround":"G4sf","./Float16Array":"ZpG8","./dataView.js":"q489"}],rDCW:[function(e,t,r){},{}],FRpO:[function(e,t,r){"use strict";var n,i="object"==typeof Reflect?Reflect:null,o=i&&"function"==typeof i.apply?i.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};n=i&&"function"==typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var a=Number.isNaN||function(e){return e!=e};function s(){s.init.call(this)}t.exports=s,s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var u=10;function l(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function f(e){return void 0===e._maxListeners?s.defaultMaxListeners:e._maxListeners}function c(e,t,r,n){var i,o,a;if(l(r),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),o=e._events),a=o[t]),void 0===a)a=o[t]=r,++e._eventsCount;else if("function"==typeof a?a=o[t]=n?[r,a]:[a,r]:n?a.unshift(r):a.push(r),(i=f(e))>0&&a.length>i&&!a.warned){a.warned=!0;var s=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");s.name="MaxListenersExceededWarning",s.emitter=e,s.type=t,s.count=a.length,function(e){console&&console.warn&&console.warn(e)}(s)}return e}function h(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=h.bind(n);return i.listener=r,n.wrapFn=i,i}function p(e,t,r){var n=e._events;if(void 0===n)return[];var i=n[t];return void 0===i?[]:"function"==typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):y(i,i.length)}function b(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function y(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return u},set:function(e){if("number"!=typeof e||e<0||a(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");u=e}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||a(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},s.prototype.getMaxListeners=function(){return f(this)},s.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var n="error"===e,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var s=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw s.context=a,s}var u=i[e];if(void 0===u)return!1;if("function"==typeof u)o(u,this,t);else{var l=u.length,f=y(u,l);for(r=0;r<l;++r)o(f[r],this,t)}return!0},s.prototype.addListener=function(e,t){return c(this,e,t,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(e,t){return c(this,e,t,!0)},s.prototype.once=function(e,t){return l(t),this.on(e,d(this,e,t)),this},s.prototype.prependOnceListener=function(e,t){return l(t),this.prependListener(e,d(this,e,t)),this},s.prototype.removeListener=function(e,t){var r,n,i,o,a;if(l(t),void 0===(n=this._events))return this;if(void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(i=-1,o=r.length-1;o>=0;o--)if(r[o]===t||r[o].listener===t){a=r[o].listener,i=o;break}if(i<0)return this;0===i?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,i),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",e,a||t)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(e){var t,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var i,o=Object.keys(r);for(n=0;n<o.length;++n)"removeListener"!==(i=o[n])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},s.prototype.listeners=function(e){return p(this,e,!0)},s.prototype.rawListeners=function(e){return p(this,e,!1)},s.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):b.call(e,t)},s.prototype.listenerCount=b,s.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]}},{}],ExO1:[function(e,t,r){t.exports=e("events").EventEmitter},{events:"FRpO"}],yh9p:[function(e,t,r){"use strict";r.byteLength=function(e){var t=l(e),r=t[0],n=t[1];return 3*(r+n)/4-n},r.toByteArray=function(e){var t,r,n=l(e),a=n[0],s=n[1],u=new o(function(e,t,r){return 3*(t+r)/4-r}(0,a,s)),f=0,c=s>0?a-4:a;for(r=0;r<c;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],u[f++]=t>>16&255,u[f++]=t>>8&255,u[f++]=255&t;return 2===s&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,u[f++]=255&t),1===s&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,u[f++]=t>>8&255,u[f++]=255&t),u},r.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],a=0,s=r-i;a<s;a+=16383)o.push(c(e,a,a+16383>s?s:a+16383));return 1===i?(t=e[r-1],o.push(n[t>>2]+n[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],o.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"=")),o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,u=a.length;s<u;++s)n[s]=a[s],i[a.charCodeAt(s)]=s;function l(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function f(e){return n[e>>18&63]+n[e>>12&63]+n[e>>6&63]+n[63&e]}function c(e,t,r){for(var n,i=[],o=t;o<r;o+=3)n=(e[o]<<16&16711680)+(e[o+1]<<8&65280)+(255&e[o+2]),i.push(f(n));return i.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},{}],JgNJ:[function(e,t,r){r.read=function(e,t,r,n,i){var o,a,s=8*i-n-1,u=(1<<s)-1,l=u>>1,f=-7,c=r?i-1:0,h=r?-1:1,d=e[t+c];for(c+=h,o=d&(1<<-f)-1,d>>=-f,f+=s;f>0;o=256*o+e[t+c],c+=h,f-=8);for(a=o&(1<<-f)-1,o>>=-f,f+=n;f>0;a=256*a+e[t+c],c+=h,f-=8);if(0===o)o=1-l;else{if(o===u)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,n),o-=l}return(d?-1:1)*a*Math.pow(2,o-n)},r.write=function(e,t,r,n,i,o){var a,s,u,l=8*o-i-1,f=(1<<l)-1,c=f>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:o-1,p=n?1:-1,b=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=f):(a=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-a))<1&&(a--,u*=2),(t+=a+c>=1?h/u:h*Math.pow(2,1-c))*u>=2&&(a++,u/=2),a+c>=f?(s=0,a=f):a+c>=1?(s=(t*u-1)*Math.pow(2,i),a+=c):(s=t*Math.pow(2,c-1)*Math.pow(2,i),a=0));i>=8;e[r+d]=255&s,d+=p,s/=256,i-=8);for(a=a<<i|s,l+=i;l>0;e[r+d]=255&a,d+=p,a/=256,l-=8);e[r+d-p]|=128*b}},{}],REa7:[function(e,t,r){var n={}.toString;t.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},{}],dskh:[function(e,t,r){var n=arguments[3],i=e("base64-js"),o=e("ieee754"),a=e("isarray");function s(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}function u(){return f.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function l(e,t){if(u()<t)throw new RangeError("Invalid typed array length");return f.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=f.prototype:(null===e&&(e=new f(t)),e.length=t),e}function f(e,t,r){if(!(f.TYPED_ARRAY_SUPPORT||this instanceof f))return new f(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return p(this,e)}return c(this,e,t,r)}function c(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?v(e,t,r,n):"string"==typeof t?b(e,t,r):g(e,t)}function h(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function d(e,t,r,n){return h(t),t<=0?l(e,t):void 0!==r?"string"==typeof n?l(e,t).fill(r,n):l(e,t).fill(r):l(e,t)}function p(e,t){if(h(t),e=l(e,t<0?0:0|m(t)),!f.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function b(e,t,r){if("string"==typeof r&&""!==r||(r="utf8"),!f.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|_(t,r),i=(e=l(e,n)).write(t,r);return i!==n&&(e=e.slice(0,i)),e}function y(e,t){var r=t.length<0?0:0|m(t.length);e=l(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function v(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");return t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n),f.TYPED_ARRAY_SUPPORT?(e=t).__proto__=f.prototype:e=y(e,t),e}function g(e,t){if(f.isBuffer(t)){var r=0|m(t.length);return 0===(e=l(e,r)).length||t.copy(e,0,0,r),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||re(t.length)?l(e,0):y(e,t);if("Buffer"===t.type&&a(t.data))return y(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function m(e){if(e>=u())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+u().toString(16)+" bytes");return 0|e}function w(e){return+e!=e&&(e=0),f.alloc(+e)}function _(e,t){if(f.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return Q(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return ee(e).length;default:if(n)return Q(e).length;t=(""+t).toLowerCase(),n=!0}}function k(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return F(this,t,r);case"utf8":case"utf-8":return A(this,t,r);case"ascii":return L(this,t,r);case"latin1":case"binary":return N(this,t,r);case"base64":return M(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function x(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function S(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=f.from(t,n)),f.isBuffer(t))return 0===t.length?-1:O(e,t,r,n,i);if("number"==typeof t)return t&=255,f.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):O(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function O(e,t,r,n,i){var o,a=1,s=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;a=2,s/=2,u/=2,r/=2}function l(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(i){var f=-1;for(o=r;o<s;o++)if(l(e,o)===l(t,-1===f?0:o-f)){if(-1===f&&(f=o),o-f+1===u)return f*a}else-1!==f&&(o-=o-f),f=-1}else for(r+u>s&&(r=s-u),o=r;o>=0;o--){for(var c=!0,h=0;h<u;h++)if(l(e,o+h)!==l(t,h)){c=!1;break}if(c)return o}return-1}function T(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var a=0;a<n;++a){var s=parseInt(t.substr(2*a,2),16);if(isNaN(s))return a;e[r+a]=s}return a}function E(e,t,r,n){return te(Q(t,e.length-r),e,r,n)}function C(e,t,r,n){return te(X(t),e,r,n)}function P(e,t,r,n){return C(e,t,r,n)}function j(e,t,r,n){return te(ee(t),e,r,n)}function R(e,t,r,n){return te($(t,e.length-r),e,r,n)}function M(e,t,r){return 0===t&&r===e.length?i.fromByteArray(e):i.fromByteArray(e.slice(t,r))}function A(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,a,s,u,l=e[i],f=null,c=l>239?4:l>223?3:l>191?2:1;if(i+c<=r)switch(c){case 1:l<128&&(f=l);break;case 2:128==(192&(o=e[i+1]))&&(u=(31&l)<<6|63&o)>127&&(f=u);break;case 3:o=e[i+1],a=e[i+2],128==(192&o)&&128==(192&a)&&(u=(15&l)<<12|(63&o)<<6|63&a)>2047&&(u<55296||u>57343)&&(f=u);break;case 4:o=e[i+1],a=e[i+2],s=e[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&(u=(15&l)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&u<1114112&&(f=u)}null===f?(f=65533,c=1):f>65535&&(f-=65536,n.push(f>>>10&1023|55296),f=56320|1023&f),n.push(f),i+=c}return D(n)}r.Buffer=f,r.SlowBuffer=w,r.INSPECT_MAX_BYTES=50,f.TYPED_ARRAY_SUPPORT=void 0!==n.TYPED_ARRAY_SUPPORT?n.TYPED_ARRAY_SUPPORT:s(),r.kMaxLength=u(),f.poolSize=8192,f._augment=function(e){return e.__proto__=f.prototype,e},f.from=function(e,t,r){return c(null,e,t,r)},f.TYPED_ARRAY_SUPPORT&&(f.prototype.__proto__=Uint8Array.prototype,f.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&f[Symbol.species]===f&&Object.defineProperty(f,Symbol.species,{value:null,configurable:!0})),f.alloc=function(e,t,r){return d(null,e,t,r)},f.allocUnsafe=function(e){return p(null,e)},f.allocUnsafeSlow=function(e){return p(null,e)},f.isBuffer=function(e){return!(null==e||!e._isBuffer)},f.compare=function(e,t){if(!f.isBuffer(e)||!f.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},f.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(e,t){if(!a(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return f.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=f.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(!f.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},f.byteLength=_,f.prototype._isBuffer=!0,f.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)x(this,t,t+1);return this},f.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)x(this,t,t+3),x(this,t+1,t+2);return this},f.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)x(this,t,t+7),x(this,t+1,t+6),x(this,t+2,t+5),x(this,t+3,t+4);return this},f.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?A(this,0,e):k.apply(this,arguments)},f.prototype.equals=function(e){if(!f.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===f.compare(this,e)},f.prototype.inspect=function(){var e="",t=r.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},f.prototype.compare=function(e,t,r,n,i){if(!f.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(n>>>=0),a=(r>>>=0)-(t>>>=0),s=Math.min(o,a),u=this.slice(n,i),l=e.slice(t,r),c=0;c<s;++c)if(u[c]!==l[c]){o=u[c],a=l[c];break}return o<a?-1:a<o?1:0},f.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},f.prototype.indexOf=function(e,t,r){return S(this,e,t,r,!0)},f.prototype.lastIndexOf=function(e,t,r){return S(this,e,t,r,!1)},f.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return T(this,e,t,r);case"utf8":case"utf-8":return E(this,e,t,r);case"ascii":return C(this,e,t,r);case"latin1":case"binary":return P(this,e,t,r);case"base64":return j(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var I=4096;function D(e){var t=e.length;if(t<=I)return String.fromCharCode.apply(String,e);for(var r="",n=0;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=I));return r}function L(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function N(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function F(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=J(e[o]);return i}function U(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function B(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function G(e,t,r,n,i,o){if(!f.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function z(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function H(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function W(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function q(e,t,r,n,i){return i||W(e,0,r,4),o.write(e,t,r,n,23,4),r+4}function K(e,t,r,n,i){return i||W(e,0,r,8),o.write(e,t,r,n,52,8),r+8}f.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),f.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=f.prototype;else{var i=t-e;r=new f(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},f.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||B(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},f.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||B(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},f.prototype.readUInt8=function(e,t){return t||B(e,1,this.length),this[e]},f.prototype.readUInt16LE=function(e,t){return t||B(e,2,this.length),this[e]|this[e+1]<<8},f.prototype.readUInt16BE=function(e,t){return t||B(e,2,this.length),this[e]<<8|this[e+1]},f.prototype.readUInt32LE=function(e,t){return t||B(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},f.prototype.readUInt32BE=function(e,t){return t||B(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},f.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||B(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},f.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||B(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},f.prototype.readInt8=function(e,t){return t||B(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},f.prototype.readInt16LE=function(e,t){t||B(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt16BE=function(e,t){t||B(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt32LE=function(e,t){return t||B(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},f.prototype.readInt32BE=function(e,t){return t||B(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},f.prototype.readFloatLE=function(e,t){return t||B(e,4,this.length),o.read(this,e,!0,23,4)},f.prototype.readFloatBE=function(e,t){return t||B(e,4,this.length),o.read(this,e,!1,23,4)},f.prototype.readDoubleLE=function(e,t){return t||B(e,8,this.length),o.read(this,e,!0,52,8)},f.prototype.readDoubleBE=function(e,t){return t||B(e,8,this.length),o.read(this,e,!1,52,8)},f.prototype.writeUIntLE=function(e,t,r,n){e=+e,t|=0,r|=0,n||G(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},f.prototype.writeUIntBE=function(e,t,r,n){e=+e,t|=0,r|=0,n||G(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},f.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||G(this,e,t,1,255,0),f.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},f.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||G(this,e,t,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):z(this,e,t,!0),t+2},f.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||G(this,e,t,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):z(this,e,t,!1),t+2},f.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||G(this,e,t,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):H(this,e,t,!0),t+4},f.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||G(this,e,t,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):H(this,e,t,!1),t+4},f.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);G(this,e,t,r,i-1,-i)}var o=0,a=1,s=0;for(this[t]=255&e;++o<r&&(a*=256);)e<0&&0===s&&0!==this[t+o-1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+r},f.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);G(this,e,t,r,i-1,-i)}var o=r-1,a=1,s=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===s&&0!==this[t+o+1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+r},f.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||G(this,e,t,1,127,-128),f.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},f.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||G(this,e,t,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):z(this,e,t,!0),t+2},f.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||G(this,e,t,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):z(this,e,t,!1),t+2},f.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||G(this,e,t,4,2147483647,-2147483648),f.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):H(this,e,t,!0),t+4},f.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||G(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):H(this,e,t,!1),t+4},f.prototype.writeFloatLE=function(e,t,r){return q(this,e,t,!0,r)},f.prototype.writeFloatBE=function(e,t,r){return q(this,e,t,!1,r)},f.prototype.writeDoubleLE=function(e,t,r){return K(this,e,t,!0,r)},f.prototype.writeDoubleBE=function(e,t,r){return K(this,e,t,!1,r)},f.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3||!f.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},f.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!f.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var a=f.isBuffer(e)?e:Q(new f(e,n).toString()),s=a.length;for(o=0;o<r-t;++o)this[o+t]=a[o%s]}return this};var V=/[^+\/0-9A-Za-z-_]/g;function Y(e){if((e=Z(e).replace(V,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}function Z(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function J(e){return e<16?"0"+e.toString(16):e.toString(16)}function Q(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],a=0;a<n;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(a+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function X(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function $(e,t){for(var r,n,i,o=[],a=0;a<e.length&&!((t-=2)<0);++a)n=(r=e.charCodeAt(a))>>8,i=r%256,o.push(i),o.push(n);return o}function ee(e){return i.toByteArray(Y(e))}function te(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function re(e){return e!=e}},{"base64-js":"yh9p",ieee754:"JgNJ",isarray:"REa7",buffer:"dskh"}],VCcH:[function(e,t,r){"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var s=e("buffer").Buffer,u=e("util").inspect,l=u&&u.custom||"inspect";function f(e,t,r){s.prototype.copy.call(e,t,r)}t.exports=function(){function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.head=null,this.tail=null,this.length=0}return function(e,t,r){t&&a(e.prototype,t),r&&a(e,r)}(e,[{key:"push",value:function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length}},{key:"unshift",value:function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length}},{key:"shift",value:function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r}},{key:"concat",value:function(e){if(0===this.length)return s.alloc(0);for(var t=s.allocUnsafe(e>>>0),r=this.head,n=0;r;)f(r.data,t,n),n+=r.data.length,r=r.next;return t}},{key:"consume",value:function(e,t){var r;return e<this.head.data.length?(r=this.head.data.slice(0,e),this.head.data=this.head.data.slice(e)):r=e===this.head.data.length?this.shift():t?this._getString(e):this._getBuffer(e),r}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(e){var t=this.head,r=1,n=t.data;for(e-=n.length;t=t.next;){var i=t.data,o=e>i.length?i.length:e;if(o===i.length?n+=i:n+=i.slice(0,e),0==(e-=o)){o===i.length?(++r,t.next?this.head=t.next:this.head=this.tail=null):(this.head=t,t.data=i.slice(o));break}++r}return this.length-=r,n}},{key:"_getBuffer",value:function(e){var t=s.allocUnsafe(e),r=this.head,n=1;for(r.data.copy(t),e-=r.data.length;r=r.next;){var i=r.data,o=e>i.length?i.length:e;if(i.copy(t,t.length-e,0,o),0==(e-=o)){o===i.length?(++n,r.next?this.head=r.next:this.head=this.tail=null):(this.head=r,r.data=i.slice(o));break}++n}return this.length-=n,t}},{key:l,value:function(e,t){return u(this,i({},t,{depth:0,customInspect:!1}))}}]),e}()},{buffer:"dskh",util:"rDCW"}],pBGv:[function(e,t,r){var n,i,o=t.exports={};function a(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function u(e){if(n===setTimeout)return setTimeout(e,0);if((n===a||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}function l(e){if(i===clearTimeout)return clearTimeout(e);if((i===s||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{return i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:a}catch(e){n=a}try{i="function"==typeof clearTimeout?clearTimeout:s}catch(e){i=s}}();var f,c=[],h=!1,d=-1;function p(){h&&f&&(h=!1,f.length?c=f.concat(c):d=-1,c.length&&b())}function b(){if(!h){var e=u(p);h=!0;for(var t=c.length;t;){for(f=c,c=[];++d<t;)f&&f[d].run();d=-1,t=c.length}f=null,h=!1,l(e)}}function y(e,t){this.fun=e,this.array=t}function v(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new y(e,t)),1!==c.length||h||u(b)},y.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.env={},o.argv=[],o.version="",o.versions={},o.on=v,o.addListener=v,o.once=v,o.off=v,o.removeListener=v,o.removeAllListeners=v,o.emit=v,o.prependListener=v,o.prependOnceListener=v,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},{}],GRUB:[function(e,t,r){e("process");var n=e("process");function i(e,t){a(e,t),o(e)}function o(e){e._writableState&&!e._writableState.emitClose||e._readableState&&!e._readableState.emitClose||e.emit("close")}function a(e,t){e.emit("error",t)}t.exports={destroy:function(e,t){var r=this,s=this._readableState&&this._readableState.destroyed,u=this._writableState&&this._writableState.destroyed;return s||u?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,n.nextTick(a,this,e)):n.nextTick(a,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,(function(e){!t&&e?r._writableState?r._writableState.errorEmitted?n.nextTick(o,r):(r._writableState.errorEmitted=!0,n.nextTick(i,r,e)):n.nextTick(i,r,e):t?(n.nextTick(o,r),t(e)):n.nextTick(o,r)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)},errorOrDestroy:function(e,t){var r=e._readableState,n=e._writableState;r&&r.autoDestroy||n&&n.autoDestroy?e.destroy(t):e.emit("error",t)}}},{process:"pBGv"}],Yj9o:[function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var i={};function o(e,t,r){r||(r=Error);var n=function(e){function r(r,n,i){return e.call(this,function(e,r,n){return"string"==typeof t?t:t(e,r,n)}(r,n,i))||this}return function(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}(r,e),r}(r);n.prototype.name=r.name,n.prototype.code=e,i[e]=n}function a(e,t){if(Array.isArray(e)){var r=e.length;return e=e.map((function(e){return String(e)})),r>2?"one of ".concat(t," ").concat(e.slice(0,r-1).join(", "),", or ")+e[r-1]:2===r?"one of ".concat(t," ").concat(e[0]," or ").concat(e[1]):"of ".concat(t," ").concat(e[0])}return"of ".concat(t," ").concat(String(e))}o("ERR_INVALID_OPT_VALUE",(function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'}),TypeError),o("ERR_INVALID_ARG_TYPE",(function(e,t,r){var i,o;if("string"==typeof t&&function(e,t,r){return e.substr(!r||r<0?0:+r,t.length)===t}(t,"not ")?(i="must not be",t=t.replace(/^not /,"")):i="must be",function(e,t,r){return(void 0===r||r>e.length)&&(r=e.length),e.substring(r-t.length,r)===t}(e," argument"))o="The ".concat(e," ").concat(i," ").concat(a(t,"type"));else{var s=function(e,t,r){return"number"!=typeof r&&(r=0),!(r+t.length>e.length)&&-1!==e.indexOf(t,r)}(e,".")?"property":"argument";o='The "'.concat(e,'" ').concat(s," ").concat(i," ").concat(a(t,"type"))}return o+". Received type ".concat(n(r))}),TypeError),o("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),o("ERR_METHOD_NOT_IMPLEMENTED",(function(e){return"The "+e+" method is not implemented"})),o("ERR_STREAM_PREMATURE_CLOSE","Premature close"),o("ERR_STREAM_DESTROYED",(function(e){return"Cannot call "+e+" after a stream was destroyed"})),o("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),o("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),o("ERR_STREAM_WRITE_AFTER_END","write after end"),o("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),o("ERR_UNKNOWN_ENCODING",(function(e){return"Unknown encoding: "+e}),TypeError),o("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),t.exports.codes=i},{}],TWVJ:[function(e,t,r){"use strict";var n=e("../../../errors").codes.ERR_INVALID_OPT_VALUE;t.exports={getHighWaterMark:function(e,t,r,i){var o=function(e,t,r){return null!=e.highWaterMark?e.highWaterMark:t?e[r]:null}(t,i,r);if(null!=o){if(!isFinite(o)||Math.floor(o)!==o||o<0)throw new n(i?r:"highWaterMark",o);return Math.floor(o)}return e.objectMode?16:16384}}},{"../../../errors":"Yj9o"}],Bm0n:[function(e,t,r){"function"==typeof Object.create?t.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}},{}],yM1o:[function(e,t,r){var n=arguments[3];function i(e,t){if(o("noDeprecation"))return e;var r=!1;return function(){if(!r){if(o("throwDeprecation"))throw new Error(t);o("traceDeprecation")?console.trace(t):console.warn(t),r=!0}return e.apply(this,arguments)}}function o(e){try{if(!n.localStorage)return!1}catch(e){return!1}var t=n.localStorage[e];return null!=t&&"true"===String(t).toLowerCase()}t.exports=i},{}],WSyY:[function(e,t,r){e("process");var n,i=arguments[3],o=e("process");function a(e){var t=this;this.next=null,this.entry=null,this.finish=function(){K(t,e)}}t.exports=P,P.WritableState=C;var s={deprecate:e("util-deprecate")},u=e("./internal/streams/stream"),l=e("buffer").Buffer,f=i.Uint8Array||function(){};function c(e){return l.from(e)}function h(e){return l.isBuffer(e)||e instanceof f}var d,p=e("./internal/streams/destroy"),b=e("./internal/streams/state"),y=b.getHighWaterMark,v=e("../errors").codes,g=v.ERR_INVALID_ARG_TYPE,m=v.ERR_METHOD_NOT_IMPLEMENTED,w=v.ERR_MULTIPLE_CALLBACK,_=v.ERR_STREAM_CANNOT_PIPE,k=v.ERR_STREAM_DESTROYED,x=v.ERR_STREAM_NULL_VALUES,S=v.ERR_STREAM_WRITE_AFTER_END,O=v.ERR_UNKNOWN_ENCODING,T=p.errorOrDestroy;function E(){}function C(t,r,i){n=n||e("./_stream_duplex"),t=t||{},"boolean"!=typeof i&&(i=r instanceof n),this.objectMode=!!t.objectMode,i&&(this.objectMode=this.objectMode||!!t.writableObjectMode),this.highWaterMark=y(this,t,"writableHighWaterMark",i),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var o=!1===t.decodeStrings;this.decodeStrings=!o,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){N(r,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new a(this)}function P(t){var r=this instanceof(n=n||e("./_stream_duplex"));if(!r&&!d.call(P,this))return new P(t);this._writableState=new C(t,this,r),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),u.call(this)}function j(e,t){var r=new S;T(e,r),o.nextTick(t,r)}function R(e,t,r,n){var i;return null===r?i=new x:"string"==typeof r||t.objectMode||(i=new g("chunk",["string","Buffer"],r)),!i||(T(e,i),o.nextTick(n,i),!1)}function M(e,t,r){return e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=l.from(t,r)),t}function A(e,t,r,n,i,o){if(!r){var a=M(t,n,i);n!==a&&(r=!0,i="buffer",n=a)}var s=t.objectMode?1:n.length;t.length+=s;var u=t.length<t.highWaterMark;if(u||(t.needDrain=!0),t.writing||t.corked){var l=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},l?l.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else I(e,t,!1,s,n,i,o);return u}function I(e,t,r,n,i,o,a){t.writelen=n,t.writecb=a,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new k("write")):r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function D(e,t,r,n,i){--t.pendingcb,r?(o.nextTick(i,n),o.nextTick(W,e,t),e._writableState.errorEmitted=!0,T(e,n)):(i(n),e._writableState.errorEmitted=!0,T(e,n),W(e,t))}function L(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}function N(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if("function"!=typeof i)throw new w;if(L(r),t)D(e,r,n,t,i);else{var a=G(r)||e.destroyed;a||r.corked||r.bufferProcessing||!r.bufferedRequest||B(e,r),n?o.nextTick(F,e,r,a,i):F(e,r,a,i)}}function F(e,t,r,n){r||U(e,t),t.pendingcb--,n(),W(e,t)}function U(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}function B(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),o=t.corkedRequestsFree;o.entry=r;for(var s=0,u=!0;r;)i[s]=r,r.isBuf||(u=!1),r=r.next,s+=1;i.allBuffers=u,I(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new a(t),t.bufferedRequestCount=0}else{for(;r;){var l=r.chunk,f=r.encoding,c=r.callback;if(I(e,t,!1,t.objectMode?1:l.length,l,f,c),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function G(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function z(e,t){e._final((function(r){t.pendingcb--,r&&T(e,r),t.prefinished=!0,e.emit("prefinish"),W(e,t)}))}function H(e,t){t.prefinished||t.finalCalled||("function"!=typeof e._final||t.destroyed?(t.prefinished=!0,e.emit("prefinish")):(t.pendingcb++,t.finalCalled=!0,o.nextTick(z,e,t)))}function W(e,t){var r=G(t);if(r&&(H(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"),t.autoDestroy))){var n=e._readableState;(!n||n.autoDestroy&&n.endEmitted)&&e.destroy()}return r}function q(e,t,r){t.ending=!0,W(e,t),r&&(t.finished?o.nextTick(r):e.once("finish",r)),t.ended=!0,e.writable=!1}function K(e,t,r){var n=e.entry;for(e.entry=null;n;){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree.next=e}e("inherits")(P,u),C.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(C.prototype,"buffer",{get:s.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(d=Function.prototype[Symbol.hasInstance],Object.defineProperty(P,Symbol.hasInstance,{value:function(e){return!!d.call(this,e)||this===P&&e&&e._writableState instanceof C}})):d=function(e){return e instanceof this},P.prototype.pipe=function(){T(this,new _)},P.prototype.write=function(e,t,r){var n=this._writableState,i=!1,o=!n.objectMode&&h(e);return o&&!l.isBuffer(e)&&(e=c(e)),"function"==typeof t&&(r=t,t=null),o?t="buffer":t||(t=n.defaultEncoding),"function"!=typeof r&&(r=E),n.ending?j(this,r):(o||R(this,n,e,r))&&(n.pendingcb++,i=A(this,n,o,e,t,r)),i},P.prototype.cork=function(){this._writableState.corked++},P.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||B(this,e))},P.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new O(e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(P.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(P.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),P.prototype._write=function(e,t,r){r(new m("_write()"))},P.prototype._writev=null,P.prototype.end=function(e,t,r){var n=this._writableState;return"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!=e&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||q(this,n,r),this},Object.defineProperty(P.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(P.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),P.prototype.destroy=p.destroy,P.prototype._undestroy=p.undestroy,P.prototype._destroy=function(e,t){t(e)}},{"util-deprecate":"yM1o","./internal/streams/stream":"ExO1",buffer:"dskh","./internal/streams/destroy":"GRUB","./internal/streams/state":"TWVJ","../errors":"Yj9o",inherits:"Bm0n","./_stream_duplex":"Hba0",process:"pBGv"}],Hba0:[function(e,t,r){e("process");var n=e("process"),i=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};t.exports=f;var o=e("./_stream_readable"),a=e("./_stream_writable");e("inherits")(f,o);for(var s=i(a.prototype),u=0;u<s.length;u++){var l=s[u];f.prototype[l]||(f.prototype[l]=a.prototype[l])}function f(e){if(!(this instanceof f))return new f(e);o.call(this,e),a.call(this,e),this.allowHalfOpen=!0,e&&(!1===e.readable&&(this.readable=!1),!1===e.writable&&(this.writable=!1),!1===e.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",c)))}function c(){this._writableState.ended||n.nextTick(h,this)}function h(e){e.end()}Object.defineProperty(f.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(f.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(f.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(f.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&this._readableState.destroyed&&this._writableState.destroyed},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}})},{"./_stream_readable":"DHrQ","./_stream_writable":"WSyY",inherits:"Bm0n",process:"pBGv"}],Wugr:[function(e,t,r){var n=e("buffer"),i=n.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function a(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=n:(o(n,r),r.Buffer=a),a.prototype=Object.create(i.prototype),o(i,a),a.from=function(e,t,r){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,t,r)},a.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},a.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},a.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}},{buffer:"dskh"}],z0rv:[function(e,t,r){"use strict";var n=e("safe-buffer").Buffer,i=n.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(e){var t=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(n.isEncoding===i||!i(e)))throw new Error("Unknown encoding: "+e);return t||e}function a(e){var t;switch(this.encoding=o(e),this.encoding){case"utf16le":this.text=l,this.end=f,t=4;break;case"utf8":this.fillLast=u,t=4;break;case"base64":this.text=c,this.end=h,t=3;break;default:return this.write=d,void(this.end=p)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function s(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function u(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function l(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function f(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function c(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function h(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function d(e){return e.toString(this.encoding)}function p(e){return e&&e.length?this.write(e):""}r.StringDecoder=a,a.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},a.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},a.prototype.text=function(e,t){var r=function(e,t,r){var n=t.length-1;if(n<r)return 0;var i=s(t[n]);return i>=0?(i>0&&(e.lastNeed=i-1),i):--n<r||-2===i?0:(i=s(t[n]))>=0?(i>0&&(e.lastNeed=i-2),i):--n<r||-2===i?0:(i=s(t[n]))>=0?(i>0&&(2===i?i=0:e.lastNeed=i-3),i):0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)},a.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},{"safe-buffer":"Wugr"}],mrEY:[function(e,t,r){"use strict";var n=e("../../../errors").codes.ERR_STREAM_PREMATURE_CLOSE;function i(){}t.exports=function e(t,r,o){if("function"==typeof r)return e(t,null,r);r||(r={}),o=function(e){var t=!1;return function(){if(!t){t=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];e.apply(this,n)}}}(o||i);var a=r.readable||!1!==r.readable&&t.readable,s=r.writable||!1!==r.writable&&t.writable,u=function(){t.writable||f()},l=t._writableState&&t._writableState.finished,f=function(){s=!1,l=!0,a||o.call(t)},c=t._readableState&&t._readableState.endEmitted,h=function(){a=!1,c=!0,s||o.call(t)},d=function(e){o.call(t,e)},p=function(){var e;return a&&!c?(t._readableState&&t._readableState.ended||(e=new n),o.call(t,e)):s&&!l?(t._writableState&&t._writableState.ended||(e=new n),o.call(t,e)):void 0},b=function(){t.req.on("finish",f)};return function(e){return e.setHeader&&"function"==typeof e.abort}(t)?(t.on("complete",f),t.on("abort",p),t.req?b():t.on("request",b)):s&&!t._writableState&&(t.on("end",u),t.on("close",u)),t.on("end",h),t.on("finish",f),!1!==r.error&&t.on("error",d),t.on("close",p),function(){t.removeListener("complete",f),t.removeListener("abort",p),t.removeListener("request",b),t.req&&t.req.removeListener("finish",f),t.removeListener("end",u),t.removeListener("close",u),t.removeListener("finish",f),t.removeListener("end",h),t.removeListener("error",d),t.removeListener("close",p)}}},{"../../../errors":"Yj9o"}],AOGM:[function(e,t,r){e("process");var n,i=e("process");function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var a=e("./end-of-stream"),s=Symbol("lastResolve"),u=Symbol("lastReject"),l=Symbol("error"),f=Symbol("ended"),c=Symbol("lastPromise"),h=Symbol("handlePromise"),d=Symbol("stream");function p(e,t){return{value:e,done:t}}function b(e){var t=e[s];if(null!==t){var r=e[d].read();null!==r&&(e[c]=null,e[s]=null,e[u]=null,t(p(r,!1)))}}function y(e){i.nextTick(b,e)}var v=Object.getPrototypeOf((function(){})),g=Object.setPrototypeOf((o(n={get stream(){return this[d]},next:function(){var e=this,t=this[l];if(null!==t)return Promise.reject(t);if(this[f])return Promise.resolve(p(void 0,!0));if(this[d].destroyed)return new Promise((function(t,r){i.nextTick((function(){e[l]?r(e[l]):t(p(void 0,!0))}))}));var r,n=this[c];if(n)r=new Promise(function(e,t){return function(r,n){e.then((function(){t[f]?r(p(void 0,!0)):t[h](r,n)}),n)}}(n,this));else{var o=this[d].read();if(null!==o)return Promise.resolve(p(o,!1));r=new Promise(this[h])}return this[c]=r,r}},Symbol.asyncIterator,(function(){return this})),o(n,"return",(function(){var e=this;return new Promise((function(t,r){e[d].destroy(null,(function(e){e?r(e):t(p(void 0,!0))}))}))})),n),v);t.exports=function(e){var t,r=Object.create(g,(o(t={},d,{value:e,writable:!0}),o(t,s,{value:null,writable:!0}),o(t,u,{value:null,writable:!0}),o(t,l,{value:null,writable:!0}),o(t,f,{value:e._readableState.endEmitted,writable:!0}),o(t,h,{value:function(e,t){var n=r[d].read();n?(r[c]=null,r[s]=null,r[u]=null,e(p(n,!1))):(r[s]=e,r[u]=t)},writable:!0}),t));return r[c]=null,a(e,(function(e){if(e&&"ERR_STREAM_PREMATURE_CLOSE"!==e.code){var t=r[u];return null!==t&&(r[c]=null,r[s]=null,r[u]=null,t(e)),void(r[l]=e)}var n=r[s];null!==n&&(r[c]=null,r[s]=null,r[u]=null,n(p(void 0,!0))),r[f]=!0})),e.on("readable",y.bind(null,r)),r}},{"./end-of-stream":"mrEY",process:"pBGv"}],Rr4m:[function(e,t,r){t.exports=function(){throw new Error("Readable.from is not available in the browser")}},{}],DHrQ:[function(e,t,r){e("process");var n,i=arguments[3],o=e("process");t.exports=j,j.ReadableState=P;e("events").EventEmitter;var a=function(e,t){return e.listeners(t).length},s=e("./internal/streams/stream"),u=e("buffer").Buffer,l=i.Uint8Array||function(){};function f(e){return u.from(e)}function c(e){return u.isBuffer(e)||e instanceof l}var h,d=e("util");h=d&&d.debuglog?d.debuglog("stream"):function(){};var p,b,y,v=e("./internal/streams/buffer_list"),g=e("./internal/streams/destroy"),m=e("./internal/streams/state"),w=m.getHighWaterMark,_=e("../errors").codes,k=_.ERR_INVALID_ARG_TYPE,x=_.ERR_STREAM_PUSH_AFTER_EOF,S=_.ERR_METHOD_NOT_IMPLEMENTED,O=_.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;e("inherits")(j,s);var T=g.errorOrDestroy,E=["error","close","destroy","pause","resume"];function C(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}function P(t,r,i){n=n||e("./_stream_duplex"),t=t||{},"boolean"!=typeof i&&(i=r instanceof n),this.objectMode=!!t.objectMode,i&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=w(this,t,"readableHighWaterMark",i),this.buffer=new v,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(p||(p=e("string_decoder/").StringDecoder),this.decoder=new p(t.encoding),this.encoding=t.encoding)}function j(t){if(n=n||e("./_stream_duplex"),!(this instanceof j))return new j(t);var r=this instanceof n;this._readableState=new P(t,this,r),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),s.call(this)}function R(e,t,r,n,i){h("readableAddChunk",t);var o,a=e._readableState;if(null===t)a.reading=!1,N(e,a);else if(i||(o=A(a,t)),o)T(e,o);else if(a.objectMode||t&&t.length>0)if("string"==typeof t||a.objectMode||Object.getPrototypeOf(t)===u.prototype||(t=f(t)),n)a.endEmitted?T(e,new O):M(e,a,t,!0);else if(a.ended)T(e,new x);else{if(a.destroyed)return!1;a.reading=!1,a.decoder&&!r?(t=a.decoder.write(t),a.objectMode||0!==t.length?M(e,a,t,!1):B(e,a)):M(e,a,t,!1)}else n||(a.reading=!1,B(e,a));return!a.ended&&(a.length<a.highWaterMark||0===a.length)}function M(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(t.awaitDrain=0,e.emit("data",r)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&F(e)),B(e,t)}function A(e,t){var r;return c(t)||"string"==typeof t||void 0===t||e.objectMode||(r=new k("chunk",["string","Buffer","Uint8Array"],t)),r}Object.defineProperty(j.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),j.prototype.destroy=g.destroy,j.prototype._undestroy=g.undestroy,j.prototype._destroy=function(e,t){t(e)},j.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof e&&((t=t||n.defaultEncoding)!==n.encoding&&(e=u.from(e,t),t=""),r=!0),R(this,e,t,!1,r)},j.prototype.unshift=function(e){return R(this,e,null,!0,!1)},j.prototype.isPaused=function(){return!1===this._readableState.flowing},j.prototype.setEncoding=function(t){p||(p=e("string_decoder/").StringDecoder);var r=new p(t);this._readableState.decoder=r,this._readableState.encoding=this._readableState.decoder.encoding;for(var n=this._readableState.buffer.head,i="";null!==n;)i+=r.write(n.data),n=n.next;return this._readableState.buffer.clear(),""!==i&&this._readableState.buffer.push(i),this._readableState.length=i.length,this};var I=1073741824;function D(e){return e>=I?e=I:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}function L(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=D(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function N(e,t){if(h("onEofChunk"),!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,t.sync?F(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,U(e)))}}function F(e){var t=e._readableState;h("emitReadable",t.needReadable,t.emittedReadable),t.needReadable=!1,t.emittedReadable||(h("emitReadable",t.flowing),t.emittedReadable=!0,o.nextTick(U,e))}function U(e){var t=e._readableState;h("emitReadable_",t.destroyed,t.length,t.ended),t.destroyed||!t.length&&!t.ended||(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,V(e)}function B(e,t){t.readingMore||(t.readingMore=!0,o.nextTick(G,e,t))}function G(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&0===t.length);){var r=t.length;if(h("maybeReadMore read 0"),e.read(0),r===t.length)break}t.readingMore=!1}function z(e){return function(){var t=e._readableState;h("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&a(e,"data")&&(t.flowing=!0,V(e))}}function H(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function W(e){h("readable nexttick read 0"),e.read(0)}function q(e,t){t.resumeScheduled||(t.resumeScheduled=!0,o.nextTick(K,e,t))}function K(e,t){h("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),V(e),t.flowing&&!t.reading&&e.read(0)}function V(e){var t=e._readableState;for(h("flow",t.flowing);t.flowing&&null!==e.read(););}function Y(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.first():t.buffer.concat(t.length),t.buffer.clear()):r=t.buffer.consume(e,t.decoder),r);var r}function Z(e){var t=e._readableState;h("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,o.nextTick(J,t,e))}function J(e,t){if(h("endReadableNT",e.endEmitted,e.length),!e.endEmitted&&0===e.length&&(e.endEmitted=!0,t.readable=!1,t.emit("end"),e.autoDestroy)){var r=t._writableState;(!r||r.autoDestroy&&r.finished)&&t.destroy()}}function Q(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}j.prototype.read=function(e){h("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&((0!==t.highWaterMark?t.length>=t.highWaterMark:t.length>0)||t.ended))return h("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?Z(this):F(this),null;if(0===(e=L(e,t))&&t.ended)return 0===t.length&&Z(this),null;var n,i=t.needReadable;return h("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&h("length less than watermark",i=!0),t.ended||t.reading?h("reading or ended",i=!1):i&&(h("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=L(r,t))),null===(n=e>0?Y(e,t):null)?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.awaitDrain=0),0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&Z(this)),null!==n&&this.emit("data",n),n},j.prototype._read=function(e){T(this,new S("_read()"))},j.prototype.pipe=function(e,t){var r=this,n=this._readableState;switch(n.pipesCount){case 0:n.pipes=e;break;case 1:n.pipes=[n.pipes,e];break;default:n.pipes.push(e)}n.pipesCount+=1,h("pipe count=%d opts=%j",n.pipesCount,t);var i=t&&!1===t.end||e===o.stdout||e===o.stderr?b:s;function s(){h("onend"),e.end()}n.endEmitted?o.nextTick(i):r.once("end",i),e.on("unpipe",(function t(i,o){h("onunpipe"),i===r&&o&&!1===o.hasUnpiped&&(o.hasUnpiped=!0,h("cleanup"),e.removeListener("close",d),e.removeListener("finish",p),e.removeListener("drain",u),e.removeListener("error",c),e.removeListener("unpipe",t),r.removeListener("end",s),r.removeListener("end",b),r.removeListener("data",f),l=!0,!n.awaitDrain||e._writableState&&!e._writableState.needDrain||u())}));var u=z(r);e.on("drain",u);var l=!1;function f(t){h("ondata");var i=e.write(t);h("dest.write",i),!1===i&&((1===n.pipesCount&&n.pipes===e||n.pipesCount>1&&-1!==Q(n.pipes,e))&&!l&&(h("false write response, pause",n.awaitDrain),n.awaitDrain++),r.pause())}function c(t){h("onerror",t),b(),e.removeListener("error",c),0===a(e,"error")&&T(e,t)}function d(){e.removeListener("finish",p),b()}function p(){h("onfinish"),e.removeListener("close",d),b()}function b(){h("unpipe"),r.unpipe(e)}return r.on("data",f),C(e,"error",c),e.once("close",d),e.once("finish",p),e.emit("pipe",r),n.flowing||(h("pipe resume"),r.resume()),e},j.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r)),this;if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var a=Q(t.pipes,e);return-1===a||(t.pipes.splice(a,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r)),this},j.prototype.on=function(e,t){var r=s.prototype.on.call(this,e,t),n=this._readableState;return"data"===e?(n.readableListening=this.listenerCount("readable")>0,!1!==n.flowing&&this.resume()):"readable"===e&&(n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.flowing=!1,n.emittedReadable=!1,h("on readable",n.length,n.reading),n.length?F(this):n.reading||o.nextTick(W,this))),r},j.prototype.addListener=j.prototype.on,j.prototype.removeListener=function(e,t){var r=s.prototype.removeListener.call(this,e,t);return"readable"===e&&o.nextTick(H,this),r},j.prototype.removeAllListeners=function(e){var t=s.prototype.removeAllListeners.apply(this,arguments);return"readable"!==e&&void 0!==e||o.nextTick(H,this),t},j.prototype.resume=function(){var e=this._readableState;return e.flowing||(h("resume"),e.flowing=!e.readableListening,q(this,e)),e.paused=!1,this},j.prototype.pause=function(){return h("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(h("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},j.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;for(var i in e.on("end",(function(){if(h("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on("data",(function(i){h("wrapped data"),r.decoder&&(i=r.decoder.write(i)),r.objectMode&&null==i||(r.objectMode||i&&i.length)&&(t.push(i)||(n=!0,e.pause()))})),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<E.length;o++)e.on(E[o],this.emit.bind(this,E[o]));return this._read=function(t){h("wrapped _read",t),n&&(n=!1,e.resume())},this},"function"==typeof Symbol&&(j.prototype[Symbol.asyncIterator]=function(){return void 0===b&&(b=e("./internal/streams/async_iterator")),b(this)}),Object.defineProperty(j.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(j.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(j.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}}),j._fromList=Y,Object.defineProperty(j.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"==typeof Symbol&&(j.from=function(t,r){return void 0===y&&(y=e("./internal/streams/from")),y(j,t,r)})},{events:"FRpO","./internal/streams/stream":"ExO1",buffer:"dskh",util:"rDCW","./internal/streams/buffer_list":"VCcH","./internal/streams/destroy":"GRUB","./internal/streams/state":"TWVJ","../errors":"Yj9o",inherits:"Bm0n","./_stream_duplex":"Hba0","string_decoder/":"z0rv","./internal/streams/async_iterator":"AOGM","./internal/streams/from":"Rr4m",process:"pBGv"}],tlBz:[function(e,t,r){"use strict";t.exports=f;var n=e("../errors").codes,i=n.ERR_METHOD_NOT_IMPLEMENTED,o=n.ERR_MULTIPLE_CALLBACK,a=n.ERR_TRANSFORM_ALREADY_TRANSFORMING,s=n.ERR_TRANSFORM_WITH_LENGTH_0,u=e("./_stream_duplex");function l(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(null===n)return this.emit("error",new o);r.writechunk=null,r.writecb=null,null!=t&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function f(e){if(!(this instanceof f))return new f(e);u.call(this,e),this._transformState={afterTransform:l.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",c)}function c(){var e=this;"function"!=typeof this._flush||this._readableState.destroyed?h(this,null,null):this._flush((function(t,r){h(e,t,r)}))}function h(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new s;if(e._transformState.transforming)throw new a;return e.push(null)}e("inherits")(f,u),f.prototype.push=function(e,t){return this._transformState.needTransform=!1,u.prototype.push.call(this,e,t)},f.prototype._transform=function(e,t,r){r(new i("_transform()"))},f.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},f.prototype._read=function(e){var t=this._transformState;null===t.writechunk||t.transforming?t.needTransform=!0:(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform))},f.prototype._destroy=function(e,t){u.prototype._destroy.call(this,e,(function(e){t(e)}))}},{"../errors":"Yj9o","./_stream_duplex":"Hba0",inherits:"Bm0n"}],nwyA:[function(e,t,r){"use strict";t.exports=i;var n=e("./_stream_transform");function i(e){if(!(this instanceof i))return new i(e);n.call(this,e)}e("inherits")(i,n),i.prototype._transform=function(e,t,r){r(null,e)}},{"./_stream_transform":"tlBz",inherits:"Bm0n"}],AJFC:[function(e,t,r){"use strict";var n;var i=e("../../../errors").codes,o=i.ERR_MISSING_ARGS,a=i.ERR_STREAM_DESTROYED;function s(e){if(e)throw e}function u(t,r,i,o){o=function(e){var t=!1;return function(){t||(t=!0,e.apply(void 0,arguments))}}(o);var s=!1;t.on("close",(function(){s=!0})),void 0===n&&(n=e("./end-of-stream")),n(t,{readable:r,writable:i},(function(e){if(e)return o(e);s=!0,o()}));var u=!1;return function(e){if(!s&&!u)return u=!0,function(e){return e.setHeader&&"function"==typeof e.abort}(t)?t.abort():"function"==typeof t.destroy?t.destroy():void o(e||new a("pipe"))}}function l(e){e()}function f(e,t){return e.pipe(t)}function c(e){return e.length?"function"!=typeof e[e.length-1]?s:e.pop():s}t.exports=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n,i=c(t);if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new o("streams");var a=t.map((function(e,r){var o=r<t.length-1;return u(e,o,r>0,(function(e){n||(n=e),e&&a.forEach(l),o||(a.forEach(l),i(n))}))}));return t.reduce(f)}},{"../../../errors":"Yj9o","./end-of-stream":"mrEY"}],tzeh:[function(e,t,r){(r=t.exports=e("./lib/_stream_readable.js")).Stream=r,r.Readable=r,r.Writable=e("./lib/_stream_writable.js"),r.Duplex=e("./lib/_stream_duplex.js"),r.Transform=e("./lib/_stream_transform.js"),r.PassThrough=e("./lib/_stream_passthrough.js"),r.finished=e("./lib/internal/streams/end-of-stream.js"),r.pipeline=e("./lib/internal/streams/pipeline.js")},{"./lib/_stream_readable.js":"DHrQ","./lib/_stream_writable.js":"WSyY","./lib/_stream_duplex.js":"Hba0","./lib/_stream_transform.js":"tlBz","./lib/_stream_passthrough.js":"nwyA","./lib/internal/streams/end-of-stream.js":"mrEY","./lib/internal/streams/pipeline.js":"AJFC"}],vexR:[function(e,t,r){t.exports=function(e){return e&&"object"==typeof e&&"function"==typeof e.copy&&"function"==typeof e.fill&&"function"==typeof e.readUInt8}},{}],tcrG:[function(e,t,r){"function"==typeof Object.create?t.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}},{}],gfUn:[function(e,t,r){e("process");var n=e("process"),i=Object.getOwnPropertyDescriptors||function(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++)r[t[n]]=Object.getOwnPropertyDescriptor(e,t[n]);return r},o=/%[sdj%]/g;r.format=function(e){if(!m(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(u(arguments[r]));return t.join(" ")}r=1;for(var n=arguments,i=n.length,a=String(e).replace(o,(function(e){if("%%"===e)return"%";if(r>=i)return e;switch(e){case"%s":return String(n[r++]);case"%d":return Number(n[r++]);case"%j":try{return JSON.stringify(n[r++])}catch(e){return"[Circular]"}default:return e}})),s=n[r];r<i;s=n[++r])v(s)||!k(s)?a+=" "+s:a+=" "+u(s);return a},r.deprecate=function(e,t){if(void 0!==n&&!0===n.noDeprecation)return e;if(void 0===n)return function(){return r.deprecate(e,t).apply(this,arguments)};var i=!1;return function(){if(!i){if(n.throwDeprecation)throw new Error(t);n.traceDeprecation?console.trace(t):console.error(t),i=!0}return e.apply(this,arguments)}};var a,s={};function u(e,t){var n={seen:[],stylize:f};return arguments.length>=3&&(n.depth=arguments[2]),arguments.length>=4&&(n.colors=arguments[3]),y(t)?n.showHidden=t:t&&r._extend(n,t),w(n.showHidden)&&(n.showHidden=!1),w(n.depth)&&(n.depth=2),w(n.colors)&&(n.colors=!1),w(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=l),c(n,e,n.depth)}function l(e,t){var r=u.styles[t];return r?"["+u.colors[r][0]+"m"+e+"["+u.colors[r][1]+"m":e}function f(e,t){return e}function c(e,t,n){if(e.customInspect&&t&&O(t.inspect)&&t.inspect!==r.inspect&&(!t.constructor||t.constructor.prototype!==t)){var i=t.inspect(n,e);return m(i)||(i=c(e,i,n)),i}var o=h(e,t);if(o)return o;var a=Object.keys(t),s=function(e){var t={};return e.forEach((function(e,r){t[e]=!0})),t}(a);if(e.showHidden&&(a=Object.getOwnPropertyNames(t)),S(t)&&(a.indexOf("message")>=0||a.indexOf("description")>=0))return d(t);if(0===a.length){if(O(t)){var u=t.name?": "+t.name:"";return e.stylize("[Function"+u+"]","special")}if(_(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(x(t))return e.stylize(Date.prototype.toString.call(t),"date");if(S(t))return d(t)}var l,f="",y=!1,v=["{","}"];return b(t)&&(y=!0,v=["[","]"]),O(t)&&(f=" [Function"+(t.name?": "+t.name:"")+"]"),_(t)&&(f=" "+RegExp.prototype.toString.call(t)),x(t)&&(f=" "+Date.prototype.toUTCString.call(t)),S(t)&&(f=" "+d(t)),0!==a.length||y&&0!=t.length?n<0?_(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special"):(e.seen.push(t),l=y?function(e,t,r,n,i){for(var o=[],a=0,s=t.length;a<s;++a)j(t,String(a))?o.push(p(e,t,r,n,String(a),!0)):o.push("");return i.forEach((function(i){i.match(/^\d+$/)||o.push(p(e,t,r,n,i,!0))})),o}(e,t,n,s,a):a.map((function(r){return p(e,t,n,s,r,y)})),e.seen.pop(),function(e,t,r){return e.reduce((function(e,t){return t.indexOf("\n"),e+t.replace(/\u001b\[\d\d?m/g,"").length+1}),0)>60?r[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+r[1]:r[0]+t+" "+e.join(", ")+" "+r[1]}(l,f,v)):v[0]+f+v[1]}function h(e,t){if(w(t))return e.stylize("undefined","undefined");if(m(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}return g(t)?e.stylize(""+t,"number"):y(t)?e.stylize(""+t,"boolean"):v(t)?e.stylize("null","null"):void 0}function d(e){return"["+Error.prototype.toString.call(e)+"]"}function p(e,t,r,n,i,o){var a,s,u;if((u=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]}).get?s=u.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):u.set&&(s=e.stylize("[Setter]","special")),j(n,i)||(a="["+i+"]"),s||(e.seen.indexOf(u.value)<0?(s=v(r)?c(e,u.value,null):c(e,u.value,r-1)).indexOf("\n")>-1&&(s=o?s.split("\n").map((function(e){return"  "+e})).join("\n").substr(2):"\n"+s.split("\n").map((function(e){return"   "+e})).join("\n")):s=e.stylize("[Circular]","special")),w(a)){if(o&&i.match(/^\d+$/))return s;(a=JSON.stringify(""+i)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=e.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=e.stylize(a,"string"))}return a+": "+s}function b(e){return Array.isArray(e)}function y(e){return"boolean"==typeof e}function v(e){return null===e}function g(e){return"number"==typeof e}function m(e){return"string"==typeof e}function w(e){return void 0===e}function _(e){return k(e)&&"[object RegExp]"===T(e)}function k(e){return"object"==typeof e&&null!==e}function x(e){return k(e)&&"[object Date]"===T(e)}function S(e){return k(e)&&("[object Error]"===T(e)||e instanceof Error)}function O(e){return"function"==typeof e}function T(e){return Object.prototype.toString.call(e)}function E(e){return e<10?"0"+e.toString(10):e.toString(10)}r.debuglog=function(e){if(w(a)&&(a=""),e=e.toUpperCase(),!s[e])if(new RegExp("\\b"+e+"\\b","i").test(a)){var t=n.pid;s[e]=function(){var n=r.format.apply(r,arguments);console.error("%s %d: %s",e,t,n)}}else s[e]=function(){};return s[e]},r.inspect=u,u.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},u.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},r.isArray=b,r.isBoolean=y,r.isNull=v,r.isNullOrUndefined=function(e){return null==e},r.isNumber=g,r.isString=m,r.isSymbol=function(e){return"symbol"==typeof e},r.isUndefined=w,r.isRegExp=_,r.isObject=k,r.isDate=x,r.isError=S,r.isFunction=O,r.isPrimitive=function(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e},r.isBuffer=e("./support/isBuffer");var C=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function P(){var e=new Date,t=[E(e.getHours()),E(e.getMinutes()),E(e.getSeconds())].join(":");return[e.getDate(),C[e.getMonth()],t].join(" ")}function j(e,t){return Object.prototype.hasOwnProperty.call(e,t)}r.log=function(){console.log("%s - %s",P(),r.format.apply(r,arguments))},r.inherits=e("inherits"),r._extend=function(e,t){if(!t||!k(t))return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e};var R="undefined"!=typeof Symbol?Symbol("util.promisify.custom"):void 0;function M(e,t){if(!e){var r=new Error("Promise was rejected with a falsy value");r.reason=e,e=r}return t(e)}r.promisify=function(e){if("function"!=typeof e)throw new TypeError('The "original" argument must be of type Function');if(R&&e[R]){var t;if("function"!=typeof(t=e[R]))throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(t,R,{value:t,enumerable:!1,writable:!1,configurable:!0}),t}function t(){for(var t,r,n=new Promise((function(e,n){t=e,r=n})),i=[],o=0;o<arguments.length;o++)i.push(arguments[o]);i.push((function(e,n){e?r(e):t(n)}));try{e.apply(this,i)}catch(e){r(e)}return n}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),R&&Object.defineProperty(t,R,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,i(e))},r.promisify.custom=R,r.callbackify=function(e){if("function"!=typeof e)throw new TypeError('The "original" argument must be of type Function');function t(){for(var t=[],r=0;r<arguments.length;r++)t.push(arguments[r]);var i=t.pop();if("function"!=typeof i)throw new TypeError("The last argument must be of type Function");var o=this,a=function(){return i.apply(o,arguments)};e.apply(this,t).then((function(e){n.nextTick(a,null,e)}),(function(e){n.nextTick(M,e,a)}))}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),Object.defineProperties(t,i(e)),t}},{"./support/isBuffer":"vexR",inherits:"tcrG",process:"pBGv"}],m2P9:[function(e,t,r){e("process");var n=e("process"),i=e("readable-stream").Transform,o=e("util").inherits;function a(e){i.call(this,e),this._destroyed=!1}function s(e,t,r){r(null,e)}function u(e){return function(t,r,n){return"function"==typeof t&&(n=r,r=t,t={}),"function"!=typeof r&&(r=s),"function"!=typeof n&&(n=null),e(t,r,n)}}o(a,i),a.prototype.destroy=function(e){if(!this._destroyed){this._destroyed=!0;var t=this;n.nextTick((function(){e&&t.emit("error",e),t.emit("close")}))}},t.exports=u((function(e,t,r){var n=new a(e);return n._transform=t,r&&(n._flush=r),n})),t.exports.ctor=u((function(e,t,r){function n(t){if(!(this instanceof n))return new n(t);this.options=Object.assign({},e,t),a.call(this,this.options)}return o(n,a),n.prototype._transform=t,r&&(n.prototype._flush=r),n})),t.exports.obj=u((function(e,t,r){var n=new a(Object.assign({objectMode:!0,highWaterMark:16},e));return n._transform=t,r&&(n._flush=r),n}))},{"readable-stream":"tzeh",util:"gfUn",process:"pBGv"}],JO74:[function(e,t,r){function n(e,t){"use strict";var r=(t=t||{}).pos||0,i="<".charCodeAt(0),o=">".charCodeAt(0),a="-".charCodeAt(0),s="/".charCodeAt(0),u="!".charCodeAt(0),l="'".charCodeAt(0),f='"'.charCodeAt(0);function c(){for(var t=[];e[r];)if(e.charCodeAt(r)==i){if(e.charCodeAt(r+1)===s)return(r=e.indexOf(">",r))+1&&(r+=1),t;if(e.charCodeAt(r+1)===u){if(e.charCodeAt(r+2)==a){for(;-1!==r&&(e.charCodeAt(r)!==o||e.charCodeAt(r-1)!=a||e.charCodeAt(r-2)!=a||-1==r);)r=e.indexOf(">",r+1);-1===r&&(r=e.length)}else for(r+=2;e.charCodeAt(r)!==o&&e[r];)r++;r++;continue}var n=b();t.push(n)}else{var l=h();l.trim().length>0&&t.push(l),r++}return t}function h(){var t=r;return-2==(r=e.indexOf("<",r)-1)&&(r=e.length),e.slice(t,r+1)}function d(){for(var t=r;-1==="\n\t>/= ".indexOf(e[r])&&e[r];)r++;return e.slice(t,r)}var p=t.noChildNodes||["img","br","input","meta","link"];function b(){r++;const t=d(),n={};let i=[];for(;e.charCodeAt(r)!==o&&e[r];){var a=e.charCodeAt(r);if(a>64&&a<91||a>96&&a<123){for(var u=d(),h=e.charCodeAt(r);h&&h!==l&&h!==f&&!(h>64&&h<91||h>96&&h<123)&&h!==o;)r++,h=e.charCodeAt(r);if(h===l||h===f){var b=y();if(-1===r)return{tagName:t,attributes:n,children:i}}else b=null,r--;n[u]=b}r++}if(e.charCodeAt(r-1)!==s)if("script"==t){var v=r+1;r=e.indexOf("<\/script>",r),i=[e.slice(v,r-1)],r+=9}else"style"==t?(v=r+1,r=e.indexOf("</style>",r),i=[e.slice(v,r-1)],r+=8):-1==p.indexOf(t)&&(r++,i=c());else r++;return{tagName:t,attributes:n,children:i}}function y(){var t=e[r],n=++r;return r=e.indexOf(t,n),e.slice(n,r)}var v,g=null;if(void 0!==t.attrValue)for(t.attrName=t.attrName||"id",g=[];-1!==(void 0,v=new RegExp("\\s"+t.attrName+"\\s*=['\"]"+t.attrValue+"['\"]").exec(e),r=v?v.index:-1);)-1!==(r=e.lastIndexOf("<",r))&&g.push(b()),e=e.substr(r),r=0;else g=t.parseNode?b():c();return t.filter&&(g=n.filter(g,t.filter)),t.setPos&&(g.pos=r),g}n.simplify=function(e){var t={};if(!e.length)return"";if(1===e.length&&"string"==typeof e[0])return e[0];for(var r in e.forEach((function(e){if("object"==typeof e){t[e.tagName]||(t[e.tagName]=[]);var r=n.simplify(e.children||[]);t[e.tagName].push(r),e.attributes&&(r._attributes=e.attributes)}})),t)1==t[r].length&&(t[r]=t[r][0]);return t},n.filter=function(e,t){var r=[];return e.forEach((function(e){if("object"==typeof e&&t(e)&&r.push(e),e.children){var i=n.filter(e.children,t);r=r.concat(i)}})),r},n.stringify=function(e){var t="";function r(e){if(e)for(var r=0;r<e.length;r++)"string"==typeof e[r]?t+=e[r].trim():n(e[r])}function n(e){for(var n in t+="<"+e.tagName,e.attributes)null===e.attributes[n]?t+=" "+n:-1===e.attributes[n].indexOf('"')?t+=" "+n+'="'+e.attributes[n].trim()+'"':t+=" "+n+"='"+e.attributes[n].trim()+"'";t+=">",r(e.children),t+="</"+e.tagName+">"}return r(e),t},n.toContentString=function(e){if(Array.isArray(e)){var t="";return e.forEach((function(e){t=(t+=" "+n.toContentString(e)).trim()})),t}return"object"==typeof e?n.toContentString(e.children):" "+e},n.getElementById=function(e,t,r){var i=n(e,{attrValue:t});return r?n.simplify(i):i[0]},n.getElementsByClassName=function(e,t,r){const i=n(e,{attrName:"class",attrValue:"[a-zA-Z0-9-s ]*"+t+"[a-zA-Z0-9-s ]*"});return r?n.simplify(i):i},n.parseStream=function(t,r){if("string"==typeof r&&(r=r.length+2),"string"==typeof t){var i=e("fs");t=i.createReadStream(t,{start:r}),r=0}var o=r,a="";return t.on("data",(function(e){a+=e;for(var r=0;;){if(!(o=a.indexOf("<",o)+1))return void(o=r);if("/"!==a[o+1]){var i=n(a,{pos:o-1,parseNode:!0,setPos:!0});if((o=i.pos)>a.length-1||o<r)return a=a.slice(r),o=0,void(r=0);t.emit("xml",i),r=o}else o+=1,r=pos}})),t.on("end",(function(){console.log("end")})),t},n.transformStream=function(t){const r=e("through2");"string"==typeof t&&(t=t.length+2);var i=t||0,o="";return r({readableObjectMode:!0},(function(e,t,r){o+=e;for(var a=0;;){if(!(i=o.indexOf("<",i)+1))return i=a,r();if("/"!==o[i+1]){var s=n(o,{pos:i-1,parseNode:!0,setPos:!0});if((i=s.pos)>o.length-1||i<a)return o=o.slice(a),i=0,a=0,r();this.push(s),a=i}else i+=1,a=pos}r()}))},"object"==typeof t&&(t.exports=n,n.xml=n)},{fs:"rDCW",through2:"m2P9"}],j27V:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.geoKeys=r.geoKeyNames=r.ExtraSamplesValues=r.photometricInterpretations=r.fieldTypes=r.fieldTypeNames=r.arrayFields=r.fieldTagTypes=r.fieldTags=r.fieldTagNames=void 0;var n={315:"Artist",258:"BitsPerSample",265:"CellLength",264:"CellWidth",320:"ColorMap",259:"Compression",33432:"Copyright",306:"DateTime",338:"ExtraSamples",266:"FillOrder",289:"FreeByteCounts",288:"FreeOffsets",291:"GrayResponseCurve",290:"GrayResponseUnit",316:"HostComputer",270:"ImageDescription",257:"ImageLength",256:"ImageWidth",271:"Make",281:"MaxSampleValue",280:"MinSampleValue",272:"Model",254:"NewSubfileType",274:"Orientation",262:"PhotometricInterpretation",284:"PlanarConfiguration",296:"ResolutionUnit",278:"RowsPerStrip",277:"SamplesPerPixel",305:"Software",279:"StripByteCounts",273:"StripOffsets",255:"SubfileType",263:"Threshholding",282:"XResolution",283:"YResolution",326:"BadFaxLines",327:"CleanFaxData",343:"ClipPath",328:"ConsecutiveBadFaxLines",433:"Decode",434:"DefaultImageColor",269:"DocumentName",336:"DotRange",321:"HalftoneHints",346:"Indexed",347:"JPEGTables",285:"PageName",297:"PageNumber",317:"Predictor",319:"PrimaryChromaticities",532:"ReferenceBlackWhite",339:"SampleFormat",340:"SMinSampleValue",341:"SMaxSampleValue",559:"StripRowCounts",330:"SubIFDs",292:"T4Options",293:"T6Options",325:"TileByteCounts",323:"TileLength",324:"TileOffsets",322:"TileWidth",301:"TransferFunction",318:"WhitePoint",344:"XClipPathUnits",286:"XPosition",529:"YCbCrCoefficients",531:"YCbCrPositioning",530:"YCbCrSubSampling",345:"YClipPathUnits",287:"YPosition",37378:"ApertureValue",40961:"ColorSpace",36868:"DateTimeDigitized",36867:"DateTimeOriginal",34665:"Exif IFD",36864:"ExifVersion",33434:"ExposureTime",41728:"FileSource",37385:"Flash",40960:"FlashpixVersion",33437:"FNumber",42016:"ImageUniqueID",37384:"LightSource",37500:"MakerNote",37377:"ShutterSpeedValue",37510:"UserComment",33723:"IPTC",34675:"ICC Profile",700:"XMP",42112:"GDAL_METADATA",42113:"GDAL_NODATA",34377:"Photoshop",33550:"ModelPixelScale",33922:"ModelTiepoint",34264:"ModelTransformation",34735:"GeoKeyDirectory",34736:"GeoDoubleParams",34737:"GeoAsciiParams"};r.fieldTagNames=n;var i={};for(var o in r.fieldTags=i,n)n.hasOwnProperty(o)&&(i[n[o]]=parseInt(o,10));r.fieldTagTypes={256:"SHORT",257:"SHORT",258:"SHORT",259:"SHORT",262:"SHORT",273:"LONG",274:"SHORT",277:"SHORT",278:"LONG",279:"LONG",282:"RATIONAL",283:"RATIONAL",284:"SHORT",286:"SHORT",287:"RATIONAL",296:"SHORT",305:"ASCII",306:"ASCII",338:"SHORT",339:"SHORT",513:"LONG",514:"LONG",1024:"SHORT",1025:"SHORT",2048:"SHORT",2049:"ASCII",33550:"DOUBLE",33922:"DOUBLE",34665:"LONG",34735:"SHORT",34737:"ASCII",42113:"ASCII"};var a=[i.BitsPerSample,i.ExtraSamples,i.SampleFormat,i.StripByteCounts,i.StripOffsets,i.StripRowCounts,i.TileByteCounts,i.TileOffsets,i.SubIFDs];r.arrayFields=a;var s={1:"BYTE",2:"ASCII",3:"SHORT",4:"LONG",5:"RATIONAL",6:"SBYTE",7:"UNDEFINED",8:"SSHORT",9:"SLONG",10:"SRATIONAL",11:"FLOAT",12:"DOUBLE",13:"IFD",16:"LONG8",17:"SLONG8",18:"IFD8"};r.fieldTypeNames=s;var u={};for(var l in r.fieldTypes=u,s)s.hasOwnProperty(l)&&(u[s[l]]=parseInt(l,10));r.photometricInterpretations={WhiteIsZero:0,BlackIsZero:1,RGB:2,Palette:3,TransparencyMask:4,CMYK:5,YCbCr:6,CIELab:8,ICCLab:9};r.ExtraSamplesValues={Unspecified:0,Assocalpha:1,Unassalpha:2};var f={1024:"GTModelTypeGeoKey",1025:"GTRasterTypeGeoKey",1026:"GTCitationGeoKey",2048:"GeographicTypeGeoKey",2049:"GeogCitationGeoKey",2050:"GeogGeodeticDatumGeoKey",2051:"GeogPrimeMeridianGeoKey",2052:"GeogLinearUnitsGeoKey",2053:"GeogLinearUnitSizeGeoKey",2054:"GeogAngularUnitsGeoKey",2055:"GeogAngularUnitSizeGeoKey",2056:"GeogEllipsoidGeoKey",2057:"GeogSemiMajorAxisGeoKey",2058:"GeogSemiMinorAxisGeoKey",2059:"GeogInvFlatteningGeoKey",2060:"GeogAzimuthUnitsGeoKey",2061:"GeogPrimeMeridianLongGeoKey",2062:"GeogTOWGS84GeoKey",3072:"ProjectedCSTypeGeoKey",3073:"PCSCitationGeoKey",3074:"ProjectionGeoKey",3075:"ProjCoordTransGeoKey",3076:"ProjLinearUnitsGeoKey",3077:"ProjLinearUnitSizeGeoKey",3078:"ProjStdParallel1GeoKey",3079:"ProjStdParallel2GeoKey",3080:"ProjNatOriginLongGeoKey",3081:"ProjNatOriginLatGeoKey",3082:"ProjFalseEastingGeoKey",3083:"ProjFalseNorthingGeoKey",3084:"ProjFalseOriginLongGeoKey",3085:"ProjFalseOriginLatGeoKey",3086:"ProjFalseOriginEastingGeoKey",3087:"ProjFalseOriginNorthingGeoKey",3088:"ProjCenterLongGeoKey",3089:"ProjCenterLatGeoKey",3090:"ProjCenterEastingGeoKey",3091:"ProjCenterNorthingGeoKey",3092:"ProjScaleAtNatOriginGeoKey",3093:"ProjScaleAtCenterGeoKey",3094:"ProjAzimuthAngleGeoKey",3095:"ProjStraightVertPoleLongGeoKey",3096:"ProjRectifiedGridAngleGeoKey",4096:"VerticalCSTypeGeoKey",4097:"VerticalCitationGeoKey",4098:"VerticalDatumGeoKey",4099:"VerticalUnitsGeoKey"};r.geoKeyNames=f;var c={};for(var h in r.geoKeys=c,f)f.hasOwnProperty(h)&&(c[f[h]]=parseInt(h,10))},{}],fpBl:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.fromWhiteIsZero=function(e,t){for(var r,n=e.width,i=e.height,o=new Uint8Array(n*i*3),a=0,s=0;a<e.length;++a,s+=3)r=256-e[a]/t*256,o[s]=r,o[s+1]=r,o[s+2]=r;return o},r.fromBlackIsZero=function(e,t){for(var r,n=e.width,i=e.height,o=new Uint8Array(n*i*3),a=0,s=0;a<e.length;++a,s+=3)r=e[a]/t*256,o[s]=r,o[s+1]=r,o[s+2]=r;return o},r.fromPalette=function(e,t){for(var r=e.width,n=e.height,i=new Uint8Array(r*n*3),o=t.length/3,a=t.length/3*2,s=0,u=0;s<e.length;++s,u+=3){var l=e[s];i[u]=t[l]/65536*256,i[u+1]=t[l+o]/65536*256,i[u+2]=t[l+a]/65536*256}return i},r.fromCMYK=function(e){for(var t=e.width,r=e.height,n=new Uint8Array(t*r*3),i=0,o=0;i<e.length;i+=4,o+=3){var a=e[i],s=e[i+1],u=e[i+2],l=e[i+3];n[o]=(255-a)/256*255*((255-l)/256),n[o+1]=(255-s)/256*255*((255-l)/256),n[o+2]=(255-u)/256*255*((255-l)/256)}return n},r.fromYCbCr=function(e){for(var t=e.width,r=e.height,n=new Uint8ClampedArray(t*r*3),i=0,o=0;i<e.length;i+=3,o+=3){var a=e[i],s=e[i+1],u=e[i+2];n[o]=a+1.402*(u-128),n[o+1]=a-.34414*(s-128)-.71414*(u-128),n[o+2]=a+1.772*(s-128)}return n},r.fromCIELab=function(e){for(var t=e.width,r=e.height,n=new Uint8Array(t*r*3),i=0,o=0;i<e.length;i+=3,o+=3){var a=(e[i+0]+16)/116,s=(e[i+1]<<24>>24)/500+a,u=a-(e[i+2]<<24>>24)/200,l=void 0,f=void 0,c=void 0;f=-.9689*(s=.95047*(s*s*s>.008856?s*s*s:(s-16/116)/7.787))+1.8758*(a=1*(a*a*a>.008856?a*a*a:(a-16/116)/7.787))+.0415*(u=1.08883*(u*u*u>.008856?u*u*u:(u-16/116)/7.787)),c=.0557*s+-.204*a+1.057*u,l=(l=3.2406*s+-1.5372*a+-.4986*u)>.0031308?1.055*Math.pow(l,1/2.4)-.055:12.92*l,f=f>.0031308?1.055*Math.pow(f,1/2.4)-.055:12.92*f,c=c>.0031308?1.055*Math.pow(c,1/2.4)-.055:12.92*c,n[o]=255*Math.max(0,Math.min(1,l)),n[o+1]=255*Math.max(0,Math.min(1,f)),n[o+2]=255*Math.max(0,Math.min(1,c))}return n}},{}],IKSA:[function(e,t,r){"use strict";function n(e,t){var r=e.length-t,n=0;do{for(var i=t;i>0;i--)e[n+t]+=e[n],n++;r-=t}while(r>0)}function i(e,t,r){for(var n=0,i=e.length,o=i/r;i>t;){for(var a=t;a>0;--a)e[n+t]+=e[n],++n;i-=t}for(var s=e.slice(),u=0;u<o;++u)for(var l=0;l<r;++l)e[r*u+l]=s[(r-l-1)*o+u]}Object.defineProperty(r,"__esModule",{value:!0}),r.applyPredictor=function(e,t,r,o,a,s){if(!t||1===t)return e;for(var u=0;u<a.length;++u){if(a[u]%8!=0)throw new Error("When decoding with predictor, only multiple of 8 bits are supported.");if(a[u]!==a[0])throw new Error("When decoding with predictor, all samples must have the same size.")}for(var l=a[0]/8,f=2===s?1:a.length,c=0;c<o&&!(c*f*r*l>=e.byteLength);++c){var h=void 0;if(2===t){switch(a[0]){case 8:h=new Uint8Array(e,c*f*r*l,f*r*l);break;case 16:h=new Uint16Array(e,c*f*r*l,f*r*l/2);break;case 32:h=new Uint32Array(e,c*f*r*l,f*r*l/4);break;default:throw new Error("Predictor 2 not allowed with ".concat(a[0]," bits per sample."))}n(h,f)}else 3===t&&i(h=new Uint8Array(e,c*f*r*l,f*r*l),f,l)}return e}},{}],FJDe:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=u(e("@babel/runtime/regenerator")),i=u(e("@babel/runtime/helpers/asyncToGenerator")),o=u(e("@babel/runtime/helpers/classCallCheck")),a=u(e("@babel/runtime/helpers/createClass")),s=e("../predictor");function u(e){return e&&e.__esModule?e:{default:e}}var l=function(){function e(){(0,o.default)(this,e)}return(0,a.default)(e,[{key:"decode",value:function(){var e=(0,i.default)(n.default.mark((function e(t,r){var i,o,a,u,l;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.decodeBlock(r);case 2:if(i=e.sent,1===(o=t.Predictor||1)){e.next=9;break}return a=!t.StripOffsets,u=a?t.TileWidth:t.ImageWidth,l=a?t.TileLength:t.RowsPerStrip||t.ImageLength,e.abrupt("return",(0,s.applyPredictor)(i,o,u,l,t.BitsPerSample,t.PlanarConfiguration));case 9:return e.abrupt("return",i);case 10:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()}]),e}();r.default=l},{"@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","../predictor":"IKSA"}],V9bQ:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=u(e("@babel/runtime/helpers/classCallCheck")),i=u(e("@babel/runtime/helpers/createClass")),o=u(e("@babel/runtime/helpers/inherits")),a=u(e("@babel/runtime/helpers/possibleConstructorReturn")),s=u(e("@babel/runtime/helpers/getPrototypeOf"));function u(e){return e&&e.__esModule?e:{default:e}}function l(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,s.default)(e);if(t){var i=(0,s.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,a.default)(this,r)}}var f=function(e){(0,o.default)(r,e);var t=l(r);function r(){return(0,n.default)(this,r),t.apply(this,arguments)}return(0,i.default)(r,[{key:"decodeBlock",value:function(e){return e}}]),r}(u(e("./basedecoder")).default);r.default=f},{"@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","./basedecoder":"FJDe"}],tcId:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=l(e("@babel/runtime/helpers/classCallCheck")),i=l(e("@babel/runtime/helpers/createClass")),o=l(e("@babel/runtime/helpers/inherits")),a=l(e("@babel/runtime/helpers/possibleConstructorReturn")),s=l(e("@babel/runtime/helpers/getPrototypeOf")),u=l(e("./basedecoder"));function l(e){return e&&e.__esModule?e:{default:e}}function f(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,s.default)(e);if(t){var i=(0,s.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,a.default)(this,r)}}function c(e,t){for(var r=t.length-1;r>=0;r--)e.push(t[r]);return e}function h(e){for(var t=new Uint16Array(4093),r=new Uint8Array(4093),n=0;n<=257;n++)t[n]=4096,r[n]=n;var i=258,o=9,a=0;function s(){i=258,o=9}function u(e){var t=function(e,t,r){var n=t%8,i=Math.floor(t/8),o=8-n,a=t+r-8*(i+1),s=8*(i+2)-(t+r),u=8*(i+2)-t;if(s=Math.max(0,s),i>=e.length)return console.warn("ran off the end of the buffer before finding EOI_CODE (end on input code)"),257;var l=e[i]&Math.pow(2,8-n)-1,f=l<<=r-o;if(i+1<e.length){var c=e[i+1]>>>s;f+=c<<=Math.max(0,r-u)}if(a>8&&i+2<e.length){var h=8*(i+3)-(t+r);f+=e[i+2]>>>h}return f}(e,a,o);return a+=o,t}function l(e,n){return r[i]=n,t[i]=e,++i-1}function f(e){for(var n=[],i=e;4096!==i;i=t[i])n.push(r[i]);return n}var h=[];s();for(var d,p=new Uint8Array(e),b=u(p);257!==b;){if(256===b){for(s(),b=u(p);256===b;)b=u(p);if(257===b)break;if(b>256)throw new Error("corrupted code at scanline ".concat(b));c(h,f(b)),d=b}else if(b<i){var y=f(b);c(h,y),l(d,y[y.length-1]),d=b}else{var v=f(d);if(!v)throw new Error("Bogus entry. Not in dictionary, ".concat(d," / ").concat(i,", position: ").concat(a));c(h,v),h.push(v[v.length-1]),l(d,v[v.length-1]),d=b}i+1>=Math.pow(2,o)&&(12===o?d=void 0:o++),b=u(p)}return new Uint8Array(h)}var d=function(e){(0,o.default)(r,e);var t=f(r);function r(){return(0,n.default)(this,r),t.apply(this,arguments)}return(0,i.default)(r,[{key:"decodeBlock",value:function(e){return h(e).buffer}}]),r}(u.default);r.default=d},{"@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","./basedecoder":"FJDe"}],J7kk:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=f(e("@babel/runtime/helpers/inherits")),i=f(e("@babel/runtime/helpers/possibleConstructorReturn")),o=f(e("@babel/runtime/helpers/getPrototypeOf")),a=f(e("@babel/runtime/helpers/classCallCheck")),s=f(e("@babel/runtime/helpers/createClass")),u=f(e("@babel/runtime/helpers/typeof")),l=f(e("./basedecoder"));function f(e){return e&&e.__esModule?e:{default:e}}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,o.default)(e);if(t){var a=(0,o.default)(this).constructor;r=Reflect.construct(n,arguments,a)}else r=n.apply(this,arguments);return(0,i.default)(this,r)}}var h=new Int32Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),d=4017,p=799,b=3406,y=2276,v=1567,g=3784,m=5793,w=2896;function _(e,t){for(var r=0,n=[],i=16;i>0&&!e[i-1];)--i;n.push({children:[],index:0});for(var o,a=n[0],s=0;s<i;s++){for(var u=0;u<e[s];u++){for((a=n.pop()).children[a.index]=t[r];a.index>0;)a=n.pop();for(a.index++,n.push(a);n.length<=s;)n.push(o={children:[],index:0}),a.children[a.index]=o.children,a=o;r++}s+1<i&&(n.push(o={children:[],index:0}),a.children[a.index]=o.children,a=o)}return n[0].children}function k(e,t,r,n,i,o,a,s,l){var f=r.mcusPerLine,c=r.progressive,d=t,p=t,b=0,y=0;function v(){if(y>0)return b>>--y&1;if(255===(b=e[p++])){var t=e[p++];if(t)throw new Error("unexpected marker: ".concat((b<<8|t).toString(16)))}return y=7,b>>>7}function g(e){for(var t,r=e;null!==(t=v());){if("number"==typeof(r=r[t]))return r;if("object"!==(0,u.default)(r))throw new Error("invalid huffman sequence")}return null}function m(e){for(var t=e,r=0;t>0;){var n=v();if(null===n)return;r=r<<1|n,--t}return r}function w(e){var t=m(e);return t>=1<<e-1?t:t+(-1<<e)+1}var _,k=0,x=0;function S(e,t,r,n,i){var o=r%f,a=(r/f|0)*e.v+n,s=o*e.h+i;t(e,e.blocks[a][s])}function O(e,t,r){var n=r/e.blocksPerLine|0,i=r%e.blocksPerLine;t(e,e.blocks[n][i])}var T,E,C,P,j,R,M=n.length;R=c?0===o?0===s?function(e,t){var r=g(e.huffmanTableDC),n=0===r?0:w(r)<<l;e.pred+=n,t[0]=e.pred}:function(e,t){t[0]|=v()<<l}:0===s?function(e,t){if(k>0)k--;else for(var r=o,n=a;r<=n;){var i=g(e.huffmanTableAC),s=15&i,u=i>>4;if(0===s){if(u<15){k=m(u)+(1<<u)-1;break}r+=16}else t[h[r+=u]]=w(s)*(1<<l),r++}}:function(e,t){for(var r=o,n=a,i=0;r<=n;){var s=h[r],u=t[s]<0?-1:1;switch(x){case 0:var f=g(e.huffmanTableAC),c=15&f;if(i=f>>4,0===c)i<15?(k=m(i)+(1<<i),x=4):(i=16,x=1);else{if(1!==c)throw new Error("invalid ACn encoding");_=w(c),x=i?2:3}continue;case 1:case 2:t[s]?t[s]+=(v()<<l)*u:0==--i&&(x=2===x?3:0);break;case 3:t[s]?t[s]+=(v()<<l)*u:(t[s]=_<<l,x=0);break;case 4:t[s]&&(t[s]+=(v()<<l)*u)}r++}4===x&&0==--k&&(x=0)}:function(e,t){var r=g(e.huffmanTableDC),n=0===r?0:w(r);e.pred+=n,t[0]=e.pred;for(var i=1;i<64;){var o=g(e.huffmanTableAC),a=15&o,s=o>>4;if(0===a){if(s<15)break;i+=16}else t[h[i+=s]]=w(a),i++}};var A,I,D=0;I=1===M?n[0].blocksPerLine*n[0].blocksPerColumn:f*r.mcusPerColumn;for(var L=i||I;D<I;){for(E=0;E<M;E++)n[E].pred=0;if(k=0,1===M)for(T=n[0],j=0;j<L;j++)O(T,R,D),D++;else for(j=0;j<L;j++){for(E=0;E<M;E++){var N=T=n[E],F=N.h,U=N.v;for(C=0;C<U;C++)for(P=0;P<F;P++)S(T,R,D,C,P)}if(++D===I)break}if(y=0,(A=e[p]<<8|e[p+1])<65280)throw new Error("marker was not found");if(!(A>=65488&&A<=65495))break;p+=2}return p-d}function x(e,t){var r=[],n=t.blocksPerLine,i=t.blocksPerColumn,o=n<<3,a=new Int32Array(64),s=new Uint8Array(64);function u(e,r,n){var i,o,a,s,u,l,f,c,h,_,k=t.quantizationTable,x=n;for(_=0;_<64;_++)x[_]=e[_]*k[_];for(_=0;_<8;++_){var S=8*_;0!==x[1+S]||0!==x[2+S]||0!==x[3+S]||0!==x[4+S]||0!==x[5+S]||0!==x[6+S]||0!==x[7+S]?(i=m*x[0+S]+128>>8,o=m*x[4+S]+128>>8,a=x[2+S],s=x[6+S],u=w*(x[1+S]-x[7+S])+128>>8,c=w*(x[1+S]+x[7+S])+128>>8,l=x[3+S]<<4,f=x[5+S]<<4,h=i-o+1>>1,i=i+o+1>>1,o=h,h=a*g+s*v+128>>8,a=a*v-s*g+128>>8,s=h,h=u-f+1>>1,u=u+f+1>>1,f=h,h=c+l+1>>1,l=c-l+1>>1,c=h,h=i-s+1>>1,i=i+s+1>>1,s=h,h=o-a+1>>1,o=o+a+1>>1,a=h,h=u*y+c*b+2048>>12,u=u*b-c*y+2048>>12,c=h,h=l*p+f*d+2048>>12,l=l*d-f*p+2048>>12,f=h,x[0+S]=i+c,x[7+S]=i-c,x[1+S]=o+f,x[6+S]=o-f,x[2+S]=a+l,x[5+S]=a-l,x[3+S]=s+u,x[4+S]=s-u):(h=m*x[0+S]+512>>10,x[0+S]=h,x[1+S]=h,x[2+S]=h,x[3+S]=h,x[4+S]=h,x[5+S]=h,x[6+S]=h,x[7+S]=h)}for(_=0;_<8;++_){var O=_;0!==x[8+O]||0!==x[16+O]||0!==x[24+O]||0!==x[32+O]||0!==x[40+O]||0!==x[48+O]||0!==x[56+O]?(i=m*x[0+O]+2048>>12,o=m*x[32+O]+2048>>12,a=x[16+O],s=x[48+O],u=w*(x[8+O]-x[56+O])+2048>>12,c=w*(x[8+O]+x[56+O])+2048>>12,l=x[24+O],f=x[40+O],h=i-o+1>>1,i=i+o+1>>1,o=h,h=a*g+s*v+2048>>12,a=a*v-s*g+2048>>12,s=h,h=u-f+1>>1,u=u+f+1>>1,f=h,h=c+l+1>>1,l=c-l+1>>1,c=h,h=i-s+1>>1,i=i+s+1>>1,s=h,h=o-a+1>>1,o=o+a+1>>1,a=h,h=u*y+c*b+2048>>12,u=u*b-c*y+2048>>12,c=h,h=l*p+f*d+2048>>12,l=l*d-f*p+2048>>12,f=h,x[0+O]=i+c,x[56+O]=i-c,x[8+O]=o+f,x[48+O]=o-f,x[16+O]=a+l,x[40+O]=a-l,x[24+O]=s+u,x[32+O]=s-u):(h=m*n[_+0]+8192>>14,x[0+O]=h,x[8+O]=h,x[16+O]=h,x[24+O]=h,x[32+O]=h,x[40+O]=h,x[48+O]=h,x[56+O]=h)}for(_=0;_<64;++_){var T=128+(x[_]+8>>4);r[_]=T<0?0:T>255?255:T}}for(var l=0;l<i;l++){for(var f=l<<3,c=0;c<8;c++)r.push(new Uint8Array(o));for(var h=0;h<n;h++){u(t.blocks[l][h],s,a);for(var _=0,k=h<<3,x=0;x<8;x++)for(var S=r[f+x],O=0;O<8;O++)S[k+O]=s[_++]}}return r}var S=function(){function e(){(0,a.default)(this,e),this.jfif=null,this.adobe=null,this.quantizationTables=[],this.huffmanTablesAC=[],this.huffmanTablesDC=[],this.resetFrames()}return(0,s.default)(e,[{key:"resetFrames",value:function(){this.frames=[]}},{key:"parse",value:function(e){var t=0;function r(){var r=e[t]<<8|e[t+1];return t+=2,r}function n(e){var t,r,n=0,i=0;for(r in e.components)e.components.hasOwnProperty(r)&&(n<(t=e.components[r]).h&&(n=t.h),i<t.v&&(i=t.v));var o=Math.ceil(e.samplesPerLine/8/n),a=Math.ceil(e.scanLines/8/i);for(r in e.components)if(e.components.hasOwnProperty(r)){t=e.components[r];for(var s=Math.ceil(Math.ceil(e.samplesPerLine/8)*t.h/n),u=Math.ceil(Math.ceil(e.scanLines/8)*t.v/i),l=o*t.h,f=a*t.v,c=[],h=0;h<f;h++){for(var d=[],p=0;p<l;p++)d.push(new Int32Array(64));c.push(d)}t.blocksPerLine=s,t.blocksPerColumn=u,t.blocks=c}e.maxH=n,e.maxV=i,e.mcusPerLine=o,e.mcusPerColumn=a}var i,o,a=r();if(65496!==a)throw new Error("SOI not found");for(a=r();65497!==a;){switch(a){case 65280:break;case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var s=(void 0,void 0,i=r(),o=e.subarray(t,t+i-2),t+=o.length,o);65504===a&&74===s[0]&&70===s[1]&&73===s[2]&&70===s[3]&&0===s[4]&&(this.jfif={version:{major:s[5],minor:s[6]},densityUnits:s[7],xDensity:s[8]<<8|s[9],yDensity:s[10]<<8|s[11],thumbWidth:s[12],thumbHeight:s[13],thumbData:s.subarray(14,14+3*s[12]*s[13])}),65518===a&&65===s[0]&&100===s[1]&&111===s[2]&&98===s[3]&&101===s[4]&&0===s[5]&&(this.adobe={version:s[6],flags0:s[7]<<8|s[8],flags1:s[9]<<8|s[10],transformCode:s[11]});break;case 65499:for(var u=r()+t-2;t<u;){var l=e[t++],f=new Int32Array(64);if(l>>4==0)for(var c=0;c<64;c++)f[h[c]]=e[t++];else{if(l>>4!=1)throw new Error("DQT: invalid table spec");for(var d=0;d<64;d++)f[h[d]]=r()}this.quantizationTables[15&l]=f}break;case 65472:case 65473:case 65474:r();for(var p={extended:65473===a,progressive:65474===a,precision:e[t++],scanLines:r(),samplesPerLine:r(),components:{},componentsOrder:[]},b=e[t++],y=void 0,v=0;v<b;v++){y=e[t];var g=e[t+1]>>4,m=15&e[t+1],w=e[t+2];p.componentsOrder.push(y),p.components[y]={h:g,v:m,quantizationIdx:w},t+=3}n(p),this.frames.push(p);break;case 65476:for(var x=r(),S=2;S<x;){for(var O=e[t++],T=new Uint8Array(16),E=0,C=0;C<16;C++,t++)T[C]=e[t],E+=T[C];for(var P=new Uint8Array(E),j=0;j<E;j++,t++)P[j]=e[t];S+=17+E,O>>4==0?this.huffmanTablesDC[15&O]=_(T,P):this.huffmanTablesAC[15&O]=_(T,P)}break;case 65501:r(),this.resetInterval=r();break;case 65498:r();for(var R=e[t++],M=[],A=this.frames[0],I=0;I<R;I++){var D=A.components[e[t++]],L=e[t++];D.huffmanTableDC=this.huffmanTablesDC[L>>4],D.huffmanTableAC=this.huffmanTablesAC[15&L],M.push(D)}var N=e[t++],F=e[t++],U=e[t++],B=k(e,t,A,M,this.resetInterval,N,F,U>>4,15&U);t+=B;break;case 65535:255!==e[t]&&t--;break;default:if(255===e[t-3]&&e[t-2]>=192&&e[t-2]<=254){t-=3;break}throw new Error("unknown JPEG marker ".concat(a.toString(16)))}a=r()}}},{key:"getResult",value:function(){var e=this.frames;if(0===this.frames.length)throw new Error("no frames were decoded");this.frames.length>1&&console.warn("more than one frame is not supported");for(var t=0;t<this.frames.length;t++)for(var r=this.frames[t].components,n=0,i=Object.keys(r);n<i.length;n++){var o=i[n];r[o].quantizationTable=this.quantizationTables[r[o].quantizationIdx],delete r[o].quantizationIdx}for(var a=e[0],s=a.components,u=a.componentsOrder,l=[],f=a.samplesPerLine,c=a.scanLines,h=0;h<u.length;h++){var d=s[u[h]];l.push({lines:x(0,d),scaleX:d.h/a.maxH,scaleY:d.v/a.maxV})}for(var p=new Uint8Array(f*c*l.length),b=0,y=0;y<c;++y)for(var v=0;v<f;++v)for(var g=0;g<l.length;++g){var m=l[g];p[b]=m.lines[0|y*m.scaleY][0|v*m.scaleX],++b}return p}}]),e}(),O=function(e){(0,n.default)(r,e);var t=c(r);function r(e){var n;return(0,a.default)(this,r),(n=t.call(this)).reader=new S,e.JPEGTables&&n.reader.parse(e.JPEGTables),n}return(0,s.default)(r,[{key:"decodeBlock",value:function(e){return this.reader.resetFrames(),this.reader.parse(new Uint8Array(e)),this.reader.getResult().buffer}}]),r}(l.default);r.default=O},{"@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/typeof":"b9XL","./basedecoder":"FJDe"}],tbG5:[function(e,t,r){"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function i(e,t){return Object.prototype.hasOwnProperty.call(e,t)}r.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var n in r)i(r,n)&&(e[n]=r[n])}}return e},r.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var o={arraySet:function(e,t,r,n,i){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),i);else for(var o=0;o<n;o++)e[i+o]=t[r+o]},flattenChunks:function(e){var t,r,n,i,o,a;for(n=0,t=0,r=e.length;t<r;t++)n+=e[t].length;for(a=new Uint8Array(n),i=0,t=0,r=e.length;t<r;t++)o=e[t],a.set(o,i),i+=o.length;return a}},a={arraySet:function(e,t,r,n,i){for(var o=0;o<n;o++)e[i+o]=t[r+o]},flattenChunks:function(e){return[].concat.apply([],e)}};r.setTyped=function(e){e?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,o)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,a))},r.setTyped(n)},{}],uxo6:[function(e,t,r){"use strict";t.exports=function(e,t,r,n){for(var i=65535&e|0,o=e>>>16&65535|0,a=0;0!==r;){r-=a=r>2e3?2e3:r;do{o=o+(i=i+t[n++]|0)|0}while(--a);i%=65521,o%=65521}return i|o<<16|0}},{}],X4kj:[function(e,t,r){"use strict";var n=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();t.exports=function(e,t,r,i){var o=n,a=i+r;e^=-1;for(var s=i;s<a;s++)e=e>>>8^o[255&(e^t[s])];return-1^e}},{}],LP5M:[function(e,t,r){"use strict";t.exports=function(e,t){var r,n,i,o,a,s,u,l,f,c,h,d,p,b,y,v,g,m,w,_,k,x,S,O,T;r=e.state,n=e.next_in,O=e.input,i=n+(e.avail_in-5),o=e.next_out,T=e.output,a=o-(t-e.avail_out),s=o+(e.avail_out-257),u=r.dmax,l=r.wsize,f=r.whave,c=r.wnext,h=r.window,d=r.hold,p=r.bits,b=r.lencode,y=r.distcode,v=(1<<r.lenbits)-1,g=(1<<r.distbits)-1;e:do{p<15&&(d+=O[n++]<<p,p+=8,d+=O[n++]<<p,p+=8),m=b[d&v];t:for(;;){if(d>>>=w=m>>>24,p-=w,0==(w=m>>>16&255))T[o++]=65535&m;else{if(!(16&w)){if(0==(64&w)){m=b[(65535&m)+(d&(1<<w)-1)];continue t}if(32&w){r.mode=12;break e}e.msg="invalid literal/length code",r.mode=30;break e}_=65535&m,(w&=15)&&(p<w&&(d+=O[n++]<<p,p+=8),_+=d&(1<<w)-1,d>>>=w,p-=w),p<15&&(d+=O[n++]<<p,p+=8,d+=O[n++]<<p,p+=8),m=y[d&g];r:for(;;){if(d>>>=w=m>>>24,p-=w,!(16&(w=m>>>16&255))){if(0==(64&w)){m=y[(65535&m)+(d&(1<<w)-1)];continue r}e.msg="invalid distance code",r.mode=30;break e}if(k=65535&m,p<(w&=15)&&(d+=O[n++]<<p,(p+=8)<w&&(d+=O[n++]<<p,p+=8)),(k+=d&(1<<w)-1)>u){e.msg="invalid distance too far back",r.mode=30;break e}if(d>>>=w,p-=w,k>(w=o-a)){if((w=k-w)>f&&r.sane){e.msg="invalid distance too far back",r.mode=30;break e}if(x=0,S=h,0===c){if(x+=l-w,w<_){_-=w;do{T[o++]=h[x++]}while(--w);x=o-k,S=T}}else if(c<w){if(x+=l+c-w,(w-=c)<_){_-=w;do{T[o++]=h[x++]}while(--w);if(x=0,c<_){_-=w=c;do{T[o++]=h[x++]}while(--w);x=o-k,S=T}}}else if(x+=c-w,w<_){_-=w;do{T[o++]=h[x++]}while(--w);x=o-k,S=T}for(;_>2;)T[o++]=S[x++],T[o++]=S[x++],T[o++]=S[x++],_-=3;_&&(T[o++]=S[x++],_>1&&(T[o++]=S[x++]))}else{x=o-k;do{T[o++]=T[x++],T[o++]=T[x++],T[o++]=T[x++],_-=3}while(_>2);_&&(T[o++]=T[x++],_>1&&(T[o++]=T[x++]))}break}}break}}while(n<i&&o<s);n-=_=p>>3,d&=(1<<(p-=_<<3))-1,e.next_in=n,e.next_out=o,e.avail_in=n<i?i-n+5:5-(n-i),e.avail_out=o<s?s-o+257:257-(o-s),r.hold=d,r.bits=p}},{}],uNlq:[function(e,t,r){"use strict";var n=e("../utils/common"),i=15,o=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],a=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],s=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],u=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,r,l,f,c,h,d){var p,b,y,v,g,m,w,_,k,x=d.bits,S=0,O=0,T=0,E=0,C=0,P=0,j=0,R=0,M=0,A=0,I=null,D=0,L=new n.Buf16(16),N=new n.Buf16(16),F=null,U=0;for(S=0;S<=i;S++)L[S]=0;for(O=0;O<l;O++)L[t[r+O]]++;for(C=x,E=i;E>=1&&0===L[E];E--);if(C>E&&(C=E),0===E)return f[c++]=20971520,f[c++]=20971520,d.bits=1,0;for(T=1;T<E&&0===L[T];T++);for(C<T&&(C=T),R=1,S=1;S<=i;S++)if(R<<=1,(R-=L[S])<0)return-1;if(R>0&&(0===e||1!==E))return-1;for(N[1]=0,S=1;S<i;S++)N[S+1]=N[S]+L[S];for(O=0;O<l;O++)0!==t[r+O]&&(h[N[t[r+O]]++]=O);if(0===e?(I=F=h,m=19):1===e?(I=o,D-=257,F=a,U-=257,m=256):(I=s,F=u,m=-1),A=0,O=0,S=T,g=c,P=C,j=0,y=-1,v=(M=1<<C)-1,1===e&&M>852||2===e&&M>592)return 1;for(;;){w=S-j,h[O]<m?(_=0,k=h[O]):h[O]>m?(_=F[U+h[O]],k=I[D+h[O]]):(_=96,k=0),p=1<<S-j,T=b=1<<P;do{f[g+(A>>j)+(b-=p)]=w<<24|_<<16|k|0}while(0!==b);for(p=1<<S-1;A&p;)p>>=1;if(0!==p?(A&=p-1,A+=p):A=0,O++,0==--L[S]){if(S===E)break;S=t[r+h[O]]}if(S>C&&(A&v)!==y){for(0===j&&(j=C),g+=T,R=1<<(P=S-j);P+j<E&&!((R-=L[P+j])<=0);)P++,R<<=1;if(M+=1<<P,1===e&&M>852||2===e&&M>592)return 1;f[y=A&v]=C<<24|P<<16|g-c|0}}return 0!==A&&(f[g+A]=S-j<<24|64<<16|0),d.bits=C,0}},{"../utils/common":"tbG5"}],GIDK:[function(e,t,r){"use strict";var n=e("../utils/common"),i=e("./adler32"),o=e("./crc32"),a=e("./inffast"),s=e("./inftrees"),u=-2,l=12,f=30;function c(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function h(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new n.Buf16(320),this.work=new n.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function d(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=1,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new n.Buf32(852),t.distcode=t.distdyn=new n.Buf32(592),t.sane=1,t.back=-1,0):u}function p(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,d(e)):u}function b(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?u:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,p(e))):u}function y(e,t){var r,n;return e?(n=new h,e.state=n,n.window=null,0!==(r=b(e,t))&&(e.state=null),r):u}var v,g,m=!0;function w(e){if(m){var t;for(v=new n.Buf32(512),g=new n.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(s(1,e.lens,0,288,v,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;s(2,e.lens,0,32,g,0,e.work,{bits:5}),m=!1}e.lencode=v,e.lenbits=9,e.distcode=g,e.distbits=5}function _(e,t,r,i){var o,a=e.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new n.Buf8(a.wsize)),i>=a.wsize?(n.arraySet(a.window,t,r-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):((o=a.wsize-a.wnext)>i&&(o=i),n.arraySet(a.window,t,r-i,o,a.wnext),(i-=o)?(n.arraySet(a.window,t,r-i,i,0),a.wnext=i,a.whave=a.wsize):(a.wnext+=o,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=o))),0}r.inflateReset=p,r.inflateReset2=b,r.inflateResetKeep=d,r.inflateInit=function(e){return y(e,15)},r.inflateInit2=y,r.inflate=function(e,t){var r,h,d,p,b,y,v,g,m,k,x,S,O,T,E,C,P,j,R,M,A,I,D,L,N=0,F=new n.Buf8(4),U=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return u;(r=e.state).mode===l&&(r.mode=13),b=e.next_out,d=e.output,v=e.avail_out,p=e.next_in,h=e.input,y=e.avail_in,g=r.hold,m=r.bits,k=y,x=v,I=0;e:for(;;)switch(r.mode){case 1:if(0===r.wrap){r.mode=13;break}for(;m<16;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}if(2&r.wrap&&35615===g){r.check=0,F[0]=255&g,F[1]=g>>>8&255,r.check=o(r.check,F,2,0),g=0,m=0,r.mode=2;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&g)<<8)+(g>>8))%31){e.msg="incorrect header check",r.mode=f;break}if(8!=(15&g)){e.msg="unknown compression method",r.mode=f;break}if(m-=4,A=8+(15&(g>>>=4)),0===r.wbits)r.wbits=A;else if(A>r.wbits){e.msg="invalid window size",r.mode=f;break}r.dmax=1<<A,e.adler=r.check=1,r.mode=512&g?10:l,g=0,m=0;break;case 2:for(;m<16;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}if(r.flags=g,8!=(255&r.flags)){e.msg="unknown compression method",r.mode=f;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=f;break}r.head&&(r.head.text=g>>8&1),512&r.flags&&(F[0]=255&g,F[1]=g>>>8&255,r.check=o(r.check,F,2,0)),g=0,m=0,r.mode=3;case 3:for(;m<32;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}r.head&&(r.head.time=g),512&r.flags&&(F[0]=255&g,F[1]=g>>>8&255,F[2]=g>>>16&255,F[3]=g>>>24&255,r.check=o(r.check,F,4,0)),g=0,m=0,r.mode=4;case 4:for(;m<16;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}r.head&&(r.head.xflags=255&g,r.head.os=g>>8),512&r.flags&&(F[0]=255&g,F[1]=g>>>8&255,r.check=o(r.check,F,2,0)),g=0,m=0,r.mode=5;case 5:if(1024&r.flags){for(;m<16;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}r.length=g,r.head&&(r.head.extra_len=g),512&r.flags&&(F[0]=255&g,F[1]=g>>>8&255,r.check=o(r.check,F,2,0)),g=0,m=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&((S=r.length)>y&&(S=y),S&&(r.head&&(A=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),n.arraySet(r.head.extra,h,p,S,A)),512&r.flags&&(r.check=o(r.check,h,S,p)),y-=S,p+=S,r.length-=S),r.length))break e;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===y)break e;S=0;do{A=h[p+S++],r.head&&A&&r.length<65536&&(r.head.name+=String.fromCharCode(A))}while(A&&S<y);if(512&r.flags&&(r.check=o(r.check,h,S,p)),y-=S,p+=S,A)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===y)break e;S=0;do{A=h[p+S++],r.head&&A&&r.length<65536&&(r.head.comment+=String.fromCharCode(A))}while(A&&S<y);if(512&r.flags&&(r.check=o(r.check,h,S,p)),y-=S,p+=S,A)break e}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;m<16;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}if(g!==(65535&r.check)){e.msg="header crc mismatch",r.mode=f;break}g=0,m=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=l;break;case 10:for(;m<32;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}e.adler=r.check=c(g),g=0,m=0,r.mode=11;case 11:if(0===r.havedict)return e.next_out=b,e.avail_out=v,e.next_in=p,e.avail_in=y,r.hold=g,r.bits=m,2;e.adler=r.check=1,r.mode=l;case l:if(5===t||6===t)break e;case 13:if(r.last){g>>>=7&m,m-=7&m,r.mode=27;break}for(;m<3;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}switch(r.last=1&g,m-=1,3&(g>>>=1)){case 0:r.mode=14;break;case 1:if(w(r),r.mode=20,6===t){g>>>=2,m-=2;break e}break;case 2:r.mode=17;break;case 3:e.msg="invalid block type",r.mode=f}g>>>=2,m-=2;break;case 14:for(g>>>=7&m,m-=7&m;m<32;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}if((65535&g)!=(g>>>16^65535)){e.msg="invalid stored block lengths",r.mode=f;break}if(r.length=65535&g,g=0,m=0,r.mode=15,6===t)break e;case 15:r.mode=16;case 16:if(S=r.length){if(S>y&&(S=y),S>v&&(S=v),0===S)break e;n.arraySet(d,h,p,S,b),y-=S,p+=S,v-=S,b+=S,r.length-=S;break}r.mode=l;break;case 17:for(;m<14;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}if(r.nlen=257+(31&g),g>>>=5,m-=5,r.ndist=1+(31&g),g>>>=5,m-=5,r.ncode=4+(15&g),g>>>=4,m-=4,r.nlen>286||r.ndist>30){e.msg="too many length or distance symbols",r.mode=f;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;m<3;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}r.lens[U[r.have++]]=7&g,g>>>=3,m-=3}for(;r.have<19;)r.lens[U[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,D={bits:r.lenbits},I=s(0,r.lens,0,19,r.lencode,0,r.work,D),r.lenbits=D.bits,I){e.msg="invalid code lengths set",r.mode=f;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;C=(N=r.lencode[g&(1<<r.lenbits)-1])>>>16&255,P=65535&N,!((E=N>>>24)<=m);){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}if(P<16)g>>>=E,m-=E,r.lens[r.have++]=P;else{if(16===P){for(L=E+2;m<L;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}if(g>>>=E,m-=E,0===r.have){e.msg="invalid bit length repeat",r.mode=f;break}A=r.lens[r.have-1],S=3+(3&g),g>>>=2,m-=2}else if(17===P){for(L=E+3;m<L;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}m-=E,A=0,S=3+(7&(g>>>=E)),g>>>=3,m-=3}else{for(L=E+7;m<L;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}m-=E,A=0,S=11+(127&(g>>>=E)),g>>>=7,m-=7}if(r.have+S>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=f;break}for(;S--;)r.lens[r.have++]=A}}if(r.mode===f)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=f;break}if(r.lenbits=9,D={bits:r.lenbits},I=s(1,r.lens,0,r.nlen,r.lencode,0,r.work,D),r.lenbits=D.bits,I){e.msg="invalid literal/lengths set",r.mode=f;break}if(r.distbits=6,r.distcode=r.distdyn,D={bits:r.distbits},I=s(2,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,D),r.distbits=D.bits,I){e.msg="invalid distances set",r.mode=f;break}if(r.mode=20,6===t)break e;case 20:r.mode=21;case 21:if(y>=6&&v>=258){e.next_out=b,e.avail_out=v,e.next_in=p,e.avail_in=y,r.hold=g,r.bits=m,a(e,x),b=e.next_out,d=e.output,v=e.avail_out,p=e.next_in,h=e.input,y=e.avail_in,g=r.hold,m=r.bits,r.mode===l&&(r.back=-1);break}for(r.back=0;C=(N=r.lencode[g&(1<<r.lenbits)-1])>>>16&255,P=65535&N,!((E=N>>>24)<=m);){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}if(C&&0==(240&C)){for(j=E,R=C,M=P;C=(N=r.lencode[M+((g&(1<<j+R)-1)>>j)])>>>16&255,P=65535&N,!(j+(E=N>>>24)<=m);){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}g>>>=j,m-=j,r.back+=j}if(g>>>=E,m-=E,r.back+=E,r.length=P,0===C){r.mode=26;break}if(32&C){r.back=-1,r.mode=l;break}if(64&C){e.msg="invalid literal/length code",r.mode=f;break}r.extra=15&C,r.mode=22;case 22:if(r.extra){for(L=r.extra;m<L;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}r.length+=g&(1<<r.extra)-1,g>>>=r.extra,m-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;C=(N=r.distcode[g&(1<<r.distbits)-1])>>>16&255,P=65535&N,!((E=N>>>24)<=m);){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}if(0==(240&C)){for(j=E,R=C,M=P;C=(N=r.distcode[M+((g&(1<<j+R)-1)>>j)])>>>16&255,P=65535&N,!(j+(E=N>>>24)<=m);){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}g>>>=j,m-=j,r.back+=j}if(g>>>=E,m-=E,r.back+=E,64&C){e.msg="invalid distance code",r.mode=f;break}r.offset=P,r.extra=15&C,r.mode=24;case 24:if(r.extra){for(L=r.extra;m<L;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}r.offset+=g&(1<<r.extra)-1,g>>>=r.extra,m-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=f;break}r.mode=25;case 25:if(0===v)break e;if(S=x-v,r.offset>S){if((S=r.offset-S)>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=f;break}S>r.wnext?(S-=r.wnext,O=r.wsize-S):O=r.wnext-S,S>r.length&&(S=r.length),T=r.window}else T=d,O=b-r.offset,S=r.length;S>v&&(S=v),v-=S,r.length-=S;do{d[b++]=T[O++]}while(--S);0===r.length&&(r.mode=21);break;case 26:if(0===v)break e;d[b++]=r.length,v--,r.mode=21;break;case 27:if(r.wrap){for(;m<32;){if(0===y)break e;y--,g|=h[p++]<<m,m+=8}if(x-=v,e.total_out+=x,r.total+=x,x&&(e.adler=r.check=r.flags?o(r.check,d,x,b-x):i(r.check,d,x,b-x)),x=v,(r.flags?g:c(g))!==r.check){e.msg="incorrect data check",r.mode=f;break}g=0,m=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;m<32;){if(0===y)break e;y--,g+=h[p++]<<m,m+=8}if(g!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=f;break}g=0,m=0}r.mode=29;case 29:I=1;break e;case f:I=-3;break e;case 31:return-4;case 32:default:return u}return e.next_out=b,e.avail_out=v,e.next_in=p,e.avail_in=y,r.hold=g,r.bits=m,(r.wsize||x!==e.avail_out&&r.mode<f&&(r.mode<27||4!==t))&&_(e,e.output,e.next_out,x-e.avail_out)?(r.mode=31,-4):(k-=e.avail_in,x-=e.avail_out,e.total_in+=k,e.total_out+=x,r.total+=x,r.wrap&&x&&(e.adler=r.check=r.flags?o(r.check,d,x,e.next_out-x):i(r.check,d,x,e.next_out-x)),e.data_type=r.bits+(r.last?64:0)+(r.mode===l?128:0)+(20===r.mode||15===r.mode?256:0),(0===k&&0===x||4===t)&&0===I&&(I=-5),I)},r.inflateEnd=function(e){if(!e||!e.state)return u;var t=e.state;return t.window&&(t.window=null),e.state=null,0},r.inflateGetHeader=function(e,t){var r;return e&&e.state?0==(2&(r=e.state).wrap)?u:(r.head=t,t.done=!1,0):u},r.inflateSetDictionary=function(e,t){var r,n=t.length;return e&&e.state?0!==(r=e.state).wrap&&11!==r.mode?u:11===r.mode&&i(1,t,n,0)!==r.check?-3:_(e,t,n,n)?(r.mode=31,-4):(r.havedict=1,0):u},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":"tbG5","./adler32":"uxo6","./crc32":"X4kj","./inffast":"LP5M","./inftrees":"uNlq"}],Q3ZD:[function(e,t,r){"use strict";var n=e("./common"),i=!0,o=!0;try{String.fromCharCode.apply(null,[0])}catch(e){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){o=!1}for(var a=new n.Buf8(256),s=0;s<256;s++)a[s]=s>=252?6:s>=248?5:s>=240?4:s>=224?3:s>=192?2:1;function u(e,t){if(t<65534&&(e.subarray&&o||!e.subarray&&i))return String.fromCharCode.apply(null,n.shrinkBuf(e,t));for(var r="",a=0;a<t;a++)r+=String.fromCharCode(e[a]);return r}a[254]=a[254]=1,r.string2buf=function(e){var t,r,i,o,a,s=e.length,u=0;for(o=0;o<s;o++)55296==(64512&(r=e.charCodeAt(o)))&&o+1<s&&56320==(64512&(i=e.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(i-56320),o++),u+=r<128?1:r<2048?2:r<65536?3:4;for(t=new n.Buf8(u),a=0,o=0;a<u;o++)55296==(64512&(r=e.charCodeAt(o)))&&o+1<s&&56320==(64512&(i=e.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(i-56320),o++),r<128?t[a++]=r:r<2048?(t[a++]=192|r>>>6,t[a++]=128|63&r):r<65536?(t[a++]=224|r>>>12,t[a++]=128|r>>>6&63,t[a++]=128|63&r):(t[a++]=240|r>>>18,t[a++]=128|r>>>12&63,t[a++]=128|r>>>6&63,t[a++]=128|63&r);return t},r.buf2binstring=function(e){return u(e,e.length)},r.binstring2buf=function(e){for(var t=new n.Buf8(e.length),r=0,i=t.length;r<i;r++)t[r]=e.charCodeAt(r);return t},r.buf2string=function(e,t){var r,n,i,o,s=t||e.length,l=new Array(2*s);for(n=0,r=0;r<s;)if((i=e[r++])<128)l[n++]=i;else if((o=a[i])>4)l[n++]=65533,r+=o-1;else{for(i&=2===o?31:3===o?15:7;o>1&&r<s;)i=i<<6|63&e[r++],o--;o>1?l[n++]=65533:i<65536?l[n++]=i:(i-=65536,l[n++]=55296|i>>10&1023,l[n++]=56320|1023&i)}return u(l,n)},r.utf8border=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;r>=0&&128==(192&e[r]);)r--;return r<0||0===r?t:r+a[e[r]]>t?r:t}},{"./common":"tbG5"}],xUUw:[function(e,t,r){"use strict";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],gMAY:[function(e,t,r){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],bdtv:[function(e,t,r){"use strict";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],WIli:[function(e,t,r){"use strict";t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],faQk:[function(e,t,r){"use strict";var n=e("./zlib/inflate"),i=e("./utils/common"),o=e("./utils/strings"),a=e("./zlib/constants"),s=e("./zlib/messages"),u=e("./zlib/zstream"),l=e("./zlib/gzheader"),f=Object.prototype.toString;function c(e){if(!(this instanceof c))return new c(e);this.options=i.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new u,this.strm.avail_out=0;var r=n.inflateInit2(this.strm,t.windowBits);if(r!==a.Z_OK)throw new Error(s[r]);if(this.header=new l,n.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=o.string2buf(t.dictionary):"[object ArrayBuffer]"===f.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(r=n.inflateSetDictionary(this.strm,t.dictionary))!==a.Z_OK))throw new Error(s[r])}function h(e,t){var r=new c(t);if(r.push(e,!0),r.err)throw r.msg||s[r.err];return r.result}c.prototype.push=function(e,t){var r,s,u,l,c,h=this.strm,d=this.options.chunkSize,p=this.options.dictionary,b=!1;if(this.ended)return!1;s=t===~~t?t:!0===t?a.Z_FINISH:a.Z_NO_FLUSH,"string"==typeof e?h.input=o.binstring2buf(e):"[object ArrayBuffer]"===f.call(e)?h.input=new Uint8Array(e):h.input=e,h.next_in=0,h.avail_in=h.input.length;do{if(0===h.avail_out&&(h.output=new i.Buf8(d),h.next_out=0,h.avail_out=d),(r=n.inflate(h,a.Z_NO_FLUSH))===a.Z_NEED_DICT&&p&&(r=n.inflateSetDictionary(this.strm,p)),r===a.Z_BUF_ERROR&&!0===b&&(r=a.Z_OK,b=!1),r!==a.Z_STREAM_END&&r!==a.Z_OK)return this.onEnd(r),this.ended=!0,!1;h.next_out&&(0!==h.avail_out&&r!==a.Z_STREAM_END&&(0!==h.avail_in||s!==a.Z_FINISH&&s!==a.Z_SYNC_FLUSH)||("string"===this.options.to?(u=o.utf8border(h.output,h.next_out),l=h.next_out-u,c=o.buf2string(h.output,u),h.next_out=l,h.avail_out=d-l,l&&i.arraySet(h.output,h.output,u,l,0),this.onData(c)):this.onData(i.shrinkBuf(h.output,h.next_out)))),0===h.avail_in&&0===h.avail_out&&(b=!0)}while((h.avail_in>0||0===h.avail_out)&&r!==a.Z_STREAM_END);return r===a.Z_STREAM_END&&(s=a.Z_FINISH),s===a.Z_FINISH?(r=n.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===a.Z_OK):s!==a.Z_SYNC_FLUSH||(this.onEnd(a.Z_OK),h.avail_out=0,!0)},c.prototype.onData=function(e){this.chunks.push(e)},c.prototype.onEnd=function(e){e===a.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Inflate=c,r.inflate=h,r.inflateRaw=function(e,t){return(t=t||{}).raw=!0,h(e,t)},r.ungzip=h},{"./zlib/inflate":"GIDK","./utils/common":"tbG5","./utils/strings":"Q3ZD","./zlib/constants":"xUUw","./zlib/messages":"gMAY","./zlib/zstream":"bdtv","./zlib/gzheader":"WIli"}],JAiC:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=l(e("@babel/runtime/helpers/classCallCheck")),i=l(e("@babel/runtime/helpers/createClass")),o=l(e("@babel/runtime/helpers/inherits")),a=l(e("@babel/runtime/helpers/possibleConstructorReturn")),s=l(e("@babel/runtime/helpers/getPrototypeOf")),u=e("pako/lib/inflate");function l(e){return e&&e.__esModule?e:{default:e}}function f(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,s.default)(e);if(t){var i=(0,s.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,a.default)(this,r)}}var c=function(e){(0,o.default)(r,e);var t=f(r);function r(){return(0,n.default)(this,r),t.apply(this,arguments)}return(0,i.default)(r,[{key:"decodeBlock",value:function(e){return(0,u.inflate)(new Uint8Array(e)).buffer}}]),r}(l(e("./basedecoder")).default);r.default=c},{"@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","pako/lib/inflate":"faQk","./basedecoder":"FJDe"}],OcPz:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=u(e("@babel/runtime/helpers/classCallCheck")),i=u(e("@babel/runtime/helpers/createClass")),o=u(e("@babel/runtime/helpers/inherits")),a=u(e("@babel/runtime/helpers/possibleConstructorReturn")),s=u(e("@babel/runtime/helpers/getPrototypeOf"));function u(e){return e&&e.__esModule?e:{default:e}}function l(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,s.default)(e);if(t){var i=(0,s.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,a.default)(this,r)}}var f=function(e){(0,o.default)(r,e);var t=l(r);function r(){return(0,n.default)(this,r),t.apply(this,arguments)}return(0,i.default)(r,[{key:"decodeBlock",value:function(e){for(var t=new DataView(e),r=[],n=0;n<e.byteLength;++n){var i=t.getInt8(n);if(i<0){var o=t.getUint8(n+1);i=-i;for(var a=0;a<=i;++a)r.push(o);n+=1}else{for(var s=0;s<=i;++s)r.push(t.getUint8(n+s+1));n+=i+1}}return new Uint8Array(r).buffer}}]),r}(u(e("./basedecoder")).default);r.default=f},{"@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","./basedecoder":"FJDe"}],FGCZ:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.getDecoder=function(e){switch(e.Compression){case void 0:case 1:return new n.default;case 5:return new i.default;case 6:throw new Error("old style JPEG compression is not supported.");case 7:return new o.default(e);case 8:case 32946:return new a.default;case 32773:return new s.default;default:throw new Error("Unknown compression method identifier: ".concat(e.Compression))}};var n=u(e("./raw")),i=u(e("./lzw")),o=u(e("./jpeg")),a=u(e("./deflate")),s=u(e("./packbits"));function u(e){return e&&e.__esModule?e:{default:e}}},{"./raw":"V9bQ","./lzw":"tcId","./jpeg":"J7kk","./deflate":"JAiC","./packbits":"OcPz"}],OQju:[function(e,t,r){"use strict";function n(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;return new(Object.getPrototypeOf(e).constructor)(t*r*n)}function i(e,t,r,i,o){var a=t/i,s=r/o;return e.map((function(e){for(var u=n(e,i,o),l=0;l<o;++l)for(var f=Math.min(Math.round(s*l),r-1),c=0;c<i;++c){var h=Math.min(Math.round(a*c),t-1),d=e[f*t+h];u[l*i+c]=d}return u}))}function o(e,t,r){return(1-r)*e+r*t}function a(e,t,r,i,a){var s=t/i,u=r/a;return e.map((function(e){for(var l=n(e,i,a),f=0;f<a;++f)for(var c=u*f,h=Math.floor(c),d=Math.min(Math.ceil(c),r-1),p=0;p<i;++p){var b=s*p,y=b%1,v=Math.floor(b),g=Math.min(Math.ceil(b),t-1),m=e[h*t+v],w=e[h*t+g],_=e[d*t+v],k=e[d*t+g],x=o(o(m,w,y),o(_,k,y),c%1);l[f*i+p]=x}return l}))}function s(e,t,r,i,o,a){for(var s=t/i,u=r/o,l=n(e,i,o,a),f=0;f<o;++f)for(var c=Math.min(Math.round(u*f),r-1),h=0;h<i;++h)for(var d=Math.min(Math.round(s*h),t-1),p=0;p<a;++p){var b=e[c*t*a+d*a+p];l[f*i*a+h*a+p]=b}return l}function u(e,t,r,i,a,s){for(var u=t/i,l=r/a,f=n(e,i,a,s),c=0;c<a;++c)for(var h=l*c,d=Math.floor(h),p=Math.min(Math.ceil(h),r-1),b=0;b<i;++b)for(var y=u*b,v=y%1,g=Math.floor(y),m=Math.min(Math.ceil(y),t-1),w=0;w<s;++w){var _=e[d*t*s+g*s+w],k=e[d*t*s+m*s+w],x=e[p*t*s+g*s+w],S=e[p*t*s+m*s+w],O=o(o(_,k,v),o(x,S,v),h%1);f[c*i*s+b*s+w]=O}return f}Object.defineProperty(r,"__esModule",{value:!0}),r.resampleNearest=i,r.resampleBilinear=a,r.resample=function(e,t,r,n,o){var s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"nearest";switch(s.toLowerCase()){case"nearest":return i(e,t,r,n,o);case"bilinear":case"linear":return a(e,t,r,n,o);default:throw new Error("Unsupported resampling method: '".concat(s,"'"))}},r.resampleNearestInterleaved=s,r.resampleBilinearInterleaved=u,r.resampleInterleaved=function(e,t,r,n,i,o){var a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"nearest";switch(a.toLowerCase()){case"nearest":return s(e,t,r,n,i,o);case"bilinear":case"linear":return u(e,t,r,n,i,o);default:throw new Error("Unsupported resampling method: '".concat(a,"'"))}}},{}],eOWo:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=p(e("@babel/runtime/helpers/slicedToArray")),i=p(e("@babel/runtime/regenerator")),o=p(e("@babel/runtime/helpers/asyncToGenerator")),a=p(e("@babel/runtime/helpers/classCallCheck")),s=p(e("@babel/runtime/helpers/createClass")),u=e("@petamoriken/float16"),l=p(e("txml")),f=e("./globals"),c=e("./rgb"),h=e("./compression"),d=e("./resample");function p(e){return e&&e.__esModule?e:{default:e}}function b(e,t,r){for(var n=0,i=t;i<r;++i)n+=e[i];return n}function y(e,t,r){switch(e){case 1:if(t<=8)return new Uint8Array(r);if(t<=16)return new Uint16Array(r);if(t<=32)return new Uint32Array(r);break;case 2:if(8===t)return new Int8Array(r);if(16===t)return new Int16Array(r);if(32===t)return new Int32Array(r);break;case 3:switch(t){case 16:case 32:return new Float32Array(r);case 64:return new Float64Array(r)}}throw Error("Unsupported data format/bitsPerSample")}function v(e,t){return(1!==e&&2!==e||!(t<=32)||t%8!=0)&&(3!==e||16!==t&&32!==t&&64!==t)}function g(e,t,r,n,i,o,a){var s=new DataView(e),u=2===r?1:n,l=y(t,i,2===r?a*o:a*o*n),f=parseInt("1".repeat(i),2);if(1===t){var c=o*(1===r?n*i:i);0!=(7&c)&&(c=c+7&-8);for(var h=0;h<a;++h)for(var d=h*c,p=0;p<o;++p)for(var b=d+p*u*i,v=0;v<u;++v){var g=b+v*i,m=(h*o+p)*u+v,w=Math.floor(g/8),_=g%8;if(_+i<=8)l[m]=s.getUint8(w)>>8-i-_&f;else if(_+i<=16)l[m]=s.getUint16(w)>>16-i-_&f;else if(_+i<=24){var k=s.getUint16(w)<<8|s.getUint8(w+2);l[m]=k>>24-i-_&f}else l[m]=s.getUint32(w)>>32-i-_&f}}return l.buffer}var m=function(){function e(t,r,n,i,o,s){(0,a.default)(this,e),this.fileDirectory=t,this.geoKeys=r,this.dataView=n,this.littleEndian=i,this.tiles=o?{}:null,this.isTiled=!t.StripOffsets;var u=t.PlanarConfiguration;if(this.planarConfiguration=void 0===u?1:u,1!==this.planarConfiguration&&2!==this.planarConfiguration)throw new Error("Invalid planar configuration.");this.source=s}return(0,s.default)(e,[{key:"getFileDirectory",value:function(){return this.fileDirectory}},{key:"getGeoKeys",value:function(){return this.geoKeys}},{key:"getWidth",value:function(){return this.fileDirectory.ImageWidth}},{key:"getHeight",value:function(){return this.fileDirectory.ImageLength}},{key:"getSamplesPerPixel",value:function(){return void 0!==this.fileDirectory.SamplesPerPixel?this.fileDirectory.SamplesPerPixel:1}},{key:"getTileWidth",value:function(){return this.isTiled?this.fileDirectory.TileWidth:this.getWidth()}},{key:"getTileHeight",value:function(){return this.isTiled?this.fileDirectory.TileLength:void 0!==this.fileDirectory.RowsPerStrip?Math.min(this.fileDirectory.RowsPerStrip,this.getHeight()):this.getHeight()}},{key:"getBlockWidth",value:function(){return this.getTileWidth()}},{key:"getBlockHeight",value:function(e){return this.isTiled||(e+1)*this.getTileHeight()<=this.getHeight()?this.getTileHeight():this.getHeight()-e*this.getTileHeight()}},{key:"getBytesPerPixel",value:function(){for(var e=0,t=0;t<this.fileDirectory.BitsPerSample.length;++t)e+=this.getSampleByteSize(t);return e}},{key:"getSampleByteSize",value:function(e){if(e>=this.fileDirectory.BitsPerSample.length)throw new RangeError("Sample index ".concat(e," is out of range."));return Math.ceil(this.fileDirectory.BitsPerSample[e]/8)}},{key:"getReaderForSample",value:function(e){var t=this.fileDirectory.SampleFormat?this.fileDirectory.SampleFormat[e]:1,r=this.fileDirectory.BitsPerSample[e];switch(t){case 1:if(r<=8)return DataView.prototype.getUint8;if(r<=16)return DataView.prototype.getUint16;if(r<=32)return DataView.prototype.getUint32;break;case 2:if(r<=8)return DataView.prototype.getInt8;if(r<=16)return DataView.prototype.getInt16;if(r<=32)return DataView.prototype.getInt32;break;case 3:switch(r){case 16:return function(e,t){return(0,u.getFloat16)(this,e,t)};case 32:return DataView.prototype.getFloat32;case 64:return DataView.prototype.getFloat64}}throw Error("Unsupported data format/bitsPerSample")}},{key:"getSampleFormat",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.fileDirectory.SampleFormat?this.fileDirectory.SampleFormat[e]:1}},{key:"getBitsPerSample",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.fileDirectory.BitsPerSample[e]}},{key:"getArrayForSample",value:function(e,t){return y(this.getSampleFormat(e),this.getBitsPerSample(e),t)}},{key:"getTileOrStrip",value:function(){var e=(0,o.default)(i.default.mark((function e(t,r,n,a,s){var u,l,f,c,h,d,p,b,y=this;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return u=Math.ceil(this.getWidth()/this.getTileWidth()),l=Math.ceil(this.getHeight()/this.getTileHeight()),c=this.tiles,1===this.planarConfiguration?f=r*u+t:2===this.planarConfiguration&&(f=n*u*l+r*u+t),this.isTiled?(h=this.fileDirectory.TileOffsets[f],d=this.fileDirectory.TileByteCounts[f]):(h=this.fileDirectory.StripOffsets[f],d=this.fileDirectory.StripByteCounts[f]),e.next=7,this.source.fetch([{offset:h,length:d}],s);case 7:return p=e.sent[0],null!==c&&c[f]?b=c[f]:(b=(0,o.default)(i.default.mark((function e(){var t,n,o;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a.decode(y.fileDirectory,p);case 2:return t=e.sent,n=y.getSampleFormat(),o=y.getBitsPerSample(),v(n,o)&&(t=g(t,n,y.planarConfiguration,y.getSamplesPerPixel(),o,y.getTileWidth(),y.getBlockHeight(r))),e.abrupt("return",t);case 7:case"end":return e.stop()}}),e)})))(),null!==c&&(c[f]=b)),e.t0=t,e.t1=r,e.t2=n,e.next=14,b;case 14:return e.t3=e.sent,e.abrupt("return",{x:e.t0,y:e.t1,sample:e.t2,data:e.t3});case 16:case"end":return e.stop()}}),e,this)})));return function(t,r,n,i,o){return e.apply(this,arguments)}}()},{key:"_readRaster",value:function(){var e=(0,o.default)(i.default.mark((function e(t,r,n,o,a,s,u,l,f){var c,h,p,y,v,g,m,w,_,k,x,S,O,T,E,C,P,j,R=this;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(c=this.getTileWidth(),h=this.getTileHeight(),p=Math.max(Math.floor(t[0]/c),0),y=Math.min(Math.ceil(t[2]/c),Math.ceil(this.getWidth()/this.getTileWidth())),v=Math.max(Math.floor(t[1]/h),0),g=Math.min(Math.ceil(t[3]/h),Math.ceil(this.getHeight()/this.getTileHeight())),m=t[2]-t[0],w=this.getBytesPerPixel(),_=[],k=[],x=0;x<r.length;++x)1===this.planarConfiguration?_.push(b(this.fileDirectory.BitsPerSample,0,r[x])/8):_.push(0),k.push(this.getReaderForSample(r[x]));for(S=[],O=this.littleEndian,T=v;T<g;++T)for(E=p;E<y;++E)for(C=function(e){var i=e,s=r[e];2===R.planarConfiguration&&(w=R.getSampleByteSize(e));var u=R.getTileOrStrip(E,T,s,a,f);S.push(u),u.then((function(e){for(var a=e.data,s=new DataView(a),u=R.getBlockHeight(e.y),l=e.y*h,f=e.x*c,d=l+u,p=(e.x+1)*c,b=k[i],y=Math.min(u,u-(d-t[3])),v=Math.min(c,c-(p-t[2])),g=Math.max(0,t[1]-l);g<y;++g)for(var x=Math.max(0,t[0]-f);x<v;++x){var S=(g*c+x)*w,T=b.call(s,S+_[i],O),E=void 0;o?(E=(g+l-t[1])*m*r.length+(x+f-t[0])*r.length+i,n[E]=T):(E=(g+l-t[1])*m+x+f-t[0],n[i][E]=T)}}))},P=0;P<r.length;++P)C(P);return e.next=16,Promise.all(S);case 16:if(!(s&&t[2]-t[0]!==s||u&&t[3]-t[1]!==u)){e.next=21;break}return(j=o?(0,d.resampleInterleaved)(n,t[2]-t[0],t[3]-t[1],s,u,r.length,l):(0,d.resample)(n,t[2]-t[0],t[3]-t[1],s,u,l)).width=s,j.height=u,e.abrupt("return",j);case 21:return n.width=s||t[2]-t[0],n.height=u||t[3]-t[1],e.abrupt("return",n);case 24:case"end":return e.stop()}}),e,this)})));return function(t,r,n,i,o,a,s,u,l){return e.apply(this,arguments)}}()},{key:"readRasters",value:function(){var e=(0,o.default)(i.default.mark((function e(){var t,r,n,o,a,s,u,l,f,c,d,p,b,v,g,m,w,_,k,x,S,O,T,E,C,P,j=arguments;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=j.length>0&&void 0!==j[0]?j[0]:{},r=t.window,n=t.samples,o=void 0===n?[]:n,a=t.interleave,s=t.pool,u=void 0===s?null:s,l=t.width,f=t.height,c=t.resampleMethod,d=t.fillValue,p=t.signal,!((b=r||[0,0,this.getWidth(),this.getHeight()])[0]>b[2]||b[1]>b[3])){e.next=4;break}throw new Error("Invalid subsets");case 4:if(v=b[2]-b[0],g=b[3]-b[1],m=v*g,w=this.getSamplesPerPixel(),o&&o.length){e.next=12;break}for(_=0;_<w;++_)o.push(_);e.next=19;break;case 12:k=0;case 13:if(!(k<o.length)){e.next=19;break}if(!(o[k]>=w)){e.next=16;break}return e.abrupt("return",Promise.reject(new RangeError("Invalid sample index '".concat(o[k],"'."))));case 16:++k,e.next=13;break;case 19:if(a)S=this.fileDirectory.SampleFormat?Math.max.apply(null,this.fileDirectory.SampleFormat):1,O=Math.max.apply(null,this.fileDirectory.BitsPerSample),x=y(S,O,m*o.length),d&&x.fill(d);else for(x=[],T=0;T<o.length;++T)E=this.getArrayForSample(o[T],m),Array.isArray(d)&&T<d.length?E.fill(d[T]):d&&!Array.isArray(d)&&E.fill(d),x.push(E);return C=u||(0,h.getDecoder)(this.fileDirectory),e.next=23,this._readRaster(b,o,x,a,C,l,f,c,p);case 23:return P=e.sent,e.abrupt("return",P);case 25:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"readRGB",value:function(){var e=(0,o.default)(i.default.mark((function e(){var t,r,n,o,a,s,u,l,h,d,p,b,y,v,g,m,w,_,k,x,S=arguments;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=S.length>0&&void 0!==S[0]?S[0]:{},r=t.window,n=t.pool,o=void 0===n?null:n,a=t.width,s=t.height,u=t.resampleMethod,l=t.enableAlpha,h=void 0!==l&&l,d=t.signal,!((p=r||[0,0,this.getWidth(),this.getHeight()])[0]>p[2]||p[1]>p[3])){e.next=4;break}throw new Error("Invalid subsets");case 4:if((b=this.fileDirectory.PhotometricInterpretation)!==f.photometricInterpretations.RGB){e.next=9;break}if(y=[0,1,2],this.fileDirectory.ExtraSamples!==f.ExtraSamplesValues.Unspecified&&h)for(y=[],v=0;v<this.fileDirectory.BitsPerSample.length;v+=1)y.push(v);return e.abrupt("return",this.readRasters({window:r,interleave:!0,samples:y,pool:o,width:a,height:s,resampleMethod:u,signal:d}));case 9:e.t0=b,e.next=e.t0===f.photometricInterpretations.WhiteIsZero||e.t0===f.photometricInterpretations.BlackIsZero||e.t0===f.photometricInterpretations.Palette?12:e.t0===f.photometricInterpretations.CMYK?14:e.t0===f.photometricInterpretations.YCbCr||e.t0===f.photometricInterpretations.CIELab?16:18;break;case 12:return g=[0],e.abrupt("break",19);case 14:return g=[0,1,2,3],e.abrupt("break",19);case 16:return g=[0,1,2],e.abrupt("break",19);case 18:throw new Error("Invalid or unsupported photometric interpretation.");case 19:return m={window:p,interleave:!0,samples:g,pool:o,width:a,height:s,resampleMethod:u,signal:d},w=this.fileDirectory,e.next=23,this.readRasters(m);case 23:_=e.sent,k=Math.pow(2,this.fileDirectory.BitsPerSample[0]),e.t1=b,e.next=e.t1===f.photometricInterpretations.WhiteIsZero?28:e.t1===f.photometricInterpretations.BlackIsZero?30:e.t1===f.photometricInterpretations.Palette?32:e.t1===f.photometricInterpretations.CMYK?34:e.t1===f.photometricInterpretations.YCbCr?36:e.t1===f.photometricInterpretations.CIELab?38:40;break;case 28:return x=(0,c.fromWhiteIsZero)(_,k),e.abrupt("break",41);case 30:return x=(0,c.fromBlackIsZero)(_,k),e.abrupt("break",41);case 32:return x=(0,c.fromPalette)(_,w.ColorMap),e.abrupt("break",41);case 34:return x=(0,c.fromCMYK)(_),e.abrupt("break",41);case 36:return x=(0,c.fromYCbCr)(_),e.abrupt("break",41);case 38:return x=(0,c.fromCIELab)(_),e.abrupt("break",41);case 40:throw new Error("Unsupported photometric interpretation.");case 41:return x.width=_.width,x.height=_.height,e.abrupt("return",x);case 44:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getTiePoints",value:function(){if(!this.fileDirectory.ModelTiepoint)return[];for(var e=[],t=0;t<this.fileDirectory.ModelTiepoint.length;t+=6)e.push({i:this.fileDirectory.ModelTiepoint[t],j:this.fileDirectory.ModelTiepoint[t+1],k:this.fileDirectory.ModelTiepoint[t+2],x:this.fileDirectory.ModelTiepoint[t+3],y:this.fileDirectory.ModelTiepoint[t+4],z:this.fileDirectory.ModelTiepoint[t+5]});return e}},{key:"getGDALMetadata",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t={};if(!this.fileDirectory.GDAL_METADATA)return null;var r=this.fileDirectory.GDAL_METADATA,n=(0,l.default)(r.substring(0,r.length-1));if(!n[0].tagName)throw new Error("Failed to parse GDAL metadata XML.");var i=n[0];if("GDALMetadata"!==i.tagName)throw new Error("Unexpected GDAL metadata XML tag.");var o=i.children.filter((function(e){return"Item"===e.tagName}));null!==e&&(o=o.filter((function(t){return Number(t.attributes.sample)===e})));for(var a=0;a<o.length;++a){var s=o[a];t[s.attributes.name]=s.children[0]}return t}},{key:"getGDALNoData",value:function(){if(!this.fileDirectory.GDAL_NODATA)return null;var e=this.fileDirectory.GDAL_NODATA;return Number(e.substring(0,e.length-1))}},{key:"getOrigin",value:function(){var e=this.fileDirectory.ModelTiepoint,t=this.fileDirectory.ModelTransformation;if(e&&6===e.length)return[e[3],e[4],e[5]];if(t)return[t[3],t[7],t[11]];throw new Error("The image does not have an affine transformation.")}},{key:"getResolution",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=this.fileDirectory.ModelPixelScale,r=this.fileDirectory.ModelTransformation;if(t)return[t[0],-t[1],t[2]];if(r)return[r[0],r[5],r[10]];if(e){var i=e.getResolution(),o=(0,n.default)(i,3),a=o[0],s=o[1],u=o[2];return[a*e.getWidth()/this.getWidth(),s*e.getHeight()/this.getHeight(),u*e.getWidth()/this.getWidth()]}throw new Error("The image does not have an affine transformation.")}},{key:"pixelIsArea",value:function(){return 1===this.geoKeys.GTRasterTypeGeoKey}},{key:"getBoundingBox",value:function(){var e=this.getOrigin(),t=this.getResolution(),r=e[0],n=e[1],i=r+t[0]*this.getWidth(),o=n+t[1]*this.getHeight();return[Math.min(r,i),Math.min(n,o),Math.max(r,i),Math.max(n,o)]}}]),e}();r.default=m},{"@babel/runtime/helpers/slicedToArray":"HETk","@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@petamoriken/float16":"Stm8",txml:"JO74","./globals":"j27V","./rgb":"fpBl","./compression":"FGCZ","./resample":"OQju"}],dqpX:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=a(e("@babel/runtime/helpers/classCallCheck")),i=a(e("@babel/runtime/helpers/createClass")),o=e("@petamoriken/float16");function a(e){return e&&e.__esModule?e:{default:e}}var s=function(){function e(t){(0,n.default)(this,e),this._dataView=new DataView(t)}return(0,i.default)(e,[{key:"getUint64",value:function(e,t){var r,n=this.getUint32(e,t),i=this.getUint32(e+4,t);if(t){if(r=n+Math.pow(2,32)*i,!Number.isSafeInteger(r))throw new Error("".concat(r," exceeds MAX_SAFE_INTEGER. Precision may be lost. Please report if you get this message to https://github.com/geotiffjs/geotiff.js/issues"));return r}if(r=Math.pow(2,32)*n+i,!Number.isSafeInteger(r))throw new Error("".concat(r," exceeds MAX_SAFE_INTEGER. Precision may be lost. Please report if you get this message to https://github.com/geotiffjs/geotiff.js/issues"));return r}},{key:"getInt64",value:function(e,t){for(var r=0,n=(128&this._dataView.getUint8(e+(t?7:0)))>0,i=!0,o=0;o<8;o++){var a=this._dataView.getUint8(e+(t?o:7-o));n&&(i?0!==a&&(a=255&~(a-1),i=!1):a=255&~a),r+=a*Math.pow(256,o)}return n&&(r=-r),r}},{key:"getUint8",value:function(e,t){return this._dataView.getUint8(e,t)}},{key:"getInt8",value:function(e,t){return this._dataView.getInt8(e,t)}},{key:"getUint16",value:function(e,t){return this._dataView.getUint16(e,t)}},{key:"getInt16",value:function(e,t){return this._dataView.getInt16(e,t)}},{key:"getUint32",value:function(e,t){return this._dataView.getUint32(e,t)}},{key:"getInt32",value:function(e,t){return this._dataView.getInt32(e,t)}},{key:"getFloat16",value:function(e,t){return(0,o.getFloat16)(this._dataView,t)}},{key:"getFloat32",value:function(e,t){return this._dataView.getFloat32(e,t)}},{key:"getFloat64",value:function(e,t){return this._dataView.getFloat64(e,t)}},{key:"buffer",get:function(){return this._dataView.buffer}}]),e}();r.default=s},{"@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@petamoriken/float16":"Stm8"}],dGLV:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=o(e("@babel/runtime/helpers/classCallCheck")),i=o(e("@babel/runtime/helpers/createClass"));function o(e){return e&&e.__esModule?e:{default:e}}var a=function(){function e(t,r,i,o){(0,n.default)(this,e),this._dataView=new DataView(t),this._sliceOffset=r,this._littleEndian=i,this._bigTiff=o}return(0,i.default)(e,[{key:"covers",value:function(e,t){return this.sliceOffset<=e&&this.sliceTop>=e+t}},{key:"readUint8",value:function(e){return this._dataView.getUint8(e-this._sliceOffset,this._littleEndian)}},{key:"readInt8",value:function(e){return this._dataView.getInt8(e-this._sliceOffset,this._littleEndian)}},{key:"readUint16",value:function(e){return this._dataView.getUint16(e-this._sliceOffset,this._littleEndian)}},{key:"readInt16",value:function(e){return this._dataView.getInt16(e-this._sliceOffset,this._littleEndian)}},{key:"readUint32",value:function(e){return this._dataView.getUint32(e-this._sliceOffset,this._littleEndian)}},{key:"readInt32",value:function(e){return this._dataView.getInt32(e-this._sliceOffset,this._littleEndian)}},{key:"readFloat32",value:function(e){return this._dataView.getFloat32(e-this._sliceOffset,this._littleEndian)}},{key:"readFloat64",value:function(e){return this._dataView.getFloat64(e-this._sliceOffset,this._littleEndian)}},{key:"readUint64",value:function(e){var t,r=this.readUint32(e),n=this.readUint32(e+4);if(this._littleEndian){if(t=r+Math.pow(2,32)*n,!Number.isSafeInteger(t))throw new Error("".concat(t," exceeds MAX_SAFE_INTEGER. Precision may be lost. Please report if you get this message to https://github.com/geotiffjs/geotiff.js/issues"));return t}if(t=Math.pow(2,32)*r+n,!Number.isSafeInteger(t))throw new Error("".concat(t," exceeds MAX_SAFE_INTEGER. Precision may be lost. Please report if you get this message to https://github.com/geotiffjs/geotiff.js/issues"));return t}},{key:"readInt64",value:function(e){for(var t=0,r=(128&this._dataView.getUint8(e+(this._littleEndian?7:0)))>0,n=!0,i=0;i<8;i++){var o=this._dataView.getUint8(e+(this._littleEndian?i:7-i));r&&(n?0!==o&&(o=255&~(o-1),n=!1):o=255&~o),t+=o*Math.pow(256,i)}return r&&(t=-t),t}},{key:"readOffset",value:function(e){return this._bigTiff?this.readUint64(e):this.readUint32(e)}},{key:"sliceOffset",get:function(){return this._sliceOffset}},{key:"sliceTop",get:function(){return this._sliceOffset+this.buffer.byteLength}},{key:"littleEndian",get:function(){return this._littleEndian}},{key:"bigTiff",get:function(){return this._bigTiff}},{key:"buffer",get:function(){return this._dataView.buffer}}]),e}();r.default=a},{"@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW"}],z2GM:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.extendSerializer=function(e,t){const r=e.deserialize.bind(e),n=e.serialize.bind(e);return{deserialize:e=>t.deserialize(e,r),serialize:e=>t.serialize(e,n)}},r.DefaultSerializer=void 0;const n=e=>Object.assign(Error(e.message),{name:e.name,stack:e.stack}),i=e=>({__error_marker:"$$error",message:e.message,name:e.name,stack:e.stack}),o={deserialize:e=>(e=>e&&"object"==typeof e&&"__error_marker"in e&&"$$error"===e.__error_marker)(e)?n(e):e,serialize:e=>e instanceof Error?i(e):e};r.DefaultSerializer=o},{}],kdjL:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.registerSerializer=function(e){i=(0,n.extendSerializer)(i,e)},r.deserialize=function(e){return i.deserialize(e)},r.serialize=function(e){return i.serialize(e)};var n=e("./serializers");let i=n.DefaultSerializer},{"./serializers":"z2GM"}],LQHy:[function(e,t,r){"use strict";let n;function i(e){return(""+e).replace(/^((?:https?|file|ftp|chrome-extension|moz-extension):\/\/.+)?\/[^/]+(?:\?.*)?$/,"$1")+"/"}Object.defineProperty(r,"__esModule",{value:!0}),r.getBaseURL=i,r.getBundleURL=function(){return n||(n=function(){try{throw new Error}catch(e){const t=(""+e.stack).match(/(https?|file|ftp|chrome-extension|moz-extension):\/\/[^)\n]+/g);if(t)return i(t[0])}return"/"}()),n}},{}],XK2w:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.selectWorkerImplementation=function(){return"undefined"==typeof Worker?class{constructor(){throw Error("No web worker implementation available. You might have tried to spawn a worker within a worker in a browser that doesn't support workers in workers.")}}:class extends Worker{constructor(e,t){"string"==typeof e&&t&&t._baseURL?e=new URL(e,t._baseURL):"string"==typeof e&&!o(e)&&(0,n.getBundleURL)().match(/^file:\/\//i)&&(e=new URL(e,(0,n.getBundleURL)().replace(/\/[^\/]+$/,"/")),e=a(`importScripts(${JSON.stringify(e)});`)),"string"==typeof e&&o(e)&&(e=a(`importScripts(${JSON.stringify(e)});`)),super(e,t)}}},r.defaultPoolSize=void 0;var n=e("./get-bundle-url.browser");const i="undefined"!=typeof navigator&&navigator.hardwareConcurrency?navigator.hardwareConcurrency:4;r.defaultPoolSize=i;const o=e=>/^(file|https?:)?\/\//i.test(e);function a(e){const t=new Blob([e],{type:"application/javascript"});return URL.createObjectURL(t)}},{"./get-bundle-url.browser":"LQHy"}],IAgR:[function(e,t,r){var n=1e3,i=60*n,o=60*i,a=24*o;function s(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return r*a;case"hours":case"hour":case"hrs":case"hr":case"h":return r*o;case"minutes":case"minute":case"mins":case"min":case"m":return r*i;case"seconds":case"second":case"secs":case"sec":case"s":return r*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function u(e){var t=Math.abs(e);return t>=a?Math.round(e/a)+"d":t>=o?Math.round(e/o)+"h":t>=i?Math.round(e/i)+"m":t>=n?Math.round(e/n)+"s":e+"ms"}function l(e){var t=Math.abs(e);return t>=a?f(e,t,a,"day"):t>=o?f(e,t,o,"hour"):t>=i?f(e,t,i,"minute"):t>=n?f(e,t,n,"second"):e+" ms"}function f(e,t,r,n){var i=t>=1.5*r;return Math.round(e/r)+" "+n+(i?"s":"")}t.exports=function(e,t){t=t||{};var r=typeof e;if("string"===r&&e.length>0)return s(e);if("number"===r&&isFinite(e))return t.long?l(e):u(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},{}],Kest:[function(e,t,r){t.exports=function(t){function r(e){let t=0;for(let r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t|=0;return n.colors[Math.abs(t)%n.colors.length]}function n(e){let t;function a(...e){if(!a.enabled)return;const r=a,i=Number(new Date),o=i-(t||i);r.diff=o,r.prev=t,r.curr=i,t=i,e[0]=n.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((t,i)=>{if("%%"===t)return t;s++;const o=n.formatters[i];if("function"==typeof o){const n=e[s];t=o.call(r,n),e.splice(s,1),s--}return t})),n.formatArgs.call(r,e),(r.log||n.log).apply(r,e)}return a.namespace=e,a.enabled=n.enabled(e),a.useColors=n.useColors(),a.color=r(e),a.destroy=i,a.extend=o,"function"==typeof n.init&&n.init(a),n.instances.push(a),a}function i(){const e=n.instances.indexOf(this);return-1!==e&&(n.instances.splice(e,1),!0)}function o(e,t){const r=n(this.namespace+(void 0===t?":":t)+e);return r.log=this.log,r}function a(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return n.debug=n,n.default=n,n.coerce=function(e){return e instanceof Error?e.stack||e.message:e},n.disable=function(){const e=[...n.names.map(a),...n.skips.map(a).map((e=>"-"+e))].join(",");return n.enable(""),e},n.enable=function(e){let t;n.save(e),n.names=[],n.skips=[];const r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(t=0;t<i;t++)r[t]&&("-"===(e=r[t].replace(/\*/g,".*?"))[0]?n.skips.push(new RegExp("^"+e.substr(1)+"$")):n.names.push(new RegExp("^"+e+"$")));for(t=0;t<n.instances.length;t++){const e=n.instances[t];e.enabled=n.enabled(e.namespace)}},n.enabled=function(e){if("*"===e[e.length-1])return!0;let t,r;for(t=0,r=n.skips.length;t<r;t++)if(n.skips[t].test(e))return!1;for(t=0,r=n.names.length;t<r;t++)if(n.names[t].test(e))return!0;return!1},n.humanize=e("ms"),Object.keys(t).forEach((e=>{n[e]=t[e]})),n.instances=[],n.names=[],n.skips=[],n.formatters={},n.selectColor=r,n.enable(n.load()),n}},{ms:"IAgR"}],jcLW:[function(e,t,r){e("process");var n=e("process");r.log=function(...e){return"object"==typeof console&&console.log&&console.log(...e)},r.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;e.splice(1,0,r,"color: inherit");let n=0,i=0;e[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))})),e.splice(i,0,r)},r.save=function(e){try{e?r.storage.setItem("debug",e):r.storage.removeItem("debug")}catch(e){}},r.load=function(){let e;try{e=r.storage.getItem("debug")}catch(e){}return!e&&void 0!==n&&"env"in n&&(e=void 0),e},r.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},r.storage=function(){try{return localStorage}catch(e){}}(),r.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.exports=e("./common")(r);const{formatters:i}=t.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},{"./common":"Kest",process:"pBGv"}],sfrJ:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.AsyncSerialScheduler=void 0;r.AsyncSerialScheduler=class{constructor(e){this._baseObserver=e,this._pendingPromises=new Set}complete(){Promise.all(this._pendingPromises).then((()=>this._baseObserver.complete())).catch((e=>this._baseObserver.error(e)))}error(e){this._baseObserver.error(e)}schedule(e){const t=Promise.all(this._pendingPromises),r=[],n=e=>r.push(e),i=Promise.resolve().then((()=>function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{u(n.next(e))}catch(e){o(e)}}function s(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){e.done?i(e.value):new r((function(t){t(e.value)})).then(a,s)}u((n=n.apply(e,t||[])).next())}))}(this,void 0,void 0,(function*(){yield t,yield e(n),this._pendingPromises.delete(i);for(const e of r)this._baseObserver.next(e)})))).catch((e=>{this._pendingPromises.delete(i),this._baseObserver.error(e)}));this._pendingPromises.add(i)}}},{}],lj3f:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.registerObservableSymbol=function(){n()&&!i("observable")&&(Symbol.observable=Symbol("observable"))},r.getSymbol=r.hasSymbol=r.hasSymbols=void 0;const n=()=>"function"==typeof Symbol;r.hasSymbols=n;const i=e=>n()&&Boolean(Symbol[e]);r.hasSymbol=i;r.getSymbol=e=>i(e)?Symbol[e]:"@@"+e,i("asyncIterator")||(Symbol.asyncIterator=Symbol.asyncIterator||Symbol.for("Symbol.asyncIterator"))},{}],B9l3:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=r.Observable=r.SubscriptionObserver=r.Subscription=void 0;var n=e("./_symbols");const i=(0,n.getSymbol)("iterator"),o=(0,n.getSymbol)("observable"),a=(0,n.getSymbol)("species");function s(e,t){const r=e[t];if(null!=r){if("function"!=typeof r)throw new TypeError(r+" is not a function");return r}}function u(e){let t=e.constructor;return void 0!==t&&null===(t=t[a])&&(t=void 0),void 0!==t?t:v}function l(e){l.log?l.log(e):setTimeout((()=>{throw e}),0)}function f(e){Promise.resolve().then((()=>{try{e()}catch(e){l(e)}}))}function c(e){const t=e._cleanup;if(void 0!==t&&(e._cleanup=void 0,t))try{if("function"==typeof t)t();else{const e=s(t,"unsubscribe");e&&e.call(t)}}catch(e){l(e)}}function h(e){e._observer=void 0,e._queue=void 0,e._state="closed"}function d(e,t,r){e._state="running";const n=e._observer;try{const i=n?s(n,t):void 0;switch(t){case"next":i&&i.call(n,r);break;case"error":if(h(e),!i)throw r;i.call(n,r);break;case"complete":h(e),i&&i.call(n)}}catch(e){l(e)}"closed"===e._state?c(e):"running"===e._state&&(e._state="ready")}function p(e,t,r){if("closed"!==e._state)return"buffering"===e._state?(e._queue=e._queue||[],void e._queue.push({type:t,value:r})):"ready"!==e._state?(e._state="buffering",e._queue=[{type:t,value:r}],void f((()=>function(e){const t=e._queue;if(t){e._queue=void 0,e._state="ready";for(const r of t)if(d(e,r.type,r.value),"closed"===e._state)break}}(e)))):void d(e,t,r)}class b{constructor(e,t){this._cleanup=void 0,this._observer=e,this._queue=void 0,this._state="initializing";const r=new y(this);try{this._cleanup=t.call(void 0,r)}catch(e){r.error(e)}"initializing"===this._state&&(this._state="ready")}get closed(){return"closed"===this._state}unsubscribe(){"closed"!==this._state&&(h(this),c(this))}}r.Subscription=b;class y{constructor(e){this._subscription=e}get closed(){return"closed"===this._subscription._state}next(e){p(this._subscription,"next",e)}error(e){p(this._subscription,"error",e)}complete(){p(this._subscription,"complete")}}r.SubscriptionObserver=y;class v{constructor(e){if(!(this instanceof v))throw new TypeError("Observable cannot be called as a function");if("function"!=typeof e)throw new TypeError("Observable initializer must be a function");this._subscriber=e}subscribe(e,t,r){return"object"==typeof e&&null!==e||(e={next:e,error:t,complete:r}),new b(e,this._subscriber)}pipe(e,...t){let r=this;for(const n of[e,...t])r=n(r);return r}tap(e,t,r){const n="object"!=typeof e||null===e?{next:e,error:t,complete:r}:e;return new v((e=>this.subscribe({next(t){n.next&&n.next(t),e.next(t)},error(t){n.error&&n.error(t),e.error(t)},complete(){n.complete&&n.complete(),e.complete()},start(e){n.start&&n.start(e)}})))}forEach(e){return new Promise(((t,r)=>{if("function"!=typeof e)return void r(new TypeError(e+" is not a function"));function n(){i.unsubscribe(),t()}const i=this.subscribe({next(t){try{e(t,n)}catch(e){r(e),i.unsubscribe()}},error:r,complete:t})}))}map(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return new(u(this))((t=>this.subscribe({next(r){let n=r;try{n=e(r)}catch(e){return t.error(e)}t.next(n)},error(e){t.error(e)},complete(){t.complete()}})))}filter(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return new(u(this))((t=>this.subscribe({next(r){try{if(!e(r))return}catch(e){return t.error(e)}t.next(r)},error(e){t.error(e)},complete(){t.complete()}})))}reduce(e,t){if("function"!=typeof e)throw new TypeError(e+" is not a function");const r=u(this),n=arguments.length>1;let i=!1,o=t;return new r((t=>this.subscribe({next(r){const a=!i;if(i=!0,!a||n)try{o=e(o,r)}catch(e){return t.error(e)}else o=r},error(e){t.error(e)},complete(){if(!i&&!n)return t.error(new TypeError("Cannot reduce an empty sequence"));t.next(o),t.complete()}})))}concat(...e){const t=u(this);return new t((r=>{let n,i=0;return function o(a){n=a.subscribe({next(e){r.next(e)},error(e){r.error(e)},complete(){i===e.length?(n=void 0,r.complete()):o(t.from(e[i++]))}})}(this),()=>{n&&(n.unsubscribe(),n=void 0)}}))}flatMap(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");const t=u(this);return new t((r=>{const n=[],i=this.subscribe({next(i){let a;if(e)try{a=e(i)}catch(e){return r.error(e)}else a=i;const s=t.from(a).subscribe({next(e){r.next(e)},error(e){r.error(e)},complete(){const e=n.indexOf(s);e>=0&&n.splice(e,1),o()}});n.push(s)},error(e){r.error(e)},complete(){o()}});function o(){i.closed&&0===n.length&&r.complete()}return()=>{n.forEach((e=>e.unsubscribe())),i.unsubscribe()}}))}[o](){return this}static from(e){const t="function"==typeof this?this:v;if(null==e)throw new TypeError(e+" is not an object");const r=s(e,o);if(r){const n=r.call(e);if(Object(n)!==n)throw new TypeError(n+" is not an object");return function(e){return e instanceof v}(n)&&n.constructor===t?n:new t((e=>n.subscribe(e)))}if((0,n.hasSymbol)("iterator")){const r=s(e,i);if(r)return new t((t=>{f((()=>{if(!t.closed){for(const n of r.call(e))if(t.next(n),t.closed)return;t.complete()}}))}))}if(Array.isArray(e))return new t((t=>{f((()=>{if(!t.closed){for(const r of e)if(t.next(r),t.closed)return;t.complete()}}))}));throw new TypeError(e+" is not observable")}static of(...e){return new("function"==typeof this?this:v)((t=>{f((()=>{if(!t.closed){for(const r of e)if(t.next(r),t.closed)return;t.complete()}}))}))}static get[a](){return this}}r.Observable=v,(0,n.hasSymbols)()&&Object.defineProperty(v,Symbol("extensions"),{value:{symbol:o,hostReportError:l},configurable:!0});var g=v;r.default=g},{"./_symbols":"lj3f"}],QC75:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){"function"==typeof e?e():e&&"function"==typeof e.unsubscribe&&e.unsubscribe()};r.default=n},{}],apmd:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=e("./_scheduler"),i=a(e("./observable")),o=a(e("./unsubscribe"));function a(e){return e&&e.__esModule?e:{default:e}}var s=function(e){return t=>new i.default((r=>{const i=new n.AsyncSerialScheduler(r),a=t.subscribe({complete(){i.complete()},error(e){i.error(e)},next(t){i.schedule((r=>function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{u(n.next(e))}catch(e){o(e)}}function s(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){e.done?i(e.value):new r((function(t){t(e.value)})).then(a,s)}u((n=n.apply(e,t||[])).next())}))}(this,void 0,void 0,(function*(){(yield e(t))&&r(t)}))))}});return()=>(0,o.default)(a)}))};r.default=s},{"./_scheduler":"sfrJ","./observable":"B9l3","./unsubscribe":"QC75"}],FgiE:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.isAsyncIterator=function(e){return e&&(0,n.hasSymbol)("asyncIterator")&&e[Symbol.asyncIterator]},r.isIterator=function(e){return e&&(0,n.hasSymbol)("iterator")&&e[Symbol.iterator]};var n=e("./_symbols")},{"./_symbols":"lj3f"}],ltny:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=e("./_scheduler"),i=e("./_util"),o=s(e("./observable")),a=s(e("./unsubscribe"));function s(e){return e&&e.__esModule?e:{default:e}}var u=function(e){return t=>new o.default((r=>{const o=new n.AsyncSerialScheduler(r),s=t.subscribe({complete(){o.complete()},error(e){o.error(e)},next(t){o.schedule((r=>function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{u(n.next(e))}catch(e){o(e)}}function s(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){e.done?i(e.value):new r((function(t){t(e.value)})).then(a,s)}u((n=n.apply(e,t||[])).next())}))}(this,void 0,void 0,(function*(){var n,o;const a=yield e(t);if((0,i.isIterator)(a)||(0,i.isAsyncIterator)(a))try{for(var s,u=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e="function"==typeof __values?__values(e):e[Symbol.iterator](),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise((function(n,i){!function(e,t,r,n){Promise.resolve(n).then((function(t){e({value:t,done:r})}),t)}(n,i,(t=e[r](t)).done,t.value)}))}}}(a);!(s=yield u.next()).done;){const e=s.value;r(e)}}catch(e){n={error:e}}finally{try{s&&!s.done&&(o=u.return)&&(yield o.call(u))}finally{if(n)throw n.error}}else a.map((e=>r(e)))}))))}});return()=>(0,a.default)(s)}))};r.default=u},{"./_scheduler":"sfrJ","./_util":"FgiE","./observable":"B9l3","./unsubscribe":"QC75"}],jAhG:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){return new n.Observable((t=>{let r=0;const n=setInterval((()=>{t.next(r++)}),e);return()=>clearInterval(n)}))};var n=e("./observable")},{"./observable":"B9l3"}],aP59:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=e("./_scheduler"),i=a(e("./observable")),o=a(e("./unsubscribe"));function a(e){return e&&e.__esModule?e:{default:e}}var s=function(e){return t=>new i.default((r=>{const i=new n.AsyncSerialScheduler(r),a=t.subscribe({complete(){i.complete()},error(e){i.error(e)},next(t){i.schedule((r=>function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{u(n.next(e))}catch(e){o(e)}}function s(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){e.done?i(e.value):new r((function(t){t(e.value)})).then(a,s)}u((n=n.apply(e,t||[])).next())}))}(this,void 0,void 0,(function*(){const n=yield e(t);r(n)}))))}});return()=>(0,o.default)(a)}))};r.default=s},{"./_scheduler":"sfrJ","./observable":"B9l3","./unsubscribe":"QC75"}],Rg1v:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=e("./observable"),i=function(e){return e&&e.__esModule?e:{default:e}}(e("./unsubscribe"));var o=function(...e){return 0===e.length?n.Observable.from([]):new n.Observable((t=>{let r=0;const n=e.map((n=>n.subscribe({error(e){t.error(e),o()},next(e){t.next(e)},complete(){++r===e.length&&(t.complete(),o())}}))),o=()=>{n.forEach((e=>(0,i.default)(e)))};return o}))};r.default=o},{"./observable":"B9l3","./unsubscribe":"QC75"}],wtHd:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("./observable"));class i extends n.default{constructor(){super((e=>(this._observers.add(e),()=>this._observers.delete(e)))),this._observers=new Set}next(e){for(const t of this._observers)t.next(e)}error(e){for(const t of this._observers)t.error(e)}complete(){for(const e of this._observers)e.complete()}}var o=i;r.default=o},{"./observable":"B9l3"}],NgdC:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=a(e("./observable")),i=a(e("./subject")),o=a(e("./unsubscribe"));function a(e){return e&&e.__esModule?e:{default:e}}var s=function(e){const t=new i.default;let r,a=0;return new n.default((n=>{r||(r=e.subscribe(t));const i=t.subscribe(n);return a++,()=>{a--,i.unsubscribe(),0===a&&((0,o.default)(r),r=void 0)}}))};r.default=s},{"./observable":"B9l3","./subject":"wtHd","./unsubscribe":"QC75"}],b7o0:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=e("./_scheduler"),i=a(e("./observable")),o=a(e("./unsubscribe"));function a(e){return e&&e.__esModule?e:{default:e}}var s=function(e,t){return r=>new i.default((i=>{let a,s=0;const u=new n.AsyncSerialScheduler(i),l=r.subscribe({complete(){u.complete()},error(e){u.error(e)},next(r){u.schedule((n=>function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{u(n.next(e))}catch(e){o(e)}}function s(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){e.done?i(e.value):new r((function(t){t(e.value)})).then(a,s)}u((n=n.apply(e,t||[])).next())}))}(this,void 0,void 0,(function*(){const i=0===s?void 0===t?r:t:a;a=yield e(i,r,s++),n(a)}))))}});return()=>(0,o.default)(l)}))};r.default=s},{"./_scheduler":"sfrJ","./observable":"B9l3","./unsubscribe":"QC75"}],cyO3:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"filter",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(r,"flatMap",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(r,"interval",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(r,"map",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(r,"merge",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(r,"multicast",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(r,"Observable",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(r,"scan",{enumerable:!0,get:function(){return f.default}}),Object.defineProperty(r,"Subject",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(r,"unsubscribe",{enumerable:!0,get:function(){return h.default}});var n=d(e("./filter")),i=d(e("./flatMap")),o=d(e("./interval")),a=d(e("./map")),s=d(e("./merge")),u=d(e("./multicast")),l=d(e("./observable")),f=d(e("./scan")),c=d(e("./subject")),h=d(e("./unsubscribe"));function d(e){return e&&e.__esModule?e:{default:e}}},{"./filter":"apmd","./flatMap":"ltny","./interval":"jAhG","./map":"aP59","./merge":"Rg1v","./multicast":"NgdC","./observable":"B9l3","./scan":"b7o0","./subject":"wtHd","./unsubscribe":"QC75"}],vpgy:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.allSettled=function(e){return Promise.all(e.map((e=>{const t=e=>({status:"fulfilled",value:e}),r=e=>({status:"rejected",reason:e}),n=Promise.resolve(e);try{return n.then(t,r)}catch(e){return Promise.reject(e)}})))}},{}],UYCO:[function(e,t,r){"use strict";var n;Object.defineProperty(r,"__esModule",{value:!0}),r.PoolEventType=void 0,r.PoolEventType=n,function(e){e.initialized="initialized",e.taskCanceled="taskCanceled",e.taskCompleted="taskCompleted",e.taskFailed="taskFailed",e.taskQueued="taskQueued",e.taskQueueDrained="taskQueueDrained",e.taskStart="taskStart",e.terminated="terminated"}(n||(r.PoolEventType=n={}))},{}],fmdM:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.$worker=r.$transferable=r.$terminate=r.$events=r.$errors=void 0;const n=Symbol("thread.errors");r.$errors=n;const i=Symbol("thread.events");r.$events=i;const o=Symbol("thread.terminate");r.$terminate=o;const a=Symbol("thread.transferable");r.$transferable=a;const s=Symbol("thread.worker");r.$worker=s},{}],LBuu:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.Thread=void 0;var n=e("../symbols");function i(e){throw Error(e)}const o={errors:e=>e[n.$errors]||i("Error observable not found. Make sure to pass a thread instance as returned by the spawn() promise."),events:e=>e[n.$events]||i("Events observable not found. Make sure to pass a thread instance as returned by the spawn() promise."),terminate:e=>e[n.$terminate]()};r.Thread=o},{"../symbols":"fmdM"}],EB3M:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"PoolEventType",{enumerable:!0,get:function(){return s.PoolEventType}}),Object.defineProperty(r,"Thread",{enumerable:!0,get:function(){return u.Thread}}),r.Pool=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(e("debug")),i=e("observable-fns"),o=e("../ponyfills"),a=e("./implementation"),s=e("./pool-types"),u=e("./thread");var l=function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{u(n.next(e))}catch(e){o(e)}}function s(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}u((n=n.apply(e,t||[])).next())}))};let f=1;class c{constructor(e,t){this.eventSubject=new i.Subject,this.initErrors=[],this.isClosing=!1,this.nextTaskID=1,this.taskQueue=[];const r="number"==typeof t?{size:t}:t||{},{size:o=a.defaultPoolSize}=r;this.debug=(0,n.default)("threads:pool:"+function(e){return e.replace(/\W/g," ").trim().replace(/\s+/g,"-")}(r.name||String(f++))),this.options=r,this.workers=function(e,t){return function(e){const t=[];for(let r=0;r<e;r++)t.push(r);return t}(t).map((()=>({init:e(),runningTasks:[]})))}(e,o),this.eventObservable=(0,i.multicast)(i.Observable.from(this.eventSubject)),Promise.all(this.workers.map((e=>e.init))).then((()=>this.eventSubject.next({type:s.PoolEventType.initialized,size:this.workers.length})),(e=>{this.debug("Error while initializing pool worker:",e),this.eventSubject.error(e),this.initErrors.push(e)}))}findIdlingWorker(){const{concurrency:e=1}=this.options;return this.workers.find((t=>t.runningTasks.length<e))}runPoolTask(e,t){return l(this,void 0,void 0,(function*(){const r=this.workers.indexOf(e)+1;this.debug(`Running task #${t.id} on worker #${r}...`),this.eventSubject.next({type:s.PoolEventType.taskStart,taskID:t.id,workerID:r});try{const n=yield t.run(yield e.init);this.debug(`Task #${t.id} completed successfully`),this.eventSubject.next({type:s.PoolEventType.taskCompleted,returnValue:n,taskID:t.id,workerID:r})}catch(e){this.debug(`Task #${t.id} failed`),this.eventSubject.next({type:s.PoolEventType.taskFailed,taskID:t.id,error:e,workerID:r})}}))}run(e,t){return l(this,void 0,void 0,(function*(){const r=(()=>l(this,void 0,void 0,(function*(){yield function(e){return new Promise((t=>setTimeout(t,e)))}(0);try{yield this.runPoolTask(e,t)}finally{e.runningTasks=e.runningTasks.filter((e=>e!==r)),this.isClosing||this.scheduleWork()}})))();e.runningTasks.push(r)}))}scheduleWork(){this.debug("Attempt de-queueing a task in order to run it...");const e=this.findIdlingWorker();if(!e)return;const t=this.taskQueue.shift();if(!t)return this.debug("Task queue is empty"),void this.eventSubject.next({type:s.PoolEventType.taskQueueDrained});this.run(e,t)}taskCompletion(e){return new Promise(((t,r)=>{const n=this.events().subscribe((i=>{i.type===s.PoolEventType.taskCompleted&&i.taskID===e?(n.unsubscribe(),t(i.returnValue)):i.type===s.PoolEventType.taskFailed&&i.taskID===e?(n.unsubscribe(),r(i.error)):i.type===s.PoolEventType.terminated&&(n.unsubscribe(),r(Error("Pool has been terminated before task was run.")))}))}))}settled(e=!1){return l(this,void 0,void 0,(function*(){const t=()=>function(e,t){return e.reduce(((e,r)=>[...e,...t(r)]),[])}(this.workers,(e=>e.runningTasks)),r=[],n=this.eventObservable.subscribe((e=>{e.type===s.PoolEventType.taskFailed&&r.push(e.error)}));return this.initErrors.length>0?Promise.reject(this.initErrors[0]):e&&0===this.taskQueue.length?(yield(0,o.allSettled)(t()),r):(yield new Promise(((e,t)=>{const r=this.eventObservable.subscribe({next(t){t.type===s.PoolEventType.taskQueueDrained&&(r.unsubscribe(),e())},error:t})})),yield(0,o.allSettled)(t()),n.unsubscribe(),r)}))}completed(e=!1){return l(this,void 0,void 0,(function*(){const t=this.settled(e),r=new Promise(((e,r)=>{const n=this.eventObservable.subscribe({next(i){i.type===s.PoolEventType.taskQueueDrained?(n.unsubscribe(),e(t)):i.type===s.PoolEventType.taskFailed&&(n.unsubscribe(),r(i.error))},error:r})})),n=yield Promise.race([t,r]);if(n.length>0)throw n[0]}))}events(){return this.eventObservable}queue(e){const{maxQueuedJobs:t=1/0}=this.options;if(this.isClosing)throw Error("Cannot schedule pool tasks after terminate() has been called.");if(this.initErrors.length>0)throw this.initErrors[0];const r=()=>this.taskCompletion(i.id);let n;const i={id:this.nextTaskID++,run:e,cancel:()=>{-1!==this.taskQueue.indexOf(i)&&(this.taskQueue=this.taskQueue.filter((e=>e!==i)),this.eventSubject.next({type:s.PoolEventType.taskCanceled,taskID:i.id}))},get then(){if(!n){const e=r();n=e.then.bind(e)}return n}};if(this.taskQueue.length>=t)throw Error("Maximum number of pool tasks queued. Refusing to queue another one.\nThis usually happens for one of two reasons: We are either at peak workload right now or some tasks just won't finish, thus blocking the pool.");return this.debug(`Queueing task #${i.id}...`),this.taskQueue.push(i),this.eventSubject.next({type:s.PoolEventType.taskQueued,taskID:i.id}),this.scheduleWork(),i}terminate(e){return l(this,void 0,void 0,(function*(){this.isClosing=!0,e||(yield this.completed(!0)),this.eventSubject.next({type:s.PoolEventType.terminated,remainingQueue:[...this.taskQueue]}),this.eventSubject.complete(),yield Promise.all(this.workers.map((e=>l(this,void 0,void 0,(function*(){return u.Thread.terminate(yield e.init)})))))}))}}function h(e,t){return new c(e,t)}c.EventType=s.PoolEventType,h.EventType=s.PoolEventType;const d=h;r.Pool=d},{debug:"jcLW","observable-fns":"cyO3","../ponyfills":"vpgy","./implementation":"XK2w","./pool-types":"UYCO","./thread":"LBuu"}],PRGP:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.createPromiseWithResolver=function(){let e,t=!1,r=n;return[new Promise((n=>{t?n(e):r=n})),n=>{t=!0,e=n,r()}]};const n=()=>{}},{}],ZLGT:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.WorkerEventType=void 0;var n;e("../symbols");r.WorkerEventType=n,function(e){e.internalError="internalError",e.message="message",e.termination="termination"}(n||(r.WorkerEventType=n={}))},{"../symbols":"fmdM"}],zXl4:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.ObservablePromise=void 0;var n=e("observable-fns");const i=()=>{},o=e=>e,a=e=>Promise.resolve().then(e);function s(e){throw e}class u extends n.Observable{constructor(e){super((t=>{const r=this,n=Object.assign(Object.assign({},t),{complete(){t.complete(),r.onCompletion()},error(e){t.error(e),r.onError(e)},next(e){t.next(e),r.onNext(e)}});try{return this.initHasRun=!0,e(n)}catch(e){n.error(e)}})),this.initHasRun=!1,this.fulfillmentCallbacks=[],this.rejectionCallbacks=[],this.firstValueSet=!1,this.state="pending"}onNext(e){this.firstValueSet||(this.firstValue=e,this.firstValueSet=!0)}onError(e){this.state="rejected",this.rejection=e;for(const t of this.rejectionCallbacks)a((()=>t(e)))}onCompletion(){this.state="fulfilled";for(const e of this.fulfillmentCallbacks)a((()=>e(this.firstValue)))}then(e,t){const r=e||o,n=t||s;let i=!1;return new Promise(((e,t)=>{const o=r=>{if(!i){i=!0;try{e(n(r))}catch(e){t(e)}}};return this.initHasRun||this.subscribe({error:o}),"fulfilled"===this.state?e(r(this.firstValue)):"rejected"===this.state?(i=!0,e(n(this.rejection))):(this.fulfillmentCallbacks.push((t=>{try{e(r(t))}catch(e){o(e)}})),void this.rejectionCallbacks.push(o))}))}catch(e){return this.then(void 0,e)}finally(e){const t=e||i;return this.then((e=>(t(),e)),(()=>t()))}static from(e){return function(e){return e&&"function"==typeof e.then}(e)?new u((t=>{e.then((e=>{t.next(e),t.complete()}),(e=>{t.error(e)}))})):super.from(e)}}r.ObservablePromise=u},{"observable-fns":"cyO3"}],qW1u:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.isTransferDescriptor=function(e){return e&&"object"==typeof e&&e[n.$transferable]},r.Transfer=function(e,t){if(!t){if(!function(e){return!(!e||"object"!=typeof e)}(e))throw Error();t=[e]}return{[n.$transferable]:!0,send:e,transferables:t}};var n=e("./symbols")},{"./symbols":"fmdM"}],fUdt:[function(e,t,r){"use strict";var n,i;Object.defineProperty(r,"__esModule",{value:!0}),r.WorkerMessageType=r.MasterMessageType=void 0,r.MasterMessageType=n,function(e){e.run="run"}(n||(r.MasterMessageType=n={})),r.WorkerMessageType=i,function(e){e.error="error",e.init="init",e.result="result",e.running="running",e.uncaughtError="uncaughtError"}(i||(r.WorkerMessageType=i={}))},{}],iMbm:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.createProxyFunction=v,r.createProxyModule=function(e,t){const r={};for(const n of t)r[n]=v(e,n);return r};var n=function(e){return e&&e.__esModule?e:{default:e}}(e("debug")),i=e("observable-fns"),o=e("../common"),a=e("../observable-promise"),s=e("../transferable"),u=e("../types/messages");const l=(0,n.default)("threads:master:messages");let f=1;const c=e=>Array.from(new Set(e)),h=e=>e&&e.type===u.WorkerMessageType.error,d=e=>e&&e.type===u.WorkerMessageType.result,p=e=>e&&e.type===u.WorkerMessageType.running;function b(e,t){return new i.Observable((r=>{let n;const i=a=>{if(l("Message from worker:",a.data),a.data&&a.data.uid===t)if(p(a.data))n=a.data.resultType;else if(d(a.data))"promise"===n?(void 0!==a.data.payload&&r.next((0,o.deserialize)(a.data.payload)),r.complete(),e.removeEventListener("message",i)):(a.data.payload&&r.next((0,o.deserialize)(a.data.payload)),a.data.complete&&(r.complete(),e.removeEventListener("message",i)));else if(h(a.data)){const t=(0,o.deserialize)(a.data.error);r.error(t),e.removeEventListener("message",i)}};return e.addEventListener("message",i),()=>e.removeEventListener("message",i)}))}function y(e){if(0===e.length)return{args:[],transferables:[]};const t=[],r=[];for(const n of e)(0,s.isTransferDescriptor)(n)?(t.push((0,o.serialize)(n.send)),r.push(...n.transferables)):t.push((0,o.serialize)(n));return{args:t,transferables:0===r.length?r:c(r)}}function v(e,t){return(...r)=>{const n=f++,{args:o,transferables:s}=y(r),c={type:u.MasterMessageType.run,uid:n,method:t,args:o};l("Sending command to run function to worker:",c);try{e.postMessage(c,s)}catch(e){return a.ObservablePromise.from(Promise.reject(e))}return a.ObservablePromise.from((0,i.multicast)(b(e,n)))}}},{debug:"jcLW","observable-fns":"cyO3","../common":"kdjL","../observable-promise":"zXl4","../transferable":"qW1u","../types/messages":"fUdt"}],a83E:[function(e,t,r){e("process"),e("process");Object.defineProperty(r,"__esModule",{value:!0}),r.spawn=function(e,t){return f(this,void 0,void 0,(function*(){h("Initializing new thread");const r=(yield function(e,t,r){return f(this,void 0,void 0,(function*(){let n;const i=new Promise(((e,i)=>{n=setTimeout((()=>i(Error(r))),t)})),o=yield Promise.race([e,i]);return clearTimeout(n),o}))}(p(e),t&&t.timeout?t.timeout:1e4,"Timeout: Did not receive an init message from worker after 10000ms. Make sure the worker calls expose().")).exposed,{termination:n,terminate:i}=y(e),o=b(e,n);if("function"===r.type)return v((0,l.createProxyFunction)(e),e,o,i);if("module"===r.type)return v((0,l.createProxyModule)(e,r.methods),e,o,i);{const e=r.type;throw Error("Worker init message states unexpected type of expose(): "+e)}}))};var n=function(e){return e&&e.__esModule?e:{default:e}}(e("debug")),i=e("observable-fns"),o=e("../common"),a=e("../promise"),s=e("../symbols"),u=e("../types/master"),l=e("./invocation-proxy");var f=function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{u(n.next(e))}catch(e){o(e)}}function s(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}u((n=n.apply(e,t||[])).next())}))};const c=(0,n.default)("threads:master:messages"),h=(0,n.default)("threads:master:spawn"),d=(0,n.default)("threads:master:thread-utils");function p(e){return new Promise(((t,r)=>{const n=i=>{c("Message from worker before finishing initialization:",i.data),(e=>e&&"init"===e.type)(i.data)?(e.removeEventListener("message",n),t(i.data)):(e=>e&&"uncaughtError"===e.type)(i.data)&&(e.removeEventListener("message",n),r((0,o.deserialize)(i.data.error)))};e.addEventListener("message",n)}))}function b(e,t){return new i.Observable((r=>{const n=e=>{const t={type:u.WorkerEventType.message,data:e.data};r.next(t)},i=e=>{d("Unhandled promise rejection event in thread:",e);const t={type:u.WorkerEventType.internalError,error:Error(e.reason)};r.next(t)};e.addEventListener("message",n),e.addEventListener("unhandledrejection",i),t.then((()=>{const t={type:u.WorkerEventType.termination};e.removeEventListener("message",n),e.removeEventListener("unhandledrejection",i),r.next(t),r.complete()}))}))}function y(e){const[t,r]=(0,a.createPromiseWithResolver)();return{terminate:()=>f(this,void 0,void 0,(function*(){d("Terminating worker"),yield e.terminate(),r()})),termination:t}}function v(e,t,r,n){const i=r.filter((e=>e.type===u.WorkerEventType.internalError)).map((e=>e.error));return Object.assign(e,{[s.$errors]:i,[s.$events]:r,[s.$terminate]:n,[s.$worker]:t})}},{debug:"jcLW","observable-fns":"cyO3","../common":"kdjL","../promise":"PRGP","../symbols":"fmdM","../types/master":"ZLGT","./invocation-proxy":"iMbm",process:"pBGv"}],HfK0:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"Pool",{enumerable:!0,get:function(){return i.Pool}}),Object.defineProperty(r,"spawn",{enumerable:!0,get:function(){return o.spawn}}),Object.defineProperty(r,"Thread",{enumerable:!0,get:function(){return a.Thread}}),r.Worker=void 0;var n=e("./implementation"),i=e("./pool"),o=e("./spawn"),a=e("./thread");const s=(0,n.selectWorkerImplementation)();r.Worker=s},{"./implementation":"XK2w","./pool":"EB3M","./spawn":"a83E","./thread":"LBuu"}],JZ8d:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){var t,r=e.Symbol;return"function"==typeof r?r.observable?t=r.observable:(t=r("observable"),r.observable=t):t="@@observable",t}},{}],LkZ7:[function(e,t,r){var n=arguments[3];Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var i,o=a(e("./ponyfill.js"));function a(e){return e&&e.__esModule?e:{default:e}}i="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==n?n:void 0!==t?t:Function("return this")();var s=(0,o.default)(i),u=s;r.default=u},{"./ponyfill.js":"JZ8d"}],UALh:[function(e,t,r){"use strict";const n=e("symbol-observable").default;t.exports=e=>Boolean(e&&e[n]&&e===e[n]())},{"symbol-observable":"LkZ7"}],AZa4:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n={isWorkerRuntime:function(){return!("undefined"==typeof self||!self.postMessage)},postMessageToMaster:function(e,t){self.postMessage(e,t)},subscribeToMasterMessages:function(e){const t=t=>{e(t.data)};return self.addEventListener("message",t),()=>{self.removeEventListener("message",t)}}};r.default=n},{}],l5pR:[function(e,t,r){e("process");var n=e("process");Object.defineProperty(r,"__esModule",{value:!0}),r.expose=function(e){if(!u.default.isWorkerRuntime())throw Error("expose() called in the master thread.");if(f)throw Error("expose() called more than once. This is not possible. Pass an object to expose() if you want to expose multiple functions.");if(f=!0,"function"==typeof e)u.default.subscribeToMasterMessages((t=>{c(t)&&!t.method&&g(t.uid,e,t.args.map(o.deserialize))})),function(){const e={type:s.WorkerMessageType.init,exposed:{type:"function"}};u.default.postMessageToMaster(e)}();else{if("object"!=typeof e||!e)throw Error("Invalid argument passed to expose(). Expected a function or an object, got: "+e);u.default.subscribeToMasterMessages((t=>{c(t)&&t.method&&g(t.uid,e[t.method],t.args.map(o.deserialize))})),function(e){const t={type:s.WorkerMessageType.init,exposed:{type:"module",methods:e}};u.default.postMessageToMaster(t)}(Object.keys(e).filter((t=>"function"==typeof e[t])))}},Object.defineProperty(r,"registerSerializer",{enumerable:!0,get:function(){return o.registerSerializer}}),Object.defineProperty(r,"Transfer",{enumerable:!0,get:function(){return a.Transfer}});var i=l(e("is-observable")),o=e("../common"),a=e("../transferable"),s=e("../types/messages"),u=l(e("./implementation"));function l(e){return e&&e.__esModule?e:{default:e}}let f=!1;const c=e=>e&&e.type===s.MasterMessageType.run,h=e=>(0,i.default)(e)||function(e){return e&&"object"==typeof e&&"function"==typeof e.subscribe}(e);function d(e){return(0,a.isTransferDescriptor)(e)?{payload:e.send,transferables:e.transferables}:{payload:e,transferables:void 0}}function p(e,t){const{payload:r,transferables:n}=d(t),i={type:s.WorkerMessageType.error,uid:e,error:(0,o.serialize)(r)};u.default.postMessageToMaster(i,n)}function b(e,t,r){const{payload:n,transferables:i}=d(r),o={type:s.WorkerMessageType.result,uid:e,complete:!!t||void 0,payload:n};u.default.postMessageToMaster(o,i)}function y(e,t){const r={type:s.WorkerMessageType.running,uid:e,resultType:t};u.default.postMessageToMaster(r)}function v(e){try{const t={type:s.WorkerMessageType.uncaughtError,error:(0,o.serialize)(e)};u.default.postMessageToMaster(t)}catch(t){console.error("Not reporting uncaught error back to master thread as it occured while reporting an uncaught error already.\nLatest error:",t,"\nOriginal error:",e)}}function g(e,t,r){return function(e,t,r,n){return new(r||(r=Promise))((function(i,o){function a(e){try{u(n.next(e))}catch(e){o(e)}}function s(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(a,s)}u((n=n.apply(e,t||[])).next())}))}(this,void 0,void 0,(function*(){let n;try{n=t(...r)}catch(t){return p(e,t)}const i=h(n)?"observable":"promise";if(y(e,i),h(n))n.subscribe((t=>b(e,!1,(0,o.serialize)(t))),(t=>p(e,(0,o.serialize)(t))),(()=>b(e,!0)));else try{const t=yield n;b(e,!0,(0,o.serialize)(t))}catch(t){p(e,(0,o.serialize)(t))}}))}"undefined"!=typeof self&&"function"==typeof self.addEventListener&&u.default.isWorkerRuntime()&&(self.addEventListener("error",(e=>{setTimeout((()=>v(e.error||e)),250)})),self.addEventListener("unhandledrejection",(e=>{const t=e.reason;t&&"string"==typeof t.message&&setTimeout((()=>v(t)),250)}))),void 0!==n&&"function"==typeof n.on&&u.default.isWorkerRuntime()&&(n.on("uncaughtException",(e=>{setTimeout((()=>v(e)),250)})),n.on("unhandledRejection",(e=>{e&&"string"==typeof e.message&&setTimeout((()=>v(e)),250)})))},{"is-observable":"UALh","../common":"kdjL","../transferable":"qW1u","../types/messages":"fUdt","./implementation":"AZa4",process:"pBGv"}],CqwR:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n={registerSerializer:!0,expose:!0,DefaultSerializer:!0,Transfer:!0};Object.defineProperty(r,"registerSerializer",{enumerable:!0,get:function(){return i.registerSerializer}}),Object.defineProperty(r,"expose",{enumerable:!0,get:function(){return a.expose}}),Object.defineProperty(r,"DefaultSerializer",{enumerable:!0,get:function(){return s.DefaultSerializer}}),Object.defineProperty(r,"Transfer",{enumerable:!0,get:function(){return u.Transfer}});var i=e("./common"),o=e("./master/index");Object.keys(o).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(n,e)||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))}));var a=e("./worker/index"),s=e("./serializers"),u=e("./transferable")},{"./common":"kdjL","./master/index":"HfK0","./worker/index":"l5pR","./serializers":"z2GM","./transferable":"qW1u"}],dHPO:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=u(e("@babel/runtime/regenerator")),i=u(e("@babel/runtime/helpers/asyncToGenerator")),o=u(e("@babel/runtime/helpers/classCallCheck")),a=u(e("@babel/runtime/helpers/createClass")),s=e("threads");function u(e){return e&&e.__esModule?e:{default:e}}var l="undefined"!=typeof navigator?navigator.hardwareConcurrency:null,f=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new s.Worker("decoder.worker.1936c0d9.js");(0,o.default)(this,e),this.pool=(0,s.Pool)((function(){return(0,s.spawn)(r)}),t)}return(0,a.default)(e,[{key:"decode",value:function(){var e=(0,i.default)(n.default.mark((function e(t,r){var o=this;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,a){o.pool.queue(function(){var o=(0,i.default)(n.default.mark((function i(o){var u;return n.default.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,o(t,(0,s.Transfer)(r));case 3:u=n.sent,e(u),n.next=10;break;case 7:n.prev=7,n.t0=n.catch(0),a(n.t0);case 10:case"end":return n.stop()}}),i,null,[[0,7]])})));return function(e){return o.apply(this,arguments)}}())})));case 1:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"destroy",value:function(){this.pool.terminate(!0)}}]),e}();r.default=f},{"@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW",threads:"CqwR","./decoder.worker.js":[["decoder.worker.1936c0d9.js","LHx9"],"decoder.worker.1936c0d9.js.map","LHx9"]}],t2zx:[function(e,t,r){t.exports=function(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}},{}],U8F3:[function(e,t,r){var n=e("./objectWithoutPropertiesLoose");t.exports=function(e,t){if(null==e)return{};var r,i,o=n(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)r=a[i],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}},{"./objectWithoutPropertiesLoose":"t2zx"}],OMTj:[function(e,t,r){t.exports=function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}},{}],kQhN:[function(e,t,r){var n=e("./arrayWithHoles"),i=e("./iterableToArray"),o=e("./unsupportedIterableToArray"),a=e("./nonIterableRest");t.exports=function(e){return n(e)||i(e)||o(e)||a()}},{"./arrayWithHoles":"OUZ9","./iterableToArray":"OMTj","./unsupportedIterableToArray":"UyFj","./nonIterableRest":"Rom6"}],ZJpj:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.parseContentType=function(e){var t=e.split(";").map((function(e){return e.trim()})),r=(0,n.default)(t);return{type:r[0],params:f(r.slice(1).map((function(e){return e.split("=")})))}},r.parseContentRange=h,r.parseByteRanges=function(e,t){for(var r=null,n=new TextDecoder("ascii"),i=[],o="--".concat(t),a="".concat(o,"--"),s=0;s<10;++s)n.decode(new Uint8Array(e,s,o.length))===o&&(r=s);if(null===r)throw new Error("Could not find initial boundary");for(;r<e.byteLength;){var u=n.decode(new Uint8Array(e,r,Math.min(o.length+1024,e.byteLength-r)));if(0===u.length||u.startsWith(a))break;if(!u.startsWith(o))throw new Error("Part does not start with boundary");var f=u.substr(o.length+2);if(0===f.length)break;var d=f.indexOf(l),p=c(f.substr(0,d)),b=h(p["content-range"]),y=b.start,v=b.end,g=b.total,m=r+o.length+d+l.length,w=parseInt(v,10)+1-parseInt(y,10);i.push({headers:p,data:e.slice(m,m+w),offset:y,length:w,fileSize:g}),r=m+w+4}return i};var n=o(e("@babel/runtime/helpers/toArray")),i=o(e("@babel/runtime/helpers/slicedToArray"));function o(e){return e&&e.__esModule?e:{default:e}}function a(e,t){var r;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=s(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,u=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return a=e.done,e},e:function(e){u=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw o}}}}function s(e,t){if(e){if("string"==typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var l="\r\n\r\n";function f(e){if(void 0!==Object.fromEntries)return Object.fromEntries(e);var t,r={},n=a(e);try{for(n.s();!(t=n.n()).done;){var o=(0,i.default)(t.value,2),s=o[0],u=o[1];r[s.toLowerCase()]=u}}catch(e){n.e(e)}finally{n.f()}return r}function c(e){return f(e.split("\r\n").map((function(e){var t=e.split(":").map((function(e){return e.trim()}));return t[0]=t[0].toLowerCase(),t})))}function h(e){var t,r,n;if(e){var o=e.match(/bytes (\d+)-(\d+)\/(\d+)/),a=(0,i.default)(o,4);t=a[1],r=a[2],n=a[3],t=parseInt(t,10),r=parseInt(r,10),n=parseInt(n,10)}return{start:t,end:r,total:n}}},{"@babel/runtime/helpers/toArray":"kQhN","@babel/runtime/helpers/slicedToArray":"HETk"}],LzCD:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.BaseSource=void 0;var n=s(e("@babel/runtime/regenerator")),i=s(e("@babel/runtime/helpers/asyncToGenerator")),o=s(e("@babel/runtime/helpers/classCallCheck")),a=s(e("@babel/runtime/helpers/createClass"));function s(e){return e&&e.__esModule?e:{default:e}}var u=function(){function e(){(0,o.default)(this,e)}return(0,a.default)(e,[{key:"fetch",value:function(){var e=(0,i.default)(n.default.mark((function e(t){var r,i=this,o=arguments;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=o.length>1&&void 0!==o[1]?o[1]:void 0,e.next=3,Promise.all(t.map((function(e){return i.fetchSlice(e,r)})));case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"fetchSlice",value:function(){var e=(0,i.default)(n.default.mark((function e(t){return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:throw new Error("fetching of slice ".concat(t," not possible, not implemented"));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"close",value:function(){var e=(0,i.default)(n.default.mark((function e(){return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()},{key:"fileSize",get:function(){return null}}]),e}();r.BaseSource=u},{"@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW"}],eMzk:[function(e,t,r){"use strict";t.exports=function(e){e.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}},{}],sfWH:[function(e,t,r){"use strict";function n(e){var t=this;if(t instanceof n||(t=new n),t.tail=null,t.head=null,t.length=0,e&&"function"==typeof e.forEach)e.forEach((function(e){t.push(e)}));else if(arguments.length>0)for(var r=0,i=arguments.length;r<i;r++)t.push(arguments[r]);return t}function i(e,t,r){var n=t===e.head?new s(r,null,t,e):new s(r,t,t.next,e);return null===n.next&&(e.tail=n),null===n.prev&&(e.head=n),e.length++,n}function o(e,t){e.tail=new s(t,e.tail,null,e),e.head||(e.head=e.tail),e.length++}function a(e,t){e.head=new s(t,null,e.head,e),e.tail||(e.tail=e.head),e.length++}function s(e,t,r,n){if(!(this instanceof s))return new s(e,t,r,n);this.list=n,this.value=e,t?(t.next=this,this.prev=t):this.prev=null,r?(r.prev=this,this.next=r):this.next=null}t.exports=n,n.Node=s,n.create=n,n.prototype.removeNode=function(e){if(e.list!==this)throw new Error("removing node which does not belong to this list");var t=e.next,r=e.prev;return t&&(t.prev=r),r&&(r.next=t),e===this.head&&(this.head=t),e===this.tail&&(this.tail=r),e.list.length--,e.next=null,e.prev=null,e.list=null,t},n.prototype.unshiftNode=function(e){if(e!==this.head){e.list&&e.list.removeNode(e);var t=this.head;e.list=this,e.next=t,t&&(t.prev=e),this.head=e,this.tail||(this.tail=e),this.length++}},n.prototype.pushNode=function(e){if(e!==this.tail){e.list&&e.list.removeNode(e);var t=this.tail;e.list=this,e.prev=t,t&&(t.next=e),this.tail=e,this.head||(this.head=e),this.length++}},n.prototype.push=function(){for(var e=0,t=arguments.length;e<t;e++)o(this,arguments[e]);return this.length},n.prototype.unshift=function(){for(var e=0,t=arguments.length;e<t;e++)a(this,arguments[e]);return this.length},n.prototype.pop=function(){if(this.tail){var e=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,e}},n.prototype.shift=function(){if(this.head){var e=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,e}},n.prototype.forEach=function(e,t){t=t||this;for(var r=this.head,n=0;null!==r;n++)e.call(t,r.value,n,this),r=r.next},n.prototype.forEachReverse=function(e,t){t=t||this;for(var r=this.tail,n=this.length-1;null!==r;n--)e.call(t,r.value,n,this),r=r.prev},n.prototype.get=function(e){for(var t=0,r=this.head;null!==r&&t<e;t++)r=r.next;if(t===e&&null!==r)return r.value},n.prototype.getReverse=function(e){for(var t=0,r=this.tail;null!==r&&t<e;t++)r=r.prev;if(t===e&&null!==r)return r.value},n.prototype.map=function(e,t){t=t||this;for(var r=new n,i=this.head;null!==i;)r.push(e.call(t,i.value,this)),i=i.next;return r},n.prototype.mapReverse=function(e,t){t=t||this;for(var r=new n,i=this.tail;null!==i;)r.push(e.call(t,i.value,this)),i=i.prev;return r},n.prototype.reduce=function(e,t){var r,n=this.head;if(arguments.length>1)r=t;else{if(!this.head)throw new TypeError("Reduce of empty list with no initial value");n=this.head.next,r=this.head.value}for(var i=0;null!==n;i++)r=e(r,n.value,i),n=n.next;return r},n.prototype.reduceReverse=function(e,t){var r,n=this.tail;if(arguments.length>1)r=t;else{if(!this.tail)throw new TypeError("Reduce of empty list with no initial value");n=this.tail.prev,r=this.tail.value}for(var i=this.length-1;null!==n;i--)r=e(r,n.value,i),n=n.prev;return r},n.prototype.toArray=function(){for(var e=new Array(this.length),t=0,r=this.head;null!==r;t++)e[t]=r.value,r=r.next;return e},n.prototype.toArrayReverse=function(){for(var e=new Array(this.length),t=0,r=this.tail;null!==r;t++)e[t]=r.value,r=r.prev;return e},n.prototype.slice=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=0,o=this.head;null!==o&&i<e;i++)o=o.next;for(;null!==o&&i<t;i++,o=o.next)r.push(o.value);return r},n.prototype.sliceReverse=function(e,t){(t=t||this.length)<0&&(t+=this.length),(e=e||0)<0&&(e+=this.length);var r=new n;if(t<e||t<0)return r;e<0&&(e=0),t>this.length&&(t=this.length);for(var i=this.length,o=this.tail;null!==o&&i>t;i--)o=o.prev;for(;null!==o&&i>e;i--,o=o.prev)r.push(o.value);return r},n.prototype.splice=function(e,t,...r){e>this.length&&(e=this.length-1),e<0&&(e=this.length+e);for(var n=0,o=this.head;null!==o&&n<e;n++)o=o.next;var a=[];for(n=0;o&&n<t;n++)a.push(o.value),o=this.removeNode(o);for(null===o&&(o=this.tail),o!==this.head&&o!==this.tail&&(o=o.prev),n=0;n<r.length;n++)o=i(this,o,r[n]);return a},n.prototype.reverse=function(){for(var e=this.head,t=this.tail,r=e;null!==r;r=r.prev){var n=r.prev;r.prev=r.next,r.next=n}return this.head=t,this.tail=e,this};try{e("./iterator.js")(n)}catch(e){}},{"./iterator.js":"eMzk"}],tpff:[function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function o(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e}var a=e("yallist"),s=Symbol("max"),u=Symbol("length"),l=Symbol("lengthCalculator"),f=Symbol("allowStale"),c=Symbol("maxAge"),h=Symbol("dispose"),d=Symbol("noDisposeOnSet"),p=Symbol("lruList"),b=Symbol("cache"),y=Symbol("updateAgeOnGet"),v=function(){return 1},g=function(){function e(t){if(n(this,e),"number"==typeof t&&(t={max:t}),t||(t={}),t.max&&("number"!=typeof t.max||t.max<0))throw new TypeError("max must be a non-negative number");this[s]=t.max||1/0;var r=t.length||v;if(this[l]="function"!=typeof r?v:r,this[f]=t.stale||!1,t.maxAge&&"number"!=typeof t.maxAge)throw new TypeError("maxAge must be a number");this[c]=t.maxAge||0,this[h]=t.dispose,this[d]=t.noDisposeOnSet||!1,this[y]=t.updateAgeOnGet||!1,this.reset()}return o(e,[{key:"rforEach",value:function(e,t){t=t||this;for(var r=this[p].tail;null!==r;){var n=r.prev;S(this,e,r,t),r=n}}},{key:"forEach",value:function(e,t){t=t||this;for(var r=this[p].head;null!==r;){var n=r.next;S(this,e,r,t),r=n}}},{key:"keys",value:function(){return this[p].toArray().map((function(e){return e.key}))}},{key:"values",value:function(){return this[p].toArray().map((function(e){return e.value}))}},{key:"reset",value:function(){var e=this;this[h]&&this[p]&&this[p].length&&this[p].forEach((function(t){return e[h](t.key,t.value)})),this[b]=new Map,this[p]=new a,this[u]=0}},{key:"dump",value:function(){var e=this;return this[p].map((function(t){return!w(e,t)&&{k:t.key,v:t.value,e:t.now+(t.maxAge||0)}})).toArray().filter((function(e){return e}))}},{key:"dumpLru",value:function(){return this[p]}},{key:"set",value:function(e,t,r){if((r=r||this[c])&&"number"!=typeof r)throw new TypeError("maxAge must be a number");var n=r?Date.now():0,i=this[l](t,e);if(this[b].has(e)){if(i>this[s])return k(this,this[b].get(e)),!1;var o=this[b].get(e).value;return this[h]&&(this[d]||this[h](e,o.value)),o.now=n,o.maxAge=r,o.value=t,this[u]+=i-o.length,o.length=i,this.get(e),_(this),!0}var a=new x(e,t,i,n,r);return a.length>this[s]?(this[h]&&this[h](e,t),!1):(this[u]+=a.length,this[p].unshift(a),this[b].set(e,this[p].head),_(this),!0)}},{key:"has",value:function(e){if(!this[b].has(e))return!1;var t=this[b].get(e).value;return!w(this,t)}},{key:"get",value:function(e){return m(this,e,!0)}},{key:"peek",value:function(e){return m(this,e,!1)}},{key:"pop",value:function(){var e=this[p].tail;return e?(k(this,e),e.value):null}},{key:"del",value:function(e){k(this,this[b].get(e))}},{key:"load",value:function(e){this.reset();for(var t=Date.now(),r=e.length-1;r>=0;r--){var n=e[r],i=n.e||0;if(0===i)this.set(n.k,n.v);else{var o=i-t;o>0&&this.set(n.k,n.v,o)}}}},{key:"prune",value:function(){var e=this;this[b].forEach((function(t,r){return m(e,r,!1)}))}},{key:"max",set:function(e){if("number"!=typeof e||e<0)throw new TypeError("max must be a non-negative number");this[s]=e||1/0,_(this)},get:function(){return this[s]}},{key:"allowStale",set:function(e){this[f]=!!e},get:function(){return this[f]}},{key:"maxAge",set:function(e){if("number"!=typeof e)throw new TypeError("maxAge must be a non-negative number");this[c]=e,_(this)},get:function(){return this[c]}},{key:"lengthCalculator",set:function(e){var t=this;"function"!=typeof e&&(e=v),e!==this[l]&&(this[l]=e,this[u]=0,this[p].forEach((function(e){e.length=t[l](e.value,e.key),t[u]+=e.length}))),_(this)},get:function(){return this[l]}},{key:"length",get:function(){return this[u]}},{key:"itemCount",get:function(){return this[p].length}}]),e}(),m=function(e,t,r){var n=e[b].get(t);if(n){var i=n.value;if(w(e,i)){if(k(e,n),!e[f])return}else r&&(e[y]&&(n.value.now=Date.now()),e[p].unshiftNode(n));return i.value}},w=function(e,t){if(!t||!t.maxAge&&!e[c])return!1;var r=Date.now()-t.now;return t.maxAge?r>t.maxAge:e[c]&&r>e[c]},_=function(e){if(e[u]>e[s])for(var t=e[p].tail;e[u]>e[s]&&null!==t;){var r=t.prev;k(e,t),t=r}},k=function(e,t){if(t){var r=t.value;e[h]&&e[h](r.key,r.value),e[u]-=r.length,e[b].delete(r.key),e[p].removeNode(t)}},x=function e(t,r,i,o,a){n(this,e),this.key=t,this.value=r,this.length=i,this.now=o,this.maxAge=a||0},S=function(e,t,r,n){var i=r.value;w(e,i)&&(k(e,r),e[f]||(i=void 0)),i&&t.call(n,i.value,i.key,e)};t.exports=g},{yallist:"sfWH"}],FOZT:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.assign=function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},r.chunk=function(e,t){for(var r=[],n=e.length,i=0;i<n;i+=t){for(var o=[],a=i;a<i+t;a++)o.push(e[a]);r.push(o)}return r},r.endsWith=function(e,t){return!(e.length<t.length)&&e.substr(e.length-t.length)===t},r.forEach=function(e,t){for(var r=e.length,n=0;n<r;n++)t(e[n],n)},r.invert=function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[e[r]]=r);return t},r.range=function(e){for(var t=[],r=0;r<e;r++)t.push(r);return t},r.times=function(e,t){for(var r=[],n=0;n<e;n++)r.push(t(n));return r},r.toArray=d,r.toArrayRecursively=function e(t){return t.length?d(t).map(e):t},r.parseContentRange=function(e){if(!e)return null;if("string"!=typeof e)throw new Error("invalid argument");var t=function(e){return Number.parseInt(e,10)},r=e.match(/^(\w*) /),n=r&&r[1];return(r=e.match(/(\d+)-(\d+)\/(\d+|\*)/))?{unit:n,first:t(r[1]),last:t(r[2]),length:"*"===r[3]?null:t(r[3])}:(r=e.match(/(\d+|\*)/))?{unit:n,first:null,last:null,length:"*"===r[1]?null:t(r[1])}:null},r.wait=function(e){return p.apply(this,arguments)},r.zip=function(e,t){var r=Array.isArray(e)?e:Array.from(e),n=Array.isArray(t)?t:Array.from(t);return r.map((function(e,t){return[e,n[t]]}))},r.AggregateError=r.CustomAggregateError=r.AbortError=void 0;var n=c(e("@babel/runtime/regenerator")),i=c(e("@babel/runtime/helpers/classCallCheck")),o=c(e("@babel/runtime/helpers/assertThisInitialized")),a=c(e("@babel/runtime/helpers/inherits")),s=c(e("@babel/runtime/helpers/possibleConstructorReturn")),u=c(e("@babel/runtime/helpers/getPrototypeOf")),l=c(e("@babel/runtime/helpers/wrapNativeSuper")),f=c(e("@babel/runtime/helpers/asyncToGenerator"));function c(e){return e&&e.__esModule?e:{default:e}}function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,u.default)(e);if(t){var i=(0,u.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,s.default)(this,r)}}function d(e){for(var t=[],r=e.length,n=0;n<r;n++)t.push(e[n]);return t}function p(){return(p=(0,f.default)(n.default.mark((function e(t){return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e){return setTimeout(e,t)})));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var b=function(e){(0,a.default)(r,e);var t=h(r);function r(e){var n;return(0,i.default)(this,r),n=t.call(this,e),Error.captureStackTrace&&Error.captureStackTrace((0,o.default)(n),r),n.name="AbortError",n}return r}((0,l.default)(Error));r.AbortError=b;var y=function(e){(0,a.default)(r,e);var t=h(r);function r(e,n){var o;return(0,i.default)(this,r),(o=t.call(this,n)).errors=e,o.message=n,o.name="AggregateError",o}return r}((0,l.default)(Error));r.CustomAggregateError=y;var v=y;r.AggregateError=v},{"@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/assertThisInitialized":"E7HD","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","@babel/runtime/helpers/wrapNativeSuper":"ozpi","@babel/runtime/helpers/asyncToGenerator":"agGE"}],xD7g:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.BlockedSource=void 0;var n=p(e("@babel/runtime/regenerator")),i=p(e("@babel/runtime/helpers/slicedToArray")),o=p(e("@babel/runtime/helpers/asyncToGenerator")),a=p(e("@babel/runtime/helpers/inherits")),s=p(e("@babel/runtime/helpers/possibleConstructorReturn")),u=p(e("@babel/runtime/helpers/getPrototypeOf")),l=p(e("@babel/runtime/helpers/classCallCheck")),f=p(e("@babel/runtime/helpers/createClass")),c=p(e("lru-cache")),h=e("./basesource"),d=e("../utils");function p(e){return e&&e.__esModule?e:{default:e}}function b(e,t){var r;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return y(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw o}}}}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function v(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,u.default)(e);if(t){var i=(0,u.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,s.default)(this,r)}}var g=function(){function e(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;(0,l.default)(this,e),this.offset=t,this.length=r,this.data=n}return(0,f.default)(e,[{key:"top",get:function(){return this.offset+this.length}}]),e}(),m=function e(t,r,n){(0,l.default)(this,e),this.offset=t,this.length=r,this.blockIds=n},w=function(e){(0,a.default)(r,e);var t=v(r);function r(e){var n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=i.blockSize,a=void 0===o?65536:o,s=i.cacheSize,u=void 0===s?100:s;return(0,l.default)(this,r),(n=t.call(this)).source=e,n.blockSize=a,n.blockCache=new c.default({max:u}),n.blockRequests=new Map,n.blockIdsToFetch=new Set,n}return(0,f.default)(r,[{key:"fetch",value:function(){var e=(0,o.default)(n.default.mark((function e(t,r){var o,a,s,u,l,f,c,h,p,y,v,g,m,w,_,k,x,S,O,T,E,C,P,j,R,M,A,I,D,L,N,F,U,B,G,z,H,W;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:o=new Map,a=new Map,s=new Set,u=b(t);try{for(u.s();!(l=u.n()).done;)for(f=l.value,c=f.offset,h=f.length,p=c+h,null!==(y=this.fileSize)&&(p=Math.min(p,y)),v=Math.floor(c/this.blockSize)*this.blockSize,g=v;g<p;g+=this.blockSize)m=Math.floor(g/this.blockSize),this.blockCache.has(m)?o.set(m,this.blockCache.get(m)):this.blockRequests.has(m)?a.set(m,this.blockRequests.get(m)):(this.blockIdsToFetch.has(m)||this.blockIdsToFetch.add(m),s.add(m))}catch(e){u.e(e)}finally{u.f()}return e.next=7,(0,d.wait)();case 7:this.fetchBlocks(r),w=b(s),e.prev=9,w.s();case 11:if((_=w.n()).done){e.next=26;break}if(k=_.value,x=this.blockRequests.get(k),S=this.blockCache.get(k),!x){e.next=19;break}a.set(k,x),e.next=24;break;case 19:if(!S){e.next=23;break}o.set(k,S),e.next=24;break;case 23:throw new Error("Block ".concat(k," is not in the block requests"));case 24:e.next=11;break;case 26:e.next=31;break;case 28:e.prev=28,e.t0=e.catch(9),w.e(e.t0);case 31:return e.prev=31,w.f(),e.finish(31);case 34:return e.next=36,Promise.allSettled(a.values());case 36:if(!(O=e.sent).some((function(e){return"rejected"===e.status}))){e.next=65;break}T=new Set,E=b((0,d.zip)(a.keys(),O));try{for(E.s();!(C=E.n()).done;)P=(0,i.default)(C.value,2),j=P[0],R=P[1],M=R.rejected,A=R.reason,M&&"AbortError"===A.name&&A.signal!==r&&(this.blockIdsToFetch.add(j),T.add(j))}catch(e){E.e(e)}finally{E.f()}if(!(this.blockIdsToFetch.length>0)){e.next=65;break}this.fetchBlocks(r),I=b(T),e.prev=44,I.s();case 46:if((D=I.n()).done){e.next=54;break}if(L=D.value,N=this.blockRequests.get(L)){e.next=51;break}throw new Error("Block ".concat(L," is not in the block requests"));case 51:a.set(L,N);case 52:e.next=46;break;case 54:e.next=59;break;case 56:e.prev=56,e.t1=e.catch(44),I.e(e.t1);case 59:return e.prev=59,I.f(),e.finish(59);case 62:return e.next=64,Promise.allSettled(Array.from(a.values()));case 64:O=e.sent;case 65:if(!O.some((function(e){return"rejected"===e.status}))){e.next=69;break}if(!r||!r.aborted){e.next=68;break}throw new d.AbortError("Request was aborted");case 68:throw new d.AggregateError(O.filter((function(e){return"rejected"===e.status})).map((function(e){return e.reason})),"Request failed");case 69:F=O.map((function(e){return e.value})),U=new Map((0,d.zip)(Array.from(a.keys()),F)),B=b(o);try{for(B.s();!(G=B.n()).done;)z=(0,i.default)(G.value,2),H=z[0],W=z[1],U.set(H,W)}catch(e){B.e(e)}finally{B.f()}return e.abrupt("return",this.readSliceData(t,U));case 74:case"end":return e.stop()}}),e,this,[[9,28,31,34],[44,56,59,62]])})));return function(t,r){return e.apply(this,arguments)}}()},{key:"fetchBlocks",value:function(e){var t=this;this.blockIdsToFetch.size>0&&function(){for(var r=t.groupBlocks(t.blockIdsToFetch),i=t.source.fetch(r,e),a=function(a){var s,u=b(r[a].blockIds);try{var l=function(){var r=s.value,u=(0,o.default)(n.default.mark((function o(){var s,u,l,f,c,h;return n.default.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,i;case 3:return n.t0=a,s=n.sent[n.t0],u=r*t.blockSize,l=u-s.offset,f=Math.min(l+t.blockSize,s.data.byteLength),c=s.data.slice(l,f),h=new g(u,c.byteLength,c),t.blockCache.set(r,h),n.abrupt("return",h);case 14:throw n.prev=14,n.t1=n.catch(0),"AbortError"===n.t1.name&&(n.t1.signal=e),n.t1;case 18:return n.prev=18,t.blockRequests.delete(r),n.finish(18);case 21:case"end":return n.stop()}}),o,null,[[0,14,18,21]])})))();t.blockRequests.set(r,u)};for(u.s();!(s=u.n()).done;)l()}catch(e){u.e(e)}finally{u.f()}},s=0;s<r.length;++s)a(s);t.blockIdsToFetch.clear()}()}},{key:"groupBlocks",value:function(e){var t=Array.from(e).sort((function(e,t){return e-t}));if(0===t.length)return[];var r,n=[],i=null,o=[],a=b(t);try{for(a.s();!(r=a.n()).done;){var s=r.value;null===i||i+1===s?(n.push(s),i=s):(o.push(new m(n[0]*this.blockSize,n.length*this.blockSize,n)),n=[s],i=s)}}catch(e){a.e(e)}finally{a.f()}return o.push(new m(n[0]*this.blockSize,n.length*this.blockSize,n)),o}},{key:"readSliceData",value:function(e,t){var r=this;return e.map((function(e){for(var n=e.offset+e.length,i=Math.floor(e.offset/r.blockSize),o=Math.floor((e.offset+e.length)/r.blockSize),a=new ArrayBuffer(e.length),s=new Uint8Array(a),u=i;u<=o;++u){var l,f=t.get(u),c=f.offset-e.offset,h=0,d=0;c<0?h=-c:c>0&&(d=c),l=f.top-n<0?f.length-h:n-f.offset-h;var p=new Uint8Array(f.data,h,l);s.set(p,d)}return a}))}},{key:"fileSize",get:function(){return this.source.fileSize}}]),r}(h.BaseSource);r.BlockedSource=w},{"@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/slicedToArray":"HETk","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","lru-cache":"tpff","./basesource":"LzCD","../utils":"FOZT"}],uL1U:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.BaseClient=r.BaseResponse=void 0;var n=s(e("@babel/runtime/regenerator")),i=s(e("@babel/runtime/helpers/asyncToGenerator")),o=s(e("@babel/runtime/helpers/classCallCheck")),a=s(e("@babel/runtime/helpers/createClass"));function s(e){return e&&e.__esModule?e:{default:e}}var u=function(){function e(){(0,o.default)(this,e)}return(0,a.default)(e,[{key:"getHeader",value:function(e){throw new Error("not implemented")}},{key:"getData",value:function(){var e=(0,i.default)(n.default.mark((function e(){return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:throw new Error("not implemented");case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()},{key:"ok",get:function(){return this.status>=200&&this.status<=299}},{key:"status",get:function(){throw new Error("not implemented")}}]),e}();r.BaseResponse=u;var l=function(){function e(t){(0,o.default)(this,e),this.url=t}return(0,a.default)(e,[{key:"request",value:function(){var e=(0,i.default)(n.default.mark((function e(){var t,r=arguments;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:throw(t=r.length>0&&void 0!==r[0]?r[0]:{}).headers,t.credentials,t.signal,new Error("request is not implemented");case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()}]),e}();r.BaseClient=l},{"@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW"}],Un5s:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.FetchClient=void 0;var n=c(e("@babel/runtime/regenerator")),i=c(e("@babel/runtime/helpers/asyncToGenerator")),o=c(e("@babel/runtime/helpers/classCallCheck")),a=c(e("@babel/runtime/helpers/createClass")),s=c(e("@babel/runtime/helpers/inherits")),u=c(e("@babel/runtime/helpers/possibleConstructorReturn")),l=c(e("@babel/runtime/helpers/getPrototypeOf")),f=e("./base");function c(e){return e&&e.__esModule?e:{default:e}}function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,l.default)(e);if(t){var i=(0,l.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,u.default)(this,r)}}var d=function(e){(0,s.default)(r,e);var t=h(r);function r(e){var n;return(0,o.default)(this,r),(n=t.call(this)).response=e,n}return(0,a.default)(r,[{key:"getHeader",value:function(e){return this.response.headers.get(e)}},{key:"getData",value:function(){var e=(0,i.default)(n.default.mark((function e(){var t;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.response.arrayBuffer){e.next=6;break}return e.next=3,this.response.arrayBuffer();case 3:e.t0=e.sent,e.next=9;break;case 6:return e.next=8,this.response.buffer();case 8:e.t0=e.sent.buffer;case 9:return t=e.t0,e.abrupt("return",t);case 11:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"status",get:function(){return this.response.status}}]),r}(f.BaseResponse),p=function(e){(0,s.default)(r,e);var t=h(r);function r(e,n){var i;return(0,o.default)(this,r),(i=t.call(this,e)).credentials=n,i}return(0,a.default)(r,[{key:"request",value:function(){var e=(0,i.default)(n.default.mark((function e(){var t,r,i,o,a,s=arguments;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=s.length>0&&void 0!==s[0]?s[0]:{},r=t.headers,i=t.credentials,o=t.signal,e.next=3,fetch(this.url,{headers:r,credentials:i,signal:o});case 3:return a=e.sent,e.abrupt("return",new d(a));case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),r}(f.BaseClient);r.FetchClient=p},{"@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","./base":"uL1U"}],hvgd:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.XHRClient=void 0;var n=d(e("@babel/runtime/helpers/slicedToArray")),i=d(e("@babel/runtime/regenerator")),o=d(e("@babel/runtime/helpers/asyncToGenerator")),a=d(e("@babel/runtime/helpers/classCallCheck")),s=d(e("@babel/runtime/helpers/createClass")),u=d(e("@babel/runtime/helpers/inherits")),l=d(e("@babel/runtime/helpers/possibleConstructorReturn")),f=d(e("@babel/runtime/helpers/getPrototypeOf")),c=e("./base"),h=e("../../utils");function d(e){return e&&e.__esModule?e:{default:e}}function p(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,f.default)(e);if(t){var i=(0,f.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,l.default)(this,r)}}var b=function(e){(0,u.default)(r,e);var t=p(r);function r(e,n){var i;return(0,a.default)(this,r),(i=t.call(this)).xhr=e,i.data=n,i}return(0,s.default)(r,[{key:"getHeader",value:function(e){return this.xhr.getResponseHeader(e)}},{key:"getData",value:function(){var e=(0,o.default)(i.default.mark((function e(){return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.data);case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"status",get:function(){return this.xhr.status}}]),r}(c.BaseResponse),y=function(e){(0,u.default)(r,e);var t=p(r);function r(){return(0,a.default)(this,r),t.apply(this,arguments)}return(0,s.default)(r,[{key:"constructRequest",value:function(e,t){var r=this;return new Promise((function(i,o){var a=new XMLHttpRequest;a.open("GET",r.url),a.responseType="arraybuffer";for(var s=0,u=Object.entries(e);s<u.length;s++){var l=(0,n.default)(u[s],2),f=l[0],c=l[1];a.setRequestHeader(f,c)}a.onload=function(){var e=a.response;i(new b(a,e))},a.onerror=o,a.onabort=function(){return o(new h.AbortError("Request aborted"))},a.send(),t&&(t.aborted&&a.abort(),t.addEventListener("abort",(function(){return a.abort()})))}))}},{key:"request",value:function(){var e=(0,o.default)(i.default.mark((function e(){var t,r,n,o,a=arguments;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=a.length>0&&void 0!==a[0]?a[0]:{},r=t.headers,n=t.signal,e.next=3,this.constructRequest(r,n);case 3:return o=e.sent,e.abrupt("return",o);case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),r}(c.BaseClient);r.XHRClient=y},{"@babel/runtime/helpers/slicedToArray":"HETk","@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","./base":"uL1U","../../utils":"FOZT"}],p5a1:[function(e,t,r){var n,i=arguments[3];r.fetch=l(i.fetch)&&l(i.ReadableStream),r.writableStream=l(i.WritableStream),r.abortController=l(i.AbortController),r.blobConstructor=!1;try{new Blob([new ArrayBuffer(1)]),r.blobConstructor=!0}catch(e){}function o(){if(void 0!==n)return n;if(i.XMLHttpRequest){n=new i.XMLHttpRequest;try{n.open("GET",i.XDomainRequest?"/":"https://example.com")}catch(e){n=null}}else n=null;return n}function a(e){var t=o();if(!t)return!1;try{return t.responseType=e,t.responseType===e}catch(e){}return!1}var s=void 0!==i.ArrayBuffer,u=s&&l(i.ArrayBuffer.prototype.slice);function l(e){return"function"==typeof e}r.arraybuffer=r.fetch||s&&a("arraybuffer"),r.msstream=!r.fetch&&u&&a("ms-stream"),r.mozchunkedarraybuffer=!r.fetch&&s&&a("moz-chunked-arraybuffer"),r.overrideMimeType=r.fetch||!!o()&&l(o().overrideMimeType),r.vbArray=l(i.VBArray),n=null},{}],Yj0v:[function(e,t,r){e("process");var n=e("process");void 0===n||!n.version||0===n.version.indexOf("v0.")||0===n.version.indexOf("v1.")&&0!==n.version.indexOf("v1.8.")?t.exports={nextTick:function(e,t,r,i){if("function"!=typeof e)throw new TypeError('"callback" argument must be a function');var o,a,s=arguments.length;switch(s){case 0:case 1:return n.nextTick(e);case 2:return n.nextTick((function(){e.call(null,t)}));case 3:return n.nextTick((function(){e.call(null,t,r)}));case 4:return n.nextTick((function(){e.call(null,t,r,i)}));default:for(o=new Array(s-1),a=0;a<o.length;)o[a++]=arguments[a];return n.nextTick((function(){e.apply(null,o)}))}}}:t.exports=n},{process:"pBGv"}],Nqn4:[function(e,t,r){var n=e("buffer"),i=n.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function a(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=n:(o(n,r),r.Buffer=a),o(i,a),a.from=function(e,t,r){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,t,r)},a.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},a.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},a.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}},{buffer:"dskh"}],Q14w:[function(e,t,r){e("buffer").Buffer;var n=e("buffer").Buffer;function i(e){return Object.prototype.toString.call(e)}r.isArray=function(e){return Array.isArray?Array.isArray(e):"[object Array]"===i(e)},r.isBoolean=function(e){return"boolean"==typeof e},r.isNull=function(e){return null===e},r.isNullOrUndefined=function(e){return null==e},r.isNumber=function(e){return"number"==typeof e},r.isString=function(e){return"string"==typeof e},r.isSymbol=function(e){return"symbol"==typeof e},r.isUndefined=function(e){return void 0===e},r.isRegExp=function(e){return"[object RegExp]"===i(e)},r.isObject=function(e){return"object"==typeof e&&null!==e},r.isDate=function(e){return"[object Date]"===i(e)},r.isError=function(e){return"[object Error]"===i(e)||e instanceof Error},r.isFunction=function(e){return"function"==typeof e},r.isPrimitive=function(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e},r.isBuffer=n.isBuffer},{buffer:"dskh"}],CQMM:[function(e,t,r){"use strict";var n=e("safe-buffer").Buffer,i=e("util");function o(e,t,r){e.copy(t,r)}t.exports=function(){function e(){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r},e.prototype.concat=function(e){if(0===this.length)return n.alloc(0);if(1===this.length)return this.head.data;for(var t=n.allocUnsafe(e>>>0),r=this.head,i=0;r;)o(r.data,t,i),i+=r.data.length,r=r.next;return t},e}(),i&&i.inspect&&i.inspect.custom&&(t.exports.prototype[i.inspect.custom]=function(){var e=i.inspect({length:this.length});return this.constructor.name+" "+e})},{"safe-buffer":"Nqn4",util:"rDCW"}],lCUh:[function(e,t,r){"use strict";var n=e("process-nextick-args");function i(e,t){e.emit("error",t)}t.exports={destroy:function(e,t){var r=this,o=this._readableState&&this._readableState.destroyed,a=this._writableState&&this._writableState.destroyed;return o||a?(t?t(e):!e||this._writableState&&this._writableState.errorEmitted||n.nextTick(i,this,e),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,(function(e){!t&&e?(n.nextTick(i,r,e),r._writableState&&(r._writableState.errorEmitted=!0)):t&&t(e)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},{"process-nextick-args":"Yj0v"}],jPmE:[function(e,t,r){e("process"),e("process");var n=arguments[3],i=e("process-nextick-args");function o(e){var t=this;this.next=null,this.entry=null,this.finish=function(){L(t,e)}}t.exports=m;var a,s=i.nextTick;m.WritableState=g;var u=Object.create(e("core-util-is"));u.inherits=e("inherits");var l={deprecate:e("util-deprecate")},f=e("./internal/streams/stream"),c=e("safe-buffer").Buffer,h=n.Uint8Array||function(){};function d(e){return c.from(e)}function p(e){return c.isBuffer(e)||e instanceof h}var b,y=e("./internal/streams/destroy");function v(){}function g(t,r){a=a||e("./_stream_duplex"),t=t||{};var n=r instanceof a;this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var i=t.highWaterMark,s=t.writableHighWaterMark,u=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(s||0===s)?s:u,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var l=!1===t.decodeStrings;this.decodeStrings=!l,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){E(r,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new o(this)}function m(t){if(a=a||e("./_stream_duplex"),!(b.call(m,this)||this instanceof a))return new m(t);this._writableState=new g(t,this),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),f.call(this)}function w(e,t){var r=new Error("write after end");e.emit("error",r),i.nextTick(t,r)}function _(e,t,r,n){var o=!0,a=!1;return null===r?a=new TypeError("May not write null values to stream"):"string"==typeof r||void 0===r||t.objectMode||(a=new TypeError("Invalid non-string/buffer chunk")),a&&(e.emit("error",a),i.nextTick(n,a),o=!1),o}function k(e,t,r){return e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=c.from(t,r)),t}function x(e,t,r,n,i,o){if(!r){var a=k(t,n,i);n!==a&&(r=!0,i="buffer",n=a)}var s=t.objectMode?1:n.length;t.length+=s;var u=t.length<t.highWaterMark;if(u||(t.needDrain=!0),t.writing||t.corked){var l=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},l?l.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else S(e,t,!1,s,n,i,o);return u}function S(e,t,r,n,i,o,a){t.writelen=n,t.writecb=a,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function O(e,t,r,n,o){--t.pendingcb,r?(i.nextTick(o,n),i.nextTick(I,e,t),e._writableState.errorEmitted=!0,e.emit("error",n)):(o(n),e._writableState.errorEmitted=!0,e.emit("error",n),I(e,t))}function T(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}function E(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(T(r),t)O(e,r,n,t,i);else{var o=R(r);o||r.corked||r.bufferProcessing||!r.bufferedRequest||j(e,r),n?s(C,e,r,o,i):C(e,r,o,i)}}function C(e,t,r,n){r||P(e,t),t.pendingcb--,n(),I(e,t)}function P(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}function j(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),a=t.corkedRequestsFree;a.entry=r;for(var s=0,u=!0;r;)i[s]=r,r.isBuf||(u=!1),r=r.next,s+=1;i.allBuffers=u,S(e,t,!0,t.length,i,"",a.finish),t.pendingcb++,t.lastBufferedRequest=null,a.next?(t.corkedRequestsFree=a.next,a.next=null):t.corkedRequestsFree=new o(t),t.bufferedRequestCount=0}else{for(;r;){var l=r.chunk,f=r.encoding,c=r.callback;if(S(e,t,!1,t.objectMode?1:l.length,l,f,c),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function R(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function M(e,t){e._final((function(r){t.pendingcb--,r&&e.emit("error",r),t.prefinished=!0,e.emit("prefinish"),I(e,t)}))}function A(e,t){t.prefinished||t.finalCalled||("function"==typeof e._final?(t.pendingcb++,t.finalCalled=!0,i.nextTick(M,e,t)):(t.prefinished=!0,e.emit("prefinish")))}function I(e,t){var r=R(t);return r&&(A(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),r}function D(e,t,r){t.ending=!0,I(e,t),r&&(t.finished?i.nextTick(r):e.once("finish",r)),t.ended=!0,e.writable=!1}function L(e,t,r){var n=e.entry;for(e.entry=null;n;){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree?t.corkedRequestsFree.next=e:t.corkedRequestsFree=e}u.inherits(m,f),g.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(g.prototype,"buffer",{get:l.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(b=Function.prototype[Symbol.hasInstance],Object.defineProperty(m,Symbol.hasInstance,{value:function(e){return!!b.call(this,e)||this===m&&e&&e._writableState instanceof g}})):b=function(e){return e instanceof this},m.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},m.prototype.write=function(e,t,r){var n=this._writableState,i=!1,o=!n.objectMode&&p(e);return o&&!c.isBuffer(e)&&(e=d(e)),"function"==typeof t&&(r=t,t=null),o?t="buffer":t||(t=n.defaultEncoding),"function"!=typeof r&&(r=v),n.ended?w(this,r):(o||_(this,n,e,r))&&(n.pendingcb++,i=x(this,n,o,e,t,r)),i},m.prototype.cork=function(){this._writableState.corked++},m.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||j(this,e))},m.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(m.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),m.prototype._write=function(e,t,r){r(new Error("_write() is not implemented"))},m.prototype._writev=null,m.prototype.end=function(e,t,r){var n=this._writableState;"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!=e&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||n.finished||D(this,n,r)},Object.defineProperty(m.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),m.prototype.destroy=y.destroy,m.prototype._undestroy=y.undestroy,m.prototype._destroy=function(e,t){this.end(),t(e)}},{"process-nextick-args":"Yj0v","core-util-is":"Q14w",inherits:"Bm0n","util-deprecate":"yM1o","./internal/streams/stream":"ExO1","safe-buffer":"Nqn4","./internal/streams/destroy":"lCUh","./_stream_duplex":"Jcj8",process:"pBGv"}],Jcj8:[function(e,t,r){"use strict";var n=e("process-nextick-args"),i=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};t.exports=c;var o=Object.create(e("core-util-is"));o.inherits=e("inherits");var a=e("./_stream_readable"),s=e("./_stream_writable");o.inherits(c,a);for(var u=i(s.prototype),l=0;l<u.length;l++){var f=u[l];c.prototype[f]||(c.prototype[f]=s.prototype[f])}function c(e){if(!(this instanceof c))return new c(e);a.call(this,e),s.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",h)}function h(){this.allowHalfOpen||this._writableState.ended||n.nextTick(d,this)}function d(e){e.end()}Object.defineProperty(c.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(c.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&this._readableState.destroyed&&this._writableState.destroyed},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),c.prototype._destroy=function(e,t){this.push(null),this.end(),n.nextTick(t,e)}},{"process-nextick-args":"Yj0v","core-util-is":"Q14w",inherits:"Bm0n","./_stream_readable":"YrHu","./_stream_writable":"jPmE"}],OUJr:[function(e,t,r){"use strict";var n=e("safe-buffer").Buffer,i=n.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(e){var t=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(n.isEncoding===i||!i(e)))throw new Error("Unknown encoding: "+e);return t||e}function a(e){var t;switch(this.encoding=o(e),this.encoding){case"utf16le":this.text=l,this.end=f,t=4;break;case"utf8":this.fillLast=u,t=4;break;case"base64":this.text=c,this.end=h,t=3;break;default:return this.write=d,void(this.end=p)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function s(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function u(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function l(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function f(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function c(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function h(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function d(e){return e.toString(this.encoding)}function p(e){return e&&e.length?this.write(e):""}r.StringDecoder=a,a.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},a.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},a.prototype.text=function(e,t){var r=function(e,t,r){var n=t.length-1;if(n<r)return 0;var i=s(t[n]);return i>=0?(i>0&&(e.lastNeed=i-1),i):--n<r||-2===i?0:(i=s(t[n]))>=0?(i>0&&(e.lastNeed=i-2),i):--n<r||-2===i?0:(i=s(t[n]))>=0?(i>0&&(2===i?i=0:e.lastNeed=i-3),i):0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)},a.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},{"safe-buffer":"Nqn4"}],YrHu:[function(e,t,r){e("process");var n=arguments[3],i=e("process"),o=e("process-nextick-args");t.exports=x;var a,s=e("isarray");x.ReadableState=k;e("events").EventEmitter;var u=function(e,t){return e.listeners(t).length},l=e("./internal/streams/stream"),f=e("safe-buffer").Buffer,c=n.Uint8Array||function(){};function h(e){return f.from(e)}function d(e){return f.isBuffer(e)||e instanceof c}var p=Object.create(e("core-util-is"));p.inherits=e("inherits");var b=e("util"),y=void 0;y=b&&b.debuglog?b.debuglog("stream"):function(){};var v,g=e("./internal/streams/BufferList"),m=e("./internal/streams/destroy");p.inherits(x,l);var w=["error","close","destroy","pause","resume"];function _(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?s(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}function k(t,r){t=t||{};var n=r instanceof(a=a||e("./_stream_duplex"));this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var i=t.highWaterMark,o=t.readableHighWaterMark,s=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(o||0===o)?o:s,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new g,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(v||(v=e("string_decoder/").StringDecoder),this.decoder=new v(t.encoding),this.encoding=t.encoding)}function x(t){if(a=a||e("./_stream_duplex"),!(this instanceof x))return new x(t);this._readableState=new k(t,this),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),l.call(this)}function S(e,t,r,n,i){var o,a=e._readableState;return null===t?(a.reading=!1,R(e,a)):(i||(o=T(a,t)),o?e.emit("error",o):a.objectMode||t&&t.length>0?("string"==typeof t||a.objectMode||Object.getPrototypeOf(t)===f.prototype||(t=h(t)),n?a.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):O(e,a,t,!0):a.ended?e.emit("error",new Error("stream.push() after EOF")):(a.reading=!1,a.decoder&&!r?(t=a.decoder.write(t),a.objectMode||0!==t.length?O(e,a,t,!1):I(e,a)):O(e,a,t,!1))):n||(a.reading=!1)),E(a)}function O(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&M(e)),I(e,t)}function T(e,t){var r;return d(t)||"string"==typeof t||void 0===t||e.objectMode||(r=new TypeError("Invalid non-string/buffer chunk")),r}function E(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}Object.defineProperty(x.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),x.prototype.destroy=m.destroy,x.prototype._undestroy=m.undestroy,x.prototype._destroy=function(e,t){this.push(null),t(e)},x.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof e&&((t=t||n.defaultEncoding)!==n.encoding&&(e=f.from(e,t),t=""),r=!0),S(this,e,t,!1,r)},x.prototype.unshift=function(e){return S(this,e,null,!0,!1)},x.prototype.isPaused=function(){return!1===this._readableState.flowing},x.prototype.setEncoding=function(t){return v||(v=e("string_decoder/").StringDecoder),this._readableState.decoder=new v(t),this._readableState.encoding=t,this};var C=8388608;function P(e){return e>=C?e=C:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}function j(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=P(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function R(e,t){if(!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,M(e)}}function M(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(y("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?o.nextTick(A,e):A(e))}function A(e){y("emit readable"),e.emit("readable"),B(e)}function I(e,t){t.readingMore||(t.readingMore=!0,o.nextTick(D,e,t))}function D(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(y("maybeReadMore read 0"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function L(e){return function(){var t=e._readableState;y("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&u(e,"data")&&(t.flowing=!0,B(e))}}function N(e){y("readable nexttick read 0"),e.read(0)}function F(e,t){t.resumeScheduled||(t.resumeScheduled=!0,o.nextTick(U,e,t))}function U(e,t){t.reading||(y("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),B(e),t.flowing&&!t.reading&&e.read(0)}function B(e){var t=e._readableState;for(y("flow",t.flowing);t.flowing&&null!==e.read(););}function G(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=z(e,t.buffer,t.decoder),r);var r}function z(e,t,r){var n;return e<t.head.data.length?(n=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):n=e===t.head.data.length?t.shift():r?H(e,t):W(e,t),n}function H(e,t){var r=t.head,n=1,i=r.data;for(e-=i.length;r=r.next;){var o=r.data,a=e>o.length?o.length:e;if(a===o.length?i+=o:i+=o.slice(0,e),0==(e-=a)){a===o.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=o.slice(a));break}++n}return t.length-=n,i}function W(e,t){var r=f.allocUnsafe(e),n=t.head,i=1;for(n.data.copy(r),e-=n.data.length;n=n.next;){var o=n.data,a=e>o.length?o.length:e;if(o.copy(r,r.length-e,0,a),0==(e-=a)){a===o.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=o.slice(a));break}++i}return t.length-=i,r}function q(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,o.nextTick(K,t,e))}function K(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function V(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}x.prototype.read=function(e){y("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return y("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?q(this):M(this),null;if(0===(e=j(e,t))&&t.ended)return 0===t.length&&q(this),null;var n,i=t.needReadable;return y("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&y("length less than watermark",i=!0),t.ended||t.reading?y("reading or ended",i=!1):i&&(y("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=j(r,t))),null===(n=e>0?G(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&q(this)),null!==n&&this.emit("data",n),n},x.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},x.prototype.pipe=function(e,t){var r=this,n=this._readableState;switch(n.pipesCount){case 0:n.pipes=e;break;case 1:n.pipes=[n.pipes,e];break;default:n.pipes.push(e)}n.pipesCount+=1,y("pipe count=%d opts=%j",n.pipesCount,t);var a=t&&!1===t.end||e===i.stdout||e===i.stderr?v:s;function s(){y("onend"),e.end()}n.endEmitted?o.nextTick(a):r.once("end",a),e.on("unpipe",(function t(i,o){y("onunpipe"),i===r&&o&&!1===o.hasUnpiped&&(o.hasUnpiped=!0,y("cleanup"),e.removeListener("close",p),e.removeListener("finish",b),e.removeListener("drain",l),e.removeListener("error",d),e.removeListener("unpipe",t),r.removeListener("end",s),r.removeListener("end",v),r.removeListener("data",h),f=!0,!n.awaitDrain||e._writableState&&!e._writableState.needDrain||l())}));var l=L(r);e.on("drain",l);var f=!1,c=!1;function h(t){y("ondata"),c=!1,!1!==e.write(t)||c||((1===n.pipesCount&&n.pipes===e||n.pipesCount>1&&-1!==V(n.pipes,e))&&!f&&(y("false write response, pause",r._readableState.awaitDrain),r._readableState.awaitDrain++,c=!0),r.pause())}function d(t){y("onerror",t),v(),e.removeListener("error",d),0===u(e,"error")&&e.emit("error",t)}function p(){e.removeListener("finish",b),v()}function b(){y("onfinish"),e.removeListener("close",p),v()}function v(){y("unpipe"),r.unpipe(e)}return r.on("data",h),_(e,"error",d),e.once("close",p),e.once("finish",b),e.emit("pipe",r),n.flowing||(y("pipe resume"),r.resume()),e},x.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r)),this;if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,r);return this}var a=V(t.pipes,e);return-1===a||(t.pipes.splice(a,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r)),this},x.prototype.on=function(e,t){var r=l.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&M(this):o.nextTick(N,this))}return r},x.prototype.addListener=x.prototype.on,x.prototype.resume=function(){var e=this._readableState;return e.flowing||(y("resume"),e.flowing=!0,F(this,e)),this},x.prototype.pause=function(){return y("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(y("pause"),this._readableState.flowing=!1,this.emit("pause")),this},x.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;for(var i in e.on("end",(function(){if(y("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on("data",(function(i){y("wrapped data"),r.decoder&&(i=r.decoder.write(i)),r.objectMode&&null==i||(r.objectMode||i&&i.length)&&(t.push(i)||(n=!0,e.pause()))})),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<w.length;o++)e.on(w[o],this.emit.bind(this,w[o]));return this._read=function(t){y("wrapped _read",t),n&&(n=!1,e.resume())},this},Object.defineProperty(x.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),x._fromList=G},{"process-nextick-args":"Yj0v",isarray:"REa7",events:"FRpO","./internal/streams/stream":"ExO1","safe-buffer":"Nqn4","core-util-is":"Q14w",inherits:"Bm0n",util:"rDCW","./internal/streams/BufferList":"CQMM","./internal/streams/destroy":"lCUh","./_stream_duplex":"Jcj8","string_decoder/":"OUJr",process:"pBGv"}],wBib:[function(e,t,r){"use strict";t.exports=a;var n=e("./_stream_duplex"),i=Object.create(e("core-util-is"));function o(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(!n)return this.emit("error",new Error("write callback called multiple times"));r.writechunk=null,r.writecb=null,null!=t&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function a(e){if(!(this instanceof a))return new a(e);n.call(this,e),this._transformState={afterTransform:o.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",s)}function s(){var e=this;"function"==typeof this._flush?this._flush((function(t,r){u(e,t,r)})):u(this,null,null)}function u(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(e._transformState.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}i.inherits=e("inherits"),i.inherits(a,n),a.prototype.push=function(e,t){return this._transformState.needTransform=!1,n.prototype.push.call(this,e,t)},a.prototype._transform=function(e,t,r){throw new Error("_transform() is not implemented")},a.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},a.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},a.prototype._destroy=function(e,t){var r=this;n.prototype._destroy.call(this,e,(function(e){t(e),r.emit("close")}))}},{"./_stream_duplex":"Jcj8","core-util-is":"Q14w",inherits:"Bm0n"}],IxGW:[function(e,t,r){"use strict";t.exports=o;var n=e("./_stream_transform"),i=Object.create(e("core-util-is"));function o(e){if(!(this instanceof o))return new o(e);n.call(this,e)}i.inherits=e("inherits"),i.inherits(o,n),o.prototype._transform=function(e,t,r){r(null,e)}},{"./_stream_transform":"wBib","core-util-is":"Q14w",inherits:"Bm0n"}],JaqQ:[function(e,t,r){(r=t.exports=e("./lib/_stream_readable.js")).Stream=r,r.Readable=r,r.Writable=e("./lib/_stream_writable.js"),r.Duplex=e("./lib/_stream_duplex.js"),r.Transform=e("./lib/_stream_transform.js"),r.PassThrough=e("./lib/_stream_passthrough.js")},{"./lib/_stream_readable.js":"YrHu","./lib/_stream_writable.js":"jPmE","./lib/_stream_duplex.js":"Jcj8","./lib/_stream_transform.js":"wBib","./lib/_stream_passthrough.js":"IxGW"}],UxIR:[function(e,t,r){e("process"),e("buffer").Buffer;var n=e("process"),i=e("buffer").Buffer,o=arguments[3],a=e("./capability"),s=e("inherits"),u=e("readable-stream"),l=r.readyStates={UNSENT:0,OPENED:1,HEADERS_RECEIVED:2,LOADING:3,DONE:4},f=r.IncomingMessage=function(e,t,r,s){var l=this;if(u.Readable.call(l),l._mode=r,l.headers={},l.rawHeaders=[],l.trailers={},l.rawTrailers=[],l.on("end",(function(){n.nextTick((function(){l.emit("close")}))})),"fetch"===r){if(l._fetchResponse=t,l.url=t.url,l.statusCode=t.status,l.statusMessage=t.statusText,t.headers.forEach((function(e,t){l.headers[t.toLowerCase()]=e,l.rawHeaders.push(t,e)})),a.writableStream){var f=new WritableStream({write:function(e){return new Promise((function(t,r){l._destroyed?r():l.push(new i(e))?t():l._resumeFetch=t}))},close:function(){o.clearTimeout(s),l._destroyed||l.push(null)},abort:function(e){l._destroyed||l.emit("error",e)}});try{return void t.body.pipeTo(f).catch((function(e){o.clearTimeout(s),l._destroyed||l.emit("error",e)}))}catch(e){}}var c=t.body.getReader();!function e(){c.read().then((function(t){if(!l._destroyed){if(t.done)return o.clearTimeout(s),void l.push(null);l.push(new i(t.value)),e()}})).catch((function(e){o.clearTimeout(s),l._destroyed||l.emit("error",e)}))}()}else if(l._xhr=e,l._pos=0,l.url=e.responseURL,l.statusCode=e.status,l.statusMessage=e.statusText,e.getAllResponseHeaders().split(/\r?\n/).forEach((function(e){var t=e.match(/^([^:]+):\s*(.*)/);if(t){var r=t[1].toLowerCase();"set-cookie"===r?(void 0===l.headers[r]&&(l.headers[r]=[]),l.headers[r].push(t[2])):void 0!==l.headers[r]?l.headers[r]+=", "+t[2]:l.headers[r]=t[2],l.rawHeaders.push(t[1],t[2])}})),l._charset="x-user-defined",!a.overrideMimeType){var h=l.rawHeaders["mime-type"];if(h){var d=h.match(/;\s*charset=([^;])(;|$)/);d&&(l._charset=d[1].toLowerCase())}l._charset||(l._charset="utf-8")}};s(f,u.Readable),f.prototype._read=function(){var e=this._resumeFetch;e&&(this._resumeFetch=null,e())},f.prototype._onXHRProgress=function(){var e=this,t=e._xhr,r=null;switch(e._mode){case"text:vbarray":if(t.readyState!==l.DONE)break;try{r=new o.VBArray(t.responseBody).toArray()}catch(e){}if(null!==r){e.push(new i(r));break}case"text":try{r=t.responseText}catch(t){e._mode="text:vbarray";break}if(r.length>e._pos){var n=r.substr(e._pos);if("x-user-defined"===e._charset){for(var a=new i(n.length),s=0;s<n.length;s++)a[s]=255&n.charCodeAt(s);e.push(a)}else e.push(n,e._charset);e._pos=r.length}break;case"arraybuffer":if(t.readyState!==l.DONE||!t.response)break;r=t.response,e.push(new i(new Uint8Array(r)));break;case"moz-chunked-arraybuffer":if(r=t.response,t.readyState!==l.LOADING||!r)break;e.push(new i(new Uint8Array(r)));break;case"ms-stream":if(r=t.response,t.readyState!==l.LOADING)break;var u=new o.MSStreamReader;u.onprogress=function(){u.result.byteLength>e._pos&&(e.push(new i(new Uint8Array(u.result.slice(e._pos)))),e._pos=u.result.byteLength)},u.onload=function(){e.push(null)},u.readAsArrayBuffer(r)}e._xhr.readyState===l.DONE&&"ms-stream"!==e._mode&&e.push(null)}},{"./capability":"p5a1",inherits:"Bm0n","readable-stream":"JaqQ",process:"pBGv",buffer:"dskh"}],AH4k:[function(e,t,r){var n=e("buffer").Buffer;t.exports=function(e){if(e instanceof Uint8Array){if(0===e.byteOffset&&e.byteLength===e.buffer.byteLength)return e.buffer;if("function"==typeof e.buffer.slice)return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)}if(n.isBuffer(e)){for(var t=new Uint8Array(e.length),r=e.length,i=0;i<r;i++)t[i]=e[i];return t.buffer}throw new Error("Argument must be a Buffer")}},{buffer:"dskh"}],yL7F:[function(e,t,r){e("buffer").Buffer,e("process");var n=e("buffer").Buffer,i=arguments[3],o=e("process"),a=e("./capability"),s=e("inherits"),u=e("./response"),l=e("readable-stream"),f=e("to-arraybuffer"),c=u.IncomingMessage,h=u.readyStates;function d(e,t){return a.fetch&&t?"fetch":a.mozchunkedarraybuffer?"moz-chunked-arraybuffer":a.msstream?"ms-stream":a.arraybuffer&&e?"arraybuffer":a.vbArray&&e?"text:vbarray":"text"}var p=t.exports=function(e){var t,r=this;l.Writable.call(r),r._opts=e,r._body=[],r._headers={},e.auth&&r.setHeader("Authorization","Basic "+new n(e.auth).toString("base64")),Object.keys(e.headers).forEach((function(t){r.setHeader(t,e.headers[t])}));var i=!0;if("disable-fetch"===e.mode||"requestTimeout"in e&&!a.abortController)i=!1,t=!0;else if("prefer-streaming"===e.mode)t=!1;else if("allow-wrong-content-type"===e.mode)t=!a.overrideMimeType;else{if(e.mode&&"default"!==e.mode&&"prefer-fast"!==e.mode)throw new Error("Invalid value for opts.mode");t=!0}r._mode=d(t,i),r._fetchTimer=null,r.on("finish",(function(){r._onFinish()}))};function b(e){try{var t=e.status;return null!==t&&0!==t}catch(e){return!1}}s(p,l.Writable),p.prototype.setHeader=function(e,t){var r=e.toLowerCase();-1===y.indexOf(r)&&(this._headers[r]={name:e,value:t})},p.prototype.getHeader=function(e){var t=this._headers[e.toLowerCase()];return t?t.value:null},p.prototype.removeHeader=function(e){delete this._headers[e.toLowerCase()]},p.prototype._onFinish=function(){var e=this;if(!e._destroyed){var t=e._opts,r=e._headers,s=null;"GET"!==t.method&&"HEAD"!==t.method&&(s=a.arraybuffer?f(n.concat(e._body)):a.blobConstructor?new i.Blob(e._body.map((function(e){return f(e)})),{type:(r["content-type"]||{}).value||""}):n.concat(e._body).toString());var u=[];if(Object.keys(r).forEach((function(e){var t=r[e].name,n=r[e].value;Array.isArray(n)?n.forEach((function(e){u.push([t,e])})):u.push([t,n])})),"fetch"===e._mode){var l=null;if(a.abortController){var c=new AbortController;l=c.signal,e._fetchAbortController=c,"requestTimeout"in t&&0!==t.requestTimeout&&(e._fetchTimer=i.setTimeout((function(){e.emit("requestTimeout"),e._fetchAbortController&&e._fetchAbortController.abort()}),t.requestTimeout))}i.fetch(e._opts.url,{method:e._opts.method,headers:u,body:s||void 0,mode:"cors",credentials:t.withCredentials?"include":"same-origin",signal:l}).then((function(t){e._fetchResponse=t,e._connect()}),(function(t){i.clearTimeout(e._fetchTimer),e._destroyed||e.emit("error",t)}))}else{var d=e._xhr=new i.XMLHttpRequest;try{d.open(e._opts.method,e._opts.url,!0)}catch(t){return void o.nextTick((function(){e.emit("error",t)}))}"responseType"in d&&(d.responseType=e._mode.split(":")[0]),"withCredentials"in d&&(d.withCredentials=!!t.withCredentials),"text"===e._mode&&"overrideMimeType"in d&&d.overrideMimeType("text/plain; charset=x-user-defined"),"requestTimeout"in t&&(d.timeout=t.requestTimeout,d.ontimeout=function(){e.emit("requestTimeout")}),u.forEach((function(e){d.setRequestHeader(e[0],e[1])})),e._response=null,d.onreadystatechange=function(){switch(d.readyState){case h.LOADING:case h.DONE:e._onXHRProgress()}},"moz-chunked-arraybuffer"===e._mode&&(d.onprogress=function(){e._onXHRProgress()}),d.onerror=function(){e._destroyed||e.emit("error",new Error("XHR error"))};try{d.send(s)}catch(t){return void o.nextTick((function(){e.emit("error",t)}))}}}},p.prototype._onXHRProgress=function(){b(this._xhr)&&!this._destroyed&&(this._response||this._connect(),this._response._onXHRProgress())},p.prototype._connect=function(){var e=this;e._destroyed||(e._response=new c(e._xhr,e._fetchResponse,e._mode,e._fetchTimer),e._response.on("error",(function(t){e.emit("error",t)})),e.emit("response",e._response))},p.prototype._write=function(e,t,r){this._body.push(e),r()},p.prototype.abort=p.prototype.destroy=function(){this._destroyed=!0,i.clearTimeout(this._fetchTimer),this._response&&(this._response._destroyed=!0),this._xhr?this._xhr.abort():this._fetchAbortController&&this._fetchAbortController.abort()},p.prototype.end=function(e,t,r){"function"==typeof e&&(r=e,e=void 0),l.Writable.prototype.end.call(this,e,t,r)},p.prototype.flushHeaders=function(){},p.prototype.setTimeout=function(){},p.prototype.setNoDelay=function(){},p.prototype.setSocketKeepAlive=function(){};var y=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","date","dnt","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"]},{"./capability":"p5a1",inherits:"Bm0n","./response":"UxIR","readable-stream":"JaqQ","to-arraybuffer":"AH4k",buffer:"dskh",process:"pBGv"}],K5Tb:[function(e,t,r){t.exports=function(){for(var e={},t=0;t<arguments.length;t++){var r=arguments[t];for(var i in r)n.call(r,i)&&(e[i]=r[i])}return e};var n=Object.prototype.hasOwnProperty},{}],OpTI:[function(e,t,r){t.exports={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Unordered Collection",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",509:"Bandwidth Limit Exceeded",510:"Not Extended",511:"Network Authentication Required"}},{}],oWqx:[function(e,t,r){var n,i=arguments[3];!function(e){var o="object"==typeof r&&r&&!r.nodeType&&r,a="object"==typeof t&&t&&!t.nodeType&&t,s="object"==typeof i&&i;s.global!==s&&s.window!==s&&s.self!==s||(e=s);var u,l,f=2147483647,c=36,h=/^xn--/,d=/[^\x20-\x7E]/,p=/[\x2E\u3002\uFF0E\uFF61]/g,b={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},y=Math.floor,v=String.fromCharCode;function g(e){throw new RangeError(b[e])}function m(e,t){for(var r=e.length,n=[];r--;)n[r]=t(e[r]);return n}function w(e,t){var r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+m((e=e.replace(p,".")).split("."),t).join(".")}function _(e){for(var t,r,n=[],i=0,o=e.length;i<o;)(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<o?56320==(64512&(r=e.charCodeAt(i++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),i--):n.push(t);return n}function k(e){return m(e,(function(e){var t="";return e>65535&&(t+=v((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+v(e)})).join("")}function x(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function S(e,t,r){var n=0;for(e=r?y(e/700):e>>1,e+=y(e/t);e>455;n+=c)e=y(e/35);return y(n+36*e/(e+38))}function O(e){var t,r,n,i,o,a,s,u,l,h,d,p=[],b=e.length,v=0,m=128,w=72;for((r=e.lastIndexOf("-"))<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&g("not-basic"),p.push(e.charCodeAt(n));for(i=r>0?r+1:0;i<b;){for(o=v,a=1,s=c;i>=b&&g("invalid-input"),((u=(d=e.charCodeAt(i++))-48<10?d-22:d-65<26?d-65:d-97<26?d-97:c)>=c||u>y((f-v)/a))&&g("overflow"),v+=u*a,!(u<(l=s<=w?1:s>=w+26?26:s-w));s+=c)a>y(f/(h=c-l))&&g("overflow"),a*=h;w=S(v-o,t=p.length+1,0==o),y(v/t)>f-m&&g("overflow"),m+=y(v/t),v%=t,p.splice(v++,0,m)}return k(p)}function T(e){var t,r,n,i,o,a,s,u,l,h,d,p,b,m,w,k=[];for(p=(e=_(e)).length,t=128,r=0,o=72,a=0;a<p;++a)(d=e[a])<128&&k.push(v(d));for(n=i=k.length,i&&k.push("-");n<p;){for(s=f,a=0;a<p;++a)(d=e[a])>=t&&d<s&&(s=d);for(s-t>y((f-r)/(b=n+1))&&g("overflow"),r+=(s-t)*b,t=s,a=0;a<p;++a)if((d=e[a])<t&&++r>f&&g("overflow"),d==t){for(u=r,l=c;!(u<(h=l<=o?1:l>=o+26?26:l-o));l+=c)w=u-h,m=c-h,k.push(v(x(h+w%m,0))),u=y(w/m);k.push(v(x(u,0))),o=S(r,b,n==i),r=0,++n}++r,++t}return k.join("")}if(u={version:"1.4.1",ucs2:{decode:_,encode:k},decode:O,encode:T,toASCII:function(e){return w(e,(function(e){return d.test(e)?"xn--"+T(e):e}))},toUnicode:function(e){return w(e,(function(e){return h.test(e)?O(e.slice(4).toLowerCase()):e}))}},"function"==typeof n&&"object"==typeof n.amd&&n.amd)n("punycode",(function(){return u}));else if(o&&a)if(t.exports==o)a.exports=u;else for(l in u)u.hasOwnProperty(l)&&(o[l]=u[l]);else e.punycode=u}(this)},{}],YsIc:[function(e,t,r){"use strict";t.exports={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"==typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},{}],J6GP:[function(e,t,r){"use strict";function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.exports=function(e,t,r,o){t=t||"&",r=r||"=";var a={};if("string"!=typeof e||0===e.length)return a;var s=/\+/g;e=e.split(t);var u=1e3;o&&"number"==typeof o.maxKeys&&(u=o.maxKeys);var l=e.length;u>0&&l>u&&(l=u);for(var f=0;f<l;++f){var c,h,d,p,b=e[f].replace(s,"%20"),y=b.indexOf(r);y>=0?(c=b.substr(0,y),h=b.substr(y+1)):(c=b,h=""),d=decodeURIComponent(c),p=decodeURIComponent(h),n(a,d)?i(a[d])?a[d].push(p):a[d]=[a[d],p]:a[d]=p}return a};var i=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},{}],bvhO:[function(e,t,r){"use strict";var n=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};t.exports=function(e,t,r,s){return t=t||"&",r=r||"=",null===e&&(e=void 0),"object"==typeof e?o(a(e),(function(a){var s=encodeURIComponent(n(a))+r;return i(e[a])?o(e[a],(function(e){return s+encodeURIComponent(n(e))})).join(t):s+encodeURIComponent(n(e[a]))})).join(t):s?encodeURIComponent(n(s))+r+encodeURIComponent(n(e)):""};var i=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function o(e,t){if(e.map)return e.map(t);for(var r=[],n=0;n<e.length;n++)r.push(t(e[n],n));return r}var a=Object.keys||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t}},{}],fk5h:[function(e,t,r){"use strict";r.decode=r.parse=e("./decode"),r.encode=r.stringify=e("./encode")},{"./decode":"J6GP","./encode":"bvhO"}],Mej7:[function(e,t,r){"use strict";var n=e("punycode"),i=e("./util");function o(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}r.parse=m,r.resolve=function(e,t){return m(e,!1,!0).resolve(t)},r.resolveObject=function(e,t){return e?m(e,!1,!0).resolveObject(t):t},r.format=function(e){return i.isString(e)&&(e=m(e)),e instanceof o?e.format():o.prototype.format.call(e)},r.Url=o;var a=/^([a-z0-9.+-]+:)/i,s=/:[0-9]*$/,u=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,l=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),f=["'"].concat(l),c=["%","/","?",";","#"].concat(f),h=["/","?","#"],d=/^[+a-z0-9A-Z_-]{0,63}$/,p=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,b={javascript:!0,"javascript:":!0},y={javascript:!0,"javascript:":!0},v={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},g=e("querystring");function m(e,t,r){if(e&&i.isObject(e)&&e instanceof o)return e;var n=new o;return n.parse(e,t,r),n}o.prototype.parse=function(e,t,r){if(!i.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var o=e.indexOf("?"),s=-1!==o&&o<e.indexOf("#")?"?":"#",l=e.split(s);l[0]=l[0].replace(/\\/g,"/");var m=e=l.join(s);if(m=m.trim(),!r&&1===e.split("#").length){var w=u.exec(m);if(w)return this.path=m,this.href=m,this.pathname=w[1],w[2]?(this.search=w[2],this.query=t?g.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var _=a.exec(m);if(_){var k=(_=_[0]).toLowerCase();this.protocol=k,m=m.substr(_.length)}if(r||_||m.match(/^\/\/[^@\/]+@[^@\/]+/)){var x="//"===m.substr(0,2);!x||_&&y[_]||(m=m.substr(2),this.slashes=!0)}if(!y[_]&&(x||_&&!v[_])){for(var S,O,T=-1,E=0;E<h.length;E++)-1!==(C=m.indexOf(h[E]))&&(-1===T||C<T)&&(T=C);for(-1!==(O=-1===T?m.lastIndexOf("@"):m.lastIndexOf("@",T))&&(S=m.slice(0,O),m=m.slice(O+1),this.auth=decodeURIComponent(S)),T=-1,E=0;E<c.length;E++){var C;-1!==(C=m.indexOf(c[E]))&&(-1===T||C<T)&&(T=C)}-1===T&&(T=m.length),this.host=m.slice(0,T),m=m.slice(T),this.parseHost(),this.hostname=this.hostname||"";var P="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!P)for(var j=this.hostname.split(/\./),R=(E=0,j.length);E<R;E++){var M=j[E];if(M&&!M.match(d)){for(var A="",I=0,D=M.length;I<D;I++)M.charCodeAt(I)>127?A+="x":A+=M[I];if(!A.match(d)){var L=j.slice(0,E),N=j.slice(E+1),F=M.match(p);F&&(L.push(F[1]),N.unshift(F[2])),N.length&&(m="/"+N.join(".")+m),this.hostname=L.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),P||(this.hostname=n.toASCII(this.hostname));var U=this.port?":"+this.port:"",B=this.hostname||"";this.host=B+U,this.href+=this.host,P&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==m[0]&&(m="/"+m))}if(!b[k])for(E=0,R=f.length;E<R;E++){var G=f[E];if(-1!==m.indexOf(G)){var z=encodeURIComponent(G);z===G&&(z=escape(G)),m=m.split(G).join(z)}}var H=m.indexOf("#");-1!==H&&(this.hash=m.substr(H),m=m.slice(0,H));var W=m.indexOf("?");if(-1!==W?(this.search=m.substr(W),this.query=m.substr(W+1),t&&(this.query=g.parse(this.query)),m=m.slice(0,W)):t&&(this.search="",this.query={}),m&&(this.pathname=m),v[k]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){U=this.pathname||"";var q=this.search||"";this.path=U+q}return this.href=this.format(),this},o.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",r=this.pathname||"",n=this.hash||"",o=!1,a="";this.host?o=e+this.host:this.hostname&&(o=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(o+=":"+this.port)),this.query&&i.isObject(this.query)&&Object.keys(this.query).length&&(a=g.stringify(this.query));var s=this.search||a&&"?"+a||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||v[t])&&!1!==o?(o="//"+(o||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):o||(o=""),n&&"#"!==n.charAt(0)&&(n="#"+n),s&&"?"!==s.charAt(0)&&(s="?"+s),t+o+(r=r.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})))+(s=s.replace("#","%23"))+n},o.prototype.resolve=function(e){return this.resolveObject(m(e,!1,!0)).format()},o.prototype.resolveObject=function(e){if(i.isString(e)){var t=new o;t.parse(e,!1,!0),e=t}for(var r=new o,n=Object.keys(this),a=0;a<n.length;a++){var s=n[a];r[s]=this[s]}if(r.hash=e.hash,""===e.href)return r.href=r.format(),r;if(e.slashes&&!e.protocol){for(var u=Object.keys(e),l=0;l<u.length;l++){var f=u[l];"protocol"!==f&&(r[f]=e[f])}return v[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(e.protocol&&e.protocol!==r.protocol){if(!v[e.protocol]){for(var c=Object.keys(e),h=0;h<c.length;h++){var d=c[h];r[d]=e[d]}return r.href=r.format(),r}if(r.protocol=e.protocol,e.host||y[e.protocol])r.pathname=e.pathname;else{for(var p=(e.pathname||"").split("/");p.length&&!(e.host=p.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==p[0]&&p.unshift(""),p.length<2&&p.unshift(""),r.pathname=p.join("/")}if(r.search=e.search,r.query=e.query,r.host=e.host||"",r.auth=e.auth,r.hostname=e.hostname||e.host,r.port=e.port,r.pathname||r.search){var b=r.pathname||"",g=r.search||"";r.path=b+g}return r.slashes=r.slashes||e.slashes,r.href=r.format(),r}var m=r.pathname&&"/"===r.pathname.charAt(0),w=e.host||e.pathname&&"/"===e.pathname.charAt(0),_=w||m||r.host&&e.pathname,k=_,x=r.pathname&&r.pathname.split("/")||[],S=(p=e.pathname&&e.pathname.split("/")||[],r.protocol&&!v[r.protocol]);if(S&&(r.hostname="",r.port=null,r.host&&(""===x[0]?x[0]=r.host:x.unshift(r.host)),r.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===p[0]?p[0]=e.host:p.unshift(e.host)),e.host=null),_=_&&(""===p[0]||""===x[0])),w)r.host=e.host||""===e.host?e.host:r.host,r.hostname=e.hostname||""===e.hostname?e.hostname:r.hostname,r.search=e.search,r.query=e.query,x=p;else if(p.length)x||(x=[]),x.pop(),x=x.concat(p),r.search=e.search,r.query=e.query;else if(!i.isNullOrUndefined(e.search))return S&&(r.hostname=r.host=x.shift(),(P=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=P.shift(),r.host=r.hostname=P.shift())),r.search=e.search,r.query=e.query,i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r;if(!x.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var O=x.slice(-1)[0],T=(r.host||e.host||x.length>1)&&("."===O||".."===O)||""===O,E=0,C=x.length;C>=0;C--)"."===(O=x[C])?x.splice(C,1):".."===O?(x.splice(C,1),E++):E&&(x.splice(C,1),E--);if(!_&&!k)for(;E--;E)x.unshift("..");!_||""===x[0]||x[0]&&"/"===x[0].charAt(0)||x.unshift(""),T&&"/"!==x.join("/").substr(-1)&&x.push("");var P,j=""===x[0]||x[0]&&"/"===x[0].charAt(0);return S&&(r.hostname=r.host=j?"":x.length?x.shift():"",(P=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=P.shift(),r.host=r.hostname=P.shift())),(_=_||r.host&&x.length)&&!j&&x.unshift(""),x.length?r.pathname=x.join("/"):(r.pathname=null,r.path=null),i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},o.prototype.parseHost=function(){var e=this.host,t=s.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},{punycode:"oWqx","./util":"YsIc",querystring:"fk5h"}],KKrj:[function(e,t,r){var n=arguments[3],i=e("./lib/request"),o=e("./lib/response"),a=e("xtend"),s=e("builtin-status-codes"),u=e("url"),l=r;l.request=function(e,t){e="string"==typeof e?u.parse(e):a(e);var r=-1===n.location.protocol.search(/^https?:$/)?"http:":"",o=e.protocol||r,s=e.hostname||e.host,l=e.port,f=e.path||"/";s&&-1!==s.indexOf(":")&&(s="["+s+"]"),e.url=(s?o+"//"+s:"")+(l?":"+l:"")+f,e.method=(e.method||"GET").toUpperCase(),e.headers=e.headers||{};var c=new i(e);return t&&c.on("response",t),c},l.get=function(e,t){var r=l.request(e,t);return r.end(),r},l.ClientRequest=i,l.IncomingMessage=o.IncomingMessage,l.Agent=function(){},l.Agent.defaultMaxSockets=4,l.globalAgent=new l.Agent,l.STATUS_CODES=s,l.METHODS=["CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REPORT","SEARCH","SUBSCRIBE","TRACE","UNLOCK","UNSUBSCRIBE"]},{"./lib/request":"yL7F","./lib/response":"UxIR",xtend:"K5Tb","builtin-status-codes":"OpTI",url:"Mej7"}],wVMl:[function(e,t,r){var n=e("http"),i=e("url"),o=t.exports;for(var a in n)n.hasOwnProperty(a)&&(o[a]=n[a]);function s(e){if("string"==typeof e&&(e=i.parse(e)),e.protocol||(e.protocol="https:"),"https:"!==e.protocol)throw new Error('Protocol "'+e.protocol+'" not supported. Expected "https:"');return e}o.request=function(e,t){return e=s(e),n.request.call(this,e,t)},o.get=function(e,t){return e=s(e),n.get.call(this,e,t)}},{http:"KKrj",url:"Mej7"}],SHlS:[function(e,t,r){e("buffer").Buffer;var n=e("buffer").Buffer;Object.defineProperty(r,"__esModule",{value:!0}),r.HttpClient=void 0;var i=v(e("@babel/runtime/helpers/defineProperty")),o=v(e("@babel/runtime/regenerator")),a=v(e("@babel/runtime/helpers/asyncToGenerator")),s=v(e("@babel/runtime/helpers/classCallCheck")),u=v(e("@babel/runtime/helpers/createClass")),l=v(e("@babel/runtime/helpers/inherits")),f=v(e("@babel/runtime/helpers/possibleConstructorReturn")),c=v(e("@babel/runtime/helpers/getPrototypeOf")),h=v(e("http")),d=v(e("https")),p=v(e("url")),b=e("./base"),y=e("../../utils");function v(e){return e&&e.__esModule?e:{default:e}}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach((function(t){(0,i.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function w(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,c.default)(e);if(t){var i=(0,c.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,f.default)(this,r)}}var _=function(e){(0,l.default)(r,e);var t=w(r);function r(e,n){var i;return(0,s.default)(this,r),(i=t.call(this)).response=e,i.dataPromise=n,i}return(0,u.default)(r,[{key:"getHeader",value:function(e){return this.response.headers[e]}},{key:"getData",value:function(){var e=(0,a.default)(o.default.mark((function e(){var t;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.dataPromise;case 2:return t=e.sent,e.abrupt("return",t);case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"status",get:function(){return this.response.statusCode}}]),r}(b.BaseResponse),k=function(e){(0,l.default)(r,e);var t=w(r);function r(e){var n;return(0,s.default)(this,r),(n=t.call(this,e)).parsedUrl=p.default.parse(n.url),n.httpApi="http:"===n.parsedUrl.protocol?h.default:d.default,n}return(0,u.default)(r,[{key:"constructRequest",value:function(e,t){var r=this;return new Promise((function(i,o){var a=r.httpApi.get(m(m({},r.parsedUrl),{},{headers:e}),(function(e){var t=new Promise((function(t){var r=[];e.on("data",(function(e){r.push(e)})),e.on("end",(function(){var e=n.concat(r).buffer;t(e)})),e.on("error",o)}));i(new _(e,t))}));a.on("error",o),t&&(t.aborted&&a.destroy(new y.AbortError("Request aborted")),t.addEventListener("abort",(function(){return a.destroy(new y.AbortError("Request aborted"))})))}))}},{key:"request",value:function(){var e=(0,a.default)(o.default.mark((function e(){var t,r,n,i,a=arguments;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=a.length>0&&void 0!==a[0]?a[0]:{},r=t.headers,n=t.signal,e.next=3,this.constructRequest(r,n);case 3:return i=e.sent,e.abrupt("return",i);case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),r}(b.BaseClient);r.HttpClient=k},{"@babel/runtime/helpers/defineProperty":"IxO8","@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0",http:"KKrj",https:"wVMl",url:"Mej7","./base":"uL1U","../../utils":"FOZT",buffer:"dskh"}],itmA:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.makeFetchSource=S,r.makeXHRSource=O,r.makeHttpSource=T,r.makeRemoteSource=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.forceXHR,i=void 0!==r&&r,o=(0,n.default)(t,["forceXHR"]);return"function"!=typeof fetch||i?"undefined"!=typeof XMLHttpRequest?O(e,o):T(e,o):S(e,o)};var n=g(e("@babel/runtime/helpers/objectWithoutProperties")),i=g(e("@babel/runtime/helpers/defineProperty")),o=g(e("@babel/runtime/regenerator")),a=g(e("@babel/runtime/helpers/asyncToGenerator")),s=g(e("@babel/runtime/helpers/classCallCheck")),u=g(e("@babel/runtime/helpers/createClass")),l=g(e("@babel/runtime/helpers/inherits")),f=g(e("@babel/runtime/helpers/possibleConstructorReturn")),c=g(e("@babel/runtime/helpers/getPrototypeOf")),h=e("./httputils"),d=e("./basesource"),p=e("./blockedsource"),b=e("./client/fetch"),y=e("./client/xhr"),v=e("./client/http");function g(e){return e&&e.__esModule?e:{default:e}}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach((function(t){(0,i.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,c.default)(e);if(t){var i=(0,c.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,f.default)(this,r)}}var k=function(e){(0,l.default)(r,e);var t=_(r);function r(e,n,i,o){var a;return(0,s.default)(this,r),(a=t.call(this)).client=e,a.headers=n,a.maxRanges=i,a.allowFullFile=o,a._fileSize=null,a}return(0,u.default)(r,[{key:"fetch",value:function(){var e=(0,a.default)(o.default.mark((function e(t,r){var n=this;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(this.maxRanges>=t.length)){e.next=4;break}return e.abrupt("return",this.fetchSlices(t,r));case 4:this.maxRanges>0&&t.length;case 5:return e.next=7,Promise.all(t.map((function(e){return n.fetchSlice(e,r)})));case 7:return e.abrupt("return",e.sent);case 8:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"fetchSlices",value:function(){var e=(0,a.default)(o.default.mark((function e(t,r){var n,i,a,s,u,l,f,c,d,p,b,y,v,g=this;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.client.request({headers:w(w({},this.headers),{},{Range:"bytes=".concat(t.map((function(e){var t=e.offset,r=e.length;return"".concat(t,"-").concat(t+r)})).join(","))}),signal:r});case 2:if((n=e.sent).ok){e.next=7;break}throw new Error("Error fetching data.");case 7:if(206!==n.status){e.next=32;break}if(i=(0,h.parseContentType)(n.getHeader("content-type")),a=i.type,s=i.params,"multipart/byteranges"!==a){e.next=18;break}return e.t0=h.parseByteRanges,e.next=13,n.getData();case 13:return e.t1=e.sent,e.t2=s.boundary,u=(0,e.t0)(e.t1,e.t2),this._fileSize=u[0].fileSize||null,e.abrupt("return",u);case 18:return e.next=20,n.getData();case 20:if(l=e.sent,f=(0,h.parseContentRange)(n.getHeader("content-range")),c=f.start,d=f.end,p=f.total,this._fileSize=p||null,b=[{data:l,offset:c,length:d-c}],!(t.length>1)){e.next=29;break}return e.next=27,Promise.all(t.slice(1).map((function(e){return g.fetchSlice(e,r)})));case 27:return y=e.sent,e.abrupt("return",b.concat(y));case 29:return e.abrupt("return",b);case 32:if(this.allowFullFile){e.next=34;break}throw new Error("Server responded with full file");case 34:return e.next=36,n.getData();case 36:return v=e.sent,this._fileSize=v.byteLength,e.abrupt("return",[{data:v,offset:0,length:v.byteLength}]);case 39:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"fetchSlice",value:function(){var e=(0,a.default)(o.default.mark((function e(t,r){var n,i,a,s,u,l,f;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.offset,i=t.length,e.next=3,this.client.request({headers:w(w({},this.headers),{},{Range:"bytes=".concat(n,"-").concat(n+i)}),signal:r});case 3:if((a=e.sent).ok){e.next=8;break}throw new Error("Error fetching data.");case 8:if(206!==a.status){e.next=17;break}return e.next=11,a.getData();case 11:return s=e.sent,u=(0,h.parseContentRange)(a.getHeader("content-range")),l=u.total,this._fileSize=l||null,e.abrupt("return",{data:s,offset:n,length:i});case 17:if(this.allowFullFile){e.next=19;break}throw new Error("Server responded with full file");case 19:return e.next=21,a.getData();case 21:return f=e.sent,this._fileSize=f.byteLength,e.abrupt("return",{data:f,offset:0,length:f.byteLength});case 24:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"fileSize",get:function(){return this._fileSize}}]),r}(d.BaseSource);function x(e,t){var r=t.blockSize,n=t.cacheSize;return null===r?e:new p.BlockedSource(e,r,n)}function S(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.headers,i=void 0===r?{}:r,o=t.credentials,a=t.maxRanges,s=void 0===a?0:a,u=t.allowFullFile,l=void 0!==u&&u,f=(0,n.default)(t,["headers","credentials","maxRanges","allowFullFile"]),c=new b.FetchClient(e,o);return x(new k(c,i,s,l),f)}function O(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.headers,i=void 0===r?{}:r,o=t.maxRanges,a=void 0===o?0:o,s=t.allowFullFile,u=void 0!==s&&s,l=(0,n.default)(t,["headers","maxRanges","allowFullFile"]),f=new y.XHRClient(e);return x(new k(f,i,a,u),l)}function T(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.headers,i=void 0===r?{}:r,o=t.maxRanges,a=void 0===o?0:o,s=t.allowFullFile,u=void 0!==s&&s,l=(0,n.default)(t,["headers","maxRanges","allowFullFile"]),f=new v.HttpClient(e);return x(new k(f,i,a,u),l)}},{"@babel/runtime/helpers/objectWithoutProperties":"U8F3","@babel/runtime/helpers/defineProperty":"IxO8","@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","./httputils":"ZJpj","./basesource":"LzCD","./blockedsource":"xD7g","./client/fetch":"Un5s","./client/xhr":"hvgd","./client/http":"SHlS"}],wM9h:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.makeBufferSource=function(e){return new h(e)};var n=f(e("@babel/runtime/helpers/classCallCheck")),i=f(e("@babel/runtime/helpers/createClass")),o=f(e("@babel/runtime/helpers/inherits")),a=f(e("@babel/runtime/helpers/possibleConstructorReturn")),s=f(e("@babel/runtime/helpers/getPrototypeOf")),u=e("./basesource"),l=e("../utils");function f(e){return e&&e.__esModule?e:{default:e}}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,s.default)(e);if(t){var i=(0,s.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,a.default)(this,r)}}var h=function(e){(0,o.default)(r,e);var t=c(r);function r(e){var i;return(0,n.default)(this,r),(i=t.call(this)).arrayBuffer=e,i}return(0,i.default)(r,[{key:"fetchSlice",value:function(e,t){if(t&&t.aborted)throw new l.AbortError("Request aborted");return this.arrayBuffer.slice(e.offset,e.offset+e.length)}}]),r}(u.BaseSource)},{"@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","./basesource":"LzCD","../utils":"FOZT"}],GiEG:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.makeFileReaderSource=function(e){return new h(e)};var n=f(e("@babel/runtime/regenerator")),i=f(e("@babel/runtime/helpers/asyncToGenerator")),o=f(e("@babel/runtime/helpers/classCallCheck")),a=f(e("@babel/runtime/helpers/createClass")),s=f(e("@babel/runtime/helpers/inherits")),u=f(e("@babel/runtime/helpers/possibleConstructorReturn")),l=f(e("@babel/runtime/helpers/getPrototypeOf"));function f(e){return e&&e.__esModule?e:{default:e}}function c(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,l.default)(e);if(t){var i=(0,l.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,u.default)(this,r)}}var h=function(e){(0,s.default)(r,e);var t=c(r);function r(e){var n;return(0,o.default)(this,r),(n=t.call(this)).file=e,n}return(0,a.default)(r,[{key:"fetchSlice",value:function(){var e=(0,i.default)(n.default.mark((function e(t,r){var i=this;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise((function(e,n){var o=i.file.slice(t.offset,t.offset+t.length),a=new FileReader;a.onload=function(t){return e(t.target.result)},a.onerror=n,a.onabort=n,a.readAsArrayBuffer(o),r&&r.addEventListener("abort",(function(){return a.abort()}))})));case 1:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}()}]),r}(e("./basesource").BaseSource)},{"@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","./basesource":"LzCD"}],G7Cz:[function(e,t,r){e("buffer").Buffer;var n=e("buffer").Buffer;Object.defineProperty(r,"__esModule",{value:!0}),r.makeFileSource=function(e){return new v(e)};var i=h(e("@babel/runtime/regenerator")),o=h(e("@babel/runtime/helpers/asyncToGenerator")),a=h(e("@babel/runtime/helpers/classCallCheck")),s=h(e("@babel/runtime/helpers/createClass")),u=h(e("@babel/runtime/helpers/inherits")),l=h(e("@babel/runtime/helpers/possibleConstructorReturn")),f=h(e("@babel/runtime/helpers/getPrototypeOf")),c=e("fs");function h(e){return e&&e.__esModule?e:{default:e}}function d(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,f.default)(e);if(t){var i=(0,f.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,l.default)(this,r)}}function p(e){return new Promise((function(t,r){(0,c.close)(e,(function(e){e?r(e):t()}))}))}function b(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return new Promise((function(n,i){(0,c.open)(e,t,r,(function(e,t){e?i(e):n(t)}))}))}function y(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return new Promise((function(e,r){c.read.apply(void 0,t.concat([function(t,n,i){t?r(t):e({bytesRead:n,buffer:i})}]))}))}var v=function(e){(0,u.default)(r,e);var t=d(r);function r(e){var n;return(0,a.default)(this,r),(n=t.call(this)).path=e,n.openRequest=b(e,"r"),n}return(0,s.default)(r,[{key:"fetchSlice",value:function(){var e=(0,o.default)(i.default.mark((function e(t){var r,o,a;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.openRequest;case 2:return r=e.sent,e.next=5,y(r,n.alloc(t.length),0,t.length,t.offset);case 5:return o=e.sent,a=o.buffer,e.abrupt("return",a.buffer);case 8:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"close",value:function(){var e=(0,o.default)(i.default.mark((function e(){var t;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.openRequest;case 2:return t=e.sent,e.next=5,p(t);case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),r}(e("./basesource").BaseSource)},{"@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0",fs:"rDCW","./basesource":"LzCD",buffer:"dskh"}],BGyE:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.writeGeotiff=function(e,t){var r,o,a,s;"number"==typeof e[0]?(r=t.height||t.ImageLength,a=t.width||t.ImageWidth,o=e.length/(r*a),s=e):(o=e.length,r=e[0].length,a=e[0][0].length,s=[],(0,i.times)(r,(function(t){(0,i.times)(a,(function(r){(0,i.times)(o,(function(n){s.push(e[n][t][r])}))}))}))),t.ImageLength=r,delete t.height,t.ImageWidth=a,delete t.width,t.BitsPerSample||(t.BitsPerSample=(0,i.times)(o,(function(){return 8}))),v.forEach((function(e){var r=e[0];if(!t[r]){var n=e[1];t[r]=n}})),t.PhotometricInterpretation||(t.PhotometricInterpretation=3===t.BitsPerSample.length?2:1),t.SamplesPerPixel||(t.SamplesPerPixel=[o]),t.StripByteCounts||(t.StripByteCounts=[o*r*a]),t.ModelPixelScale||(t.ModelPixelScale=[360/a,180/r,0]),t.SampleFormat||(t.SampleFormat=(0,i.times)(o,(function(){return 1})));var u=Object.keys(t).filter((function(e){return(0,i.endsWith)(e,"GeoKey")})).sort((function(e,t){return f[e]-f[t]}));if(!t.GeoKeyDirectory){var l=[1,1,0,u.length];u.forEach((function(e){var r,i,o,a=Number(f[e]);l.push(a),"SHORT"===n.fieldTagTypes[a]?(r=1,i=0,o=t[e]):"GeogCitationGeoKey"===e?(r=t.GeoAsciiParams.length,i=Number(f.GeoAsciiParams),o=0):console.log("[geotiff.js] couldn't get TIFFTagLocation for ".concat(e)),l.push(i),l.push(r),l.push(o)})),t.GeoKeyDirectory=l}for(var c in u)u.hasOwnProperty(c)&&delete t[c];["Compression","ExtraSamples","GeographicTypeGeoKey","GTModelTypeGeoKey","GTRasterTypeGeoKey","ImageLength","ImageWidth","PhotometricInterpretation","PlanarConfiguration","ResolutionUnit","SamplesPerPixel","XPosition","YPosition"].forEach((function(e){t[e]&&(t[e]=function(e){return Array.isArray(e)?e:[e]}(t[e]))}));var h=function(e){var t={};for(var r in e)"StripOffsets"!==r&&(f[r]||console.error(r,"not in name2code:",Object.keys(f)),t[f[r]]=e[r]);return t}(t);return y(s,a,r,h)};var n=e("./globals"),i=e("./utils");function o(e,t){var r;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=a(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return s=e.done,e},e:function(e){u=!0,o=e},f:function(){try{s||null==r.return||r.return()}finally{if(u)throw o}}}}function a(e,t){if(e){if("string"==typeof e)return s(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var u=(0,i.invert)(n.fieldTagNames),l=(0,i.invert)(n.geoKeyNames),f={};(0,i.assign)(f,u),(0,i.assign)(f,l);var c=(0,i.invert)(n.fieldTypeNames),h=1e3,d={nextZero:function(e,t){for(var r=t;0!==e[r];)r++;return r},readUshort:function(e,t){return e[t]<<8|e[t+1]},readShort:function(e,t){var r=d.ui8;return r[0]=e[t+1],r[1]=e[t+0],d.i16[0]},readInt:function(e,t){var r=d.ui8;return r[0]=e[t+3],r[1]=e[t+2],r[2]=e[t+1],r[3]=e[t+0],d.i32[0]},readUint:function(e,t){var r=d.ui8;return r[0]=e[t+3],r[1]=e[t+2],r[2]=e[t+1],r[3]=e[t+0],d.ui32[0]},readASCII:function(e,t,r){return r.map((function(r){return String.fromCharCode(e[t+r])})).join("")},readFloat:function(e,t){var r=d.ui8;return(0,i.times)(4,(function(n){r[n]=e[t+3-n]})),d.fl32[0]},readDouble:function(e,t){var r=d.ui8;return(0,i.times)(8,(function(n){r[n]=e[t+7-n]})),d.fl64[0]},writeUshort:function(e,t,r){e[t]=r>>8&255,e[t+1]=255&r},writeUint:function(e,t,r){e[t]=r>>24&255,e[t+1]=r>>16&255,e[t+2]=r>>8&255,e[t+3]=r>>0&255},writeASCII:function(e,t,r){(0,i.times)(r.length,(function(n){e[t+n]=r.charCodeAt(n)}))},ui8:new Uint8Array(8)};d.fl64=new Float64Array(d.ui8.buffer),d.writeDouble=function(e,t,r){d.fl64[0]=r,(0,i.times)(8,(function(r){e[t+r]=d.ui8[7-r]}))};var p=function(e,t,r,a){var s=r,u=Object.keys(a).filter((function(e){return null!=e&&"undefined"!==e}));e.writeUshort(t,s,u.length);var l,f=(s+=2)+12*u.length+4,h=o(u);try{var d=function(){var r=l.value,o=null;"number"==typeof r?o=r:"string"==typeof r&&(o=parseInt(r,10));var u=n.fieldTagTypes[o],h=c[u];if(null==u||void 0===u||void 0===u)throw new Error("unknown type of tag: ".concat(o));var d=a[r];if(void 0===d)throw new Error("failed to get value for key ".concat(r));"ASCII"===u&&"string"==typeof d&&!1===(0,i.endsWith)(d,"\0")&&(d+="\0");var p=d.length;e.writeUshort(t,s,o),s+=2,e.writeUshort(t,s,h),s+=2,e.writeUint(t,s,p);var b=[-1,1,1,2,4,8,0,0,0,0,0,0,8][h]*p,y=s+=4;b>4&&(e.writeUint(t,s,f),y=f),"ASCII"===u?e.writeASCII(t,y,d):"SHORT"===u?(0,i.times)(p,(function(r){e.writeUshort(t,y+2*r,d[r])})):"LONG"===u?(0,i.times)(p,(function(r){e.writeUint(t,y+4*r,d[r])})):"RATIONAL"===u?(0,i.times)(p,(function(r){e.writeUint(t,y+8*r,Math.round(1e4*d[r])),e.writeUint(t,y+8*r+4,1e4)})):"DOUBLE"===u&&(0,i.times)(p,(function(r){e.writeDouble(t,y+8*r,d[r])})),b>4&&(f+=b+=1&b),s+=4};for(h.s();!(l=h.n()).done;)d()}catch(e){h.e(e)}finally{h.f()}return[s,f]},b=function(e){var t=new Uint8Array(h),r=4,n=d;t[0]=77,t[1]=77,t[3]=42;var i=8;if(n.writeUint(t,r,i),r+=4,e.forEach((function(r,o){var a=p(n,t,i,r);i=a[1],o<e.length-1&&n.writeUint(t,a[0],i)})),t.slice)return t.slice(0,i).buffer;for(var o=new Uint8Array(i),a=0;a<i;a++)o[a]=t[a];return o.buffer},y=function(e,t,r,n){if(null==r)throw new Error("you passed into encodeImage a width of type ".concat(r));if(null==t)throw new Error("you passed into encodeImage a width of type ".concat(t));var o={256:[t],257:[r],273:[h],278:[r],305:"geotiff.js"};if(n)for(var a in n)n.hasOwnProperty(a)&&(o[a]=n[a]);var s=new Uint8Array(b([o])),u=new Uint8Array(e),l=o[277],f=new Uint8Array(h+t*r*l);return(0,i.times)(s.length,(function(e){f[e]=s[e]})),(0,i.forEach)(u,(function(e,t){f[h+t]=e})),f.buffer},v=[["Compression",1],["PlanarConfiguration",1],["XPosition",0],["YPosition",0],["ResolutionUnit",1],["ExtraSamples",0],["GeoAsciiParams","WGS 84\0"],["ModelTiepoint",[0,0,0,-180,90,0]],["GTModelTypeGeoKey",2],["GTRasterTypeGeoKey",1],["GeographicTypeGeoKey",4326],["GeogCitationGeoKey","WGS 84"]]},{"./globals":"j27V","./utils":"FOZT"}],dy4f:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.setLogger=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new a;s=e},r.debug=function(){var e;return(e=s).debug.apply(e,arguments)},r.log=function(){var e;return(e=s).log.apply(e,arguments)},r.info=function(){var e;return(e=s).info.apply(e,arguments)},r.warn=function(){var e;return(e=s).warn.apply(e,arguments)},r.error=function(){var e;return(e=s).error.apply(e,arguments)},r.time=function(){var e;return(e=s).time.apply(e,arguments)},r.timeEnd=function(){var e;return(e=s).timeEnd.apply(e,arguments)};var n=o(e("@babel/runtime/helpers/classCallCheck")),i=o(e("@babel/runtime/helpers/createClass"));function o(e){return e&&e.__esModule?e:{default:e}}var a=function(){function e(){(0,n.default)(this,e)}return(0,i.default)(e,[{key:"log",value:function(){}},{key:"debug",value:function(){}},{key:"info",value:function(){}},{key:"warn",value:function(){}},{key:"error",value:function(){}},{key:"time",value:function(){}},{key:"timeEnd",value:function(){}}]),e}(),s=new a},{"@babel/runtime/helpers/classCallCheck":"fcMS","@babel/runtime/helpers/createClass":"P8NW"}],bsJs:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.fromUrl=function(e){return G.apply(this,arguments)},r.fromArrayBuffer=function(e,t){return z.apply(this,arguments)},r.fromFile=function(e,t){return H.apply(this,arguments)},r.fromBlob=function(e,t){return W.apply(this,arguments)},r.fromUrls=function(e){return q.apply(this,arguments)},r.writeArrayBuffer=function(e,t){return K.apply(this,arguments)},Object.defineProperty(r,"Pool",{enumerable:!0,get:function(){return y.default}}),Object.defineProperty(r,"getDecoder",{enumerable:!0,get:function(){return S.getDecoder}}),Object.defineProperty(r,"setLogger",{enumerable:!0,get:function(){return O.setLogger}}),r.rgb=r.globals=r.MultiGeoTIFF=r.default=r.GeoTIFF=void 0;var n=C(e("@babel/runtime/regenerator")),i=C(e("@babel/runtime/helpers/defineProperty")),o=C(e("@babel/runtime/helpers/slicedToArray")),a=C(e("@babel/runtime/helpers/asyncToGenerator")),s=C(e("@babel/runtime/helpers/createClass")),u=C(e("@babel/runtime/helpers/inherits")),l=C(e("@babel/runtime/helpers/possibleConstructorReturn")),f=C(e("@babel/runtime/helpers/getPrototypeOf")),c=C(e("@babel/runtime/helpers/wrapNativeSuper")),h=C(e("@babel/runtime/helpers/classCallCheck")),d=C(e("./geotiffimage")),p=C(e("./dataview64")),b=C(e("./dataslice")),y=C(e("./pool")),v=e("./source/remote"),g=e("./source/arraybuffer"),m=e("./source/filereader"),w=e("./source/file"),_=E(e("./globals"));r.globals=_;var k=e("./geotiffwriter"),x=E(e("./rgb"));r.rgb=x;var S=e("./compression"),O=e("./logging");function T(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return T=function(){return e},e}function E(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=T();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var o=n?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(r,i,o):r[i]=e[i]}return r.default=e,t&&t.set(e,r),r}function C(e){return e&&e.__esModule?e:{default:e}}function P(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?P(Object(r),!0).forEach((function(t){(0,i.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function R(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=(0,f.default)(e);if(t){var i=(0,f.default)(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return(0,l.default)(this,r)}}function M(e){switch(e){case _.fieldTypes.BYTE:case _.fieldTypes.ASCII:case _.fieldTypes.SBYTE:case _.fieldTypes.UNDEFINED:return 1;case _.fieldTypes.SHORT:case _.fieldTypes.SSHORT:return 2;case _.fieldTypes.LONG:case _.fieldTypes.SLONG:case _.fieldTypes.FLOAT:case _.fieldTypes.IFD:return 4;case _.fieldTypes.RATIONAL:case _.fieldTypes.SRATIONAL:case _.fieldTypes.DOUBLE:case _.fieldTypes.LONG8:case _.fieldTypes.SLONG8:case _.fieldTypes.IFD8:return 8;default:throw new RangeError("Invalid field type: ".concat(e))}}function A(e){var t=e.GeoKeyDirectory;if(!t)return null;for(var r={},n=4;n<=4*t[3];n+=4){var i=_.geoKeyNames[t[n]],o=t[n+1]?_.fieldTagNames[t[n+1]]:null,a=t[n+2],s=t[n+3],u=null;if(o){if(null==(u=e[o]))throw new Error("Could not get value of geoKey '".concat(i,"'."));"string"==typeof u?u=u.substring(s,s+a-1):u.subarray&&(u=u.subarray(s,s+a),1===a&&(u=u[0]))}else u=s;r[i]=u}return r}function I(e,t,r,n){var i=null,o=null,a=M(t);switch(t){case _.fieldTypes.BYTE:case _.fieldTypes.ASCII:case _.fieldTypes.UNDEFINED:i=new Uint8Array(r),o=e.readUint8;break;case _.fieldTypes.SBYTE:i=new Int8Array(r),o=e.readInt8;break;case _.fieldTypes.SHORT:i=new Uint16Array(r),o=e.readUint16;break;case _.fieldTypes.SSHORT:i=new Int16Array(r),o=e.readInt16;break;case _.fieldTypes.LONG:case _.fieldTypes.IFD:i=new Uint32Array(r),o=e.readUint32;break;case _.fieldTypes.SLONG:i=new Int32Array(r),o=e.readInt32;break;case _.fieldTypes.LONG8:case _.fieldTypes.IFD8:i=new Array(r),o=e.readUint64;break;case _.fieldTypes.SLONG8:i=new Array(r),o=e.readInt64;break;case _.fieldTypes.RATIONAL:i=new Uint32Array(2*r),o=e.readUint32;break;case _.fieldTypes.SRATIONAL:i=new Int32Array(2*r),o=e.readInt32;break;case _.fieldTypes.FLOAT:i=new Float32Array(r),o=e.readFloat32;break;case _.fieldTypes.DOUBLE:i=new Float64Array(r),o=e.readFloat64;break;default:throw new RangeError("Invalid field type: ".concat(t))}if(t!==_.fieldTypes.RATIONAL&&t!==_.fieldTypes.SRATIONAL)for(var s=0;s<r;++s)i[s]=o.call(e,n+s*a);else for(var u=0;u<r;u+=2)i[u]=o.call(e,n+u*a),i[u+1]=o.call(e,n+(u*a+4));return t===_.fieldTypes.ASCII?new TextDecoder("utf-8").decode(i):i}var D=function e(t,r,n){(0,h.default)(this,e),this.fileDirectory=t,this.geoKeyDirectory=r,this.nextIFDByteOffset=n},L=function(e){(0,u.default)(r,e);var t=R(r);function r(e){var n;return(0,h.default)(this,r),(n=t.call(this,"No image at index ".concat(e))).index=e,n}return r}((0,c.default)(Error)),N=function(){function e(){(0,h.default)(this,e)}return(0,s.default)(e,[{key:"readRasters",value:function(){var e=(0,a.default)(n.default.mark((function e(){var t,r,i,a,s,u,l,f,c,h,d,p,b,y,v,g,m,w,_,k,x,S,O,T,E,C,P,R,M,A,I,D,L,N,F,U,B,G,z,H=arguments;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=H.length>0&&void 0!==H[0]?H[0]:{},r=t.window,i=t.width,a=t.height,s=t.resX,u=t.resY,l=t.bbox,e.next=5,this.getImage();case 5:return f=e.sent,c=f,e.next=9,this.getImageCount();case 9:if(h=e.sent,d=f.getBoundingBox(),!r||!l){e.next=13;break}throw new Error('Both "bbox" and "window" passed.');case 13:if(!i&&!a){e.next=24;break}if(r&&(p=f.getOrigin(),b=(0,o.default)(p,2),y=b[0],v=b[1],g=f.getResolution(),m=(0,o.default)(g,2),w=m[0],_=m[1],l=[y+r[0]*w,v+r[1]*_,y+r[2]*w,v+r[3]*_]),k=l||d,!i){e.next=20;break}if(!s){e.next=19;break}throw new Error("Both width and resX passed");case 19:s=(k[2]-k[0])/i;case 20:if(!a){e.next=24;break}if(!u){e.next=23;break}throw new Error("Both width and resY passed");case 23:u=(k[3]-k[1])/a;case 24:if(!s&&!u){e.next=48;break}x=[],S=0;case 27:if(!(S<h)){e.next=36;break}return e.next=30,this.getImage(S);case 30:O=e.sent,T=O.fileDirectory,E=T.SubfileType,C=T.NewSubfileType,(0===S||2===E||1&C)&&x.push(O);case 33:++S,e.next=27;break;case 36:x.sort((function(e,t){return e.getWidth()-t.getWidth()})),P=0;case 38:if(!(P<x.length)){e.next=48;break}if(R=x[P],M=(d[2]-d[0])/R.getWidth(),A=(d[3]-d[1])/R.getHeight(),c=R,!(s&&s>M||u&&u>A)){e.next=45;break}return e.abrupt("break",48);case 45:++P,e.next=38;break;case 48:return I=r,l&&(D=f.getOrigin(),L=(0,o.default)(D,2),N=L[0],F=L[1],U=c.getResolution(f),B=(0,o.default)(U,2),G=B[0],z=B[1],I=[Math.round((l[0]-N)/G),Math.round((l[1]-F)/z),Math.round((l[2]-N)/G),Math.round((l[3]-F)/z)],I=[Math.min(I[0],I[2]),Math.min(I[1],I[3]),Math.max(I[0],I[2]),Math.max(I[1],I[3])]),e.abrupt("return",c.readRasters(j(j({},t),{},{window:I})));case 51:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),F=function(e){(0,u.default)(r,e);var t=R(r);function r(e,n,i,o){var a,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return(0,h.default)(this,r),(a=t.call(this)).source=e,a.littleEndian=n,a.bigTiff=i,a.firstIFDOffset=o,a.cache=s.cache||!1,a.ifdRequests=[],a.ghostValues=null,a}return(0,s.default)(r,[{key:"getSlice",value:function(){var e=(0,a.default)(n.default.mark((function e(t,r){var i;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=this.bigTiff?4048:1024,e.t0=b.default,e.next=4,this.source.fetch([{offset:t,length:void 0!==r?r:i}]);case 4:return e.t1=e.sent[0],e.t2=t,e.t3=this.littleEndian,e.t4=this.bigTiff,e.abrupt("return",new e.t0(e.t1,e.t2,e.t3,e.t4));case 9:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}()},{key:"parseFileDirectoryAt",value:function(){var e=(0,a.default)(n.default.mark((function e(t){var r,i,o,a,s,u,l,f,c,h,d,p,b,y,v,g,m,w,k,x;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.bigTiff?20:12,i=this.bigTiff?8:2,e.next=4,this.getSlice(t);case 4:if(o=e.sent,a=this.bigTiff?o.readUint64(t):o.readUint16(t),s=a*r+(this.bigTiff?16:6),o.covers(t,s)){e.next=11;break}return e.next=10,this.getSlice(t,s);case 10:o=e.sent;case 11:u={},l=t+(this.bigTiff?8:2),f=0;case 14:if(!(f<a)){e.next=41;break}if(c=o.readUint16(l),h=o.readUint16(l+2),d=this.bigTiff?o.readUint64(l+4):o.readUint32(l+4),p=void 0,b=void 0,y=M(h),v=l+(this.bigTiff?12:8),!(y*d<=(this.bigTiff?8:4))){e.next=26;break}p=I(o,h,d,v),e.next=36;break;case 26:if(g=o.readOffset(v),m=M(h)*d,!o.covers(g,m)){e.next=32;break}p=I(o,h,d,g),e.next=36;break;case 32:return e.next=34,this.getSlice(g,m);case 34:w=e.sent,p=I(w,h,d,g);case 36:b=1===d&&-1===_.arrayFields.indexOf(c)&&h!==_.fieldTypes.RATIONAL&&h!==_.fieldTypes.SRATIONAL?p[0]:p,u[_.fieldTagNames[c]]=b;case 38:l+=r,++f,e.next=14;break;case 41:return k=A(u),x=o.readOffset(t+i+r*a),e.abrupt("return",new D(u,k,x));case 44:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"requestIFD",value:function(){var e=(0,a.default)(n.default.mark((function e(t){var r=this;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.ifdRequests[t]){e.next=4;break}return e.abrupt("return",this.ifdRequests[t]);case 4:if(0!==t){e.next=9;break}return this.ifdRequests[t]=this.parseFileDirectoryAt(this.firstIFDOffset),e.abrupt("return",this.ifdRequests[t]);case 9:if(this.ifdRequests[t-1]){e.next=19;break}e.prev=10,this.ifdRequests[t-1]=this.requestIFD(t-1),e.next=19;break;case 14:if(e.prev=14,e.t0=e.catch(10),!(e.t0 instanceof L)){e.next=18;break}throw new L(t);case 18:throw e.t0;case 19:return this.ifdRequests[t]=(0,a.default)(n.default.mark((function e(){var i;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.ifdRequests[t-1];case 2:if(0!==(i=e.sent).nextIFDByteOffset){e.next=5;break}throw new L(t);case 5:return e.abrupt("return",r.parseFileDirectoryAt(i.nextIFDByteOffset));case 6:case"end":return e.stop()}}),e)})))(),e.abrupt("return",this.ifdRequests[t]);case 21:case"end":return e.stop()}}),e,this,[[10,14]])})));return function(t){return e.apply(this,arguments)}}()},{key:"getImage",value:function(){var e=(0,a.default)(n.default.mark((function e(){var t,r,i=arguments;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=i.length>0&&void 0!==i[0]?i[0]:0,e.next=3,this.requestIFD(t);case 3:return r=e.sent,e.abrupt("return",new d.default(r.fileDirectory,r.geoKeyDirectory,this.dataView,this.littleEndian,this.cache,this.source));case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getImageCount",value:function(){var e=(0,a.default)(n.default.mark((function e(){var t,r;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=0,r=!0;case 2:if(!r){e.next=18;break}return e.prev=3,e.next=6,this.requestIFD(t);case 6:++t,e.next=16;break;case 9:if(e.prev=9,e.t0=e.catch(3),!(e.t0 instanceof L)){e.next=15;break}r=!1,e.next=16;break;case 15:throw e.t0;case 16:e.next=2;break;case 18:return e.abrupt("return",t);case 19:case"end":return e.stop()}}),e,this,[[3,9]])})));return function(){return e.apply(this,arguments)}}()},{key:"getGhostValues",value:function(){var e=(0,a.default)(n.default.mark((function e(){var t,r,i,a,s,u,l,f,c=this;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this.bigTiff?16:8,!this.ghostValues){e.next=3;break}return e.abrupt("return",this.ghostValues);case 3:return i=(r="GDAL_STRUCTURAL_METADATA_SIZE=").length+100,e.next=7,this.getSlice(t,i);case 7:if(a=e.sent,r!==I(a,_.fieldTypes.ASCII,r.length,t)){e.next=19;break}if(s=I(a,_.fieldTypes.ASCII,i,t),u=s.split("\n")[0],!((l=Number(u.split("=")[1].split(" ")[0])+u.length)>i)){e.next=16;break}return e.next=15,this.getSlice(t,l);case 15:a=e.sent;case 16:f=I(a,_.fieldTypes.ASCII,l,t),this.ghostValues={},f.split("\n").filter((function(e){return e.length>0})).map((function(e){return e.split("=")})).forEach((function(e){var t=(0,o.default)(e,2),r=t[0],n=t[1];c.ghostValues[r]=n}));case 19:return e.abrupt("return",this.ghostValues);case 20:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"close",value:function(){return"function"==typeof this.source.close&&this.source.close()}}],[{key:"fromSource",value:function(){var e=(0,a.default)(n.default.mark((function e(t,i,o){var a,s,u,l,f,c,h;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.fetch([{offset:0,length:1024}],o);case 2:if(a=e.sent[0],s=new p.default(a),18761!==(u=s.getUint16(0,0))){e.next=9;break}l=!0,e.next=14;break;case 9:if(19789!==u){e.next=13;break}l=!1,e.next=14;break;case 13:throw new TypeError("Invalid byte order value.");case 14:if(42!==(f=s.getUint16(2,l))){e.next=19;break}c=!1,e.next=27;break;case 19:if(43!==f){e.next=26;break}if(c=!0,8===s.getUint16(4,l)){e.next=24;break}throw new Error("Unsupported offset byte-size.");case 24:e.next=27;break;case 26:throw new TypeError("Invalid magic number.");case 27:return h=c?s.getUint64(8,l):s.getUint32(4,l),e.abrupt("return",new r(t,l,c,h,i));case 29:case"end":return e.stop()}}),e)})));return function(t,r,n){return e.apply(this,arguments)}}()}]),r}(N);r.GeoTIFF=F;var U=F;r.default=U;var B=function(e){(0,u.default)(r,e);var t=R(r);function r(e,n){var i;return(0,h.default)(this,r),(i=t.call(this)).mainFile=e,i.overviewFiles=n,i.imageFiles=[e].concat(n),i.fileDirectoriesPerFile=null,i.fileDirectoriesPerFileParsing=null,i.imageCount=null,i}return(0,s.default)(r,[{key:"parseFileDirectoriesPerFile",value:function(){var e=(0,a.default)(n.default.mark((function e(){var t;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=[this.mainFile.parseFileDirectoryAt(this.mainFile.firstIFDOffset)].concat(this.overviewFiles.map((function(e){return e.parseFileDirectoryAt(e.firstIFDOffset)}))),e.next=3,Promise.all(t);case 3:return this.fileDirectoriesPerFile=e.sent,e.abrupt("return",this.fileDirectoriesPerFile);case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getImage",value:function(){var e=(0,a.default)(n.default.mark((function e(){var t,r,i,o,a,s,u,l=arguments;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=l.length>0&&void 0!==l[0]?l[0]:0,e.next=3,this.getImageCount();case 3:return e.next=5,this.parseFileDirectoriesPerFile();case 5:r=0,i=0,o=0;case 8:if(!(o<this.imageFiles.length)){e.next=26;break}a=this.imageFiles[o],s=0;case 11:if(!(s<this.imageCounts[o])){e.next=22;break}if(t!==r){e.next=17;break}return e.next=15,a.requestIFD(i);case 15:return u=e.sent,e.abrupt("return",new d.default(u.fileDirectory,u.geoKeyDirectory,a.dataView,a.littleEndian,a.cache,a.source));case 17:r++,i++;case 19:s++,e.next=11;break;case 22:i=0;case 23:o++,e.next=8;break;case 26:throw new RangeError("Invalid image index");case 27:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getImageCount",value:function(){var e=(0,a.default)(n.default.mark((function e(){var t;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null===this.imageCount){e.next=2;break}return e.abrupt("return",this.imageCount);case 2:return t=[this.mainFile.getImageCount()].concat(this.overviewFiles.map((function(e){return e.getImageCount()}))),e.next=5,Promise.all(t);case 5:return this.imageCounts=e.sent,this.imageCount=this.imageCounts.reduce((function(e,t){return e+t}),0),e.abrupt("return",this.imageCount);case 8:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),r}(N);function G(){return(G=(0,a.default)(n.default.mark((function e(t){var r,i,o=arguments;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=o.length>1&&void 0!==o[1]?o[1]:{},i=o.length>2?o[2]:void 0,e.abrupt("return",F.fromSource((0,v.makeRemoteSource)(t,r),i));case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function z(){return(z=(0,a.default)(n.default.mark((function e(t,r){return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",F.fromSource((0,g.makeBufferSource)(t),r));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function H(){return(H=(0,a.default)(n.default.mark((function e(t,r){return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",F.fromSource((0,w.makeFileSource)(t),r));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function W(){return(W=(0,a.default)(n.default.mark((function e(t,r){return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",F.fromSource((0,m.makeFileReaderSource)(t),r));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function q(){return(q=(0,a.default)(n.default.mark((function e(t){var r,i,o,a,s,u=arguments;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=u.length>1&&void 0!==u[1]?u[1]:[],i=u.length>2&&void 0!==u[2]?u[2]:{},o=u.length>3?u[3]:void 0,e.next=5,F.fromSource((0,v.makeRemoteSource)(t,i),o);case 5:return a=e.sent,e.next=8,Promise.all(r.map((function(e){return F.fromSource((0,v.makeRemoteSource)(e,i))})));case 8:return s=e.sent,e.abrupt("return",new B(a,s));case 10:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function K(){return(K=(0,a.default)(n.default.mark((function e(t,r){return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,k.writeGeotiff)(t,r));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}r.MultiGeoTIFF=B},{"@babel/runtime/regenerator":"PMvg","@babel/runtime/helpers/defineProperty":"IxO8","@babel/runtime/helpers/slicedToArray":"HETk","@babel/runtime/helpers/asyncToGenerator":"agGE","@babel/runtime/helpers/createClass":"P8NW","@babel/runtime/helpers/inherits":"d4H2","@babel/runtime/helpers/possibleConstructorReturn":"pxk2","@babel/runtime/helpers/getPrototypeOf":"UJE0","@babel/runtime/helpers/wrapNativeSuper":"ozpi","@babel/runtime/helpers/classCallCheck":"fcMS","./geotiffimage":"eOWo","./dataview64":"dqpX","./dataslice":"dGLV","./pool":"dHPO","./source/remote":"itmA","./source/arraybuffer":"wM9h","./source/filereader":"GiEG","./source/file":"G7Cz","./globals":"j27V","./geotiffwriter":"BGyE","./rgb":"fpBl","./compression":"FGCZ","./logging":"dy4f"}]},{},["bsJs"],"GeoTIFF");
//# sourceMappingURL=/sm/6b05ec3e6aee27214397dcd933f0a1a0def04511045c0ced378787b04765ef87.map