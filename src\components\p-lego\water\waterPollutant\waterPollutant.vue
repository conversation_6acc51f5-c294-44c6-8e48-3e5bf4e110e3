<!-- @format -->

<template>
    <div class="">
        <p-line
            :data="data"
            :option="lineOption"
            :config="{
                unit: unit,
                color: ['#6789AB', '#7ECF51'],
                markLine: markLine
            }"
            style="width: 100%; height: 100%"
        ></p-line>
    </div>
</template>

<script>
import { getPollutionmarkLine } from './utils/Pollution.js';

export default {
    name: '',
    props: {
        data: {
            //堆叠图数据
            type: Object,
            default: function () {
                return { xAxis: [], series: [] };
            }
        },
        pollutantVal: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            unit: '浓度（mg/L）',

            lineOption: {
                grid: {
                    top: '15%',
                    bottom: 10,
                    right: '15%'
                },
                legend: {
                    left: 'center',
                    itemWidth: 12,
                    itemHeight: 12,
                    icon: 'rect'
                },
                tooltip: {
                    trigger: 'axis'
                },
                series: [
                    {
                        smooth: false,
                        areaStyle: { color: 'rgba(0, 110, 254, 0.1)' }
                    }
                ],
                xAxis: {
                    name: '月份',
                    nameTextStyle: {
                        padding: [0, 0, 0, 10],
                        verticalAlign: 'top'
                    },
                    type: 'category',
                    boundaryGap: 0,
                    splitLine: { show: false },
                    axisTick: { show: true }
                },
                yAxis: {
                    nameTextStyle: {
                        padding: [0, 0, 0, -10]
                    },
                    splitLine: {
                        lineStyle: {
                            type: 'solid'
                        }
                    }
                }
            },
            markLine: []
        };
    },
    watch: {
        pollutantVal() {
            this.markLine = getPollutionmarkLine(this.pollutantVal, 'lake');
            if (this.pollutantVal === 'w01001_DBS') {
                this.unit = '无量纲';
            } else {
                this.unit = '浓度（mg/L）';
            }
        }
    },
    mounted() {},
    methods: {}
};
</script>

<style lang="less" scoped></style>
