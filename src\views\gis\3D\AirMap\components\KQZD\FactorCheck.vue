<!-- @format -->

<!-- 空气站-因子切换  -->
<template>
    <dl class="gis-dloption1">
        <dd>
            <ul class="gis-ullst1">
                <li
                    @click="itemClick('AQI')"
                    style="width: 100%"
                    :class="{ on: selectYZ == 'AQI' }"
                >
                    AQI
                </li>
                <li
                    @click="itemClick('PM25')"
                    :class="{ on: selectYZ == 'PM25' }"
                >
                    PM<sub>2.5</sub>
                </li>
                <li
                    @click="itemClick('PM10')"
                    :class="{ on: selectYZ == 'PM10' }"
                >
                    PM<sub>10</sub>
                </li>
                <li
                    @click="itemClick('SO2')"
                    :class="{ on: selectYZ == 'SO2' }"
                >
                    SO<sub>2</sub>
                </li>
                <li
                    @click="itemClick('NO2')"
                    :class="{ on: selectYZ == 'NO2' }"
                >
                    NO<sub>2</sub>
                </li>
                <li @click="itemClick('O3')" :class="{ on: selectYZ == 'O3' }">
                    O<sub>3</sub>
                </li>
                <li @click="itemClick('CO')" :class="{ on: selectYZ == 'CO' }">
                    CO
                </li>
            </ul>
        </dd>
    </dl>
</template>

<script>
export default {
    data() {
        return {
            selectYZ: 'AQI'
        };
    },
    props: [],

    mounted() {},
    methods: {
        itemClick(yz) {
            this.selectYZ = yz;
            this.$emit('factorChange', this.selectYZ);
        }
    },

    computed: {},

    watch: {}
};
</script>

<style></style>
