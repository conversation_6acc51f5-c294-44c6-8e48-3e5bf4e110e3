<!-- @format -->

<template>
    <div>
        <el-row class="tac">
            <el-col :span="24">
                <el-menu
                    active-text-color="#13BFE5"
                    background-color="#2E374D"
                    text-color="#fff"
                    router
                    :default-active="default_path"
                    :collapse="isCollapse"
                    @select="get_menu_item"
                    @close="handleClose"
                >
                    <template v-for="(menu, index) in menu_data" :key="index">
                        <el-menu-item :index="menu.path" v-if="!menu.children">
                            <el-icon><Document /></el-icon>
                            <span>{{ menu.menu_name }}</span>
                        </el-menu-item>
                        <el-sub-menu
                            v-if="menu.children"
                            v-show="menu.menu_show_status"
                            :index="'i' + index"
                        >
                            <template #title>
                                <el-icon><Document /></el-icon>
                                <span>{{ menu.menu_name }}</span>
                            </template>
                            <el-menu-item
                                v-for="(item, i) in menu.children"
                                v-show="item.menu_show_status"
                                :key="i"
                                :index="item.path"
                            >
                                <span>
                                    <i :class="item.menu_icon"></i>
                                    {{ item.menu_name }}
                                </span>
                            </el-menu-item>
                        </el-sub-menu>
                    </template>
                </el-menu>
            </el-col>
        </el-row>
    </div>
</template>
<script>
import {
    Document,
    Menu as IconMenu,
    Location,
    Setting
} from '@element-plus/icons-vue';
export default {
    components: { Document },
    props: ['menu_data'],
    data() {
        return {
            isCollapse: false,
            default_path: sessionStorage.getItem('aside_default_path')
        };
    },
    methods: {
        handleClose(data) {
            console.log(data);
        },
        get_menu_item(path) {
            sessionStorage.setItem('aside_default_path', path);
        }
    }
};
</script>
