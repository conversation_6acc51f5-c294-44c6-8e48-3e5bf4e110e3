/*默认黑色主题*/

.zy-hd {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: #2a3141;
    box-shadow: 0 1px 9px 1px rgba(19, 70, 93, 0.85);
}

.zy-hd .logo {
    float: left;
    height: 100%;
    display: flex;
    align-items: center;
    margin-left: 15px;
    position: relative;
    z-index: 3;
}

.zy-hd .nav {
    float: left;
    display: flex;
    margin-right: 30px;
    position: relative;
    z-index: 3;
}

.nav li {
    float: left;
    height: 60px;
    line-height: 60px;
    font-size: 16px;
    color: white;
    position: relative;
    width: 105px;
    text-align: center;
    cursor: pointer;
    margin-right: 10px;
    z-index: 20;
}

.nav li.cur>span {
    color: #13bfe5;
}

.nav li.cur.teshu.on span {
    background-image: url(../images/arrow-blue-up.png);
}

.nav li.teshu span {
    padding-right: 12px;
    background: url(../images/arrow-white-down.png) right center no-repeat;
}

.nav li .xiala {
    display: none;
    position: absolute;
    top: 55px;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding-top: 5px;
    border-radius: 3px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.nav .teshu.on .xiala {
    display: block;
}

.nav li .xiala::after {
    content: "";
    position: absolute;
    top: -14px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border: 7px solid #fff;
    border-color: transparent transparent #fff transparent;
}

.nav li .xiala p {
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    color: #333;
    text-align: center;
    cursor: pointer;
}

.nav li .xiala p.cur {
    background-color: #dbf6fa;
    color: #00a3bb;
}

.zy-hd .user-wrap {
    float: left;
    display: flex;
    height: 60px;
    align-items: center;
    position: relative;
    z-index: 3;
}

.zy-hd .user-wrap .user {
    display: flex;
    align-items: center;
}

.zy-hd .user-wrap .user span {
    margin-left: 8px;
    font-size: 14px;
    color: #fff;
}

.zy-hd .user-wrap .line {
    width: 1px;
    height: 13px;
    background-color: #4a5263;
    margin: 0 13px;
}

.zy-hd .user-wrap .signout {
    display: flex;
    align-items: center;
    margin-right: 18px;
    cursor: pointer;
}

#J_dotLine {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.4;
}

.zy-container {
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
}

.zy-lside {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 200px;
    background-color: #2e374d;
    box-shadow: 4px 0 3px 1px rgba(79, 79, 79, 0.48);
    z-index: 2;
}

.waves {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200px;
    height: 400px;
}

.menu {
    position: relative;
    z-index: 2;
}

.menu>li h3 {
    display: flex;
    align-items: center;
    height: 45px;
    position: relative;
    padding-left: 13px;
}

.menu>li h3 .ic {
    width: 18px;
    height: 45px;
}

.menu>li h3 span {
    font-size: 14px;
    color: #dddddd;
    margin-left: 11px;
}

.menu>li h3 .arrow {
    position: absolute;
    top: 20px;
    right: 17px;
    width: 13px;
    height: 7px;
    background: url(../images/arrow-down.png) no-repeat;
}

.menu>li .sub-menu {
    background-color: #252a38;
    border-left: 1px solid #2e374d;
    height: 0;
    overflow: hidden;
}

.menu>li .sub-menu li {
    height: 45px;
    padding-left: 40px;
    font-size: 14px;
    color: #fff;
    line-height: 45px;
    position: relative;
    left: -1px;
    cursor: pointer;
}

.menu>li .sub-menu li.on {
    background: url(../images/sub-menu-li.png) left center no-repeat;
}

.menu>li h3 .ic1 {
    background: url(../images/menu-li1.png) center no-repeat;
}

.menu>li.on .ic1 {
    background-image: url(../images/menu-li1-cur.png);
}

.menu>li h3 .ic2 {
    background: url(../images/menu-li2.png) center no-repeat;
}

.menu>li.on .ic2 {
    background-image: url(../images/menu-li2-cur.png);
}

.menu>li h3 .ic3 {
    background: url(../images/menu-li3.png) center no-repeat;
}

.menu>li.on .ic3 {
    background-image: url(../images/menu-li3-cur.png);
}

.menu>li h3 .ic4 {
    background: url(../images/menu-li4.png) center no-repeat;
}

.menu>li.on .ic4 {
    background-image: url(../images/menu-li4-cur.png);
}

.menu>li h3 .ic5 {
    background: url(../images/menu-li5.png) center no-repeat;
}

.menu>li.on .ic5 {
    background-image: url(../images/menu-li5-cur.png);
}

.menu>li.on h3 span {
    color: #fff;
}

.menu>li.on h3 .arrow {
    transform: rotate(180deg);
}

.menu>li.on .sub-menu {
    height: auto;
}

.zy-content {
    position: absolute;
    top: 0;
    left: 200px;
    right: 0;
    bottom: 0;
    /* background-color: #ebf1f5; */
}

.zy-content .wrap {
    padding: 20px;
}


/*下拉框*/

.selectBox {
    position: relative;
    background: url(../images/arwbot1.png) no-repeat 93% center;
    overflow: hidden;
    display: inline-block;
    *display: inline;
    *zoom: 1;
}

.selectBox select {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    filter: alpha(opacity=0);
    cursor: pointer;
}

.selectBox span {
    display: inline-block;
}


/*单选框*/

.radioBox {
    display: inline-block;
}

.radioBox .radio-icon {
    display: inline-block;
    background: url(../images/radioic.png) no-repeat;
    width: 14px;
    height: 14px;
}

.radioBox .radio-icon.on {
    background: url(../images/radioicon.png) no-repeat;
}

.radioBox input[type="radio"] {
    display: none;
}

.radioBox span {
    vertical-align: middle;
}

.pd-mod {
    background: #fff;
    box-shadow: 0 0 9px rgba(182, 194, 228, .48);
    border-radius: 5px;
}

.pd-table1a {
    width: 100%;
    border-collapse: collapse;
}

.pd-table1a tr td {
    font-size: 14px;
    color: #333;
    padding: 8px 0;
}

.pd-table1a tr td.td-hd {
    text-align: right;
    color: #666;
}

.pd-table1a tr td .btn {
    padding: 0 15px;
    height: 32px;
    border-radius: 4px;
    background: #15b8dd;
    font-size: 14px;
    color: #fff;
    box-shadow: 0 0 9px rgba(182, 194, 228, .48);
}

.pd-table1a tr td .btn img {
    vertical-align: middle;
    margin-right: 5px;
}

.pd-inptxt1 {
    border: 1px solid #ddd;
    border-radius: 4px;
    height: 30px;
    line-height: 30px;
    text-indent: 10px;
    font-size: 14px;
    color: #333;
    outline: none;
}

.pd-inptxt1::-webkit-input-placeholder {
    color: #999;
}

.pd-slecbx2 {
    font-size: 0;
    float: left;
}

.pd-slecbx2 .selectBox {
    border: 1px solid #ddd;
    border-radius: 4px;
    height: 30px;
}

.pd-slecbx2 .selectBox+.selectBox {
    margin-left: 10px;
}

.pd-slecbx2 .selectBox select {
    font-size: 14px;
}

.pd-slecbx2 .selectBox span {
    font-size: 14px;
    color: #333;
    text-indent: 10px;
    line-height: 30px;
}

.pd-ultbs4 {
    overflow: hidden;
}

.pd-ultbs4 li {
    float: left;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 15px;
    font-size: 14px;
    color: #333;
    line-height: 30px;
}

.pd-ultbs4 li.on {
    background: #15b8dd;
    color: #fff;
    border-color: #15b8dd;
}

.pd-ultbs4 li+li {
    margin-left: 10px;
}

.pd-radiobx1 {
    font-size: 0;
}

.pd-radiobx1 .radioBox+.radioBox {
    margin-left: 30px;
}

.pd-radiobx1 .radioBox .radio-icon {
    margin-right: 5px;
}

.pd-radiobx1 .radioBox span {
    font-size: 14px;
    color: #999;
}

.pd-tablelst1 {
    width: 100%;
    border-collapse: collapse;
}

.pd-tablelst1 tr td {
    height: 40px;
    font-size: 14px;
    color: #333;
    text-align: center;
}

.pd-tablelst1 thead tr td {
    background: #eee;
}

.pd-tablelst1 tbody tr+tr td {
    border-top: 1px solid #eee;
}

.pd-tablelst1 tbody tr td.tols i {
    color: #15b8dd;
    margin: 0 18px;
}

.pd-tablelst1 tbody tr td.tols i.on {
    color: #666;
}

input,
select,
button {
    border: none;
    outline: none;
}


/*主题--青色*/

.theme-qingse {
    background-color: #056a99;
}

.theme-qingse .user-wrap .line {
    background-color: #82b5cc;
}

.theme-qingse .nav li span {
    color: #fff;
    display: block;
    font-size: 16px;
    line-height: 60px;
    padding: 0 18px;
}

.theme-qingse .nav li.cur {
    background-color: rgba(0, 62, 91, 0.5);
    position: relative;
}

.theme-qingse .nav li.cur::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #22b8eb;
}

.theme-qingse .menu>li .sub-menu {
    background-color: #004566;
    border-left: 1px solid #005780;
}


/*主题--天蓝色*/

.theme-tianlanse {
    background-color: #3686e7;
}

.theme-tianlanse .user-wrap .line {
    background-color: #82b5cc;
}

.theme-tianlanse .nav li span {
    color: #cbe3ff;
    display: block;
    font-size: 16px;
    line-height: 60px;
    padding: 0 18px;
}

.theme-tianlanse .nav li.cur span {
    color: #fff;
}

.theme-tianlanse .menu>li .sub-menu li.on {
    background: url(../images/sub-menu-li-tianlanse.png) left center no-repeat;
}

.theme-tianlanse .menu>li .sub-menu {
    background-color: #216dc9;
    border-left: 1px solid #216dc9;
}


/* .theme-tianlanse .pd-tablelst1 tbody tr td.tols i {
    color: #3686e7;
} */


/*主题-白色*/

.cs-content {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
}

.cs-head {
    z-index: 5;
    position: relative;
    background: url("../images/head-bg.png") no-repeat center #1643b8;
    height: 60px;
    line-height: 60px;
    background-size: 100% 100%;
    box-shadow: 0 0 15px rgba(177, 177, 177, .24);
}

.cs-logo {
    float: left;
    margin: -2px 0 0 20px;
}

.cs-logo img {
    vertical-align: middle;
}

.cs-head-cut {
    position: absolute;
    top: 0;
    left: 25%;
}

.cs-head-cut ul,
.cs-head-user ul {
    overflow: hidden
}

.cs-head-cut ul li {
    float: left;
    line-height: 60px;
    font-size: 16px;
    color: #e4eeff;
    padding: 0 20px;
    cursor: pointer;
    transition: ease-in-out .2s
}

.cs-head-cut ul li.on {
    background: url("../images/lis-on-bg.png") no-repeat center;
    background-size: 100% 100%;
    color: #Fff;
    font-weight: bold;
}

.cs-head-user {
    float: right;
    margin-top: 21px;
}

.cs-head-user ul li {
    float: left;
    padding: 0 14px;
    border-left: 1px solid #ddd;
    color: #fff;
    font-size: 14px;
}

.cs-head-user ul li:first-child {
    border-left: none;
}

.cs-head-user ul li img {
    margin: 0 7px;
}

.cs-module {
    position: absolute;
    top: 60px;
    left: 0;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
    min-height: 700px;
}

.cs-item-left {
    background-color: #fff;
    height: 100%;
    width: 255px;
    float: left;
    border-right: 1px solid #ddd;
    box-sizing: border-box;
    overflow-y: auto
}

.cs-item-right,
.cs-item-stats {
    background-color: #fff;
    height: 100%;
    margin-left: 265px;
    border-left: 1px solid #ddd;
    box-sizing: border-box;
    padding: 0 18px;
    overflow-y: auto
}

.cs-item-left ul li div.cs-menu-li {
    border-bottom: 1px solid #ddd;
    padding: 14px 12px 12px 20px;
    cursor: pointer;
    -webkit-user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
    user-select: none;
    height: 47px;
    box-sizing: border-box;
    font-size: 14px;
}

.cs-item-left ul li i.icon-logo {
    display: inline-block;
    width: 19px;
    height: 19px;
    margin: -3px 10px 0 0;
    background: url("../images/icon-kh.png") no-repeat center;
    vertical-align: middle
}

.cs-item-left ul li i.icon-up {
    transition: ease-in-out .2s;
    float: right;
    background: url("../images/icon-up.png") no-repeat center;
    width: 13px;
    height: 8px;
    margin-top: 5px;
}

.cs-hide-select {
    padding-left: 28px;
    padding-bottom: 5px;
    display: none;
}

.cs-item-left ul li:first-child div.cs-hide-select {
    display: block;
}

.cs-item-left ul li dl {
    font-size: 14px;
}

.cs-item-left ul li:first-child i.icon-up {
    transform: rotate(-180deg)
}

.cs-hide-select dd dl {
    padding: 15px 10px 15px 35px;
    position: relative;
    cursor: pointer;
    transition: ease-in-out .2s
}

.cs-hide-select dd dl.on {
    color: #1643b8
}

.cs-hide-select dd {
    border-left: 1px dashed #DCDCDC;
}

.cs-hide-select dd dl:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 21px;
    height: 1px;
    border-top: 1px dashed #ddd;
    margin-top: -0.5px;
}

.cs-hide-select dd dl:after {
    content: '';
    position: absolute;
    left: 21px;
    top: 50%;
    width: 8px;
    height: 8px;
    background-color: #dcdcdc;
    margin-top: -4px;
}

.cs-hide-select dd dl.on:after {
    background-color: #1643b8
}

.cs-right-form {
    padding: 20px 0
}

.cs-right-form label {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
}

.cs-right-form input,
.cs-right-form select {
    border: 1px solid #ddd;
    height: 35px;
    box-sizing: border-box;
    padding: 0 10px;
    font-size: 14px;
}

.cs-right-form input {
    width: 160px;
}

.cs-right-form select {
    width: 142px;
    background: url("../images/icon-select-logo.png") no-repeat 92% center;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding-right: 25px;
    font-size: 14px;
}

.cs-right-form select::-ms-expand {
    display: none;
}

.cs-right-form button {
    background-color: #1643b8;
    color: #fff;
    border: none;
    display: inline-block;
    vertical-align: middle;
    height: 35px;
    font-size: 14px;
}

.cs-right-form button.search-btn {
    width: 79px;
    margin-left: 10px;
}

.cs-right-form button.add-new-btn {
    float: right;
    background: url("../images/icon-add-new.png") no-repeat 18px center #1643b8;
    width: 85px;
    text-indent: 1.5rem;
}

.cs-right-table {
    padding-top: 10px;
}

.cs-right-table table {
    width: 100%;
    table-layout: fixed;
}

.cs-right-table table thead tr th {
    background-color: #eee;
    font-weight: bold;
    padding: 15px 0;
    font-size: 14px;
}

.cs-right-table table tbody tr td {
    text-align: center;
    padding: 15px 0;
    border-bottom: 1px solid #ddd;
    word-break: break-word;
    font-size: 14px;
}

.cs-right-table table tbody tr td a {
    display: inline-block;
    margin: 0 15px;
    color: #333;
}

.cs-right-table table tbody tr td a.edit {
    color: #1643b8;
}

.cs-right-table table tbody tr td a:hover {
    color: #FF9900;
}

.cs-pop {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 998;
    background: rgba(0, 0, 0, 0.4);
}

.cs-pop-details {
    overflow: hidden;
    position: absolute;
    left: 50%;
    top: 50%;
    width: 875px;
    height: 626px;
    margin-top: -313px;
    margin-left: -437.5px;
    background-color: #fff;
    border-radius: 10px;
}

.cs-pop-details h2 {
    color: #fff;
    font-size: 16px;
    background-color: #1643b8;
    line-height: 40px;
    padding: 0 20px 0 30px;
}

.cs-pop-details h2 i.close {
    cursor: pointer;
    float: right;
    background: url("../images/icon-close.png") no-repeat center;
    width: 14px;
    height: 14px;
    margin-top: 13px;
    transition: ease-in-out .3s;
}

.cs-pop-details h2 i.close:hover {
    transform: rotate(90deg);
}

.cs-pop-content {
    height: 585px;
    overflow-y: auto;
}

.cs-pop-list {
    padding: 14px 0;
    border-bottom: 1px solid #ddd;
}

.cs-pop-list ul {
    overflow: hidden;
}

.cs-pop-list ul li {
    float: left;
    padding: 0 32px;
    border-left: 1px solid #ddd;
    cursor: pointer;
    font-size: 14px;
}

.cs-pop-list ul li:first-child {
    border-left: none;
}

.cs-pop-list ul li.on {
    color: #1643b8;
    font-weight: bold;
}

.cs-pop-table {
    padding: 15px 20px;
}

.cs-pop-table table {
    width: 100%;
    table-layout: fixed;
    border: 1px solid #ddd;
}

.cs-pop-table table tr td {
    border: 1px solid #dddd;
    padding: 12px 13px;
    box-sizing: border-box;
    font-size: 14px;
}

.cs-pop-table table tr td:nth-child(odd) {
    text-align: right;
    background-color: #eee;
    width: 160px;
}

.cs-pop-table table tr td:nth-child(even) {
    color: #666;
}

table {
    border: none;
    border-collapse: collapse;
}

.zy-btn1 {
    transition: all 0.3s;
    background-color: #15b8dd!important;
}

.zy-btn1:hover {
    cursor: pointer;
    background-color: #12a5c6!important;
}

.zy-btn2 {
    transition: all 0.3s;
    background-color: #3686e7!important;
    border-color: #3686e7!important;
}

.zy-btn2:hover {
    cursor: pointer;
    background-color: #2c75cc!important;
}

.zy-table-blue tbody tr td.tols i {
    color: #3686e7;
}