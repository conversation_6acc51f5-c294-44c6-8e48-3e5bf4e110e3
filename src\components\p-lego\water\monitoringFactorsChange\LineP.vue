<!-- @format -->

<template>
    <p-line
        :data="data"
        :option="option"
        :config="{
            color: ['#d8cf3a', '#20cb44'],
            showFillArea: true,
            showLabel: true
        }"
        style="width: 1270px; height: 250px"
    ></p-line>
</template>

<script>
export default {
    props: {
        arr: {
            type: Array
        },
        day: {
            type: String
        },
        //  WRW:{
        //   type:Array,
        //   default:['001']
        // },
        WRWMC: {
            type: Array,
            default: () => []
        },
        WRWObj: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            data: {
                xAxis: [],
                series: []
            },
            option: {
                legend: {
                    show: false
                },
                grid: {
                    left: '2%',
                    right: '4%',
                    top: '20%',
                    bottom: '8%'
                },
                yAxis: {
                    name: '浓度',
                    nameTextStyle: {
                        color: '#fff',
                        lineHeight: 35
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#0b5f72'
                        }
                    },
                    axisLabel: {
                        color: '#fff'
                    }
                },
                xAxis: {
                    boundaryGap: true,
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#0b5f72'
                        }
                    },
                    axisLabel: {
                        color: '#fff',
                        rotate: 50,
                        formatter: (val) => {
                            let str = '';
                            if (this.$props.day == 'R') {
                                str = this.$dayjs(val).format('MM-DD');
                            } else {
                                str = this.$dayjs(val).format('MM-DD HH');
                            }

                            return str;
                        }
                    }
                },
                series: [
                    {
                        itemStyle: {
                            color: '#21bbe0'
                        },
                        smooth: false,
                        symbolSize: 10,
                        symbol: 'circle'
                    }
                ]
            }
        };
    },
    watch: {
        arr() {
            this.getData(this.WRWObj);
        },
        WRWObj(v) {
            this.getData(v);
        }
    },
    mounted() {
        this.getData(this.WRWObj);
    },
    methods: {
        getData(WRWObj) {
            console.log('map', WRWObj);
            this.data.series = [];
            let xAxis = [];
            let data = [];
            this.$props.arr.forEach((item, index) => {
                xAxis.push(item.jcsj || item.JCSJ);
            });
            // 按污染物名称匹配数据
            WRWObj.map((t, i) => {
                this.data.series.push({ data: [], name: t.label });
                this.arr.map((item) => {
                    if (item.WRWMC == t.label)
                        this.data.series[i].data.push(item.JCZ);
                });
            });

            this.data.xAxis = [...xAxis];
            this.data.xAxis = [...new Set(this.data.xAxis)];
        }
    }
};
</script>
