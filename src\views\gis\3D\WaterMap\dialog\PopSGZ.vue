<!-- @format -->

<template>
    <div
        class="yy-mapalert1"
        style="
            height: 280px;
            width: 500px;
            margin-bottom: 0;
            transform: translate(-50%, 0);
        "
    >
        <div class="flx1 ac jb hdbox" style="margin-bottom: -10px">
            <h1 class="t1" style="width: 310px">
                {{ selectItem.CDMC
                }}<i class="yygkbtn">{{ selectItem.JGJBMC }}</i>
            </h1>
            <p class="time1">
                {{ selectItem.JCSJ ? selectItem.JCSJ.substring(0, 10) : '--' }}
            </p>
        </div>
        <img
            src="../css/images/yy-alinebg.png"
            alt=""
            style="width: 460px"
            class="yy-alinebg"
        />
        <div class="bdbox">
            <div class="flx1 ac mbkind">
                <p class="f1" :class="bgCls">
                    {{ selectItem.SZLB ? selectItem.SZLB : '--' }}
                </p>
                <div class="rfont">
                    <p class="p1">
                        目标：{{ selectItem.ZXBZMC ? selectItem.ZXBZMC : '--' }}
                    </p>
                    <p class="p1">
                        超标因子及超标倍数：
                        {{ selectItem.CBXM ? selectItem.CBXM : '--' }}
                    </p>
                </div>
            </div>
        </div>

        <dl class="sw0630-dlbx2" style="margin-top: 5px">
            <dd>
                <ul>
                    <li
                        style="width: 30%; margin: 10px 10px 0 0"
                        v-for="(item, index) of arrYZ"
                        :key="index"
                        :class="item.borderCls"
                    >
                        <h1 :class="item.bgCls" style="padding: 0 10px">
                            {{ item.name }}
                        </h1>
                        <p :class="item.colorCls">
                            {{ item.value }} / {{ item.SZLB }}
                        </p>
                    </li>
                </ul>
            </dd>
        </dl>
    </div>
</template>

<script>
import { getScStationSzxx } from '@/api/gis/3D/WaterMap/index';
export default {
    props: ['selectItem', 'layerID'],
    data() {
        return {
            arrYZ: [
                {
                    name: 'pH值',
                    value: '-',
                    SZLB: '-',
                    SZLBBS: '0',
                    bgCls: 'water0',
                    colorCls: 'water-color0',
                    borderCls: 'water-border0'
                },
                {
                    name: '溶解氧',
                    value: '-',
                    SZLB: '-',
                    SZLBBS: '0',
                    bgCls: 'water0',
                    colorCls: 'water-color0',
                    borderCls: 'water-border0'
                },
                {
                    name: '高锰酸盐指数',
                    value: '-',
                    SZLB: '-',
                    SZLBBS: '0',
                    bgCls: 'water0',
                    colorCls: 'water-color0',
                    borderCls: 'water-border0'
                },
                {
                    name: '氨氮',
                    value: '-',
                    SZLB: '-',
                    SZLBBS: '0',
                    bgCls: 'water0',
                    colorCls: 'water-color0',
                    borderCls: 'water-border0'
                },
                {
                    name: '总磷',
                    value: '-',
                    SZLB: '-',
                    SZLBBS: '0',
                    bgCls: 'water0',
                    colorCls: 'water-color0',
                    borderCls: 'water-border0'
                },
                {
                    name: '总氮',
                    value: '-',
                    SZLB: '-',
                    SZLBBS: '0',
                    bgCls: 'water0',
                    colorCls: 'water-color0',
                    borderCls: 'water-border0'
                }
            ]
        };
    },
    mounted() {
        setTimeout(() => {
            this.getData();
        }, 200);
    },
    methods: {
        getData() {
            let param2 = {
                pointCode: this.selectItem.DWDM
            };
            getScStationSzxx(param2).then((res) => {
                if (res.data) {
                    for (let obj of res.data) {
                        for (let item of this.arrYZ) {
                            if (obj.FXXMMC == item.name) {
                                item.value = obj.BCJGBS;
                                item.SZLB = obj.SZLB;
                                item.SZLBBS = obj.SZLBBS || '0';
                                item.bgCls = 'water' + item.SZLBBS;
                                item.colorCls = 'water-color' + item.SZLBBS;
                                item.borderCls = 'water-border' + item.SZLBBS;
                            }
                        }
                    }
                }
            });
        },

        closeClick() {
            this.$emit('close', '自动站');
        }
    },
    computed: {
        bgCls() {
            let obj = {};
            obj['water' + this.selectItem.SZLBBS] = true;
            return obj;
        }
    }
};
</script>

<style></style>
