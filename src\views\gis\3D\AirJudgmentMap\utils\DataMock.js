/** @format */

import dayjs from 'dayjs';
import { result } from 'lodash';
import Mock from 'mockjs';

// debugger;
Mock.setup({ timeout: 2000 });

//空气站点
Mock.mock(RegExp('/air/gis/getZbzdinfoList' + '.*'), 'get', (option) => {
    const { data } = Mock.mock({
        'data|5-10': [
            {
                SSQX: '421303',
                SFYJ: 0,
                YJID: '',
                O3: 82,
                PM25: 18,
                CDMC: '市委党校',
                CO: 0.6,
                WD: 0,
                SYWRW: '',
                NO2: 2,
                JCSJ: '2023-07-10 13:00:00',
                KQZLLB: '优',
                CDDM: '421300051',
                'JGJB|1': ['1', '2', '3'],
                SSXZQ: '随州市',
                SO2: 7,
                'AQI|1-300': 1,
                PM10: 31,
                JD: 0,
                JGJBNAME: '国控'
            }
        ]
    });

    for (let item of data) {
        item.JD = 120.6144750928243 + (Math.random() - 0.5) * 0.03;
        item.WD = 30.975579857498687 + (Math.random() - 0.5) * 0.03;
    }

    let result = {
        data: data,
        status: '000'
    };

    console.log('空气站数据接口');
    console.log(result);

    return result;
});

//工业源
Mock.mock(RegExp('/air/gis/getSqwryxxList' + '.*'), 'get', (option) => {
    const { data } = Mock.mock({
        'data|10-20': [
            {
                SSQX: '420581',
                FRMC: '@cname',
                SFZDQY: '0',
                QYGM: '',
                WD: '30.250528',
                HBRLXDH: '15926699138',
                JCSJ: dayjs(new Date()).format('YYYY-MM-DD HH'),
                WRYBH: '1653308345554038957056',
                'SFCB|1': ['1', '0'],
                QYMC: '泰山石膏有限公司',
                HYDM: 'F',
                QYDZ: '',
                HBLXR: '艾斌',
                JD: '111.537778',
                SFYC: '0',
                HYLX: 'F-批发和零售业'
            }
        ]
    });

    for (let item of data) {
        item.JD = 120.6144750928243 + (Math.random() - 0.5) * 0.05;
        item.WD = 30.975579857498687 + (Math.random() - 0.5) * 0.05;
    }

    console.log('工业源数据接口');
    console.log(result);

    return {
        data: data,
        status: '000'
    };
});

//污染源-数量统计
Mock.mock(RegExp('/air/gis/getAirSourceTotal' + '.*'), 'get', (option) => {
    const { data } = Mock.mock({
        data: [
            {
                name: '工业源',
                fiterType: 'GYY',
                'total|1-100': 1 //生成1 到20的随机数
            },
            {
                name: '扬尘源',
                fiterType: 'YCY',
                'total|1-100': 1 //生成1 到20的随机数
            }
        ]
    });

    let result = {
        data: data,
        status: '000'
    };

    console.log('污染源数量统计接口');
    console.log(result);

    return result;
});
