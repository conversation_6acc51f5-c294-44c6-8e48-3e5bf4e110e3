@charset "utf-8";

/* -- Reset -- */

body, ul, ol, dl, dd, h1, h2, h3, h4, h5, h6, p, form, input, textarea, select, button { margin: 0; padding: 0; font: 12px 'Microsoft YaHei', SimSun, Arial, Helvetica, sans-serif; }
ul, ol { list-style-type: none; }
table { border-collapse: collapse; border-spacing: 0; }
input, button, textarea, select { -webkit-appearance: none; appearance: none; box-sizing: border-box; border-radius: 0; padding: 0; margin: 0; border: none; outline: none; }
caption, th, td { font-weight: normal; vertical-align: middle; }
button.hidefocus::-moz-focus-inner { border: none; }
[hidefocus], summary { outline: 0; }
input::-ms-clear { display: none; }
textarea { resize: none; }
a img { border: none; }
a.hidefocus, a:active, a:focus, .hidefocus:focus, input[type=button], input[type=submit] { outline: none; -moz-outline: none; }
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary { display: block; }
img { border: 0 none; }
em, i { font-style: normal; }
a { text-decoration: none; }
a:link { color: #4F14F7; text-decoration: none; }
a:visited { color: #551a8b; }
a:hover { color: #FF9900; text-decoration: underline; }
a:active { color: #cc0000; }

/* -- flex布局-- */
.flx1 { display: flex; }
.flx1.ac { align-items: center; }
.flx1.jc { justify-content: center; }
.flx1.jb { justify-content: space-between; }
.flx1.ja { justify-content: space-around; }
.flx1.start { justify-content: flex-start; }
.flx1.end { justify-content: flex-end; }
.flx1.ev { justify-content: space-evenly; }

/* -- Common style -- */
.dn { display: none; }
.db { display: block; }
.fl { float: left; }
.fr { float: right; }
.rel { position: relative; }
.abs { position: absolute; }
.gap { height: 10px; width: 100%; }
.auto { margin: 0 auto; }
.clear { clear: both; }
.clearfix:after { content: "\200B"; display: block; height: 0; clear: both; }
.clearfix { *zoom: 1; }

/*栅格系统*/
.raster { padding: 0 10px; margin: 0 auto; }
.row { margin: 0 -5px; }
.row:before, .row:after { content: ''; display: table; clear: both; }
.col { min-height: 1px; float: left; position: relative; padding: 0 5px; box-sizing: border-box; }
.col-1 { width: 8.333333%; }
.col-2 { width: 16.666666%; }
.col-3 { width: 25%; }
.col-4 { width: 33.333333%; }
.col-5 { width: 41.666666%; }
.col-6 { width: 50%; }
.col-7 { width: 58.333333%; }
.col-8 { width: 66.666666%; }
.col-9 { width: 75%; }
.col-10 { width: 83.333333%; }
.col-11 { width: 91.666666%; }
.col-12 { width: 100%; }

/* 按钮 */
.btn { display: inline-block; border: none; outline: none; cursor: pointer; }

/* 图标 */
.icon { display: inline-block; cursor: pointer; }

/* 遮罩层 */
.mask { position: absolute; width: 100%; height: 100%; top: 0; left: 0; background: black; opacity: 0.8; filter: alpha(opacity=80); z-index: 999; display: none; }



