/** @format */

import axios from '_u/ajaxRequest';

const BASE_URL = ServerGlobalConstant.dataUrl;

//公共代码集
export const getCommonCodesFromCache = (data) => {
    return axios.request({
        method: 'post',
        url:
            BASE_URL +
            '/platform/system/commoncodecontroller/getCommonCodesFromCache',
        data: data
    });
};

// 通知公告接口
export const queryTzgg = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/workplatform/workplatformcontroller/queryTzgg',
        data: data
    });
};

// 今日学习情况接口
export const getTodayLearnInfo = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/home/<USER>/getTodayLearnInfo',
        data: data
    });
};

// 月度学习情况接口
export const getMonthLearnInfo = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/home/<USER>/getMonthLearnInfo',
        data: data
    });
};

// 提醒
export const getWarningInfo = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/home/<USER>/getWarningInfo',
        data: data
    });
};

// 关闭提醒
export const closeWarningInfo = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/home/<USER>/closeWarningInfo',
        data: data
    });
};

// 待办任务
export const getWaitHandleTaskCount = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/home/<USER>/getWaitHandleTaskCount',
        data: data
    });
};
