<!-- @format -->

<template>
    <div class="calendar">
        <table>
            <tr class="calendar-head">
                <th><span>日</span></th>
                <th><span>一</span></th>
                <th><span>二</span></th>
                <th><span>三</span></th>
                <th><span>四</span></th>
                <th><span>五</span></th>
                <th><span>六</span></th>
            </tr>
            <tr v-for="item in arrDate" :key="item">
                <td v-for="v in item" :key="v.val" style="position: relative">
                    <div
                        class="calendar-item-date"
                        :class="v.trClass"
                        v-bind:style="{
                            background: v.color,
                            color: v.fontColor
                        }"
                    >
                        <p>{{ v.val }}</p>
                        <p>{{ v.aqi }}</p>
                    </div>
                    <!-- <span class="hover-item" v-show="v.show"
                                ><i
                                    class="hover-item-a"
                                    v-bind:style="{
                                        background: v.color
                                    }"
                                ></i>
                                AQI:{{ v.aqi }}<br />
                                主要污染物:
                                {{ renderValue(v.itemObj, v.mainPollution) }}
                            </span> -->
                </td>
            </tr>
        </table>
    </div>
</template>

<script>
import util from './util';
import dayjs from 'dayjs';
//核心，根据月份 返回一组数据，6*7 = 42 的日期
export default {
    name: 'airCalendar',
    props: {
        aqiData: {
            type: Array,
            default() {
                return [];
            }
        },
        date: {
            type: String,
            default: ''
        }
    },
    watch: {
        date: 'getData',
        aqiData: 'getData'
    },
    data() {
        return {
            timeObj: {
                year: '',
                month: '',
                day: ''
            },
            arrDate: []
        };
    },
    mounted() {
        //获取日期数组
        this.getData();
        // this.getDateArr(this.date);
    },
    methods: {
        getData() {
            this.getDateArr(this.date);
        },
        renderValue(item, pollutionName) {
            switch (pollutionName) {
                case 'O3_8H':
                case 'O3_8h':
                    pollutionName = 'O3';
                    break;
            }
            if (pollutionName) {
                let val = item[pollutionName] || '';
                if (pollutionName == 'CO') {
                    return this.replacePltName(pollutionName) + `(${val}mg/m³)`;
                } else {
                    return this.replacePltName(pollutionName) + `(${val}ug/m³)`;
                }
            } else {
                return '无';
            }

            /*v.mainPollution == 'O3_8H' ? 'O₃' : replacePltName(v.mainPollution);*/
        },
        replacePltName(value) {
            value = value || '';
            let labelObj = {
                'PM2.5': 'PM₂.₅',
                PM25: 'PM₂.₅',
                PM10: 'PM₁₀',
                O3: 'O₃',
                NO2: 'NO₂',
                SO2: 'SO₂'
            };
            return value.replace(/[A-Z]+[0-9]+\.*[0-9]*/g, function () {
                return labelObj[arguments[0]] || arguments[0];
            });
        },
        getDateArr(dateStr) {
            //设置日期
            this.getNowDate(dateStr);
        },
        renderDate() {
            //获取这个月多少天
            let nowDayNum = this.getDayNum(this.timeObj);
            //获取前一个月多少天
            let prevMonthDayNum = this.getDayNum(
                this.getDateOther(this.timeObj, true)
            );
            //获取这个月星期几，然后往数组里面加上上个月的尾巴数字，后面加上42-这个月-上个月添加的
            let day = new Date(
                this.timeObj.year,
                this.timeObj.month,
                1
            ).getDay();

            // getAqiCalendar(this.timeObj, this.cityCode, this.qylx).then(
            //     (res) => {
            let aqiData = this.aqiData;
            //上个月加上天数
            this.getFullTimeArr(prevMonthDayNum, nowDayNum, day, aqiData);
            //     }
            // );
        },
        //上一年，下一年
        prevYear(isPrev) {
            if (isPrev) {
                this.timeObj.year = parseInt(this.timeObj.year) - 1;
            } else {
                this.timeObj.year = parseInt(this.timeObj.year) + 1;
            }
            this.renderDate();
        },
        //上个月，下个月
        prevNext(isPrev) {
            let date = this.getDateOther(this.timeObj, isPrev);
            this.timeObj.year = date.year;
            this.timeObj.month = date.month;
            this.timeObj.day = date.day;

            this.renderDate();
        },
        //getFullTimeArr  返回日历的42个数组,(上个月天数，这个月天数，星期几, aqi的污染系数）
        getFullTimeArr(prevMonthDayNum, nowDayNum, day, aqiArr) {
            console.log(aqiArr);

            let arrDate = [];
            //处理星期天的情况
            if (day == 0) {
                day = 7;
            }
            let num = day;
            let lastMonthNum = 42 - num - nowDayNum;
            //上个月数据
            while (num != 0) {
                let obj = {
                    val: prevMonthDayNum - num + 1,
                    trClass: 'grep',
                    fontColor: '',
                    color: ''
                };
                num--;
                arrDate.push(obj);
            }

            // 获取当前颜色主题
            let theme = window.localStorage.getItem('themeType') || 'dark';

            //当月天数
            for (let i = 1; i <= nowDayNum; i++) {
                let aqiLevelColor = '';
                let mainPollution = '';
                let aqiVal = '';
                let itemObj = {};
                aqiArr.forEach((item) => {
                    if (parseInt(item.JCR) == i) {
                        aqiLevelColor = util.getLevelPollution(
                            'AQI',
                            item.AQI
                        ).color;
                        mainPollution = item.mainPollution;
                        aqiVal = item.AQI;
                        itemObj = item;
                    }
                });
                let obj = {};
                if (aqiLevelColor) {
                    //环境判断
                    obj = {
                        val: i,
                        trClass: '',
                        fontColor: aqiVal ? '#EFE5DE' : '#716F6F',
                        color: aqiLevelColor,
                        show: false,
                        aqi: aqiVal,
                        mainPollution: mainPollution,
                        itemObj: itemObj
                    };
                } else {
                    obj = {
                        val: i,
                        trClass: '',
                        fontColor: theme === 'light' ? '#716F6F' : '#fff',
                        color: theme === 'light' ? '#F4F4F4' : 'rgba(0,0,0,.1)',
                        show: false,
                        aqi: aqiVal,
                        mainPollution: mainPollution,
                        itemObj: itemObj
                    };
                }

                arrDate.push(obj);
            }
            //下个月天数
            for (let i = 1; i <= lastMonthNum; i++) {
                let obj = {
                    val: i,
                    trClass: 'grep',
                    color: '',
                    show: false,
                    aqi: '',
                    itemObj: {}
                };
                arrDate.push(obj);
            }

            //转换格式,一维数组转换为二维数组
            let newArrDate = new Array(Math.ceil(arrDate.length / 7));
            for (let i = 0; i < newArrDate.length; i++) {
                newArrDate[i] = [];
            }
            for (let j = 0; j < arrDate.length; j++) {
                newArrDate[parseInt(j / 7)][j % 7] = arrDate[j];
            }

            this.arrDate = newArrDate;
        },
        //设置日期
        getNowDate(dateStr) {
            dateStr = dayjs(dateStr).format('YYYY-MM');
            // this.timeObj = {
            //     year: '',
            //     month: '',
            //     day: '01'
            // };
            // getAqiCalendarInit(this.timeObj, this.cityCode, this.qylx).then(
            //     (res) => {
            //         this.timeObj = {
            //             year: res.data.selectedDate.split('-')[0],
            //             month:
            //                 parseInt(res.data.selectedDate.split('-')[1]) - 1,
            //             day: 1
            //         };
            //         //渲染日期
            //         this.renderDate();
            //     }
            // );
            let date = new Date(dateStr.replace(/-/g, '/'));
            // let date = new Date();
            this.timeObj = {
                year: date.getFullYear(),
                month: date.getMonth(),
                day: date.getDate()
            };
            this.renderDate();
        },
        //获取一个月的时间
        getDayNum(date) {
            let dayCount = new Date(date.year, date.month + 1, 0).getDate();
            return dayCount;
        },
        //获取其他日期
        getDateOther(date, isPrev) {
            // isPrev:true:上一个月; false: 下一个月
            let year = date.year; // 获取当前日期的年份
            let month = isPrev
                ? parseInt(date.month) - 1
                : parseInt(date.month) + 1; // 前一个月
            let day = date.day; // 获取当前日期的日
            if (month === -1) {
                year = parseInt(year) - 1;
                month = 11;
            }
            if (month === 12) {
                year = parseInt(year) + 1;
                month = 0;
            }
            let days2 = new Date(year, month, 0).getDate(); // //获取上或者下个月天数
            if (day > days2) {
                day = days2;
            }
            return { year: year, month: month, day: day };
        }
    }
};
</script>

<style scoped lang="scss">
.calendar {
    table {
        color: white;
        width: 100%;
        height: 350px;

        tr {
            border-top: none;
        }
        tr:nth-child(2n) {
            background: none;
        }
        tr td {
            padding: 4px;
            border: none;
        }
        tr td span {
            display: inline-block;
            width: 100%;
            height: 34px;
            line-height: 34px;
            text-align: center;
            -webkit-border-radius: 50%;
            -moz-border-radius: 50%;
            border-radius: 50%;
            font-size: 14px;
        }
    }

    & .calendar-head {
        border-bottom: 1px solid var(--el-border-color);
        td,
        th {
            padding: 0;
        }
        td span,
        th span {
            padding: 0px;
            // color: var(--font-color);
            // color: #acacac;
        }
    }
    &-item-date {
        height: 42px;
        border-radius: 4px;
        width: 45px;
        border: 1px solid var(--el-border-color);
    }
    &-item-date p {
        text-align: center;
        font-size: 14px;
        line-height: 19px;
        margin: 0;
    }

    .hover-item {
        display: inline-block;
        position: absolute;
        background: #27474e;
        width: auto;
        min-width: 180px;
        height: auto;
        border-radius: 10%;
        top: -11px;
        left: 51px;
        opacity: 0.85;
        line-height: 24px;
        z-index: 1000;
        text-align: left;
    }
    .hover-item-a {
        display: inline-block;
        border-radius: 10px;
        width: 10px;
        height: 10px;
    }
}
/* 无数据字体颜色 */
.lightTheme .calendar-item-date.grep {
    color: #6c859d;
}
.darkTheme .calendar-item-date.grep {
    color: #dddddd;
}
</style>
