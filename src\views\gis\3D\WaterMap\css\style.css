
input, button, textarea, select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    box-sizing: border-box;
    border-radius: 0;
    padding: 0;
    margin: 0;
    border: none;
    outline: none;
}

.flx1 {
    display: flex;
}
.flx1.ac {
    align-items: center;
}
.flx1.jc {
    justify-content: center;
}

.yy0812-tctit {
    font-size: 16px;
    color: #fff;
    font-weight: bold;
    text-align: center;
    height: 40px;
    line-height: 40px;
}

.sw0811-dlbx5{border: 1px solid #00b8fb; border-radius: 20px; background: linear-gradient(rgba(9,59,88,.7),rgba(6,85,90,.7));}
.sw0811-dlbx5 dt{height: 40px; background: rgba(0,167,236,.1); border-radius: 20px 20px 0 0;}
.sw0811-dlbx5 dt strong{font-size: 16px; color: #fff;}
.sw0811-dlbx5 dt strong.ic1{background: url(./images/sw0811_ic2.png) no-repeat 10px center; padding-left: 25px;}

.sw0811-dlbx5.yy0812-dlbx5 {
    background: linear-gradient(rgba(9, 59, 88, 0.9), rgba(6, 85, 90, 0.9));
}
.sw0811-dlbx5.type2 dt{background: rgba(73,165,230,.5);}


.sw0811-check1{display: flex; align-items: center;}
.sw0811-check1 input[type="checkbox"]{background: url(./images/sw0811_checkic.png) no-repeat; width: 16px; height: 16px;}
.sw0811-check1 input[type="checkbox"]:checked{background-image: url(./images/sw0811_checkicon.png);}
.sw0811-check1 span,
.sw0811-check1 strong{font-size: 16px; color: #fff; padding-left: 14px;}
.sw0811-checkbox1 .sw0811-check1 + .sw0811-check1 {
    margin-top: 7px;
}

.sw0811-checkbox1.yy0812-checkbox1 {
    padding-left: 15px;
    padding-right: 5px;
}
.sw0811-checkbox1 {
    padding: 10px 0 10px 20px;
}


.yy-tckzbox {
    padding: 20px 15px;
}

.yy-tckzbox li {
    background: url(./images/yy-sketch-off.png) no-repeat 0 center;
    padding-left: 43px;
    font-size: 16px;
    color: #fff;
    cursor: pointer;
    padding-left: 55px;
}

.yy-tckzbox li + li {
    margin-top: 10px;
}

.yy-tckzbox li.on {
    background-image: url(./images/yy-sketch-on.png);
}

