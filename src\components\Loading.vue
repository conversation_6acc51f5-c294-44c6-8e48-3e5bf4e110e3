<!-- @format -->

<template>
    <div class="page-loader loading-box">
        <div class="loader-section el-loading-spinner">
            <img src="../assets/images/loading.gif" alt="正在加载" />
            <p class="text" v-show="text">{{ text }}</p>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Loading',
    props: {
        animation: {
            type: String,
            default: 'line-scale-pulse-out'
        },
        text: {
            type: String,
            default: ''
        }
    },
    data() {
        return {};
    }
};
</script>
<style lang="scss">
.loading-box {
    position: fixed;
    z-index: 10000;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}
</style>
