<!-- @format -->
<!-- 业务主要入口 -->
<template>
    <div>
        <!-- 左侧 -->
        <BasicLeftPart></BasicLeftPart>

        <!-- 搜索框 -->
        <SearchTool
            class="gis-search"
            :arrSearch="arrSearch"
            :map="map"
            dock="left"
        ></SearchTool>

        <!-- 时间轴 -->
        <TimeLine
            class="footer"
            ref="childTimeLine"
            @dateChange="dateChangeHandle"
        ></TimeLine>

        <Map3Danalysis :map="map" :markerParam="markerParam3"></Map3Danalysis>

        <Map3DFillExtrusion
            :map="map"
            :markerParam="markerParam4"
        ></Map3DFillExtrusion>

        <Map3DMarkerType
            :map="map"
            :markerParam="markerParam"
        ></Map3DMarkerType>

        <!-- 图例 -->
        <LegendTool
            class="gis-legend"
            :arrLegend="arrLegend"
            :expand="true"
        ></LegendTool>

        <div class="topCon">
            <el-checkbox
                v-model="showFX"
                label="大气站点周边分析"
                size="large"
            />

            <el-checkbox
                v-model="show3D"
                label="行政区3D拉升统计"
                size="large"
            />
        </div>

        <div style="position: absolute; top: 100px; right: 100px">
            <el-radio-group v-model="funcType" @change="radioButtonChange">
                <el-radio-button
                    :label="name"
                    v-for="(name, index) of arrFuncType"
                    :key="index"
                />
            </el-radio-group>
        </div>
    </div>
</template>
<script>
import BasicLeftPart from './BasicLeftPart';
import SearchTool from '@/components/gis/3D/SearchTool';
import TimeLine from '@/components/gis/common/TimeLineDay.vue';
import LegendTool from '@/components/gis/3D/LegendTool';

import Map3DMarkerType from '@/components/gis/3D/Map3DMarkerType';
import Map3Danalysis from '@/components/gis/3D/Map3Danalysis';
import Map3DFillExtrusion from '@/components/gis/3D/Map3DFillExtrusion';

import { getPointHourMaxDate } from '@/api/gis/3D/BasicFramework/index';

export default {
    name: 'BasicMain',
    created() {},
    data() {
        return {
            arrSearch: [], //用于搜索框的数组
            arrLegend: [],

            showPanel: false,

            arrFuncType: ['微站', '大气站点', '大气站点2', '水质站点'],
            funcType: '微站',

            //标签的参数
            markerParam: {
                pointData: null,
                type: '微站', //大气站点2
                showAno: false, //是否显示名称
                FJZS: true, //分级展示
                FJZOOM: 12,
                yz: 'AQI', //大气才有的字段
                visible: true
            },

            showFX: false,

            markerParam3: {
                centralPoint: null,
                centraltype: '大气站点3',
                yz: 'AQI',
                showAno: true,
                type: 'SFX', //SFX  ZBFX
                visible: true,
                angle: 45,
                distanc: [1, 3, 5],
                filterData: null
            },

            show3D: false,
            markerParam4: {
                centralPoint: null,
                visible: false
            }
        };
    },
    props: ['map'],
    unmounted() {},
    components: {
        BasicLeftPart,
        SearchTool,
        TimeLine,
        LegendTool,
        Map3DMarkerType,
        Map3Danalysis,
        Map3DFillExtrusion
    },
    mounted() {
        //搜索数据
        this.arrSearch = [
            {
                name: '洞庭湖',
                type: '自动站',
                JD: 112.95,
                WD: 29.2961
            },
            {
                name: '汨罗江',
                type: '自动站'
            },
            {
                name: '污水处理厂',
                type: '企业'
            }
        ];

        //图例数组
        this.arrLegend = [
            {
                title: '点位类型',
                data: [
                    { name: '断面', url: 'tuli1.png' },
                    { name: '自动站', url: 'tuli2.png' }
                ]
            },
            {
                title: '水体类型',
                data: [
                    { name: '河流', url: 'tuli3.png' },
                    { name: '饮用水', url: 'tuli4.png' },
                    { name: '湖库', url: 'tuli5.png' }
                ]
            },
            {
                title: '控制级别',
                data: [
                    { name: '国控', url: 'tuli6.png' },
                    { name: '省控', url: 'tuli7.png' },
                    { name: '市控', url: 'tuli8.png' },
                    { name: '其他', url: 'tuli9.png' }
                ]
            },
            {
                title: '功能区',
                data: [
                    { name: 'I~II类', url: 'tuli10.png' },
                    { name: 'Ⅲ类', url: 'tuli11.png' },
                    { name: 'Ⅳ类', url: 'tuli12.png' },
                    { name: 'V类', url: 'tuli3.png' }
                ]
            }
        ];

        this.initPage();
    },
    methods: {
        //页面初始化
        initPage() {
            if (!this.map) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.initEvent();

            this.initTimeLine();

            setTimeout(() => {
                this.resetData();

                this.markerParam4.pointData = [
                    {
                        XH: '66',
                        JCZ: '30',
                        JCZ_DW: '%',
                        PXH: '',
                        XZQMC: '黄石港区',
                        ZBDM: 'STHJZL_STHJZS',
                        XZQDM: '330700',
                        JD: '119.642701745',
                        WD: '29.0820617950001',
                        ZBRQ: '2022-01-31 10:00:00'
                    },
                    {
                        XH: '66',
                        JCZ: '72.2',
                        JCZ_DW: '%',
                        PXH: '',
                        XZQMC: '西塞山区',
                        ZBDM: 'STHJZL_STHJZS',
                        XZQDM: '330703',
                        JD: '119.79577581961044',
                        WD: '29.15155113185252',
                        ZBRQ: '2022-01-31 10:00:00'
                    },
                    {
                        XH: '66',
                        JCZ: '83.3',
                        JCZ_DW: '%',
                        PXH: '',
                        XZQMC: '下陆区',
                        ZBDM: 'STHJZL_STHJZS',
                        XZQDM: '330726',
                        JD: '119.8969914242359',
                        WD: '29.52256047516492',
                        ZBRQ: '2022-01-31 10:00:00'
                    },
                    {
                        XH: '66',
                        JCZ: '50',
                        JCZ_DW: '%',
                        PXH: '',
                        XZQMC: '铁山区',
                        ZBDM: 'STHJZL_STHJZS',
                        XZQDM: '330781',
                        JD: '119.52013408165851',
                        WD: '29.280386648761425',
                        ZBRQ: '2022-01-31 10:00:00'
                    },
                    {
                        XH: '66',
                        JCZ: '81',
                        JCZ_DW: '%',
                        PXH: '',
                        XZQMC: '阳新县',
                        ZBDM: 'STHJZL_STHJZS',
                        XZQDM: '330783',
                        JD: '120.37058538993517',
                        WD: '29.23525201101662',
                        ZBRQ: '2022-01-31 10:00:00'
                    },
                    {
                        XH: '66',
                        JCZ: '95',
                        JCZ_DW: '%',
                        PXH: '',
                        XZQMC: '大冶市',
                        ZBDM: 'STHJZL_STHJZS',
                        XZQDM: '330702',
                        JD: '119.50583275150933',
                        WD: '28.98004741860937',
                        ZBRQ: '2022-01-31 10:00:00'
                    }
                ];
            }, 1000);
        },

        //加载地图相关资源、以及初始化地图操作相关的图层等
        createMap() {},

        //定义地图事件
        initEvent() {
            let self = this;
            // this.map.on('zoomend', (evt) => {});
        },

        //获取起报最新时间
        getPointHourMaxDate() {
            getPointHourMaxDate({}).then((res) => {
                //  this.curentTime = res.data.JCSJ.substring(0, 13);
            });
        },

        //前端右侧面板
        showPanleChangeHandle(showPanel) {
            this.showPanel = showPanel;
        },

        //地图点击事件,触发事件，与前端进行交互
        pointClickHandle(type, item) {
            this.$emit('pointClick', type, item);
        },

        initTimeLine() {
            // TimeLineHour2 初始方式
            // let dd = '2022-05-21 12';
            // let dd2 = '2022-05-21 12';
            // let option = {
            //     initDate: dd, //初始时间
            //     selectDate: dd2, //初始选中时间
            //     days: -3,
            //     gap: 1,
            //     labelShow: true,
            //     showDatePick: true,
            //     showPlay: true,
            //     type: 'hour'
            // };
            // this.$refs.childTimeLine.initTimeLine(option);
            //TimeLineMonthMultiple 配置
            // let option = {
            //     lastDate: '2022-04',
            //     selectDate: '2022-04',
            //     startDate: '2019-05',
            //     nums: -4,
            //     gap: 1,
            //     labelShow: true,
            //     showDatePick: true,
            //     showPlay: true,
            //     interVal: 3,
            //     type: 'month'
            // };
            // this.$refs.childTimeLine.initTimeLine(option);
            //TimeLineQuarterMultiple 配置
            // let option = {
            //     lastDate: '2022-02', // 二季度
            //     selectDate: '2022-02',
            //     startDate: '2019-02',
            //     nums: -4,
            //     gap: 1,
            //     labelShow: true,
            //     showDatePick: true,
            //     showPlay: true,
            //     interVal: 3,
            //     type: 'quarter'
            // };
            // this.$refs.childTimeLine.initTimeLine(option);
            //年 TimeLineYear
            // let option = {
            //     lastDate: '2022',
            //     selectDate: '2022',
            //     startDate: '2019',
            //     nums: -3,
            //     showDatePick: false,
            //     showPlay: true,
            //     interVal: 3,
            //     type: 'year'
            // };
            // this.$refs.childTimeLine.initTimeLine(option);
            //小时  TimeLineHour
            // let option = {
            //     lastDate: '2202-05-10 12',
            //     selectDate: '2202-05-10 12',
            //     showDatePick: true,
            //     showPlay: true
            // };
            // this.$refs.childTimeLine.initTimeLine(option);
            //月  TimeLineMonth
            // let option = {
            //     initDate: '2022-05',
            //     selectDate: '2022-05',
            //     showDatePick: true,
            //     showPlay: true
            // };
            // this.$refs.childTimeLine.initTimeLine(option);
            // 年 TimeLineYear
            // let option = {
            //     lastDate: '2022',
            //     selectDate: '2022',
            //     showDatePick: true,
            //     showPlay: true,
            //     nums: 5,
            //     interVal: 1
            // };
            // this.$refs.childTimeLine.initTimeLine(option);
            //自定义播放条
            // let option = [
            //     { SJ: '20210011' },
            //     { SJ: '20210012' },
            //     { SJ: '20210013' }
            // ];
            // this.$refs.childTimeLine.initTimeLine(option);
            //  日数据播放条
            let option = {
                selectTime: '2023-01-18'
            };
            this.$refs.childTimeLine.initTimeLine(option);
        },

        dateChangeHandle(selectDate, isPlay) {
            console.log(selectDate);
        },

        radioButtonChange() {
            this.resetData();
        },

        resetData() {
            this.markerParam.type = this.funcType;

            if (this.funcType == '水质站点') {
                this.markerParam.pointData = [
                    {
                        jcsj: '2022-02-10 12:00:00',
                        SSHL: '府河',
                        pointCode: 'HLHBWHFH20180301',
                        jgjb: '3',
                        level: '市控',
                        pointName: '东流港闸',
                        exceedItems: '化学需氧量,氨氮',
                        WD: '29.85561773025344',
                        ZXBZMC: 'Ⅲ类',
                        szlbbs: 3,
                        pointType: '河流',
                        sxmc: '',
                        lymc: '长江',
                        isExceed: '1',
                        areaCode: '420100',
                        areaName: '武汉市',
                        JGJB: '3',
                        waterQuality: 'Ⅲ类',
                        lakeName: '府河',
                        lakeCode: '651100',
                        cbbs: '',
                        HLID: '385',
                        JD: '115.11224748782826'
                    }
                ];
            } else {
                this.markerParam.pointData = [
                    {
                        O3: '10',
                        PM25: '200',
                        CDMC: '沈家营2',
                        CO: '5',
                        WD: '29.883',
                        SYWRW: '',
                        NO2: '100',
                        JCSJ: '2022-02-10 15:00:00',
                        CDDM: '420200051',
                        JGJB: '1',
                        SSXZQ: '黄石港区',
                        SO2: '100',
                        AQI: '12',
                        PM10: '100',
                        JD: '114.941',
                        JGJBNAME: '国控'
                    },
                    {
                        O3: '10',
                        PM25: '200',
                        CDMC: '沈家营',
                        CO: '5',
                        WD: '29.7452',
                        SYWRW: '',
                        NO2: '100',
                        JCSJ: '2022-02-10 15:00:00',
                        CDDM: '420200051',
                        JGJB: '1',
                        SSXZQ: '黄石港区',
                        SO2: '100',
                        AQI: '12',
                        PM10: '100',
                        JD: '115.0856',
                        JGJBNAME: '国控'
                    }
                ];
            }
        }
    },
    watch: {
        showFX(val) {
            if (val) {
                this.markerParam3.visible = true;
                this.markerParam3.centralPoint = [
                    {
                        O3: '10',
                        PM25: '200',
                        CDMC: '沈家营',
                        CO: '5',
                        WD: '30.235278',
                        SYWRW: '',
                        NO2: '100',
                        JCSJ: '2022-02-10 15:00:00',
                        CDDM: '420200051',
                        JGJB: '1',
                        SSXZQ: '黄石港区',
                        SO2: '100',
                        AQI: '12',
                        PM10: '100',
                        JD: '115.0625',
                        JGJBNAME: '国控'
                    }
                ];

                this.markerParam3.filterData = [
                    {
                        CDMC: '污染源1',
                        WD: '30.270699413850636',
                        JD: '115.07519119724566'
                    },
                    {
                        CDMC: '污染源2',
                        WD: '30.255046732127568',
                        JD: '115.05963578898059'
                    },
                    {
                        CDMC: '污染源3',
                        WD: '30.26677158069309',
                        JD: '115.07979386193068'
                    },
                    {
                        CDMC: '污染源4',
                        WD: '30.244413970175273',
                        JD: '115.09223165688388'
                    }
                ];
            } else {
                this.markerParam3.centralPoint = [];
                this.markerParam3.filterData = [];
                this.markerParam3.visible = false;
                this.map.flyTo(GisServerGlobalConstant.mapbox.mapBoxOption);
            }
        },

        show3D(val) {
            if (val) {
                this.map.setTerrain(null);
            } else {
                this.map.setTerrain({
                    map: 'mapbox-dem',
                    exaggeration: 1.5
                });
            }
            this.markerParam4.visible = val;
        }
    },
    computed: {}
};
</script>

<style>
.gis-search {
    position: absolute;
    top: 77px;
    left: 240px;
}

.gis-legend {
    position: absolute;
    left: 240px;
    bottom: 100px;
}

.footer {
    position: fixed;
    bottom: 10px;
    left: 235px;
    right: 40px;
}

.topCon {
    position: absolute;
    top: 100px;
    right: 500px;
    width: 320px;
    height: 35px;
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid #eee;
    padding: 0 10px;
}

.darkTheme .topCon {
    border: 1px solid #21497e;
    background-color: rgba(11, 34, 55, 0.8);
}
</style>
