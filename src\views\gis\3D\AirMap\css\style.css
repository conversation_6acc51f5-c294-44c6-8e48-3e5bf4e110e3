
.yy-mapalert1 {
    width: 585px;
    height: 404px;
    background-color: rgb(1, 48, 69, 0.7);
    border: solid 1px #02d9fd;
    border-radius: 20px;
    position: absolute;
    left: 0;
    bottom: 100%;
    /* margin-bottom: 20px; */
    padding: 0 20px 15px;
    box-sizing: border-box;
    box-shadow: 0 0 20px 7px rgba(0, 184, 251, 0.4) inset;
}
.yy-mapalert1 .hdbox {
    height: 50px;
    line-height: 50px;
    /*   background: url(../images/yy-alinebg.png) no-repeat left bottom; */
}
.yy-mapalert1 .t1 {
    font-size: 18px;
    color: #fff;
    font-weight: bold;
}
.yy-mapalert1 .time1 {
    font-size: 16px;
    color: #fff;
    padding-left: 24px;
    margin-top: 5px;
    background: url(./images/yy0816-time2.png) no-repeat 0 center;
}
.yy0816-acls {
    cursor: pointer;
    margin-right: 5px;
}
.yy-mapalert1 .mbkind {
    /* margin-top: 20px; */

    margin-top: 10px;
}
.yy-mapalert1 .mbkind .f1 {
    font-size: 20px;
    color: #fff;
    width: 94px;
    height: 54px;
    line-height: 54px;
    text-align: center;
    background-color: #73bb31;
    border-radius: 8px;
    margin-right: 20px;
}
.yy-mapalert1 .mbkind .rfont .p1 {
    font-size: 16px;
    color: #fff;
}
.yy-mapalert1 .mbkind .rfont .p1 + .p1 {
    margin-top: 10px;
}


.yygkbtn {
    display: inline-block;
    /* width: 61px; */
    padding: 0 15px;
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    color: #02d9fd;
    font-weight: bold;
    text-align: center;
    background: url(./images/yy0815-gkbg.png) no-repeat center center;
    background-size: 100% 100%;
    margin-left: 15px;
}



.sw0630-dlbx2 {
    height: 135px;
    display: flex;
}

.sw0630-dlbx2 dt {
    width: 230px;
    background: #f87c12;
    padding: 20px 0 20px 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
}

.sw0630-dlbx2 dt h1 {
    font-size: 24px;
    color: #fff;
    font-family: "DIN-Bold";
}

.sw0630-dlbx2 dt h2 {
    display: flex;
    align-items: center;
}

.sw0630-dlbx2 dt h2 i {
    font-size: 40px;
    font-family: "DIN-Bold";
    color: #fff;
}

.sw0630-dlbx2 dt h2 p {
    flex: 1;
    padding-left: 10px;
}

.sw0630-dlbx2 dt h2 p span {
    font-size: 18px;
    color: #fff;
    display: block;
}

.sw0630-dlbx2 dd {
    flex: 1;
}

.sw0630-dlbx2 dd ul {
    display: flex;
    flex-wrap: wrap;
    margin-top: -7px;
}

.sw0630-dlbx2 dd ul li {
    width: 92px;
    height: 54px;
    border: 1px solid #24bd5d;
    text-align: center;
    margin: 7px 0 0 7px;
}

.sw0630-dlbx2 dd ul li h1 {
    line-height: 28px;
    font-size: 18px;
    font-family: "DIN-Bold";
    background: #24bd5d;
    color: #fff;
}

.sw0630-dlbx2 dd ul li h1 sub {
    vertical-align: baseline;
    font-size: 14px;
}

.sw0630-dlbx2 dd ul li p {
    font-size: 18px;
    color: #24bd5d;
    font-family: "DIN-Bold";
    line-height: 26px;
}

.flx1.jb {
    justify-content: space-between;
}


.flx1.ac {
    align-items: center;
}

.flx1 {
    display: flex;
}



.mapbox-tooltip-tip-air .mapboxgl-popup-tip {
    pointer-events: none;
    /* background: rgba(0, 0, 0, 0) !important; */
}

.mapbox-tooltip-tip-air .mapboxgl-popup-content {
    background: rgba(0, 0, 0, 0);
    padding: 0;

    /* pointer-events: none; */
}
.mapbox-tooltip-tip-air {
    z-index: 999;
}


