<!-- @format -->

<template>
    <div>
        <div class="gap"></div>
        <div class="gap"></div>
        <div class="tz-pd-row jcb aic">
            <p class="gd-tit1"></p>
            <p class="gd-txt1">更新时间：{{ data.JCSJ }}</p>
        </div>
        <div class="gap h15"></div>
        <div class="gd-fwrap">
            <div
                class="gd-lfbox"
                :style="{
                    backgroundColor: getPollution('AQI', data.AQI).color
                }"
            >
                <p class="p1">AQI</p>
                <div class="gd-flx">
                    <div class="r1">{{ data.AQI }}</div>
                    <div class="r2">
                        <p>{{ getPollution('AQI', data.AQI).txt }}</p>
                        <p>首污：{{ data.firstPollution || '-' }}</p>
                    </div>
                </div>
                <p class="p2">
                    优良天数占比：<em>{{ data.yll }}%</em>
                </p>
            </div>
            <div class="gd-rtbox">
                <ul class="gd-rtboxul">
                    <li>
                        <p
                            class="p1"
                            :style="{
                                backgroundColor: getPollution('PM25', data.PM25)
                                    .color
                            }"
                        >
                            PM<em>2.5</em>
                        </p>
                        <p class="p2">{{ data.PM25 }}</p>
                    </li>
                    <li>
                        <p
                            class="p1"
                            :style="{
                                backgroundColor: getPollution('PM10', data.PM10)
                                    .color
                            }"
                        >
                            PM<em>10</em>
                        </p>
                        <p class="p2">{{ data.PM10 }}</p>
                    </li>
                    <li>
                        <p
                            class="p1"
                            :style="{
                                backgroundColor: getPollution('O3', data.O3)
                                    .color
                            }"
                        >
                            O<em>3</em>
                        </p>
                        <p class="p2">{{ data.O3 }}</p>
                    </li>
                    <li>
                        <p
                            class="p1"
                            :style="{
                                backgroundColor: getPollution('SO2', data.SO2)
                                    .color
                            }"
                        >
                            SO<em>2</em>
                        </p>
                        <p class="p2">{{ data.SO2 }}</p>
                    </li>
                    <li>
                        <p
                            class="p1"
                            :style="{
                                backgroundColor: getPollution('NO2', data.NO2)
                                    .color
                            }"
                        >
                            NO<em>2</em>
                        </p>
                        <p class="p2">{{ data.NO2 }}</p>
                    </li>
                    <li>
                        <p
                            class="p1"
                            :style="{
                                backgroundColor: getPollution('CO', data.CO)
                                    .color
                            }"
                        >
                            CO
                        </p>
                        <p class="p2">{{ data.CO }}</p>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
import util from '_u/util';
export default {
    name: 'airQuality',
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    AQI: 0,
                    AQIDJ: '',
                    AQIDJBS: '',
                    CO: 0,
                    JCSJ: '',
                    NO2: 0,
                    O3: 0,
                    PM10: 0,
                    PM25: 0,
                    QW: '-',
                    SO2: 0,
                    SSXZQ: '',
                    SYWRW: '',
                    XZQDM: '',
                    firstPollution: '',
                    yll: ''
                };
            }
        }
    },
    methods: {
        getPollution(name, value) {
            return util.getLevelPollution(name, value);
        }
    }
};
</script>

<style scoped>
.tz-pd-row.aic {
    align-items: center;
}
.tz-pd-row.jcb {
    justify-content: space-between;
}
.tz-pd-row {
    display: flex;
}
.gd-fwrap {
    display: flex;
    justify-content: space-between;
}
.gd-lfbox {
    width: 212px;
    height: 140px;
    background-color: #f87c12;
    color: #fff;
    text-align: left;
}
.gd-lfbox .p1 {
    font-size: 18px;
    color: #fee9de;
    padding: 16px 0 0 15px;
}
.gd-lfbox .p2 {
    font-size: 16px;
    color: #fee9de;
    padding: 0 0 0 15px;
}
.gd-lfbox .p2 em {
    font-size: 20px;
}
.gd-flx {
    display: flex;
    justify-content: space-around;
    align-items: center;
}
.gd-flx .r1 {
    font-size: 50px;
}
.gd-flx .r2 p {
    font-size: 16px;
}
.gd-flx .r2 p em {
    font-size: 12px;
}
.gd-rtbox {
    flex: 1;
    padding-left: 10px;
}
.gd-rtbox .p1 em {
    font-size: 12px;
}
.gd-rtbox .p1 {
    font-size: 16px;
    width: 79px;
    height: 29px;
    line-height: 29px;
    text-align: center;
    color: #052c4a;
    background-color: #24bd5d;
    font-family: 'Arial';
}
.gd-rtbox .p2 {
    font-size: 16px;
    width: 79px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    color: #fff;
    background-color: #235a63;
    font-family: 'Arial';
}

.gd-rtboxul {
    display: flex;
    flex-wrap: wrap;
    align-content: space-between;
    justify-content: space-between;
    height: 140px;
}

.gd-ultbs1 {
    display: flex;
}
.gd-ultbs1 li {
    padding: 0 15px;
    font-size: 16px;
    color: #fff;
    background: transparent;
    line-height: 30px;
    border: 1px solid #0099d8;
}
.gd-ultbs1 li.on {
    background: #0099d8;
}
.gd-ultbs1 li + li {
    margin-left: 10px;
}

.gd-txt2 {
    font-size: 16px;
    color: #fff;
}
.gd-txt2 em {
    font-size: 20px;
    color: #ee3b5b;
}
.gd-txt2 i {
    font-size: 18px;
    color: #ffc80a;
}

.gd-tablelst1 {
    width: 100%;
}
.gd-tablelst1 tr td sub {
    font-size: 12px;
    vertical-align: baseline;
}
.gd-tablelst1 tr td img {
    vertical-align: middle;
}
.gd-tablelst1 tr td i {
    display: inline-block;
    padding: 0 13px;
    color: #052d3d;
    border-radius: 300px;
    line-height: 26px;
}
</style>
