/**
 * /*
 *
 * @format
 * @Author: Caijw
 * @LastEditors: Caijw
 * @Description: 数据服务平台公共方法处理
 * @Date: 2019-03-28 08:53:24
 * @LastEditTime: 2019-03-28 11:30:49
 */

export default new (class DataUtils {
    constructor() {}

    // 处理水质带比例
    dealWaterCategoryPercent(obj) {
        return [
            {
                name: 'Ⅰ类 ' + obj.oneRate + '%',
                value: obj.oneRate,
                num: obj.oneTotal
            },
            {
                name: 'Ⅱ类 ' + obj.twoRate + '%',
                value: obj.twoRate,
                num: obj.twoTotal
            },
            {
                name: 'Ⅲ类 ' + obj.threeRate + '%',
                value: obj.threeRate,
                num: obj.threeTotal
            },
            {
                name: 'Ⅳ类 ' + obj.fourRate + '%',
                value: obj.fourRate,
                num: obj.fourTotal
            },
            {
                name: 'Ⅴ类 ' + obj.fiveRate + '%',
                value: obj.fiveRate,
                num: obj.fiveTotal
            },
            {
                name: '劣Ⅴ类 ' + obj.badFiveRate + '%',
                value: obj.badFiveRate,
                num: obj.badFiveTotal
            }
        ];
    }
    // 处理水质带比例
    dealWaterCategoryPercent_wkq(obj) {
        return [
            {
                name: 'Ⅰ类' + obj.oneRate,

                value: obj.oneTotal
            },
            {
                name: 'Ⅱ类' + obj.twoRate,

                value: obj.twoTotal
            },
            {
                name: 'Ⅲ类' + obj.threeRate,

                value: obj.threeTotal
            },
            {
                name: 'Ⅳ类' + obj.fourRate,

                value: obj.fourTotal
            },
            {
                name: 'Ⅴ类' + obj.fiveRate,

                value: obj.fiveTotal
            }
        ];
    }
    dealWaterCategoryPercentTwo(obj) {
        return [
            {
                name: 'Ⅰ类 ',
                rate: obj.oneRate,
                value: obj.oneTotal
            },
            {
                name: 'Ⅱ类 ',
                rate: obj.twoRate,
                value: obj.twoTotal
            },
            {
                name: 'Ⅲ类 ',
                rate: obj.threeRate,
                value: obj.threeTotal
            },
            {
                name: 'Ⅳ类 ',
                rate: obj.fourRate,
                value: obj.fourTotal
            },
            {
                name: 'Ⅴ类 ',
                rate: obj.fiveRate,
                value: obj.fiveTotal
            },
            {
                name: '劣Ⅴ类 ',
                rate: obj.badFiveRate,
                value: obj.badFiveTotal
            }
        ];
    }
    dealSeaWaterCategoryPercent(obj) {
        return [
            {
                name: '一类 ' + obj.oneRate + '%',
                value: obj.oneRate,
                num: obj.oneTotal
            },
            {
                name: '二类 ' + obj.twoRate + '%',
                value: obj.twoRate,
                num: obj.twoTotal
            },
            {
                name: '三类 ' + obj.threeRate + '%',
                value: obj.threeRate,
                num: obj.threeTotal
            },
            {
                name: '四类 ' + obj.fourRate + '%',
                value: obj.fourRate,
                num: obj.fourTotal
            },
            {
                name: '劣四类 ' + obj.badFourRate + '%',
                value: obj.badFourRate,
                num: obj.badFourTotal
            }
        ];
    }

    dealWaterEutPercent_(obj) {
        return [
            //海洋水的富营养和其他不太一样
            {
                name: '贫营养 ' + obj.PYYBL + '%',
                value: obj.PYYBL,
                num: obj.PYYGS
            },
            {
                name: '轻度富营养 ' + obj.QDFYYBL + '%',
                value: obj.QDFYYBL,
                num: obj.QDFYYGS
            },
            {
                name: '中度富营养 ' + obj.ZYYBL + '%',
                value: obj.ZYYBL,
                num: obj.ZYYGS
            },
            {
                name: '重度富营养 ' + obj.ZDFYYBL + '%',
                value: obj.ZDFYYBL,
                num: obj.ZDFYYGS
            }
        ];
    }
    // 处理营养状态比例
    dealWaterEutPercent(obj) {
        return [
            //海洋水的富营养和其他不太一样
            {
                name: '贫营养 ' + obj.PYYBL + '%',
                value: obj.PYYBL,
                num: obj.PYYGS
            },
            {
                name: '轻度富营养 ' + obj.ZYYBL + '%',
                value: obj.ZYYBL,
                num: obj.ZYYGS
            },
            {
                name: '中度富营养 ' + obj.QDFYYBL + '%',
                value: obj.QDFYYBL,
                num: obj.QDFYYGS
            },
            {
                name: '重度富营养 ' + obj.ZHDFYYBL + '%',
                value: obj.ZHDFYYBL,
                num: obj.ZHDFYYGS
            },
            {
                name: '严重富营养 ' + obj.ZDFYYBL + '%',
                value: obj.ZDFYYBL,
                num: obj.ZDFYYGS
            }
        ];

        /* return [ 
            {
                name: '贫营养 ' + obj.PYYBL + '%',
                value: obj.PYYBL,
                num: obj.PYYGS
            },
            {
                name: '中营养 ' + obj.ZYYBL + '%',
                value: obj.ZYYBL,
                num: obj.ZYYGS
            },
            {
                name: '轻度富营养 ' + obj.QDFYYBL + '%',
                value: obj.QDFYYBL,
                num: obj.QDFYYGS
            },
            {
                name: '中度富营养 ' + obj.ZDFYYBL + '%',
                value: obj.ZDFYYBL,
                num: obj.ZDFYYGS
            },
            {
                name: '重度富营养 ' + obj.ZDFYYBL + '%',
                value: obj.ZDFYYBL,
                num: obj.ZDFYYGS
            }
        ]; */
    }

    // 处理水质不带带比例
    dealWaterCategory(obj) {
        return [
            { name: 'Ⅰ类', value: obj.oneRate },
            { name: 'Ⅱ类', value: obj.twoRate },
            { name: 'Ⅲ类', value: obj.threeRate },
            { name: 'Ⅳ类', value: obj.fourRate },
            { name: 'Ⅴ类', value: obj.fiveRate },
            { name: '劣Ⅴ类', value: obj.badFiveRate }
        ];
    }

    // 处理空气不带比例
    dealAirCategory(obj) {
        return [
            {
                name: '优 ' + obj.excellentRate + '%',
                value: obj.excellentRate,
                num: obj.excellentDays
            },
            {
                name: '良 ' + obj.goodRate + '%',
                value: obj.goodRate,
                num: obj.goodDays
            },
            {
                name: '轻度 ' + obj.mildRate + '%',
                value: obj.mildRate,
                num: obj.mildDays
            },
            {
                name: '中度 ' + obj.modRate + '%',
                value: obj.modRate,
                num: obj.modDays
            },
            {
                name: '重度 ' + obj.severeRate + '%',
                value: obj.severeRate,
                num: obj.severeDays
            },
            {
                name: '严重 ' + obj.seriousRate + '%',
                value: obj.seriousRate,
                num: obj.seriousDays
            }
        ];
    }

    // 优良率同比环比
    dealOneThreeRate(arr) {
        if (!(arr[0][0] && arr[1][0] && arr[2][0])) {
            return {
                isError: true,
                num: 0,
                now: 0, // 当前
                yearErlier: '', // 同比
                monthErlier: '' // 环比
            };
        }
        return {
            num: arr[0][0].oneThreeTotal,
            now: arr[0][0].oneThreeRate, // 当前
            yearErlier: this.toFixed(
                arr[0][0].oneThreeRate - arr[1][0].oneThreeRate,
                2
            ), // 同比
            monthErlier: this.toFixed(
                arr[0][0].oneThreeRate - arr[2][0].oneThreeRate,
                2
            ) // 环比
        };
    }

    dealBadFiveRate(arr) {
        if (!(arr[0][0] && arr[1][0] && arr[2][0])) {
            return {
                isError: true,
                num: 0,
                now: 0, // 当前
                yearErlier: '', // 同比
                monthErlier: '' // 环比
            };
        }
        return {
            num: arr[0][0].badFiveTotal, // 当前
            now: arr[0][0].badFiveRate, // 当前
            yearErlier: this.toFixed(
                arr[0][0].badFiveRate - arr[1][0].badFiveRate,
                2
            ), // 同比
            monthErlier: this.toFixed(
                arr[0][0].badFiveRate - arr[2][0].badFiveRate,
                2
            ) // 环比
        };
    }

    // 处理空气优良天数
    dealAirGoodDays(arr) {
        let now = arr[0][0].excellentDays + arr[0][0].goodDays;
        let last = arr[1][0].excellentDays + arr[1][0].goodDays || 0;
        let yearErlier = now - last;
        return {
            now: now, // 当前
            yearErlier: yearErlier
        };
    }
    // 处理空气超标天数
    dealAirBadDays(arr) {
        let now = arr[0][0].overproofDays;
        let last = arr[1][0].overproofDays || 0;
        let yearErlier = now - last;
        return {
            now: now, // 当前
            yearErlier: yearErlier
        };
    }

    // 处理超标污染物
    dealPollutionDays(obj) {
        let html = '';
        if (obj.so2Days > 0) {
            html += 'SO₂超标天数:' + obj.so2Days + ';';
        }
        if (obj.no2Days > 0) {
            html += 'NO₂超标天数:' + obj.no2Days + ';';
        }
        if (obj.o3Days > 0) {
            html += 'O₃超标天数:' + obj.o3Days + ';';
        }
        if (obj.coDays > 0) {
            html += 'CO超标天数:' + obj.coDays + ';';
        }
        if (obj.pm25Days > 0) {
            html += 'PM₂.₅超标天数:' + obj.pm25Days + ';';
        }
        if (obj.pm10Days > 0) {
            html += 'PM₁₀超标天数:' + obj.pm10Days + ';';
        }
        if (html == '') {
            html = '暂无污染物超标';
        }
        return html;
    }

    toFixed(num, s) {
        let times = Math.pow(10, s);
        let des = num * times + 0.5;
        des = parseInt(des, 10) / times;
        return des + '';
    }

    // 超标率同比环比
    dealBadRate(arr) {
        if (!(arr[0][0] && arr[1][0] && arr[2][0])) {
            return {
                isError: true,
                num: arr[0][0] ? arr[0][0].badTotal : 0,
                now: arr[0][0] ? this.toFixed(100 - arr[0][0].goodRate, 2) : 0, // 当前
                yearErlier: '', // 同比
                monthErlier: '' // 环比
            };
        }
        let badRate = 100 - arr[0][0].goodRate;
        let yearBadRate = 100 - arr[1][0].goodRate;
        let monthBadRate = 100 - arr[2][0].goodRate;
        return {
            num: arr[0][0].badTotal,
            now: this.toFixed(badRate, 2), // 当前
            yearErlier: this.toFixed(badRate - yearBadRate, 2), // 同比
            monthErlier: this.toFixed(badRate - monthBadRate, 2) // 环比
        };
    }

    // 判断水质优良情况
    dealWaterCategoryStatus(obj) {
        // 1-3类比例大于90%  优 蓝色
        // 1-3类比例 >=75% < 90%      良好 绿色
        // 1-3类比例 <75%  劣5类<20%   轻度 黄色
        // 1-3类比例 <75%  劣5类>=20%&&<40%   中度 橙色
        // 1-3类比例 <60%  劣5类>40%   严重 红色
        let oneThreeRate = obj.oneThreeRate;
        let badFiveRate = obj.badFiveRate;
        let color = '';
        let txt = '';
        if (oneThreeRate >= 90) {
            color = '#51a5fd';
            txt = '优';
        } else if (oneThreeRate < 90 && oneThreeRate >= 75) {
            color = '#73bb31';
            txt = '良好';
        } else if (oneThreeRate < 75 && badFiveRate < 20) {
            color = '#eebd15';
            txt = '轻度';
        } else if (oneThreeRate < 75 && badFiveRate >= 20 && badFiveRate < 40) {
            color = '#f88e17';
            txt = '中度';
        } else if (oneThreeRate < 60 && badFiveRate > 40) {
            color = '#ee3b5b';
            txt = '重度';
        }

        return {
            color: color,
            txt: txt
        };
    }
})();
