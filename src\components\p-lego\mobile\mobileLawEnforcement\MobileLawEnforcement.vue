<!-- @format -->

<template>
    <div class="zy-cell" style="width: 580px; height: 457px">
        <div class="zy-cell-hd">
            <p class="til til1">综合执法</p>
            <span class="more"></span>
        </div>

        <div class="zy-line2">
            <div class="zy-data5">
                <p class="zy-til1">执法次数TOP10</p>
                <ul v-if="data.ZFCS_LIST">
                    <li
                        v-for="(item, index) in data.ZFCS_LIST.slice(0, 10)"
                        :key="index"
                    >
                        <span
                            :class="
                                index == 0
                                    ? 's1 no1'
                                    : index == 1
                                    ? 's1 no2'
                                    : index == 2
                                    ? 's1 no3'
                                    : 's1'
                            "
                            >{{ index + 1 }}</span
                        >
                        <span
                            class="s2 txt-left ellipsis"
                            :title="item.XZQHMC"
                            >{{ item.XZQHMC }}</span
                        >
                        <span class="s3">{{ item.TOTAL }} </span>
                    </li>
                </ul>
            </div>
            <div class="zy-data5">
                <p class="zy-til1">发现问题TOP10</p>
                <ul v-if="data.FXWT_LIST">
                    <li
                        v-for="(item, index) in data.FXWT_LIST.slice(0, 10)"
                        :key="index"
                    >
                        <span
                            :class="
                                index == 0
                                    ? 's1 no1'
                                    : index == 1
                                    ? 's1 no2'
                                    : index == 2
                                    ? 's1 no3'
                                    : 's1'
                            "
                            >{{ index + 1 }}</span
                        >
                        <span
                            class="s2 txt-left ellipsis"
                            :title="item.XZQHMC"
                            >{{ item.XZQHMC }}</span
                        >
                        <span class="s3">{{ item.TOTAL }} </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'mobileLawEnforcement',
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    ZFCS_LIST: [
                        { TOTAL: '0', XZQHDM: '431300', XZQHMC: '娄底市' },
                        { TOTAL: '0', XZQHDM: '430400', XZQHMC: '衡阳市' },
                        { TOTAL: '0', XZQHDM: '430900', XZQHMC: '益阳市' },
                        { TOTAL: '0', XZQHDM: '433100', XZQHMC: '湘西州' },
                        { TOTAL: '0', XZQHDM: '430500', XZQHMC: '邵阳市' },
                        { TOTAL: '0', XZQHDM: '431000', XZQHMC: '郴州市' },
                        { TOTAL: '0', XZQHDM: '430100', XZQHMC: '长沙市' },
                        { TOTAL: '0', XZQHDM: '430600', XZQHMC: '岳阳市' },
                        { TOTAL: '0', XZQHDM: '431100', XZQHMC: '永州市' },
                        { TOTAL: '0', XZQHDM: '430200', XZQHMC: '株洲市' },
                        { TOTAL: '0', XZQHDM: '430700', XZQHMC: '常德市' },
                        { TOTAL: '0', XZQHDM: '431200', XZQHMC: '怀化市' },
                        { TOTAL: '0', XZQHDM: '430300', XZQHMC: '湘潭市' },
                        { TOTAL: '0', XZQHDM: '430800', XZQHMC: '张家界市' }
                    ],
                    FXWT_LIST: [
                        { TOTAL: '0', XZQHDM: '430900', XZQHMC: '益阳市' },
                        { TOTAL: '0', XZQHDM: '433100', XZQHMC: '湘西州' },
                        { TOTAL: '0', XZQHDM: '430500', XZQHMC: '邵阳市' },
                        { TOTAL: '0', XZQHDM: '431000', XZQHMC: '郴州市' },
                        { TOTAL: '0', XZQHDM: '430100', XZQHMC: '长沙市' },
                        { TOTAL: '0', XZQHDM: '430600', XZQHMC: '岳阳市' },
                        { TOTAL: '0', XZQHDM: '431100', XZQHMC: '永州市' },
                        { TOTAL: '0', XZQHDM: '430200', XZQHMC: '株洲市' },
                        { TOTAL: '0', XZQHDM: '430700', XZQHMC: '常德市' },
                        { TOTAL: '0', XZQHDM: '431200', XZQHMC: '怀化市' },
                        { TOTAL: '0', XZQHDM: '430300', XZQHMC: '湘潭市' },
                        { TOTAL: '0', XZQHDM: '430800', XZQHMC: '张家界市' },
                        { TOTAL: '0', XZQHDM: '431300', XZQHMC: '娄底市' },
                        { TOTAL: '0', XZQHDM: '430400', XZQHMC: '衡阳市' }
                    ]
                };
            }
        }
    },
    data() {
        return {};
    },
    watch: {},
    mounted() {},
    methods: {}
};
</script>

<style lang="scss" scoped>
/* -- Reset -- */
body,
ul,
ol,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
p,
form,
input,
textarea,
select,
button {
    margin: 0;
    padding: 0;
    font: 12px 'Microsoft YaHei', SimSun, Arial, Helvetica, sans-serif;
}
@font-face {
    font-family: 'TTTGB';
    src: url('./fonts/TTTGB-Medium.woff2') format('woff2'),
        url('./fonts/TTTGB-Medium.woff') format('woff'),
        url('./fonts/TTTGB-Medium.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'DIN-Bold';
    src: url('./fonts/DIN-Bold.woff2') format('woff2'),
        url('./fonts/DIN-Bold.woff') format('woff'),
        url('./fonts/DIN-Bold.ttf') format('truetype'),
        url('./fonts/DIN-Bold.eot') format('embedded-opentype'),
        url('./fonts/DIN-Bold.svg') format('svg'),
        url('./fonts/DIN-Bold.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}
.zy-cell {
    background-color: rgba(16, 37, 58, 1);
    position: relative;
    margin-bottom: 10px;
    padding-bottom: 1px;
}
.zy-cell::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    background-image: url(./images/cell-bg.png);
}
.zy-cell-hd {
    display: flex;
    height: 46px;
    padding: 0 10px;
    padding-bottom: 6px;
    background: url(./images/hd-bg.png) center bottom no-repeat;
    justify-content: space-between;
    align-items: center;
}

.zy-cell-hd .til {
    padding-left: 38px;
    font-size: 22px;
    color: #fff;
    line-height: 46px;
    background-position: 0 10px;
    background-repeat: no-repeat;
    font-family: 'TTTGB';
    text-shadow: 1px 1px 5px rgba(255, 255, 255, 0.8);
}

.zy-cell-hd .til1 {
    background-image: url(./images/til1.png);
}

.zy-cell-hd .more {
    width: 20px;
    height: 20px;
    background: url(./images/ic-more.png) no-repeat;
    cursor: pointer;
    margin-right: 20px;
}
.zy-line2 {
    display: flex;
    justify-content: space-between;
}
.zy-data5 {
    padding: 0 18px;
    width: 42%;
    flex: none;
}

.zy-data5 .zy-til1 {
    margin-left: 0;
}

.zy-data5 ul {
    position: relative;
}

.zy-data5:nth-of-type(1) ul::after {
    position: absolute;
    right: -30px;
    content: '';
    top: 7px;
    bottom: 7px;
    border-right: 1px dashed #03406c;
}

.zy-data5 ul li {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.zy-data5 ul li .s1 {
    font-size: 16px;
    color: #fff;
    line-height: 26px;
    width: 53px;
    height: 26px;
    text-align: center;
}

.zy-data5 ul li .no1 {
    text-indent: -999em;
    background: url(./images/top1.png) 0 0 no-repeat;
    margin: 4px 0;
}

.zy-data5 ul li .no2 {
    text-indent: -999em;
    background: url(./images/top2.png) 0 0 no-repeat;
    margin: 6px 0;
}

.zy-data5 ul li .no3 {
    text-indent: -999em;
    background: url(./images/top3.png) 0 0 no-repeat;
    margin: 6px 0;
}

.zy-data5 ul li .s2 {
    font-size: 16px;
    color: #fff;
    line-height: 34px;
    width: 95px;
}

.zy-data5 ul li .s3 {
    font-size: 18px;
    color: #00b4ff;
    line-height: 34px;
    font-family: 'DIN-Bold';
    text-align: center;
    width: 53px;
    height: 26px;
}
.zy-til1 {
    font-size: 18px;
    color: #fff;
    padding-left: 30px;
    background: url(./images/til1-bg.png) 0 center no-repeat;
    line-height: 50px;
    margin-left: 20px;
}
</style>
