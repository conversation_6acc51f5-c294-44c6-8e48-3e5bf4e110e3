<!-- @format -->

<template>
    <div ref="childMap" class="mapContainer"></div>
</template>

<script>
import axios from '_u/ajaxRequest';
export default {
    name: 'MapBoxGLMap',
    props: ['mapOption'],
    data() {
        return {
            glmap: null,
            roate: 0,
            loadComplete: false
        };
    },
    mounted() {
        console.log('mapgl-map');

        this.createMap();
    },
    methods: {
        getToken(data) {
            return axios.request({
                url: GisServerGlobalConstant.tokenUrl,
                method: 'get',
                params: data
            });
        },

        //创建地图
        createMap() {
            let baseUrl = window.location.origin + window.location.pathname;

            GisServerGlobalConstant.mapbox.mapBoxOption.style.sprite =
                baseUrl + 'gis/3D/sprite/sprite';

            GisServerGlobalConstant.mapbox.mapBoxOption.style.glyphs =
                './gis/3D/tilecache/mapbox_res/fonts/{fontstack}/{range}.pbf';

            let option = {};

            Object.assign(
                option,
                GisServerGlobalConstant.mapbox.mapBoxOption,
                {
                    container: this.$refs.childMap,
                    preserveDrawingBuffer: true
                },
                this.mapOption
            );

            this.glmap = new mapboxgl.Map(option);

            // this.glmap.addControl(
            //     new MapboxExportControl({
            //         PageSize: Size.A3,
            //         PageOrientation: PageOrientation.Portrait,
            //         Format: Format.PNG,
            //         DPI: DPI[96],
            //         Crosshair: true,
            //         PrintableArea: true,
            //         accessToken:
            //             GisServerGlobalConstant.mapbox.mapBoxOption.accessToken
            //     }),
            //     'top-right'
            // );

            // map.addControl(new MapboxLanguage({ defaultLanguage: 'zh' })); //中文问题
            this.glmap.on('load', (e) => {
                if (!this.loadComplete) {
                    this.$emit('onMapLoaded', this.glmap);
                    this.loadComplete = true;
                }

                if (this.glmap.getSource('mapbox-dem')) {
                    setTimeout(() => {
                        this.glmap.setTerrain({
                            source: 'mapbox-dem',
                            exaggeration: 1.5
                        });
                    }, 1000);
                }
            });

            //2S后还没加载完执行这个
            setTimeout(() => {
                if (!this.loadComplete) {
                    this.$emit('onMapLoaded', this.glmap);
                    this.loadComplete = true;
                }
            }, 2000);

            this.glmap.on('click', (e) => {
                console.log(e.lngLat);
                console.log('center');
                console.log(this.glmap.getCenter());
                // console.log('bearing');
                // console.log(this.glmap.getBearing());
                // console.log('pitch');
                // console.log(this.glmap.getPitch());
                // console.log('zoom');
                // console.log(this.glmap.getZoom());
            });
        }
    }
};
</script>

<style scoped>
.mapContainer {
    width: 100%;
    height: 100%;
    position: relative;
}
</style>

<style>
/* .mapboxgl-ctrl {
    display: none !important;
} */

.mapboxgl-ctrl-bottom-left {
    display: none !important;
}
</style>
