// https://d3js.org/d3-contour/ v3.0.1 Copyright 2012-2021 <PERSON>
!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("d3-array")):"function"==typeof define&&define.amd?define(["exports","d3-array"],r):r((t="undefined"!=typeof globalThis?globalThis:t||self).d3=t.d3||{},t.d3)}(this,(function(t,r){"use strict";var n=Array.prototype.slice;function e(t,r){return t-r}var i=t=>()=>t;function o(t,r){for(var n,e=-1,i=r.length;++e<i;)if(n=a(t,r[e]))return n;return 0}function a(t,r){for(var n=r[0],e=r[1],i=-1,o=0,a=t.length,f=a-1;o<a;f=o++){var u=t[o],c=u[0],d=u[1],l=t[f],s=l[0],g=l[1];if(h(u,l,r))return 0;d>e!=g>e&&n<(s-c)*(e-d)/(g-d)+c&&(i=-i)}return i}function h(t,r,n){var e,i,o,a;return function(t,r,n){return(r[0]-t[0])*(n[1]-t[1])==(n[0]-t[0])*(r[1]-t[1])}(t,r,n)&&(i=t[e=+(t[0]===r[0])],o=n[e],a=r[e],i<=o&&o<=a||a<=o&&o<=i)}function f(){}var u=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function c(){var t=1,a=1,h=r.thresholdSturges,c=g;function d(t){var n=h(t);if(Array.isArray(n))n=n.slice().sort(e);else{const e=r.extent(t),i=r.tickStep(e[0],e[1],n);n=r.ticks(Math.floor(e[0]/i)*i,Math.floor(e[1]/i-1)*i,n)}return n.map((r=>l(t,r)))}function l(r,n){var e=[],i=[];return function(r,n,e){var i,o,h,f,c,d,l=new Array,g=new Array;i=o=-1,f=r[0]>=n,u[f<<1].forEach(v);for(;++i<t-1;)h=f,f=r[i+1]>=n,u[h|f<<1].forEach(v);u[f<<0].forEach(v);for(;++o<a-1;){for(i=-1,f=r[o*t+t]>=n,c=r[o*t]>=n,u[f<<1|c<<2].forEach(v);++i<t-1;)h=f,f=r[o*t+t+i+1]>=n,d=c,c=r[o*t+i+1]>=n,u[h|f<<1|c<<2|d<<3].forEach(v);u[f|c<<3].forEach(v)}i=-1,c=r[o*t]>=n,u[c<<2].forEach(v);for(;++i<t-1;)d=c,c=r[o*t+i+1]>=n,u[c<<2|d<<3].forEach(v);function v(t){var r,n,a=[t[0][0]+i,t[0][1]+o],h=[t[1][0]+i,t[1][1]+o],f=s(a),u=s(h);(r=g[f])?(n=l[u])?(delete g[r.end],delete l[n.start],r===n?(r.ring.push(h),e(r.ring)):l[r.start]=g[n.end]={start:r.start,end:n.end,ring:r.ring.concat(n.ring)}):(delete g[r.end],r.ring.push(h),g[r.end=u]=r):(r=l[u])?(n=g[f])?(delete l[r.start],delete g[n.end],r===n?(r.ring.push(h),e(r.ring)):l[n.start]=g[r.end]={start:n.start,end:r.end,ring:n.ring.concat(r.ring)}):(delete l[r.start],r.ring.unshift(a),l[r.start=f]=r):l[f]=g[u]={start:f,end:u,ring:[a,h]}}u[c<<3].forEach(v)}(r,n,(function(t){c(t,r,n),function(t){for(var r=0,n=t.length,e=t[n-1][1]*t[0][0]-t[n-1][0]*t[0][1];++r<n;)e+=t[r-1][1]*t[r][0]-t[r-1][0]*t[r][1];return e}(t)>0?e.push([t]):i.push(t)})),i.forEach((function(t){for(var r,n=0,i=e.length;n<i;++n)if(-1!==o((r=e[n])[0],t))return void r.push(t)})),{type:"MultiPolygon",value:n,coordinates:e}}function s(r){return 2*r[0]+r[1]*(t+1)*4}function g(r,n,e){r.forEach((function(r){var i,o=r[0],h=r[1],f=0|o,u=0|h,c=n[u*t+f];o>0&&o<t&&f===o&&(i=n[u*t+f-1],r[0]=o+(e-i)/(c-i)-.5),h>0&&h<a&&u===h&&(i=n[(u-1)*t+f],r[1]=h+(e-i)/(c-i)-.5)}))}return d.contour=l,d.size=function(r){if(!arguments.length)return[t,a];var n=Math.floor(r[0]),e=Math.floor(r[1]);if(!(n>=0&&e>=0))throw new Error("invalid size");return t=n,a=e,d},d.thresholds=function(t){return arguments.length?(h="function"==typeof t?t:Array.isArray(t)?i(n.call(t)):i(t),d):h},d.smooth=function(t){return arguments.length?(c=t?g:f,d):c===g},d}function d(t,r,n){for(var e=t.width,i=t.height,o=1+(n<<1),a=0;a<i;++a)for(var h=0,f=0;h<e+n;++h)h<e&&(f+=t.data[h+a*e]),h>=n&&(h>=o&&(f-=t.data[h-o+a*e]),r.data[h-n+a*e]=f/Math.min(h+1,e-1+o-h,o))}function l(t,r,n){for(var e=t.width,i=t.height,o=1+(n<<1),a=0;a<e;++a)for(var h=0,f=0;h<i+n;++h)h<i&&(f+=t.data[a+h*e]),h>=n&&(h>=o&&(f-=t.data[a+(h-o)*e]),r.data[a+(h-n)*e]=f/Math.min(h+1,i-1+o-h,o))}function s(t){return t[0]}function g(t){return t[1]}function v(){return 1}t.contourDensity=function(){var t=s,e=g,o=v,a=960,h=500,f=20,u=2,w=3*f,p=a+2*w>>u,y=h+2*w>>u,M=i(20);function E(n){var i=new Float32Array(p*y),a=new Float32Array(p*y),h=Math.pow(2,-u);n.forEach((function(r,n,a){var f=(t(r,n,a)+w)*h,u=(e(r,n,a)+w)*h,c=+o(r,n,a);if(f>=0&&f<p&&u>=0&&u<y){var d=Math.floor(f),l=Math.floor(u),s=f-d-.5,g=u-l-.5;i[d+l*p]+=(1-s)*(1-g)*c,i[d+1+l*p]+=s*(1-g)*c,i[d+1+(l+1)*p]+=s*g*c,i[d+(l+1)*p]+=(1-s)*g*c}})),d({width:p,height:y,data:i},{width:p,height:y,data:a},f>>u),l({width:p,height:y,data:a},{width:p,height:y,data:i},f>>u),d({width:p,height:y,data:i},{width:p,height:y,data:a},f>>u),l({width:p,height:y,data:a},{width:p,height:y,data:i},f>>u),d({width:p,height:y,data:i},{width:p,height:y,data:a},f>>u),l({width:p,height:y,data:a},{width:p,height:y,data:i},f>>u);var s=M(i);if(!Array.isArray(s)){var g=r.max(i);s=r.tickStep(0,g,s),(s=r.range(0,Math.floor(g/s)*s,s)).shift()}return c().thresholds(s).size([p,y])(i).map(A)}function A(t){return t.value*=Math.pow(2,-2*u),t.coordinates.forEach(m),t}function m(t){t.forEach(z)}function z(t){t.forEach(b)}function b(t){t[0]=t[0]*Math.pow(2,u)-w,t[1]=t[1]*Math.pow(2,u)-w}function x(){return p=a+2*(w=3*f)>>u,y=h+2*w>>u,E}return E.x=function(r){return arguments.length?(t="function"==typeof r?r:i(+r),E):t},E.y=function(t){return arguments.length?(e="function"==typeof t?t:i(+t),E):e},E.weight=function(t){return arguments.length?(o="function"==typeof t?t:i(+t),E):o},E.size=function(t){if(!arguments.length)return[a,h];var r=+t[0],n=+t[1];if(!(r>=0&&n>=0))throw new Error("invalid size");return a=r,h=n,x()},E.cellSize=function(t){if(!arguments.length)return 1<<u;if(!((t=+t)>=1))throw new Error("invalid cell size");return u=Math.floor(Math.log(t)/Math.LN2),x()},E.thresholds=function(t){return arguments.length?(M="function"==typeof t?t:Array.isArray(t)?i(n.call(t)):i(t),E):M},E.bandwidth=function(t){if(!arguments.length)return Math.sqrt(f*(f+1));if(!((t=+t)>=0))throw new Error("invalid bandwidth");return f=Math.round((Math.sqrt(4*t*t+1)-1)/2),x()},E},t.contours=c,Object.defineProperty(t,"__esModule",{value:!0})}));
