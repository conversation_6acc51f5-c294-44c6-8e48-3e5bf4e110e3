<!-- @format -->
<!-- 业务主要入口 -->
<template>
    <div style="position: fixed; top: 10px; left: 10px" v-show="mod != 'look'">
        <div class="condition">
            <label>经度：</label>
            <input type="text" v-model="selectItem.JD" />

            <label style="margin-left: 20px">纬度：</label>
            <input type="text" v-model="selectItem.WD" />

            <el-button
                type="primary"
                @click="btnPlotClick('POINT')"
                style="margin-left: 10px"
                >选点</el-button
            >

            <el-button type="primary" @click="btnPointClick()">定位</el-button>

            <el-button type="warning" @click="btnClearAllClick">清除</el-button>
            <el-button type="success" @click="btnConfirmClick">保存</el-button>

            <!-- <input type="text" v-model="keyStr" placeholder="关键字搜索" /> -->
        </div>

        <!-- <Map2DPlot ref="childPlot" :map="map"></Map2DPlot> -->
    </div>
</template>
<script>
import PointUtil from './components/PointUtil';

export default {
    name: 'GetPointMain',
    created() {},
    data() {
        return {
            selectItem: {},
            mod: '',
            drawTool: null,
            keyStr: '',

            active: false
        };
    },
    props: ['map'],
    unmounted() {},
    components: {},
    mounted() {
        this.queryObj = PowerGL.getQueryObject();

        if (this.queryObj.mod) {
            this.mod = this.queryObj.mod;
        }

        this.initPage();
    },
    methods: {
        //页面初始化
        initPage() {
            if (!this.map) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.pointUtil = new PointUtil(this.map);

            this.initEvent();

            //look 模式下，绘制点位
            if (this.queryObj.mod) {
                this.drawGraphic(this.queryObj);
            }
        },

        //定义地图事件,与后端页面交互
        initEvent() {
            // this.map.on('extent-change', (evt) => {
            //     if (this.queryObj && this.queryObj.mod == 'look') {
            //         PowerGis.removeMapTip();
            //         this.drawEnd();
            //     }
            // });

            this.map.on('click', (e) => {
                if (this.active) {
                    let x = e.lngLat.lng;
                    let y = e.lngLat.lat;

                    this.drawEndHandle(x, y);
                }
            });

            //地图加载完成交互
            window.parent.postMessage(
                {
                    type: 'mapLoadComplete',
                    item: {}
                },
                '*'
            );

            //接受父页面的初始值
            window.addEventListener(
                'message',
                (e) => {
                    if (e.data.type == 'initData') {
                        console.log('接收到父页面消息');
                        console.log(e.data.item);
                        this.selectItem = e.data.item;
                        this.drawInitGra();
                    }
                },
                false
            );
        },

        //绘制点位，如果URL上参数为look 模式,显示点位tip
        drawInitGra() {
            if (this.selectItem.JD && this.selectItem.WD) {
                this.drawGraphic(this.selectItem);
            }
        },

        //绘制完成
        drawGraphic(queryObj) {
            if (queryObj.JD && queryObj.WD) {
                this.pointUtil.addPoint('定位点', [queryObj]);
                PowerGL.pointTo(this.map, queryObj, 14, false);

                //显示tip
                this.showTip(queryObj);
            }
        },

        //默认显示tip
        showTip(queryObj) {
            let contentStr = `<h1 style="font-size: 14px;font-weight: bold;height: 30px; line-height: 30px; margin:0; padding:0">${this.queryObj.QYMC}</h1>
            <p><span style="font-weight: bold;  margin:0; padding:0;">企业地址：</span>${queryObj.QYDZ}</p>`;

            // let cssObj = {
            //     padding: '10px',
            //     'border-radius': '3px',
            //     color: '#333',
            //     transform: 'translate(-50%,-100%)',
            //     background: 'rgba(255,255,255,0.8)',
            //     'margin-top': '-30px'
            // };

            let lnglat = [queryObj.JD, queryObj.WD];

            window.glTooltip = new mapboxgl.Popup({
                className: 'mapbox-tooltip-tip',
                closeOnClick: false,
                closeButton: false
            })
                .setOffset([0, -30])
                .setLngLat(lnglat)
                .setHTML(contentStr)
                .setMaxWidth('none')
                .addTo(this.map);
            this.map.triggerRepaint();
        },

        //标绘
        btnPlotClick(type) {
            PowerGL.removeLayerFromName(this.map, '定位点');
            this.active = true;
        },

        //绘制完成，将数据保存到selectItem，并在页面显示坐标
        drawEndHandle(x, y) {
            this.active = false;

            let item = {
                JD: x.toFixed(6),
                WD: y.toFixed(6)
            };

            Object.assign(this.selectItem, item);
            this.pointUtil.addPoint('定位点', [this.selectItem]);
        },

        //清除全部
        btnClearAllClick() {
            this.selectItem = {
                JD: '',
                WD: ''
            };
            PowerGL.removeLayerFromName(this.map, '定位点');
        },

        //获取点位数据，传递给后端
        btnConfirmClick() {
            let param = JSON.parse(
                JSON.stringify({
                    type: 'PlotSucess', //标绘成功
                    item: this.selectItem
                })
            );

            console.log('gis传递给后端页面结果');
            console.log(param);

            window.parent.postMessage(param, '*');
        },

        //定位
        btnPointClick() {
            if (this.selectItem && this.selectItem.JD && this.selectItem.WD) {
                this.pointUtil.addPoint('定位点', [this.selectItem]);
                PowerGL.pointTo(this.map, this.selectItem, 14, false);
            }
        }

        //115.059189,29.792519
    },
    watch: {},
    computed: {}
};
</script>

<style>
.gis-search {
    position: absolute;
    top: 77px;
    left: 172px;
}

.gis-legend {
    position: absolute;
    left: 170px;
    bottom: 80px;
}

.condition {
    padding: 10px;
    background: rgba(255, 255, 255, 0.9);
}

.condition label {
    font-size: 14px;
    color: #333;
}

.condition input[type='text'] {
    height: 30px;
    font-size: 14px;
    padding-left: 10px;
}

.mapbox-tooltip-tip .mapboxgl-popup-content {
    background: rgba(255, 255, 255, 0.8);
}
</style>
