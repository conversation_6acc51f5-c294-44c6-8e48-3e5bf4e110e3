/** @format */

let geoserver = ServerGlobalConstant.geoserverHost;

let tiandituConf = {
    keys: [
        '6474014347483beefbbc83441fa4768e',
        'e579e72fcd3a5c469e99f907dbdef097',
        '944541a04638dd67339543d7ec4f8076',
        '7dd25a71ca96a3b65cad1a0c31325a33',
        'bc28c5f2ad8ff5bb0d42de0a12680bf6',
        '97350805d73bf363cb37af838b14ac9f',
        '2c91fcc69a8a55f5ed816c344f4b6a51'
    ],
    getKey() {
        let length = this.keys.length;
        let index = Math.floor(Math.random() * length);
        return this.keys[index];
    }
};
let simple = {
    version: 8,
    light: {
        anchor: 'viewport',
        color: 'rgb(255,255,255)',
        intensity: 1
    },
    // "terrain": {
    //     "source": "mapbox-dem",
    //     "exaggeration": 1.5
    // },
    sprite: 'mapbox://styles/mapbox/streets-v11',
    glyphs: './gis/3D/tilecache/mapbox_res/fonts/{fontstack}/{range}.pbf',
    sources: {
        'mapbox-dem': {
            type: 'raster-dem',
            url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
            tileSize: 512,
            maxzoom: 17
        },
        mapbox_img: {
            //EsriArcGIS卫星影像图
            type: 'raster',
            // tiles: [
            //     'https://sdi.zjzwfw.gov.cn/services/wmts/imgmap/default/oss?token=sy-516ff27b-6ffc-4640-b54f-777a7d8f42c1&REQUEST=GetTile&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}'
            // ],
            tiles: [
                'http://t6.tianditu.gov.cn/img_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={z}&TILECOL={x}&TILEROW={y}&tk=1f08becab78a7afba1156ddb3f539147'
            ],
            tileSize: 256
        },
        adminmap: {
            type: 'vector',
            scheme: 'tms',
            tiles: [
                geoserver +
                    '/gwc/service/tms/1.0.0/zhejiang:adminmap@EPSG:4326@pbf/{z}/{x}/{y}.pbf' //这个坐标系要改成4326
            ],
            zoomOffset: -1 // 这个比较关键
        },
        River_PJ: {
            type: 'vector',
            scheme: 'tms',
            tiles: [
                geoserver +
                    '/gwc/service/tms/1.0.0/zhj_qz:River_PJ@EPSG:4326@pbf/{z}/{x}/{y}.pbf'
            ],
            zoomOffset: -1
        },
        basemap: {
            type: 'vector',
            scheme: 'tms',
            tiles: [
                geoserver +
                    '/gwc/service/tms/1.0.0/zhejiang:basemap@EPSG:4326@pbf/{z}/{x}/{y}.pbf'
            ],
            zoomOffset: -1
        },
        hydmap: {
            type: 'vector',
            scheme: 'tms',
            tiles: [
                geoserver +
                    '/gwc/service/tms/1.0.0/zhejiang:hydmap@EPSG:4326@pbf/{z}/{x}/{y}.pbf'
            ],
            zoomOffset: -1
        }
    },
    layers: [
        {
            id: 'boua1_sjxzqh',
            type: 'fill',
            source: 'adminmap',
            'source-layer': 'BOUA2_DJSXZQH',
            minzoom: 10,
            paint: {
                'fill-color': 'rgba(5,55,80,1)'
            }
        },
        {
            id: 'raster_img',
            type: 'raster',
            source: 'mapbox_img',
            minzoom: 0,
            maxzoom: 20,
            paint: {
                'raster-opacity': 1
            }
        },
        {
            id: 'boua_mask',
            type: 'fill',
            source: 'adminmap',
            'source-layer': 'ZBDQ_BACKGROUND',
            minzoom: 8,
            maxzoom: 24,
            paint: {
                'fill-color': '#122944',
                'fill-opacity': 0.5
            }
        },
        {
            id: 'boua_mask2',
            type: 'fill',
            source: 'adminmap',
            'source-layer': 'ZBDQ_BOUA1_SJXZQH',
            minzoom: 5,
            maxzoom: 8,
            filter: ['!=', 'PROVINCE', '浙江省'],
            layout: {
                visibility: 'none'
            },
            paint: {
                'fill-color': '#072236',
                'fill-opacity': 1
            }
        },
        {
            id: 'boua_mask5',
            type: 'fill',
            source: 'adminmap',
            'source-layer': 'ZBDQ_BACKGROUND',
            minzoom: 0,
            maxzoom: 20,
            paint: {
                'fill-color': '#072236',
                'fill-opacity': 0.5
            }
        },
        {
            id: 'zz6',
            type: 'fill',
            source: 'hydmap',
            'source-layer': 'ZBDQ_SHUIXI_4_8',
            minzoom: 5,
            paint: {
                'fill-color': 'rgba(81,165,253,0.1)',
                'fill-outline-color': 'rgba(81,165,253,0)',
                'fill-opacity': 1
            },
            layout: {
                visibility: 'visible'
            }
        },
        {
            id: 'boua_mask3',
            type: 'fill',
            source: 'adminmap',
            'source-layer': 'ZBDQ_BOUA3_QXJXZQH',
            minzoom: 23,
            maxzoom: 23,
            filter: ['!=', 'PROVINCE', '浙江省'],
            paint: {
                'fill-color': 'rgb(2, 115, 194)',
                'fill-opacity': 0.4
            }
        },
        {
            id: 'boua_mask4',
            type: 'fill',
            source: 'adminmap',
            'source-layer': 'ZBDQ_BOUA3_QXJXZQH',
            minzoom: 5,
            maxzoom: 20,
            layout: {
                visibility: 'none'
            },
            paint: {
                'fill-color': '#c30013',
                'fill-opacity': 0
            }
        },
        {
            id: 'ZBDQ_BOUL0_ZG_GJX',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'ZBDQ_BOUL0_ZG_GJX',
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#c8a65e',
                'line-width': 3
            }
        },
        {
            id: 'zbdq_boul_xzjjx/sj_1',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'ZBDQ_BOUL1_XZJJX_SJ',
            minzoom: 2,
            maxzoom: 16,
            layout: {
                'line-cap': 'round',
                'line-join': 'round',
                visibility: 'none'
            },
            paint: {
                'line-color': '#adbd9c',
                'line-width': 0.5
            }
        },
        {
            id: 'zbdq_boul_xzjjx/ds_1',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'ZBDQ_BOUL2_XZJJX_DSJ',
            minzoom: 8,
            maxzoom: 16,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#adbd9c',
                'line-width': 1
            }
        },
        {
            id: 'zbdq_boul_xzjjx/qx_1',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'ZBDQ_BOUL3_XZJJX_QXJ',
            minzoom: 9,
            maxzoom: 16,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#96a687',
                'line-width': 0.5
            }
        },
        {
            id: 'ZBDQ_AANP_HYDM_8',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'ZBDQ_AANP_HYDM',
            filter: ['==', 'DISPCLASS', '8'],
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 11,
                'text-padding': 10,
                'text-anchor': 'top',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'agnp1_shengjxzzx/beijing',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'ZBDQ_AGNP1_SJXZZX',
            minzoom: 1,
            maxzoom: 6,
            filter: ['==', 'NAME', '北京市'],
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Bold'],
                'text-size': 12,
                'text-anchor': 'center',
                'text-pitch-alignment': 'viewport',
                'text-optional': true
            },
            paint: {
                'text-color': 'rgb(220,220,220)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'ZBDQ_AGNP4_XZJD/1',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'ZBDQ_AGNP4_XZJD',
            minzoom: 11,
            filter: ['!=', 'PROVINCE', '浙江省'],
            layout: {
                'text-size': 12,
                'text-rotation-alignment': 'viewport',
                'text-pitch-alignment': 'viewport',
                'text-font': ['Microsoft YaHei Regular'],
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-field': '{NAME}',
                visibility: 'visible'
            },
            paint: {
                'text-color': 'rgb(170,170,170)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'zbdq_agnp3_qxjxzzx/1',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'ZBDQ_AGNP3_QXJXZZX',
            minzoom: 8,
            filter: ['!=', 'PROVINCE', '浙江省'],
            layout: {
                'text-size': 12,
                'text-rotation-alignment': 'viewport',
                'text-pitch-alignment': 'viewport',
                'text-font': ['Microsoft YaHei Regular'],
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-field': '{NAME}',
                visibility: 'visible'
            },
            paint: {
                'text-color': 'rgb(170,170,170)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'zbdq_agnp2_djsxzzx/1',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'ZBDQ_AGNP2_DJSXZZX',
            minzoom: 6,
            filter: ['!=', 'PROVINCE', '浙江省'],
            layout: {
                'text-field': '{SNAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 13,
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-rotation-alignment': 'viewport',
                'text-pitch-alignment': 'viewport',
                'text-optional': true,
                visibility: 'none'
            },
            paint: {
                'text-color': 'rgb(220,220,220)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'zbdq_agnp1_shengjxzzx/1',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'ZBDQ_AGNP1_SJXZZX',
            minzoom: 3,
            maxzoom: 6.5,
            layout: {
                'text-field': '{CITY}',
                'text-font': ['Microsoft YaHei Bold'],
                'text-size': 13,
                'text-anchor': 'center',
                'text-pitch-alignment': 'viewport',
                'text-optional': true,
                visibility: 'none'
            },
            paint: {
                'text-color': 'rgb(220,220,220)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'P_LANDUSE_AOI',
            type: 'fill',
            source: 'basemap',
            'source-layer': 'P_GYLD',
            minzoom: 13,
            paint: {
                'fill-color': '#3c8a30',
                'fill-opacity': 0.4
            }
        },
        {
            id: 'hyd_heliu/1',
            type: 'fill',
            source: 'adminmap',
            'source-layer': 'ZBDQ_SHUIXI_4_8',
            minzoom: 11,
            maxzoom: 14,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6', '5']],
            paint: {
                'fill-color': '#51A5FD',
                'fill-opacity': 0.3
            }
        },
        {
            id: 'hyd_heliu/2',
            type: 'fill',
            source: 'adminmap',
            'source-layer': 'ZBDQ_SHUIXI_4_8',
            minzoom: 14,
            filter: [
                'all',
                ['in', 'DISPCLASS', '8', '7', '6', '5', '4', '3', '2', '1']
            ],
            paint: {
                'fill-color': '#51A5FD',
                'fill-opacity': 0.3
            }
        },
        {
            id: 'hyd_river_l/1',
            type: 'line',
            source: 'hydmap',
            'source-layer': 'HYDL_SHUIXI',
            minzoom: 6,
            maxzoom: 12,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6', '5', '4', '3']],
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#51a5fd',
                'line-width': [
                    'match',
                    ['get', 'DISPCLASS'],
                    '7',
                    3.3,
                    '6',
                    3.3,
                    '5',
                    3,
                    '4',
                    2.2,
                    '3',
                    1,
                    '2',
                    1,
                    '1',
                    1,
                    1
                ]
            }
        },
        {
            id: 'hyd_river_l/2',
            type: 'line',
            source: 'hydmap',
            'source-layer': 'HYDL_SHUIXI',
            minzoom: 12,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#51a5fd',
                'line-width': [
                    'match',
                    ['get', 'DISPCLASS'],
                    '7',
                    3.3,
                    '6',
                    3.3,
                    '5',
                    3,
                    '4',
                    2.2,
                    '3',
                    1,
                    '2',
                    1,
                    '1',
                    1,
                    1
                ]
            }
        },
        {
            id: 'hyd_river/1',
            type: 'fill',
            source: 'hydmap',
            'source-layer': 'HYDA_HELIU',
            minzoom: 6,
            maxzoom: 14,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6', '5', '4']],
            paint: {
                'fill-color': '#51a5fd',
                'fill-opacity': 1
            }
        },
        {
            id: 'hyd_river/2',
            type: 'fill',
            source: 'hydmap',
            'source-layer': 'HYDA_HELIU',
            minzoom: 14,
            maxzoom: 16,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6', '5', '4', '3']],
            paint: {
                'fill-color': '#51a5fd',
                'fill-opacity': 0.9
            }
        },
        {
            id: 'hyd_river/3',
            type: 'fill',
            source: 'hydmap',
            'source-layer': 'HYDA_HELIU',
            minzoom: 16,
            paint: {
                'fill-color': '#51a5fd',
                'fill-opacity': 0.9
            }
        },
        {
            id: 'hyd_lake/1',
            type: 'fill',
            source: 'hydmap',
            'source-layer': 'HYDA_HUPOSHUIKU',
            minzoom: 6,
            maxzoom: 12.5,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6', '5']],
            paint: {
                'fill-color': '#51a5fd',
                'fill-opacity': 0.9
            }
        },
        {
            id: 'hyd_lake/2',
            type: 'fill',
            source: 'hydmap',
            'source-layer': 'HYDA_HUPOSHUIKU',
            minzoom: 12.5,
            maxzoom: 15,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6', '5', '4', '3']],
            paint: {
                'fill-color': '#51a5fd',
                'fill-opacity': 1
            }
        },
        {
            id: 'hyd_lake/3',
            type: 'fill',
            source: 'hydmap',
            'source-layer': 'HYDA_HUPOSHUIKU',
            minzoom: 15,
            filter: [
                'all',
                ['in', 'DISPCLASS', '8', '7', '6', '5', '4', '3', '2']
            ],
            paint: {
                'fill-color': '#51a5fd',
                'fill-opacity': 1
            }
        },
        {
            id: 'hyd_ZBDQ_SHUIXI_4_8/1',
            type: 'fill',
            source: 'hydmap',
            'source-layer': 'ZBDQ_SHUIXI_4_8',
            minzoom: 5,
            paint: {
                'fill-color': 'rgba(81,165,253,0.1)',
                'fill-outline-color': 'rgba(81,165,253,0)',
                'fill-opacity': 0
            }
        },
        {
            id: 'boua2_djszqh/cur_l2',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'BOUA_DQXZQ',
            minzoom: 7,
            maxzoom: 15,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#75c9e7',
                'line-width': 0.7
            }
        },
        {
            id: 'ROAD8_qtdl/1',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD8_QTDL',
            minzoom: 13.5,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#847640',
                'line-width': 1.5
            }
        },
        {
            id: 'ROAD7_sqzl/1',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD7_SQZL',
            minzoom: 14,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#847640',
                'line-width': 2
            }
        },
        {
            id: 'ROAD6_sqdl/1',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD6_SQDL',
            minzoom: 13.5,
            maxzoom: 14,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#847640',
                'line-width': 1.5
            }
        },
        {
            id: 'ROAD6_sqdl/2',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD6_SQDL',
            minzoom: 14,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#847640',
                'line-width': 3
            }
        },
        {
            id: 'ROAD5_xd/1',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD5_XD',
            minzoom: 15,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': 'rgb(20,20,20)',
                'line-width': 3.5
            }
        },
        {
            id: 'ROAD5_xd/11',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD5_XD',
            minzoom: 14,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#847640',
                'line-width': 1.5
            }
        },
        {
            id: 'ROAD4_sd/21',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD4_SD',
            minzoom: 15,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': 'rgb(20,20,20)',
                'line-width': 3.5
            }
        },
        {
            id: 'ROAD4_sd/22',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD4_SD',
            minzoom: 13,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#847640',
                'line-width': 2
            }
        },
        {
            id: 'ROAD3_gd/11',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD3_GD',
            minzoom: 12,
            maxzoom: 14,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#847640',
                'line-width': 1.8
            }
        },
        {
            id: 'ROAD3_gd/21',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD3_GD',
            minzoom: 14,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': 'rgb(20,20,20)',
                'line-width': 5.5
            }
        },
        {
            id: 'ROAD3_gd/22',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD3_GD',
            minzoom: 14,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#867634',
                'line-width': 4.5
            }
        },
        {
            id: 'ROAD1_gsgl/11',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD1_GSGL',
            minzoom: 12,
            maxzoom: 14,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#847640',
                'line-width': 2
            }
        },
        {
            id: 'ROAD1_gsgl/21',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD1_GSGL',
            minzoom: 14,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': 'rgb(20,20,20)',
                'line-width': 6
            }
        },
        {
            id: 'ROAD1_gsgl/22',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD1_GSGL',
            minzoom: 14,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#957736',
                'line-width': 5
            }
        },
        {
            id: 'r_dtqg/1',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD_DITIE',
            minzoom: 13.5,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': '#fffd80',
                'line-width': 6,
                'line-blur': 4
            }
        },
        {
            id: 'r_dtqg/12',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD_DITIE',
            minzoom: 13.5,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': [
                    'match',
                    ['get', 'NAME'],
                    '深圳地铁5号线',
                    '#ADC3C0',
                    '武汉地铁7号线',
                    '#F4B67F',
                    '#4ef2e5'
                ],
                'line-width': 2.5
            }
        },
        {
            id: 'r_tl/1',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD_TIELU',
            minzoom: 14,
            layout: {
                'line-join': 'round'
            },
            paint: {
                'line-color': 'rgb(20,20,20)',
                'line-width': 4
            }
        },
        {
            id: 'r_tl/2',
            type: 'line',
            source: 'basemap',
            'source-layer': 'ROAD_TIELU',
            minzoom: 14,
            layout: {
                'line-join': 'round'
            },
            paint: {
                'line-color': '#545454',
                'line-width': 2,
                'line-dasharray': [5, 8]
            }
        },
        {
            id: 'ROAD8_qtdl/label/rdname 1',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD8_QTDL',
            minzoom: 15,
            layout: {
                'symbol-placement': 'line',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 11,
                'text-padding': 10,
                'text-field': '{RDNAME}',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#d3b669',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'ROAD7_sqzl/label/rdname 1',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD7_SQZL',
            minzoom: 14.5,
            layout: {
                'symbol-placement': 'line',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 11,
                'text-padding': 10,
                'text-field': '{RDNAME}',
                'text-pitch-alignment': 'viewport',
                'text-optional': true
            },
            paint: {
                'text-color': '#d3b669',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'ROAD6_sqdl/label/rdname 1',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD6_SQDL',
            minzoom: 14,
            layout: {
                'symbol-placement': 'line',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 11,
                'text-padding': 10,
                'text-field': '{RDNAME}',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#d3b669',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'ROAD5_xd/label/rdname 1',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD5_XD',
            minzoom: 13.5,
            layout: {
                'symbol-placement': 'line',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 11,
                'text-padding': 10,
                'text-field': '{RDNAME}',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#d3b669',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'ROAD4_sd/label/Class rdcode',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD4_SD',
            minzoom: 13.5,
            filter: ['all', ['>=', 'CODELEN', 2], ['<=', 'CODELEN', 4]],
            layout: {
                'symbol-placement': 'point',
                'icon-image': 'cn-provincial-expy-4',
                'icon-anchor': 'center',
                'icon-padding': 80,
                'icon-size': 0.65,
                'text-pitch-alignment': 'viewport',
                'text-font': ['Microsoft YaHei Bold'],
                'text-size': 9.3,
                'text-anchor': 'center',
                'text-field': '{RDCODE}'
            },
            paint: {
                'text-color': 'rgb(220,220,220)'
            }
        },
        {
            id: 'ROAD4_sd/label/rdname 1',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD4_SD',
            minzoom: 13.5,
            layout: {
                'symbol-placement': 'line',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 11,
                'text-padding': 10,
                'text-field': '{RDNAME}',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#d3b669',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'ROAD3_gd/label/rdname 1',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD3_GD',
            minzoom: 13.5,
            layout: {
                'symbol-placement': 'line',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12.5,
                'text-padding': 10,
                'text-field': '{RDNAME}',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#d3b669',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'ROAD3_gd/label/rdcode',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD3_GD',
            minzoom: 13.5,
            filter: ['all', ['>=', 'CODELEN', 2], ['<=', 'CODELEN', 5]],
            layout: {
                'symbol-placement': 'point',
                'icon-image': 'cn-provincial-expy-4',
                'icon-anchor': 'center',
                'icon-padding': 80,
                'icon-size': 0.8,
                'text-pitch-alignment': 'viewport',
                'text-font': ['Microsoft YaHei Bold'],
                'text-size': 9.8,
                'text-anchor': 'center',
                'text-field': '{RDCODE}'
            },
            paint: {
                'text-color': 'rgb(220,220,220)'
            }
        },
        {
            id: 'ROAD1_gsgl/label/rdname 1',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD1_GSGL',
            minzoom: 13.5,
            layout: {
                'symbol-placement': 'line',
                'text-pitch-alignment': 'viewport',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 13,
                'text-padding': 10,
                'text-field': '{RDNAME}'
            },
            paint: {
                'text-color': 'rgb(198,198,198)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'r_dtqg/label/12',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD_DITIE',
            minzoom: 13,
            layout: {
                'symbol-placement': 'line',
                'text-pitch-alignment': 'viewport',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 13,
                'text-padding': 10,
                'text-field': '{NAME}'
            },
            paint: {
                'text-color': 'rgb(198,198,198)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'ROAD1_gsgl/label/rdcode 1',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'ROAD1_GSGL',
            minzoom: 13,
            filter: ['all', ['>=', 'CODELEN', 2], ['<=', 'CODELEN', 5]],
            layout: {
                'symbol-placement': 'point',
                'icon-image': 'cn-nths-expy-4',
                'icon-anchor': 'center',
                'icon-padding': 80,
                'icon-size': 0.8,
                'text-pitch-alignment': 'viewport',
                'text-font': ['Microsoft YaHei Bold'],
                'text-size': 9.8,
                'text-padding': 20,
                'text-anchor': 'center',
                'text-field': '{RDCODE}'
            },
            paint: {
                'text-color': 'rgb(220,220,220)'
            }
        },
        {
            id: 'buildings_3d',
            type: 'fill-extrusion',
            source: 'basemap',
            'source-layer': 'BUILDING',
            minzoom: 12.5,
            paint: {
                'fill-extrusion-color': [
                    'interpolate',
                    ['linear'],
                    ['to-number', ['get', 'Height']],
                    0,
                    '#3b537b',
                    110,
                    '#81a3db'
                ],
                'fill-extrusion-height': ['to-number', ['get', 'Height']],
                'fill-extrusion-opacity': 0.8
            }
        },
        {
            id: 'POI02_jcgkmt_jichang',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI02_JT_JC_GK_MT',
            minzoom: 11,
            filter: ['all', ['in', 'KIND', '8100']],
            layout: {
                'icon-image': 'airport-15',
                'icon-anchor': 'top',
                'icon-size': 1,
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-anchor': 'bottom',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(230,230,230)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'POI02_jcgkmt_gangkoumatou',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI02_JT_JC_GK_MT',
            minzoom: 14,
            filter: ['all', ['in', 'KIND', '8180']],
            layout: {
                'icon-image': 'harbor-11',
                'icon-anchor': 'top',
                'icon-size': 1,
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12.5,
                'text-anchor': 'bottom',
                'text-max-width': 10,
                'text-pitch-alignment': 'viewport',
                'icon-allow-overlap': true
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'POI04_qcz_qichezhan',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI04_JT_QCZ',
            minzoom: 14,
            filter: ['all', ['in', 'KIND', '8083']],
            layout: {
                'icon-image': 'bus',
                'icon-anchor': 'top',
                'icon-size': 0.8,
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-anchor': 'bottom',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport',
                'icon-allow-overlap': true
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'POI03_JT_HCZ_DTZ',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI03_JT_HCZ_DTZ',
            minzoom: 12.5,
            filter: ['all', ['in', 'KIND', '8081', '8088']],
            layout: {
                'icon-image': 'rail',
                'icon-anchor': 'top',
                'icon-size': 1,
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 13,
                'text-anchor': 'bottom',
                'text-max-width': 10,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'POI15_ylfw/yiyuan',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI15_GG_YLFW',
            minzoom: 16,
            filter: ['all', ['in', 'KIND', '7200'], ['<=', 'NALEN', 12]],
            layout: {
                'icon-image': 'hospital-11',
                'icon-anchor': 'top',
                'icon-size': 1,
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-padding': 10,
                'text-anchor': 'bottom',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'POI16_kyjy/xuexiao',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI16_GG_KYJY',
            minzoom: 16.5,
            filter: [
                'all',
                ['in', 'KIND', 'A700', 'A701', 'A702', 'A703'],
                ['<=', 'NALEN', 12]
            ],
            layout: {
                'icon-image': 'college-15',
                'icon-anchor': 'top',
                'icon-size': 1,
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-padding': 10,
                'text-anchor': 'bottom',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'POI19_zzxq',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI19_GG_ZZXQ',
            minzoom: 16,
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-padding': 10,
                'text-anchor': 'top',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'POI17_SY_GSQY',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI17_SY_GSQY',
            minzoom: 16.2,
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-padding': 10,
                'text-anchor': 'top',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'POI11_syds',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI11_SY_SYDS',
            minzoom: 15.5,
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-padding': 10,
                'text-anchor': 'top',
                'text-pitch-alignment': 'viewport',
                'text-optional': true
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'POI18_GG_GYGC',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI18_GG_GYGC',
            minzoom: 14,
            layout: {
                'icon-image': 'park-15',
                'icon-anchor': 'top',
                'icon-size': 1,
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-padding': 10,
                'text-anchor': 'bottom',
                'text-pitch-alignment': 'viewport',
                'text-optional': true
            },
            paint: {
                'text-color': '#B0E8CF',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'POI03_hczdtz_ditiezhan',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI03_JT_HCZ_DTZ',
            minzoom: 14,
            filter: ['all', ['in', 'KIND', '8085']],
            layout: {
                'icon-image': 'rail-metro',
                'icon-anchor': 'center',
                'icon-size': 1,
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12.5,
                'text-offset': [1, 0],
                'text-anchor': 'left',
                'text-max-width': 10,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#c9c448',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'p_shan_1',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'POI_SHANMAI',
            minzoom: 15,
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-padding': 10,
                'text-anchor': 'top',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'aanp_hydm_1',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'AANP_HYDM',
            minzoom: 13,
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-padding': 10,
                'text-anchor': 'top',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'aanp_hydm_8',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'AANP_HYDM',
            maxzoom: 13,
            filter: ['==', 'DISPCLASS', '8'],
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 11,
                'text-padding': 10,
                'text-anchor': 'top',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(220,235,220)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'p_cunzhuang',
            type: 'symbol',
            source: 'basemap',
            'source-layer': 'AGNP_P_CUNZHUANG',
            minzoom: 13.5,
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-padding': 10,
                'text-anchor': 'center',
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': 'rgb(240,240,240)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1.2
            }
        },
        {
            id: 'boul_xzjjx/qx_cur2',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'BOUL3_XZJJX_QXJ',
            minzoom: 10.8,
            maxzoom: 15,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': 'rgba(180,180,180,0.7)',
                'line-width': 2.2,
                'line-blur': 1
            }
        },
        {
            id: 'boul_xzjjx/qx_cur3',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'BOUL3_XZJJX_QXJ',
            minzoom: 10,
            maxzoom: 15,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': 'rgba(220,220,220,1)',
                'line-width': 1,
                'line-opacity': 0.7
            }
        },
        {
            id: 'boua2_parent',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'ZBDQ_BOUA2_DJSXZQH',
            minzoom: 5,
            maxzoom: 16,
            filter: ['==', 'PROVINCE', '浙江省'],
            layout: {
                'line-cap': 'round',
                'line-join': 'round',
                visibility: 'none'
            },
            paint: {
                'line-color': 'rgba(220, 220, 220, 0.41)',
                'line-width': 1
            }
        },
        {
            id: 'boua2_PROVINCE',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'BOUA1_SJXZQH',
            minzoom: 5,
            maxzoom: 16,
            filter: ['==', 'PROVINCE', '浙江省'],
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': 'rgba(220,220,220,1)',
                'line-width': 0.5
            }
        },
        {
            id: 'boua2_djszqh/cur_l_blur2',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'BOUA_DQXZQ',
            minzoom: 0,
            maxzoom: 20,
            layout: {
                'line-cap': 'round',
                'line-join': 'round',
                visibility: 'none'
            },
            paint: {
                'line-color': 'rgba(34,34,34,0.8)',
                'line-width': 3
            }
        },
        {
            id: 'boua2_djszqh/cur_l_blur3',
            type: 'line',
            source: 'adminmap',
            'source-layer': 'BOUA_DQXZQ',
            minzoom: 6.5,
            layout: {
                'line-cap': 'round',
                'line-join': 'round'
            },
            paint: {
                'line-color': 'rgba(14, 131, 166,1)',
                'line-width': 1
            }
        },
        {
            id: 'HYDL/label0',
            type: 'symbol',
            source: 'hydmap',
            'source-layer': 'HYDL_SHUIXI',
            minzoom: 6,
            maxzoom: 12,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6', '5']],
            layout: {
                'symbol-placement': 'point',
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 13,
                'text-padding': 20,
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#73DFFF',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'HYDL/label1',
            type: 'symbol',
            source: 'hydmap',
            'source-layer': 'HYDL_SHUIXI',
            minzoom: 12,
            maxzoom: 15,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6', '5', '4']],
            layout: {
                'symbol-placement': 'line',
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 13,
                'text-padding': 20,
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#73DFFF',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'HYDL/label2',
            type: 'symbol',
            source: 'hydmap',
            'source-layer': 'HYDL_SHUIXI',
            minzoom: 15,
            layout: {
                'symbol-placement': 'line',
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 14.5,
                'text-padding': 20,
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#73DFFF',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'hyd_huposk/label0',
            type: 'symbol',
            source: 'hydmap',
            'source-layer': 'HYDA_HUPOSHUIKU',
            minzoom: 6,
            maxzoom: 13,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6']],
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12.5,
                'text-padding': 20,
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#73DFFF',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'hyd_huposk/label1',
            type: 'symbol',
            source: 'hydmap',
            'source-layer': 'HYDA_HUPOSHUIKU',
            minzoom: 13,
            maxzoom: 14.5,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6', '5', '4']],
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12.5,
                'text-padding': 20,
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#73DFFF',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'hyd_huposk/label2',
            type: 'symbol',
            source: 'hydmap',
            'source-layer': 'HYDA_HUPOSHUIKU',
            minzoom: 14.5,
            maxzoom: 16.5,
            filter: ['all', ['in', 'DISPCLASS', '8', '7', '6', '5', '4', '3']],
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 14,
                'text-padding': 20,
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#73DFFF',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'hyd_huposk/label3',
            type: 'symbol',
            source: 'hydmap',
            'source-layer': 'HYDA_HUPOSHUIKU',
            minzoom: 16.5,
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 14,
                'text-padding': 20,
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport'
            },
            paint: {
                'text-color': '#73DFFF',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'ZBDQ_SHUIXI_4_8/label1',
            type: 'symbol',
            source: 'hydmap',
            'source-layer': 'ZBDQ_SHUIXI_4_8',
            minzoom: 6,
            filter: ['all', ['in', 'KIND', '0121', '0125']],
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 12,
                'text-padding': 60,
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-pitch-alignment': 'viewport',
                visibility: 'none'
            },
            paint: {
                'text-color': '#73DFFF',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'AGNP4_XZJDXZZX/1',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'AGNP4_XZJDXZZX',
            minzoom: 10.5,
            maxzoom: 13.5,
            layout: {
                'text-font': ['Microsoft YaHei Regular'],
                'text-size': 13,
                'text-padding': 2,
                'text-max-width': 8,
                'text-anchor': 'bottom',
                'text-field': '{NAME}',
                'text-optional': true
            },
            paint: {
                'text-color': 'rgb(248,248,248)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'AGNP4_XZJDXZZX/2',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'AGNP4_XZJDXZZX',
            minzoom: 13.5,
            layout: {
                'icon-image': 'circle-white-2',
                'icon-anchor': 'top',
                'icon-size': 0.5,
                'text-font': ['Microsoft YaHei Bold'],
                'text-size': 14,
                'text-padding': 10,
                'text-max-width': 8,
                'text-anchor': 'bottom',
                'text-field': '{NAME}',
                'text-optional': true
            },
            paint: {
                'text-color': 'rgb(235,235,235)',
                'text-halo-color': 'rgb(15,15,15)',
                'text-halo-width': 1
            }
        },
        {
            id: 'agnp3_qxjxzzx/1',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'AGNP3_QXJXZZX',
            minzoom: 8,
            maxzoom: 13,
            layout: {
                'text-size': 13,
                'text-rotation-alignment': 'viewport',
                'text-pitch-alignment': 'viewport',
                'text-font': ['Microsoft YaHei Regular'],
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-field': '{NAME}',
                visibility: 'visible'
            },
            paint: {
                'text-color': 'rgb(248,248,248)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'agnp3_qxjxzzx/2',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'AGNP3_QXJXZZX',
            minzoom: 13,
            layout: {
                'icon-image': 'circle-white-2',
                'icon-anchor': 'top',
                'icon-size': 0.3,
                'text-size': 12,
                'text-rotation-alignment': 'viewport',
                'text-pitch-alignment': 'viewport',
                'text-font': ['Microsoft YaHei Bold'],
                'text-anchor': 'bottom',
                'text-max-width': 8,
                'text-field': '{SNAME}',
                'text-optional': true,
                visibility: 'visible'
            },
            paint: {
                'text-color': 'rgb(220,220,220)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'agnp2_djsxzzx/1',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'AGNP2_DJSXZZX',
            minzoom: 6,
            maxzoom: 10,
            layout: {
                'text-field': '{SNAME}',
                'text-font': ['Microsoft YaHei Bold'],
                'text-size': 13,
                'text-anchor': 'center',
                'text-max-width': 8,
                'text-rotation-alignment': 'viewport',
                'text-pitch-alignment': 'viewport',
                'text-optional': true
            },
            paint: {
                'text-color': 'rgb(245,245,245)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'agnp2_djsxzzx/2',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'AGNP2_DJSXZZX',
            minzoom: 10,
            layout: {
                'icon-image': 'circle-white-2',
                'icon-anchor': 'top',
                'icon-size': 0.6,
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Bold'],
                'text-size': 15,
                'text-anchor': 'bottom',
                'text-max-width': 8,
                'text-rotation-alignment': 'viewport',
                'text-pitch-alignment': 'viewport',
                'text-optional': true
            },
            paint: {
                'text-color': 'rgb(240,240,240)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'agnp1_shengjxzzx/2',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'AGNP1_SJXZZX',
            minzoom: 5,
            maxzoom: 6,
            layout: {
                'text-field': '{CITY}',
                'text-font': ['Microsoft YaHei Bold'],
                'text-size': 12,
                'text-anchor': 'center',
                'text-pitch-alignment': 'viewport',
                'text-optional': true
            },
            paint: {
                'text-color': 'rgb(220,220,220)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        },
        {
            id: 'agnp1_shengjxzzx/1',
            type: 'symbol',
            source: 'adminmap',
            'source-layer': 'AGNP1_SJXZZX',
            minzoom: 3,
            maxzoom: 5,
            layout: {
                'text-field': '{NAME}',
                'text-font': ['Microsoft YaHei Bold'],
                'text-size': 12,
                'text-anchor': 'center',
                'text-pitch-alignment': 'viewport',
                'text-optional': true
            },
            paint: {
                'text-color': 'rgb(220,220,220)',
                'text-halo-color': 'rgb(25,25,25)',
                'text-halo-width': 1
            }
        }
    ]
};
