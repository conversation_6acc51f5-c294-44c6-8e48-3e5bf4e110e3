/** @format */

import axios from '_u/ajaxRequest';

const BASE_URL = ServerGlobalConstant.dataUrl;

//公共代码集
export const getCommonCodesFromCache = (data) => {
    return axios.request({
        method: 'post',
        url:
            BASE_URL +
            '/platform/system/commoncodecontroller/getCommonCodesFromCache',
        data: data
    });
};

//获取试卷题目
export const queryKsstList = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/ksgl/KsglController/queryKsstList',
        data: data
    });
};

//提交考试答案
export const submitExamination = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/ksgl/KsglController/submitExamination',
        data: data
    });
};

//查询考试信息
export const getExaminationJbxx = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/ksgl/KsglController/getExaminationJbxx',
        data: data
    });
};

//结束考试
export const finishExamination = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/ksgl/KsglController/finishExamination',
        data: data
    });
};
