@charset "utf-8";


/* 左侧地图控制--start */

.gis-option-wrap{width: 216px; position: fixed; left: 10px; top: 80px;}
.gis-dloption1{border-radius: 5px; box-sizing: border-box; overflow: hidden;}
.gis-dloption1 dt{font-size: 18px; line-height: 40px; height: 40px; text-align: center;}
.gis-ullst1{display: flex; flex-wrap: wrap;}
.gis-ullst1 li{width: 50%; font-size: 16px;  text-align: center; line-height: 40px; box-sizing: border-box; cursor: pointer;}
.gis-ullst1 li sub{font-size: 12px; vertical-align: baseline;}

.gis-optwrap1{padding: 8px 10px 8px 20px;}
.gis-optwrap1 .pd-label{display: flex; align-items: center; height: 35px;}
.gis-optwrap1 .pd-label.on ~ .gis-sublevel{display: block;}

.gis-sublevel{padding-left: 27px; display: none;}
.gis-ullst2 li{font-size: 16px;  line-height: 40px; cursor: pointer; text-align: center;}

.pd-ullst3{padding: 8px 20px;}
.pd-ullst3 li{display: flex; align-items: center; padding: 8px 0;}
.pd-ullst3 li span{font-size: 16px;  padding-left: 14px;}

.swfic { width: 31px; height: 18px; display: inline-block;}

/* 单选和复选 */
.pd-label{display: inline-block; vertical-align: middle;}
.pd-label input{display:none;}
.pd-label i{display: inline-block; vertical-align: middle; background-repeat: no-repeat; background-position: center; width: 16px; height: 16px; margin-right: 10px;}
.pd-label span{display: inline-block; vertical-align: middle; font-size: 16px; }



.darkTheme .gis-dloption1{ border: 1px solid #1c4d73; background-color: rgba(11, 34, 55, 0.8); }
.darkTheme .gis-dloption1 dt{background: #075393;  color: #fff; }
.darkTheme .gis-ullst1 li{ color: #fff;}


.darkTheme .gis-ullst1 li{ border: 1px solid #0e4371; }
.darkTheme .gis-ullst1 li:hover,
.darkTheme .gis-ullst1 li.on{background: #137fdb;}

.darkTheme .gis-optwrap1 .pd-label.arw{background: url(../images/gis_arwbt1.png) no-repeat right center;}
.darkTheme .gis-optwrap1 .pd-label.arw.on{background-image: url(../images/gis_arwtp1.png);}
.darkTheme .gis-ullst2 li{color: #fff; }
.darkTheme .gis-ullst2 li:hover,.gis-ullst2 li.on{background: #137fdb;}

.darkTheme .pd-ullst3 li span{color: #fff; }
.darkTheme .swfic { background-image: url(../images/gis_swfic3.png);}
.darkTheme .swfic.on{ background-image: url(../images/gis_swfic2.png);}


/* 单选和复选 */
.darkTheme .pd-label input[type="checkbox"]~i{background-image:url(../images/gis_checkic.png);}
.darkTheme .pd-label input[type="checkbox"]:checked~i{background-image:url(../images/gis_checkicon.png);}
.darkTheme .pd-label input[type="radio"]~i{background-image:url(../images/gis_radioic.png);}
.darkTheme .pd-label input[type="radio"]:checked~i{background-image:url(../images/gis_radioicon.png);}
.darkTheme .pd-label span{ color: #fff;}



.lightTheme .gis-dloption1{ background-color: rgba(255,255,255,.9);box-shadow: 0 3px 6px rgb(149 149 149 / 25%); border: none;} 
.lightTheme .gis-dloption1 dt{  background: #dfedff;color: #4895ea; }
.lightTheme .gis-ullst1 li{ color: #333;}
.lightTheme .gis-ullst1 li:nth-child(2n){border-left: 1px solid #ddd;}
.lightTheme .gis-ullst1 li:nth-child(n+3){border-top: 1px solid #ddd;}
.lightTheme .gis-ullst1 li:hover,
.lightTheme .gis-ullst1 li.on{background-color: #4895ea; color: #fff;}

.lightTheme .gis-optwrap1 .pd-label.arw{background: url(../images/gis_light_arwbt1.png) no-repeat right center;}
.lightTheme .gis-optwrap1 .pd-label.arw.on{background-image: url(../images/gis_light_arwtp1.png);}
.lightTheme .gis-ullst2 li{color: #333; }
.lightTheme .gis-ullst2 li:hover,.gis-ullst2 li.on{background: #137fdb; color:#fff}

.lightTheme .pd-ullst3 li span{color: #333; }
.lightTheme .swfic { background-image: url(../images/gis_light_swfic1.png);}
.lightTheme .swfic.on{ background-image: url(../images/gis_light_swfic2.png);}


/* 单选和复选 */
.lightTheme .pd-label input[type="checkbox"]~i{background-image:url(../images/gis_light_checkic.png);}
.lightTheme .pd-label input[type="checkbox"]:checked~i{background-image:url(../images/gis_light_checkicon.png);}
.lightTheme .pd-label input[type="radio"]~i{background-image:url(../images/gis_light_radioic.png);}
.lightTheme .pd-label input[type="radio"]:checked~i{background-image:url(../images/gis_light_radioicon.png);}
.lightTheme .pd-label span{ color: #333 !important;}




