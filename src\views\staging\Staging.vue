<!-- @format -->

<template>
    <div
        class="sw1212-wrap"
        style="background: #fff; width: 100%; height: 100%"
    >
        <div>
            <!-- <div class="sw1212-header">
                <img
                    src="@/assets/exam/images/sw1212_logo.png"
                    class="sw1212-logo"
                />
            </div> -->
            <div class="sw1212-container flx1" style="padding: 20px; top: 0">
                <div class="sw1212-lcon" style="width: 66%">
                    <div class="sw1212-banner">
                        <h1>欢迎使用工作台</h1>
                        <input
                            type="text"
                            class="sw1212-inpsrh1"
                            placeholder="请输入"
                        />
                        <!-- <sup>
                            <img
                                :src="weatherIcon"
                                alt=""
                                style="width: 25px; height: 25px"
                            />
                            <i class="i1" style="padding-left: 5px">{{
                                weather.wea || '未知'
                            }}</i>
                            <i class="i1">{{ weather.tem || '-' }}℃</i><em></em
                            ><i class="i2">{{ weather.city || '未知' }}</i></sup
                        > -->
                    </div>
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <div class="sw1212-mod" style="height: 295px">
                        <div class="gap"></div>
                        <div class="flx1 ac jb">
                            <strong class="sw1212-txt1">待办任务</strong>
                            <img
                                @click="openNotice(item, 'zsshAll')"
                                src="@/assets/exam/images/sw1212_arwic1.png"
                                class="icon"
                            />
                        </div>
                        <div class="gap"></div>
                        <div class="gap"></div>
                        <!-- <ul class="sw1212-ullst3" style="padding: 0 15px">
                            <li @click="openNotice(item, 'zssh')">
                                <h1>知识审核</h1>
                                <p>
                                    <i>{{ handleTaskData.ZSKH || 0 }}</i> 件
                                </p>
                            </li>
                        </ul> -->

                        <ul
                            class="sw0228-ullst1"
                            style="height: 218px; overflow-y: auto"
                        >
                            <li
                                v-for="(item, index) in handleTaskData"
                                :key="index"
                            >
                                <b class="type t1">知识审核</b>
                                <span
                                    ><em>标题：</em>{{ item.RWMC || '-' }}</span
                                >
                                <span
                                    ><em>审核状态：</em
                                    >{{ item.DQBZ || '-' }}</span
                                >
                                <span
                                    ><em>创建时间：</em
                                    >{{ item.CJSJ || '-' }}</span
                                >

                                <button
                                    type="button"
                                    class="btn"
                                    @click="openNotice(item, 'zssh')"
                                >
                                    审核
                                    <img
                                        src="@/assets/exam/images/sw0228_arwic2.png"
                                        alt=""
                                    />
                                </button>
                            </li>
                        </ul>
                        <div class="gap"></div>
                        <div class="gap"></div>
                    </div>
                    <div class="gap16"></div>
                    <div class="sw1212-mod">
                        <div class="gap"></div>
                        <div class="flx1 ac jb">
                            <strong class="sw1212-txt1"
                                >提醒（{{ remindTotal }}）</strong
                            >
                            <img
                                src="@/assets/exam/images/sw1212_arwic1.png"
                                class="icon"
                            />
                        </div>
                        <div class="gap"></div>
                        <div class="gap"></div>
                        <div class="sw1212-slideleft1" v-if="showRemind">
                            <div class="hd">
                                <a class="next"></a>
                                <a class="prev"></a>
                            </div>
                            <div class="bd">
                                <ul class="sw1212-ullst2">
                                    <li
                                        v-for="(
                                            item, index
                                        ) in remindData.CONTENT"
                                        :key="index"
                                    >
                                        <span class="text" :title="item.TEXT">
                                            {{ item.TEXT }}
                                        </span>
                                        <sup
                                            @click.stop="closeRemind(item)"
                                        ></sup>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="gap"></div>
                        <div class="gap"></div>
                        <div class="gap"></div>
                    </div>
                    <div class="gap16"></div>
                    <div class="sw1212-mod" style="height: 285px">
                        <div class="gap"></div>
                        <div class="flx1 ac jb">
                            <strong class="sw1212-txt1">通知公告</strong>
                            <img
                                src="@/assets/exam/images/sw1212_arwic1.png"
                                class="icon"
                                @click="openNotice('', 'all')"
                            />
                        </div>
                        <ul
                            class="sw1212-ullst1"
                            style="height: 218px; overflow-y: auto"
                        >
                            <!-- <li class="on">
                                <span class="ico text-ellipsis"
                                    >辐射安全人员2023年第二期考试通知</span
                                ><span style="white-space: nowrap"
                                    >2023-07-01</span
                                >
                            </li> -->
                            <li
                                v-for="(item, index) in noticeList"
                                :key="index"
                                @click="openNotice(item, 'noce')"
                            >
                                <span class="ico text-ellipsis">{{
                                    item.BT || '-'
                                }}</span
                                ><span style="white-space: nowrap">{{
                                    item.CJSJ || '-'
                                }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="sw1212-rcon" style="width: 33.3%">
                    <div class="sw1212-mod">
                        <div class="gap"></div>
                        <div class="flx1 ac jb">
                            <strong class="sw1212-txt1">今日学习情况</strong>
                        </div>
                        <div class="gap"></div>
                        <ul class="sw1212-ulbx1">
                            <li>
                                <dl>
                                    <dt>学习人数</dt>
                                    <dd>
                                        <i>{{ studyInfo.XXRS || 0 }}</i
                                        >人
                                    </dd>
                                </dl>
                                <img
                                    src="@/assets/exam/images/sw1212_ic5.png"
                                    alt=""
                                />
                            </li>
                            <li>
                                <dl>
                                    <dt>阅读量</dt>
                                    <dd>
                                        <i>{{ studyInfo.YDL || 0 }}</i
                                        >次
                                    </dd>
                                </dl>
                                <img
                                    src="@/assets/exam/images/sw1212_ic6.png"
                                    alt=""
                                />
                            </li>
                        </ul>
                        <div class="gap16"></div>
                    </div>
                    <div class="gap16"></div>

                    <div class="sw1212-mod">
                        <div class="gap"></div>
                        <div class="flx1 ac jb">
                            <strong class="sw1212-txt1">月度学习情况</strong>
                        </div>
                        <div class="gap"></div>
                        <div class="gap"></div>
                        <!-- <img
                            src="@/assets/exam/images/sw1212_char1.png"
                            class="db"
                        /> -->
                        <p-bar
                            v-if="showChart"
                            :data="barData"
                            style="width: 100%; height: 270px"
                            :config="{
                                barWidth: 15
                            }"
                        ></p-bar>
                        <div class="gap16"></div>
                    </div>
                    <div class="gap16"></div>

                    <div class="sw1212-mod">
                        <div class="gap"></div>
                        <div class="flx1 ac jb">
                            <div class="sw1212-txt1">
                                <span
                                    style="cursor: pointer"
                                    :style="{
                                        'font-weight':
                                            listType === '1' ? 'bold' : 'unset'
                                    }"
                                    @click="listTypeChange('1')"
                                    >最新上架</span
                                >
                                |
                                <span
                                    style="cursor: pointer"
                                    :style="{
                                        'font-weight':
                                            listType === '2' ? 'bold' : 'unset'
                                    }"
                                    @click="listTypeChange('2')"
                                    >未阅</span
                                >
                            </div>
                        </div>
                        <div class="gap"></div>
                        <div class="gap"></div>
                        <!-- <ul class="sw1212-ulbx2">
                            <li
                                v-for="(item, index) in studyList"
                                :key="item.XH"
                                style="cursor: pointer"
                                @click="previewFile(item)"
                            >
                                <img :src="getBanner(item)" class="img" />
                                <p class="text-ellipsis">{{ item.BT || '' }}</p>
                            </li>
                        </ul> -->

                        <ul
                            class="sw1212-ullst1"
                            style="height: 368px; overflow-y: auto"
                        >
                            <li
                                v-for="(item, index) in studyList"
                                :key="index"
                                @click="previewFile(item)"
                                style="
                                    padding-left: 0;
                                    justify-content: space-between;
                                "
                            >
                                <span
                                    class="ico text-ellipsis"
                                    style="width: 62%"
                                    >{{ item.BT || '-' }}</span
                                ><span style="white-space: nowrap">{{
                                    item.CJSJ || '-'
                                }}</span>
                            </li>
                        </ul>
                        <div class="gap16"></div>
                    </div>
                </div>
            </div>
        </div>

        <FilePreview
            ref="filePreview"
            :url="frameUrl"
            :fileInfo="currentFile"
            :isVideo="isVideo"
            :watermark="watermark"
            :previewData="previewData"
        ></FilePreview>
        <el-dialog
            class="dialog_custom"
            v-model="show"
            :title="
                popType === 'zssh' || popType === 'zsshAll'
                    ? '知识审核'
                    : '通知公告'
            "
            :width="1400"
            :before-close="closePreviewDialog"
        >
            <iframe
                v-if="show"
                :src="listUrl"
                frameborder="0"
                style="width: 100%; height: 700px"
            ></iframe>
        </el-dialog>
    </div>
</template>

<script>
import {
    getTodayLearnInfo,
    getMonthLearnInfo,
    queryTzgg,
    getWaitHandleTaskCount,
    getWarningInfo,
    closeWarningInfo
} from '@/api/staging.js';
import {
    getKnowledgeList,
    getWaterMark,
    getUnreadList
} from '@/api/knowledge.js';
import { analysiscontroller } from '@/api/exchange.js';
import FilePreview from '../knowledge/FilePreview.vue';
import CryptoJS from 'crypto-js';
import { jsonp } from 'vue-jsonp';
import axios from 'axios';
export default {
    name: 'Staging',
    components: {
        FilePreview
    },
    provide() {
        return {
            closePreview: this.closePreview
        };
    },
    data() {
        return {
            popType: '',
            timer: null,
            showChart: true,
            weather: {}, //天气信息
            url: '/stfs_gd/workplatform/workplatformcontroller/',
            listUrl: '',
            show: false,
            frameUrl: '',
            currentFile: {},
            isVideo: false,
            watermark: '',
            previewData: {},
            studyInfo: {}, //今日学习情况数据
            monthStudyInfo: [], //月度学习情况数据
            barData: {
                xAxis: [],
                series: [
                    {
                        name: '学习人数',
                        data: []
                    },
                    {
                        name: '阅读量',
                        data: []
                    }
                ]
            }, //月度学习情况柱状图数据
            studyList: [], //最新上架数据
            noticeList: [], //通知公告列表
            remindData: {
                ZS: '',
                CONTENT: []
            }, //提醒数据
            handleTaskData: [], //待办任务数据
            remindTotal: 0,
            showRemind: false,
            listType: '1'
        };
    },
    created() {
        //
    },
    computed: {
        weatherIcon() {
            if (this.weather.wea_img) {
                return require(`@/assets/images/${this.weather.wea_img}.png`);
            } else {
                return require(`@/assets/images/999.png`);
            }
        }
    },
    mounted() {
        window.addEventListener('resize', this.refreshChart);
        this.$nextTick(() => {
            shipei($('.sw1212-wrap'));
        });
        // this.getWeather();
        this.getData();
        this.getWaterMark();
        this.$nextTick(() => {
            this.refreshChart();
        });
        this.getWarningInfo();
        this.getWaitHandleTaskCount();
    },
    methods: {
        listTypeChange(type) {
            this.listType = type;
            if (type === '1') {
                this.getNewList();
            } else if (type === '2') {
                this.getUnreadList();
            }
        },
        //获取最新上架数据
        async getNewList() {
            //最新上架数据
            this.studyList = (
                await getKnowledgeList({
                    searchTxt: this.searchTxt || '',
                    ZSFL: this.knowledgeClassification || '',
                    ZSXS: this.knowledgeForm || '',
                    pageNum: 1,
                    pageSize: 6
                })
            ).result.list;
        },
        //获取未阅数据
        async getUnreadList() {
            //未阅数据
            this.studyList = (await getUnreadList({})).result;
        },
        async getData() {
            //通知公告数据
            this.noticeList = (await queryTzgg({})).data_json;
            //今日学习情况数据
            this.studyInfo = (await getTodayLearnInfo({})).result;
            //月度学习情况数据
            this.monthStudyInfo = (await getMonthLearnInfo({})).result;
            if (this.monthStudyInfo.length) {
                this.monthStudyInfo.reverse();
                this.monthStudyInfo.map((e) => {
                    this.barData.xAxis.push(e.YF);
                    this.barData.series[0].data.push(e.XXRS);
                    this.barData.series[1].data.push(e.YDL);
                });
            } else {
                this.barData = {
                    xAxis: [],
                    series: [
                        {
                            name: '学习人数',
                            data: []
                        },
                        {
                            name: '阅读量',
                            data: []
                        }
                    ]
                };
            }

            this.getNewList();
        },
        getBanner(item) {
            return ServerGlobalConstant.bannerUrl + item.FmInfo.WJID;
        },
        getWeather() {
            axios.get('http://api.ipify.org/').then((response) => {
                jsonp(
                    `http://v1.yiketianqi.com/free/day?appid=58691734&appsecret=k6CEn8xP&unescape=1&ip=${response.data}`,
                    {}
                ).then((json) => {
                    this.weather = json;
                });
            });
        },
        getWaterMark() {
            getWaterMark({}).then((res) => {
                this.watermark = res.result || '';
            });
        },

        //待办任务数据
        getWaitHandleTaskCount() {
            getWaitHandleTaskCount({
                YHID:
                    window.localStorage.user_id ||
                    window.localStorage.username ||
                    'SYSTEM'
            }).then((res) => {
                this.handleTaskData = res.data_json;
            });
            // analysiscontroller({
            //     xh: '1701417477454033538048',
            //     urlParams: {
            //         isImmediatelyQuery: true,
            //         isView: '',
            //         multi: true
            //     },
            //     pageSize: 1,
            //     pageNum: 5
            // }).then((res) => {
            //     this.handleTaskData = res.list;
            // });
        },

        //提醒数据
        getWarningInfo() {
            getWarningInfo({
                YHID:
                    window.localStorage.user_id ||
                    window.localStorage.username ||
                    'SYSTEM'
            }).then((res) => {
                this.showRemind = false;
                this.remindData = res.data_json;
                this.remindTotal = res.data_json.CONTENT.length;
                setTimeout(() => {
                    this.showRemind = true;
                    this.$nextTick(() => {
                        jQuery('.sw1212-slideleft1').slide({
                            mainCell: '.bd ul',
                            autoPage: true,
                            effect: 'leftLoop',
                            autoPlay: true,
                            vis: 4,
                            trigger: 'click'
                        });
                    });
                }, 200);
            });
        },
        previewFile(item) {
            this.previewData = item;
            this.isVideo = item.XS === 'SP' ? true : false;
            this.currentFile = item.FileInfo;
            let fileUrl;
            if (item.XS === 'WD') {
                fileUrl = `${ServerGlobalConstant.fileDownloadUrl}${item.FileInfo.WJID}?fullfilename=${item.FileInfo.WJMC}&page=2`;
            } else {
                fileUrl = `${ServerGlobalConstant.fileDownloadUrl}${item.FileInfo.WJID}?fullfilename=${item.FileInfo.WJMC}`;
            }
            this.frameUrl =
                ServerGlobalConstant.previewUrl +
                CryptoJS.enc.Base64.stringify(
                    CryptoJS.enc.Utf8.parse(encodeURI(fileUrl))
                ) +
                `&watermarkTxt=${encodeURIComponent(this.watermark)}`;
            this.$refs.filePreview.showPreviewDialog();
        },
        openNotice(item, type) {
            this.popType = type;
            if (type == 'all') {
                this.listUrl = `${this.url}/moreNotice`;
            } else if (type == 'noce') {
                this.listUrl = `${this.url}/noticeDetail?xh=${item.XH}`;
            } else if (type == 'zsshAll') {
                this.listUrl = `/stfs_gd/platform/component/queryservice/analysis/analysiscontroller/showview/1701417477454033538048`;
            } else if (type == 'zssh') {
                this.listUrl = `/stfs_gd/dynamicform/viewresolvercontroller/render/20231130165808089d9e509a524341895caa6f3905416a?isView=true&recordId=${item.ZSXH}`;
            }

            setTimeout(() => {
                this.show = true;
            }, 100);
        },
        closePreview() {
            this.currentFile = {};
            this.previewData = {};
            this.frameUrl = '';
        },
        closePreviewDialog() {
            this.show = false;
        },
        refreshChart() {
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                this.showChart = false;
                let unsync = setTimeout(() => {
                    this.showChart = true;
                    clearTimeout(unsync);
                }, 0);
            }, 200);
        },

        //提醒关闭弹窗
        closeRemind(item) {
            this.$confirm('确定关闭该提醒吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    closeWarningInfo({
                        XH: item.XH,
                        TABLE: item.TABLE
                        // JD: item.JD,
                        // YHID:
                        //     window.localStorage.user_id ||
                        //     window.localStorage.username ||
                        //     'SYSTEM'
                    }).then((res) => {
                        if (res.data_json.success == 0) {
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            this.getWarningInfo();
                        } else {
                            this.$message({
                                type: 'error',
                                message: '删除失败!'
                            });
                        }
                    });
                })
                .catch(() => {});
        }
    }
};
</script>

<style lang="scss" scoped></style>
