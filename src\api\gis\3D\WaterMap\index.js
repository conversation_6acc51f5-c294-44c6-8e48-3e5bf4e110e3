/** @format */

import axios from '_u/ajaxRequest';
import axios2 from 'axios';

// eslint-disable-next-line no-undef
const BASE_URL = ServerGlobalConstant.dataUrl;

// 自动站数量统计
export const getZDZPointCount = (data) => {
    return axios.request({
        url: BASE_URL + '/water/gis/getZDZPointCount',
        method: 'get',
        params: data
    });
};

// 手工站数量统计
export const getSGZPointCount = (data) => {
    return axios.request({
        url: BASE_URL + '/water/gis/getSGZPointCount',
        method: 'get',
        params: data
    });
};

//污染源数量
export const getWryPointCount = (data) => {
    return axios.request({
        url: BASE_URL + '/water/gis/getWryPointCount',
        method: 'get',
        params: data
    });
};

// 左侧专题--手工监测
export const getSgjcStatiion = (data) => {
    return axios.request({
        url: BASE_URL + '/water/gis/getSgjcStatiion',
        method: 'get',
        params: data
    });
};

// 左侧专题--自动站点
export const getZdjcStatiion = (data) => {
    return axios.request({
        url: BASE_URL + '/water/gis/getZdjcStatiion',
        method: 'get',
        params: data
    });
};

// 左侧专题--污染源基本信息
export const getWryjbxx = (data) => {
    return axios.request({
        url: BASE_URL + '/water/gis/getWryjbxx',
        method: 'get',
        params: data
    });
};

// 左侧专题--自动站弹出框
export const getZdStationSzxx = (data) => {
    return axios.request({
        url: BASE_URL + '/water/gis/getZdStationSzxx',
        method: 'get',
        params: data,
        showLoading: false
    });
};

// 左侧专题--手工断面弹出框
export const getScStationSzxx = (data) => {
    return axios.request({
        url: BASE_URL + '/water/gis/getScStationSzxx',
        method: 'get',
        params: data,
        showLoading: false
    });
};

//获取服务器json数据
export const getJosnData = (url) => {
    return axios2.get(url, {
        params: {}
    });
};
