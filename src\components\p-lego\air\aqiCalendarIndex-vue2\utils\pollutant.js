/** @format */

export function getPollutantUnit(name) {
    let lowerCaseName = name ? name.toLowerCase() : '';
    if (/^(co)$/i.test(lowerCaseName)) {
        return 'mg/m³';
    } else if (/^(pm2.5)|(pm10)|(o3)|(so2)|(no2)$/i.test(lowerCaseName)) {
        return 'μg/m³';
    }
    return '';
}

/**
 * 污染物名称转义
 * @param {String} name
 */
export function escapePollutantName(name) {
    return name.replace(/(\d\.*\d*)/g, s => s.sub());
}

/**
 * 污染物名称转义
 * @param {String} name
 * @deprecated
 */
export function escapePollutantNameWithUnit(name, unit) {
    name = `${name}(${unit || 'μg/m³'})`;
    return name.replace(/(\d*\.*\d*\([mμ]g\/m³\))/g, s => s.sub());
}

/**
 * 从字符串提取污染物名
 * @param {String} text - 字符串
 * @param {Boolean} deleteChineseCharater - 是否删除中文字符
 * @param {Boolean} deleteParenthesis - 是否删除括号
 */
export function extractPollutantName(
    text,
    deleteChineseCharater = false,
    deleteParenthesis = false
) {
    if (text) {
        if (deleteChineseCharater) {
            text = text.replace(/[\u4e00-\u9fa5]/g, '');
        }
        if (deleteParenthesis) {
            text = text.replace(/\(|\)/g, '');
        }
        // console.log(text, text.split(/(pm2.5|pm10|o3|so2|no2|co)/gi))
        let arr = text.split(/(pm2.5|pm10|o3|so2|no2|co)/gi);
        return arr.filter(e => e);
    }
    return [];
}
