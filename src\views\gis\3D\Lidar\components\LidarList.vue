<!-- @format -->

<template>
    <div class="qj-pd-ullst1 lidarList">
        <div class="pd-modhd">
            <span>激光雷达</span>
        </div>
        <ul
            class="pd-ul1 otr"
            style="
                display: flex;
                margin-top: 10px;
                margin-bottom: 10px;
                height: 40px;
            "
        >
            <li
                v-for="(item, index) in arrDataType"
                :key="index"
                :title="item.title"
                :class="{ on: item.bh == dataType }"
                @click="changeDataType(item)"
            >
                {{ item.name }}
            </li>
        </ul>

        <div class="condition">
            日期：
            <el-date-picker
                v-model="selectDate"
                type="date"
                placeholder="选择日期"
                :clearable="false"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                @change="selectDateChanged"
                style="float: right"
            >
            </el-date-picker>
        </div>

        <div class="condition">
            <span>半径：</span>
            <span style="float: right; margin-right: 15px">千米</span>
            <span style="float: right; margin-right: 5px">{{
                this.initRidus / 20
            }}</span>
            <el-slider
                style="float: right; width: 110px; margin-right: 15px"
                v-model="initRidus"
                :format-tooltip="formatTooltip2"
                @change="opcSliderChange2"
                :step="5"
                show-stops
            >
            </el-slider>
        </div>

        <div class="condition">
            <span>透明度：</span>
            <span style="float: right; margin-right: 5px">{{
                this.initOpcity / 100
            }}</span>
            <el-slider
                style="float: right; width: 160px; margin-right: 15px"
                v-model="initOpcity"
                :format-tooltip="formatTooltip"
                @change="opcSliderChange"
                :step="10"
                show-stops
            >
            </el-slider>
        </div>

        <div class="condition">
            <span>上限值：</span>
            <span style="float: right; margin-right: 5px">{{
                this.dataType == 'XGXS'
                    ? (this.initMax * 10) / 100
                    : (this.initMax * 5) / 100
            }}</span>
            <el-slider
                style="float: right; width: 150px; margin-right: 15px"
                v-model="initMax"
                :format-tooltip="formatTooltip3"
                @change="opcSliderChange3"
                show-stops
            >
            </el-slider>
        </div>

        <ul class="pd-timelst">
            <li
                v-for="(item, index) of resultList"
                :key="index"
                @click="getLidarData(item)"
                :class="{ on: this.seleItem.XH == item.XH }"
            >
                {{ item.formartStr }}
            </li>
        </ul>

        <div class="scale1">
            <div class="sd">
                <canvas
                    style="width: 100%; height: 100%"
                    ref="scale_kqzl"
                ></canvas>
            </div>
            <div class="scale_desc">
                <p>0</p>
                <p>{{ toFixed(tlMaxVal / 5, 1) }}</p>
                <p>{{ toFixed((tlMaxVal * 2) / 5, 1) }}</p>
                <p>{{ toFixed((tlMaxVal * 3) / 5, 1) }}</p>
                <p>{{ toFixed((tlMaxVal * 4) / 5, 1) }}</p>
                <p>{{ tlMaxVal }}</p>
            </div>
            <p class="dw" id="scale-dw" style="display: block">ug/m3</p>
        </div>
    </div>
</template>

<script>
import * as turf from '@turf/turf';
import Lidar from '../utils/Lidar';
import { lidarMaxTime, lidarDataTimeList, lidarData } from '../api/index';
import lddata from './lddata';

export default {
    data() {
        return {
            show: false,
            resultList: [],
            seleItem: {},

            arrDataType: [
                {
                    bh: 'XGXS',
                    name: '气溶胶消光系数',
                    title: '胶体对光的吸收大小值。 反映污染程度，消光系数值越高，代表球形粒子污染程度越严重。'
                },
                {
                    bh: 'TPB',
                    name: '气溶胶退偏比',
                    title: '垂直分量的散射波强度与平行分量的散射波强度之比。反映粒子的不规则程度，大气分子的退偏比0.296；球形气溶胶颗粒物的退偏比0.1左右；沙尘的退偏比0.15~0.35。退偏比越小，粒子越接近球形。'
                }
            ],

            dataType: 'XGXS',

            selectDate: '',

            initOpcity: 70,
            initMax: 10,
            initRidus: 100,

            tlMaxVal: 1,
            currentData: null,

            resultStr: ' 扫描数据显示除山体遮挡外未见异常点位。',

            layerName: '雷达数据图层'
        };
    },

    components: {},
    props: ['map'],
    mounted() {
        this.initPage();
    },

    unmounted() {
        this.clear();
    },

    methods: {
        initPage() {
            if (!this.map) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }
            this.makeScale(this.$refs['scale_kqzl']);
            this.getMaxTime();
        },

        getMaxTime() {
            let param = {};

            lidarMaxTime(param).then((res) => {
                this.selectDate = res.data.TIME;
                this.getDataList();
            });
        },

        getDataList() {
            let param = {
                time: this.selectDate
            };

            lidarDataTimeList(param).then((res) => {
                this.resultList = res.data.data.map((item, index) => {
                    let arr = item.split('--');

                    return {
                        XH: index,
                        sTime: arr[0],
                        eTime: arr[1],
                        formartStr: arr[0] + ' -- ' + arr[1].substring(10)
                    };
                });

                if (this.resultList.length > 0) {
                    this.getLidarData(this.resultList[0]);
                }
            });
        },

        getLidarData(item) {
            this.seleItem = item;
            let param = {
                type: this.dataType,
                startTime: item.sTime,
                endTime: item.eTime
            };

            lidarData(param).then((res) => {
                if (res && res.length > 0) {
                    this.clear();
                    // console.log(lddata);

                    this.currentData = lddata.data; // res;
                    this.lidarClick(this.currentData);
                } else {
                    this.clear();
                }
            });
        },

        lidarClick(data) {
            //每7.5米一个数据，2000个数据是15公里,  如果initRidus为100， this.initRidus * 50 值是5000， 则表示5公里范围
            let len = Math.floor((this.initRidus * 50) / 7.5);

            let tempData = data.map((item) => {
                // return JSON.parse(item.JCZ).slice(0, len);

                return item.JCZ.split(',').slice(0, len);
            });

            let gap = parseInt(data[1].HEAD) - parseInt(data[0].HEAD);

            let option = {
                startAngle: parseInt(data[0].HEAD),
                gapAngle: gap,
                data: tempData,
                radius: 250, //像素
                max: this.tlMaxVal
            };

            let lidar = new Lidar(option);
            let url = lidar.getImage();

            let center = [parseFloat(data[0].JD), parseFloat(data[0].WD)];
            let radius = this.initRidus / 20;
            let options = {
                steps: 100,
                units: 'kilometers',
                properties: { foo: 'bar' }
            };

            let circle = turf.circle(center, radius, options);
            let bounds = turf.bbox(circle);

            lidar.addImage(
                this.map,
                this.layerName,
                url,
                bounds,
                this.initOpcity / 100
            );
        },

        clear() {
            if (this.map.getLayer(this.layerName)) {
                PowerGL.removeLayerFromName(this.map, this.layerName);
            }
        },

        selectDateChanged() {
            this.getDataList();
        },

        formatTooltip(val) {
            return val / 100;
        },

        formatTooltip2(val) {
            return (val * 5) / 100;
        },

        formatTooltip3(val) {
            if (this.dataType == 'XGXS') {
                return (val * 10) / 100;
            } else {
                return (val * 5) / 100;
            }
        },

        //设置透明度
        opcSliderChange(val) {
            if (this.map.getLayer(this.layerName)) {
                // this.map.setLayoutProperty(
                //     this.layerName,
                //     'visibility',
                //     'visible'
                // );

                this.map.setPaintProperty(
                    this.layerName,
                    'raster-opacity',
                    val / 100
                );
            }
        },

        //设置距离
        opcSliderChange2(val) {
            if (this.map.getLayer(this.layerName)) {
                this.lidarClick(this.currentData);
            }
        },

        getDataSet() {
            let result = [];

            let len = Math.floor(
                (this.arrResult[0].length * this.initRidus) / 100
            );

            for (let item of this.arrResult) {
                result.push(item.slice(0, len));
            }

            return result;
        },

        //设置上限值
        opcSliderChange3(val) {
            let vv =
                this.dataType == 'XGXS' ? (val * 10) / 100 : (val * 5) / 100;
            this.tlMaxVal = vv;

            if (this.map.getLayer(this.layerName)) {
                this.lidarClick(this.currentData);
            }
        },

        //切换数据类型
        changeDataType(item) {
            this.dataType = item.bh;
            //调整图例
            if (this.dataType == 'XGXS') {
                this.tlMaxVal = 1;
            } else {
                this.tlMaxVal = 0.5;
            }
            this.initMax = 10;

            if (this.seleItem) {
                this.getLidarData(this.seleItem);
            }
        },

        /**
         * 生成色带
         **/
        makeScale(colorBar) {
            this.getColor = this.segmentedColorScale([
                [0, [35, 14, 208]],
                [5, [19, 55, 219]],
                [10, [12, 132, 233]],
                [15, [2, 234, 251]],
                [20, [0, 220, 186]],

                [25, [0, 181, 108]],
                [30, [0, 139, 22]],
                [35, [41, 148, 13]],
                [40, [96, 176, 31]],
                [45, [143, 199, 46]],

                [50, [189, 221, 33]],
                [55, [215, 235, 19]],
                [60, [245, 249, 5]],
                [65, [255, 226, 0]],
                [70, [255, 193, 0]],

                [75, [254, 168, 0]],
                [80, [255, 115, 0]],
                [85, [255, 59, 0]],
                [90, [252, 1, 0]],
                [95, [213, 0, 0]],
                [100, [169, 0, 0]]
            ]);

            let c = colorBar,
                g = c.getContext('2d'),
                n = c.width - 1;

            for (let i = 0; i <= n; i++) {
                let rgb = this.getColor(this.spread(i / n, 0, 100), 1, 0, 100);
                g.fillStyle =
                    'rgb(' + rgb[0] + ',' + rgb[1] + ',' + rgb[2] + ')';
                g.fillRect(i, 0, 1, c.height);
            }
        },

        spread(p, low, high) {
            return p * (high - low) + low;
        },

        segmentedColorScale(segments) {
            let points = [],
                interpolators = [],
                ranges = [];
            for (let i = 0; i < segments.length - 1; i++) {
                points.push(segments[i + 1][0]);
                interpolators.push(
                    this.colorInterpolator(segments[i][1], segments[i + 1][1])
                );
                ranges.push([segments[i][0], segments[i + 1][0]]);
            }
            return function (point, alpha, min, max) {
                let i;
                point = (100 * (point - min)) / (max - min);
                for (i = 0; i < points.length - 1; i++) {
                    if (point <= points[i]) {
                        break;
                    }
                }
                let range = ranges[i];
                return interpolators[i](
                    this.proportion(point, range[0], range[1]),
                    alpha
                );
            };
        },

        colorInterpolator(start, end) {
            let r = start[0],
                g = start[1],
                b = start[2];
            let Δr = end[0] - r,
                Δg = end[1] - g,
                Δb = end[2] - b;
            return function (i, a) {
                return [
                    Math.floor(r + i * Δr),
                    Math.floor(g + i * Δg),
                    Math.floor(b + i * Δb),
                    a
                ];
            };
        },

        proportion(x, low, high) {
            return (this.clamp(x, low, high) - low) / (high - low);
        },

        clamp(x, low, high) {
            return Math.max(low, Math.min(x, high));
        },
        /*
         * 保留小数位数
         */
        toFixed(num, deci) {
            if (this.hasDot(num)) {
                return num.toFixed(deci);
            } else {
                return num;
            }
        },

        hasDot(num) {
            if (!isNaN(num)) {
                return (num + '').indexOf('.') != -1 ? true : false;
            } else {
                return false;
            }
        }
    }
};
</script>

<style scoped>
.lidarList {
    width: 300px;
    position: absolute;
    top: 300px;
    background: rgba(14, 37, 56, 0.8);
    left: 40px;
    border-radius: 20px;
    /* padding: 0 20px; */
    height: 650px;
}

.lidarList .pd-ul1 li {
    height: 35px;
    line-height: 35px;
    text-align: center;
    font-size: 14px;
    color: #fff;
    border: 1px solid #056a99;
    border-radius: 300px;
    cursor: pointer;
    padding: 5px !important;
}

.lidarList .pd-ul1.otr li {
    float: left;
    width: 128px;
    height: 26px;
    line-height: 26px;
    margin-right: 7px;
}
.lidarList .pd-ul1 li.on {
    background-color: #056a99;
}

.lidarList .pd-timelst {
    padding: 0 14px;
    height: 300px;
    overflow-y: auto;
}

.lidarList .pd-timelst li {
    overflow: hidden;
    cursor: pointer;
    color: #fff;
    font-size: 16px;
}

.lidarList .pd-timelst li.on span {
    color: #3e89f2;
}
.lidarList .pd-timelst li span {
    font-size: 13px;
    color: #fff;
    float: left;
}

.lidarList .pd-modhd {
    height: 35px;
    background: rgb(5, 106, 153) !important;
    padding: 0 10px;
}

.lidarList .pd-modhd span {
    float: left;
    font-size: 14px;
    color: #fff;
    padding-left: 11px;
    line-height: 35px;
}

.lidarList .condition {
    padding: 0px 15px;
    height: 40px;
    line-height: 40px;
    color: white;
}

.lidarList .condition::after {
    display: block;
    clear: both;
    content: '';
    overflow: hidden;
    height: 0;
}

.lidarList .resultCls {
    height: 65px;
    position: absolute;
    bottom: 50px;
    padding: 10px;
    color: white;
    background-color: #056a99;
    margin: 10px;
    overflow-y: auto;
}

.lidarList .resultCls em {
    font-weight: bold;
}

.scale1 {
    position: absolute;
    bottom: 5px;
    width: 100%;
    height: 60px;
    /* border: solid 1px rgb(4, 5, 5); */
    background: #05364d;
    font-size: 12px;
}

.scale1 .sd {
    width: 230px;
    height: 10px;
    /* float: left; */
    margin: 10px;
}

.scale1 .scale_desc {
    width: 250px;
    height: 30px;
    /* float: left; */
    margin-top: 10px;
}

.scale1 .scale_desc p {
    width: 41px;
    line-height: 25px;
    color: #fff;
    text-align: center;
    float: left;
    /* width: 100%; */
    margin: 0;
}

.scale1 .dw {
    height: 20px;
    line-height: 20px;
    /* width: 190px; */
    /* margin: 0px 5px; */
    color: #fff;
    float: right;
    margin-right: 10px;
    margin-top: -30px;
}
</style>
