<!-- @format -->

<template>
    <li @click="showWindClick()">
        <i class="swfic" :class="{ on: showWind }"></i><span>风场</span>
    </li>
</template>

<script>
import { getScz, getJosnData } from '@/api/gis/3D/AirMap/index';
import WindUtil from './WindUtil';
export default {
    data() {
        return {
            showWind: false,
            windLayer: null //风向图
        };
    },
    props: ['map', 'curentTime', 'isOpen'],

    unmounted() {
        if (this.windLayer) {
            this.windLayer.destory();
        }
    },

    mounted() {
        this.initPage();
    },

    methods: {
        initPage() {
            if (!this.map) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.windLayer = new PowerGL.WindyMap(this.map, {
                lineWidth: 2,
                colorScale: ['RGB(203,235,252)'], // 深色图 203,235,252    浅色图 90,196,251
                frameRate: 15,
                velocityScale: 0.005, // 长度： 默认0.005
                particleAge: 50,
                particleMultiplier: 1 / 800 // 密度：默认 1/400
            });

            if (this.isOpen) {
                setTimeout(() => {
                    this.showWindClick();
                }, 500);
            }
        },

        showWindClick() {
            this.showWind = !this.showWind;

            if (this.showWind) {
                this.map.setPitch(0);
                this.map.setBearing(0);
                this.map.dragRotate.disable();
            } else {
                this.map.dragRotate.enable();
            }
            this.getWindData();
        },

        getWindData() {
            if (this.showWind) {
                //接口
                //this.showData();

                // 直接json
                //this.showData2();

                //直接图片
                this.showData3();

                //1*1 公里网格
                //this.showData4();
            } else {
                if (this.windLayer) {
                    this.windLayer.setVisible(false);
                    // this.windLayer.destory();
                }
            }
        },

        //接口请求json数据
        showData() {
            this.getScz(this.curentTime, 'WIND', (flag, result) => {
                if (flag && result && result.data && result.data[0]) {
                    this.windLayer.setData(result.data);
                } else {
                    this.windLayer.setVisible(false);
                }
            });
        },

        //直接请求json，一般用于测试
        showData2() {
            let url = './gis/3D/data/2022093016_WIND.json';
            getJosnData(url).then((result) => {
                if (result && result && result[0]) {
                    this.windLayer.setData(result);
                } else {
                    this.windLayer.setVisible(false);
                }
            });
        },

        //请求灰度图
        showData3() {
            let url = this.getUrl();
            let scale = 5;
            this.windLayer.loadImg(url, scale);
        },

        // 1*1 公里网格
        showData4() {
            let baseUrl = '/caiyun_uv/HBS/';
            let url = WindUtil.getSrc(baseUrl, this.curentTime);

            WindUtil.loadImage(url)
                .then(
                    (result) => {
                        this.windLayer.setData(result);
                    },
                    (error) => {
                        this.windLayer.destory();
                    }
                )
                .catch((error) => {
                    this.windLayer.destory();
                });
        },

        //获取灰度图地址
        getUrl() {
            let year = this.curentTime.substring(0, 4);
            let month = year + this.curentTime.substring(5, 7);
            let day = month + this.curentTime.substring(8, 10);
            let hour = day + this.curentTime.substring(11, 13);

            let url =
                ServerGlobalConstant.czUrl +
                `/${year}/${month}/${day}/${hour}_WIND.png`;

            url = './gis/3D/data/2023072800_WIND.png';

            return url;
        },

        /**
         * 获取差值与风场
         */
        getScz(currentDate, yz, callBack) {
            getScz({
                date: currentDate + ':00',
                type: 'scz',
                wrw: yz
            }).then(
                (res) => {
                    if (callBack) {
                        callBack(true, res);
                    }
                },
                (res) => {
                    if (callBack) {
                        callBack(false, res);
                    }
                }
            );
        }
    },

    watch: {
        curentTime(val) {
            this.getWindData();
        }
    }
};
</script>

<style></style>
