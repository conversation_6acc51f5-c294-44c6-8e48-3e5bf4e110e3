<!-- @format -->

<!-- 
  使用说明：
    1、look 模式：点位坐标已经存在，仅查看与展示点位的信息，调用方式如下
        http://localhost:8099/#/2d/getPoint?JD=119.042493&WD=28.521057&mod=look&QYMC=企业名称&QYDZ=企业地址

    2、编辑模式下，url 不需要加参数，使用postMessage交互
       http://localhost:8099/#/2d/getPoint


            window.addEventListener(
                    'message',
                    function (e) {
                       
                        if (e.type == 'mapLoadComplete') {  //地图加载完成
                            
                            //子页面发初始化信息
                            $('#id_url_jbxx')[0].contentWindow.postMessage(
                                {
                                    type: 'initData',
                                    item: { JD: 100, WD: 20 }
                                },
                                '*'
                            );
                        } else if (e.type == 'PlotSucess') {
                            console.log(e.item);
                        }
                    },
                    false
                );
 -->

<template>
    <el-container
        class="main"
        :class="{ 'dark-theme': skin == 'dark' }"
        direction="vertical"
        style="height: 100%"
    >
        <el-main class="gisMain" style="padding: 0px; overflow: hidden">
            <div style="width: 100%; height: 100%">
                <esri-map
                    :mapOption="mapOption"
                    @onMapLoaded="onMapLoadedHandle"
                ></esri-map>
            </div>

            <!--  基础工具 (右上) -->
            <TopRightTool
                class="baseTool"
                :mapOption="mapOption"
                :mapCls="mapCls"
                :map="map"
                :class="{ off: !showPanel }"
            ></TopRightTool>

            <!-- 右下角 -->
            <BottomRightTool
                :map="map"
                :mapOption="mapOption"
                class="bottomRightToolCls"
                :class="{ off: !showPanel }"
            ></BottomRightTool>

            <!-- 业务面板 -->
            <BasicMain
                :map="map"
                @mapEvent="mapEventHandle"
                ref="childMain"
            ></BasicMain>
        </el-main>
    </el-container>
</template>
<script>
import EsriMap from '@/components/gis/2D/EsriMap';
import BottomRightTool from '@/components/gis/2D/normal/BottomRightTool';
import TopRightTool from './components/TopRightTool';
import BasicMain from './GetPointMain.vue';

export default {
    name: 'GetPointLayout', //地图绘制、获取坐标模块
    created() {},
    data() {
        return {
            mapOption: {
                basemaps: [
                    {
                        label: '影像图',
                        type: 'tianditumtimage',
                        visible: true,
                        layerControl: true,
                        minImg: 'map-yxt.png'
                    },
                    {
                        label: '电子地图',
                        type: 'tianditumtmap',
                        visible: false,
                        layerControl: true,
                        minImg: 'map-slt.png'
                    }
                ]
            },
            map: null,
            skin: '',
            mapCls: '.gisMain',
            showPanel: false //右侧面板是否关闭
        };
    },
    unmounted() {},
    components: {
        EsriMap,
        TopRightTool,
        BottomRightTool,
        BasicMain
    },
    mounted() {
        this.skin = ServerGlobalConstant.skin;
    },
    methods: {
        //地图加载完成
        onMapLoadedHandle(map) {
            this.map = map;
        },

        //地图事件：与前端交互
        mapEventHandle(type, item) {
            //type：mapClick(地图点位点击)、
            this.$emit('mapEvent', type, item);
        },

        //地图交互方法：供前端调用
        mapFuc(type, item) {
            switch (type) {
                case 'RPanel': //右侧面板
                    this.showPanel = item;
                    // this.$refs.childMain.showPanleChangeHandle(this.showPanel);
                    break;

                case 'pointTo': //定位(item中必须包含JD、WD字段)
                    PowerGis.pointTo(this.map, item, true);
                    break;
            }
        }
    }
};
</script>

<style>
@import '~_as/gis/commom/mapCommon.css';
@import '~_as/gis/commom/mapTool.css';
</style>

<style scoped>
.baseTool {
    position: absolute;
    top: 10px;
    right: 380px;
}

.baseTool.off {
    right: 10px;
}

.bottomRightToolCls {
    position: absolute;
    bottom: 10px;
    right: 380px;
}

.bottomRightToolCls.off {
    right: 10px;
}
</style>
