<!-- @format -->

<!--  -->
<template>
    <div class="cldBody">
        <div class="rl-legend">
            <ul>
                <li>
                    <span class="rl-legend-zc fg"></span>
                    <span>正常</span>
                </li>
                <li>
                    <p class="rl-legend-cb fg"></p>
                    突增
                </li>
                <li>
                    <p class="rl-legend-tj fg"></p>
                    突降
                </li>
                <li>
                    <p class="rl-legend-tc fg"></p>
                    停产
                </li>
            </ul>
        </div>
        <div class="gap"></div>
        <div class="cld-header">
            <p class="cld-rq">{{ StateList.SJ }}</p>
        </div>
        <table>
            <thead>
                <tr id="week">
                    <td>日</td>
                    <td>一</td>
                    <td>二</td>
                    <td>三</td>
                    <td>四</td>
                    <td>五</td>
                    <td>六</td>
                </tr>
            </thead>
            <tbody id="tbody">
                <tr
                    v-html="item"
                    v-for="(item, index) in trArray"
                    :key="index"
                ></tr>
            </tbody>
        </table>
    </div>
</template>

<script>
export default {
    data() {
        return {
            caendarBody: '', //
            trArray: [] //
        };
    },

    props: {
        bodyClass: {
            default: '',
            type: String
        },
        RqData: function () {
            return {};
        },
        StateList: {
            type: Object,
            default: function () {
                return {
                    SJ: '2021-01', //月份  （利2021-01）
                    LIST: [
                        {
                            RQ: 1, // 日期(日)
                            LX: 'ZC' //正常
                        },
                        {
                            RQ: 2, // 日期(日)
                            LX: 'ZC' //正常
                        },
                        {
                            RQ: 3, // 日期(日)
                            LX: 'TC' //停产
                        },
                        {
                            RQ: 4, // 日期(日)
                            LX: 'SS' //停产
                        },
                        {
                            RQ: 5, // 日期(日)
                            LX: 'XJ' //停产
                        },
                        {
                            RQ: 6, // 日期(日)
                            LX: 'ZC' //正常
                        },
                        {
                            RQ: 7, // 日期(日)
                            LX: 'ZC' //正常
                        },
                        {
                            RQ: 8, // 日期(日)
                            LX: 'ZC' //正常
                        },
                        {
                            RQ: 9, // 日期(日)
                            LX: 'ZC' //正常
                        }
                    ]
                };
            }
        },
        watch: {
            StateList(newValue, oldValue) {
                this.initCalander();
            }
        }
    },
    components: {},

    computed: {},

    mounted() {
        this.initCalander();
    },

    methods: {
        getColor(status) {
            let color = 'wsj';
            switch (status) {
                case 'ZC':
                    color = 'qyzc';
                    break;
                case 'SS':
                    color = 'qyss';
                    break;
                case 'XJ':
                    color = 'qyxj';
                    break;
                case 'TC':
                    color = 'qytc';
                    break;
                case 'NODATA':
                    color = 'wsj';
                    break;
                default:
                    break;
            }
            return color;
        },
        initCalander() {
            let that = this;
            let date = new Date();
            date.setFullYear(this.StateList.SJ.slice(0, 4));
            date.setMonth(parseInt(this.StateList.SJ.slice(5, 7)) - 1);
            date.setDate('01');
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let monthDay = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]; // 创建数组存放每个月有多少天 ,默认2月为28天
            // 判断闰年
            if ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0) {
                monthDay[1] = 29;
            }

            // 计算每个月的天数
            let days = monthDay[month - 1];
            // 判断每月第一天为周几
            date.setYear(year); //某年
            date.setMonth(month - 1); //某年的某月
            date.setDate(1); // 某月的某天
            let weekday = date.getDay(); // 判断某天是周几
            let tbodyHtml = '';
            // 补齐每月的日期
            for (let k = 0; k < weekday; k++) {
                tbodyHtml += '<td></td>';
            }

            let changLine = weekday;
            for (let i = 1; i <= days; i++) {
                tbodyHtml +=
                    '<td><p class="' +
                    this.getDayColor(i) +
                    ' com">' +
                    i +
                    '</p></td>';
                changLine = (changLine + 1) % 7;
                if (changLine == 0 && i != days) {
                    //是否换行填充的判断
                    that.trArray.push(tbodyHtml);
                    tbodyHtml = '';
                }

                if (i == days && tbodyHtml.length > 0) {
                    that.trArray.push(tbodyHtml);
                }
            }
            this.caendarBody = tbodyHtml;
            this.$nextTick(function () {
                this.$forceUpdate();
            });
        },
        //返回false 说明日期大于当前日期不会有数据
        determineDate(rq) {
            let crurrentDay = this.$dayjs(new Date()).format('YYYYMMDD');
            let day = '';
            if (parseInt(rq) < 10) {
                day = this.$dayjs(this.StateList.SJ + '-' + '0' + rq).format(
                    'YYYYMMDD'
                );
            } else {
                day = this.$dayjs(this.StateList.SJ + '-' + rq).format(
                    'YYYYMMDD'
                );
            }
            if (parseInt(crurrentDay) < parseInt(day)) {
                return false;
            } else {
                return true;
            }
        },
        getDayColor(e) {
            let array = this.StateList.LIST;
            let index = e;
            let color = 'wsj';

            if (array && array.length >= 0) {
                for (let i = 0; i < array.length; i++) {
                    let item = array[i];
                    if (item.RQ == index) {
                        color = this.getColor(item.LX);
                        return color;
                    }
                }
            }
            return color;
        }
    }
};
</script>
<style scoped>
.cldBody {
    background: #f7f7f7;
    width: 520px;
    margin: 10px 10px;
}

.cld-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.cld-header .cld-rq {
    margin-left: 10px;
}
.cld-header .cld-cbl {
    margin-right: 10px;
}
.cld-header .cld-cbl span {
    margin-left: 5px;
    color: red;
}

.cldBody .top {
    height: 60px;
    line-height: 60px;
    text-align: center;
    position: relative;
}
#topDate {
    font-size: 24px;
}
#week td {
    font-size: 15px;
}
td {
    width: 60px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 20px;
}
#tbody td:hover {
    background: #ededed;
    cursor: pointer;
}
.curDate {
    color: red;
    font-weight: bold;
}
#left,
#right {
    width: 60px;
    height: 60px;
    position: absolute;
    cursor: pointer;
}
#left {
    left: 0;
}
#right {
    right: 0;
}
#left:hover,
#right:hover {
    background-color: rgba(30, 30, 30, 0.2);
}
#tbody,
th,
td {
    text-align: center;
}

/*legend 样式 */
.rl-legend {
    text-align: right;
    margin-right: 30px;
    margin-top: 10px;
}

.rl-legend ul {
    display: flex;
    flex-direction: row;
    justify-content: end;
}
.rl-legend ul li {
    margin-left: 10px;
    text-align: center;
    display: flex;
}

.rl-legend ul li .fg {
    width: 14px;
    height: 14px;
    border-radius: 3px;
    display: inline-block;
    margin-right: 3px;
}

.rl-legend ul li .rl-legend-zc {
    background-color: #00b457;
}

.rl-legend ul li .rl-legend-cb {
    background-color: #f8111a;
}

.rl-legend ul li .rl-legend-tj {
    background-color: #1fc4fb;
}

.rl-legend ul li .rl-legend-tc {
    background-color: #8e8e8e;
}
</style>

<style>
.cldBody .qyss {
    background-color: #f8111a;
}

.cldBody .qyzc {
    background-color: #00b457;
}

.cldBody .qyxj {
    background-color: #1fc4fb;
}

.cldBody .qytc {
    background-color: #8e8e8e;
}

.cldBody .wsj {
    background-color: unset;
    color: unset;
}

.cldBody .com {
    height: 30px;
    width: 30px;
    line-height: 30px;
    margin: 5px;
    border-radius: 3px;
}
</style>
