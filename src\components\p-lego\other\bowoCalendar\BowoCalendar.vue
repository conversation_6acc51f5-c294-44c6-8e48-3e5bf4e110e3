<!-- @format -->
<!-- 
  @Author: maliang
  @Time: 2023/1/16
  @Des: 基础日历组件 在此基础上可封装其它业务组件 如水质日历 详见 src/views/maliangTest/componentViews/BowoCalendarTest
 -->
<template>
    <div class="bowo-calendar">
        <div class="bowo-calendar__head">
            <ul class="bowo-calendar__head-tr">
                <li
                    v-for="week in weekList"
                    :key="week.key"
                    class="bowo-calendar__head-td"
                >
                    {{ week.label }}
                </li>
            </ul>
        </div>
        <div class="bowo-calendar__body">
            <ul class="bowo-calendar__body-tr">
                <li
                    v-for="item in preDays"
                    :key="item"
                    class="bowo-calendar__body-td"
                ></li>
                <li
                    v-for="(item, index) in listDays"
                    :key="index"
                    class="bowo-calendar__body-td"
                >
                    <div v-if="!renderItem">
                        {{ item.day }}
                    </div>
                    <div v-else>
                        <Cell
                            :render="renderItem"
                            :index="index"
                            :info="item"
                        />
                    </div>
                </li>
                <li
                    v-for="item in afterDays"
                    :key="item"
                    class="bowo-calendar__body-td"
                ></li>
            </ul>
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs';
import Cell from './cell';
export default {
    components: {
        Cell
    },
    props: {
        // 包含 YYYY-MM 时间格式
        dateTime: {
            type: String,
            required: true
        },
        list: {
            type: Array,
            default() {
                return [];
            }
        },
        timeFieldKey: {
            type: String,
            default: 'JCSJ'
        },
        renderItem: {
            type: Function
        }
    },
    data() {
        return {
            weekList: [
                {
                    key: '0',
                    label: '周日'
                },
                {
                    key: '1',
                    label: '周一'
                },
                {
                    key: '2',
                    label: '周二'
                },
                {
                    key: '3',
                    label: '周三'
                },
                {
                    key: '4',
                    label: '周四'
                },
                {
                    key: '5',
                    label: '周五'
                },
                {
                    key: '6',
                    label: '周六'
                }
            ], // 表头
            preDays: 0, // 日期前置数量
            afterDays: 0, // 日期后置数量
            arrDays: [],
            yearAndMonth: ''
        };
    },
    computed: {
        listDays() {
            return this.arrDays.map((day) => {
                const target = this.getDayData(day);
                return { ...target, day };
            });
        }
    },
    watch: {
        dateTime: {
            handler(nv) {
                this.initDays();
            },
            immediate: true
        }
    },
    methods: {
        // 根据年月生成日期表格
        initDays() {
            this.yearAndMonth = dayjs(this.dateTime).format('YYYY-MM');
            const daysInMonth = dayjs(this.dateTime).daysInMonth();
            const startDayInMonth = dayjs(this.dateTime).format('YYYY-MM-01');
            const endDayInMonth = dayjs(this.dateTime).format(
                `YYYY-MM-${daysInMonth}`
            );
            const startDayInWeek = dayjs(startDayInMonth).day();
            const endDayInWeek = dayjs(endDayInMonth).day();
            this.preDays = this.weekList.findIndex(
                (_) => _.key == startDayInWeek
            );
            this.afterDays =
                6 - this.weekList.findIndex((_) => _.key == endDayInWeek);
            const num = dayjs(this.dateTime).daysInMonth();
            let arr = [];
            for (let i = 1; i <= num; i++) {
                arr.push(i);
            }
            this.arrDays = arr;
        },
        getDayData(day) {
            const target = this.list.filter((item) => {
                const targetDay = dayjs(item[this.timeFieldKey]).date();
                return targetDay == day;
            });
            return target[0] || {};
        }
    }
};
</script>

<style lang="scss" scoped>
@import './bowo-calendar.scss';
</style>
