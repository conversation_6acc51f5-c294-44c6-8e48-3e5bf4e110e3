<!-- @format -->

<template>
    <div
        class="sw1212-wrap"
        style="background: #fff; width: 100%; height: 100%"
    >
        <div>
            <div style="padding: 20px; top: 0">
                <div>
                    <div style="display: flex; align-items: center">
                        <!-- <p style="font-size: 16px">监测类型：</p>
            <el-select
                v-model="value"
                placeholder="请选择"
                @change="typeChange"
            >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select> -->
                        <p style="font-size: 16px; margin-left: 10px">时间：</p>
                        <el-date-picker
                            @change="timeChange"
                            v-model="time"
                            type="year"
                            placeholder="选择年"
                            style="height: 35px"
                            value-format="YYYY"
                        >
                        </el-date-picker>
                        <p style="font-size: 16px; margin-left: 10px">地市：</p>
                        <el-select
                            v-model="SSDS"
                            placeholder="请选择"
                            @change="SSDSChange"
                        >
                            <el-option
                                v-for="item in SSDSOptions"
                                :key="item.XZQHDM"
                                :label="item.XZQH"
                                :value="item.XZQHDM"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div class="gap"></div>
                    <el-table
                        :data="listData"
                        style="width: 100%; height: 500px"
                    >
                        <el-table-column
                            type="index"
                            width="100"
                            label="序号"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="XZQH"
                            label="区域"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="RYZS"
                            label="人员总数"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="JLZH"
                            label="剂量总和"
                            align="center"
                        >
                            <template #default="scope">
                                <span
                                    class="canClick"
                                    @click="openPage(scope.row)"
                                    >{{ scope.row.JLZH }}</span
                                >
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="MAXNUM"
                            label="最高值"
                            align="center"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="AVGNUM"
                            label="平均值"
                            align="center"
                        >
                        </el-table-column>
                    </el-table>
                    <div class="gap"></div>
                    <p-bar
                        v-if="showChart"
                        :data="lineData"
                        :config="{
                            color: ['#4874cb', '#ef8330', '#f2ba02'],
                            showFillArea: true,
                            barWidth: 10
                        }"
                        style="width: 100%; height: 400px"
                    ></p-bar>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    grjlAvgStatistics,
    queryAdministrativeRegionByCondition
} from '@/api/knowledge.js';

export default {
    name: 'Annual',
    components: {},
    provide() {
        return {};
    },
    data() {
        return {
            options: [
                {
                    value: '1',
                    label: '常规剂量数据采集'
                },
                {
                    value: '2',
                    label: '特殊剂量数据采集'
                }
            ],
            value: '1',
            time: this.$dayjs().format('YYYY'),
            timer: null,
            listData: [],
            lineData: {},
            showChart: true,
            SSDS: '',
            SSDSOptions: [],
            XZQHDM: ''
        };
    },
    created() {
        this.getSSDSOptions();
    },
    computed: {},
    mounted() {
        window.addEventListener('resize', this.refreshChart);

        this.getData();

        this.$nextTick(() => {
            this.refreshChart();
        });
    },
    methods: {
        SSDSChange(e) {
            this.XZQHDM = e;
            this.getData();
        },
        getSSDSOptions() {
            queryAdministrativeRegionByCondition({
                LEVEL: '2',
                FDM: '440000',
                XZJB: '2'
            }).then((res) => {
                this.SSDSOptions = res;
                this.SSDSOptions.unshift({
                    XZQH: '全部',
                    XZQHDM: ''
                });
            });
        },
        getData() {
            grjlAvgStatistics({
                ND: this.time,
                XZQH: this.XZQHDM
            }).then((res) => {
                this.listData = res.data_json;
                this.lineData = {
                    xAxis: [],
                    series: [
                        {
                            name: '个人剂量总和',
                            data: []
                        },
                        {
                            name: '个人剂量最高值',
                            data: []
                        },
                        {
                            name: '个人剂量平均值',
                            data: []
                        }
                    ]
                };
                if (res.data_json.length) {
                    res.data_json.map((e) => {
                        this.lineData.xAxis.push(e.XZQH);
                        this.lineData.series[0].data.push(e.JLZH);
                        this.lineData.series[1].data.push(e.MAXNUM);
                        this.lineData.series[2].data.push(e.AVGNUM);
                    });
                }
            });
        },

        refreshChart() {
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                this.showChart = false;
                let unsync = setTimeout(() => {
                    this.showChart = true;
                    clearTimeout(unsync);
                }, 0);
            }, 200);
        },
        typeChange(e) {
            this.value = e;
            this.getData();
        },
        timeChange(e) {
            this.time = e;
            this.getData();
        },
        openPage(item) {
            window.open(
                '/stfs_gd/platform/component/queryservice/analysis/analysiscontroller/showview/1720432667286032747520?XZQHDM=' +
                    item.XZQHDM
            );
        }
    }
};
</script>

<style lang="scss" scoped>
.canClick {
    color: #005cb9;
    cursor: pointer;
}
.canClick:hover {
    text-decoration: underline;
}
</style>
