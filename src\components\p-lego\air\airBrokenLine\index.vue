<!-- @format -->

<template>
    <p-line
        :data="isAll"
        :config="{
            showFillArea: true
        }"
        :option="showOption"
        style="width: 100%; height: 100%"
    ></p-line>
</template>
<script>
export default {
    created() {
        this.$pChart.setChartConfig({
            THEME_COLOR: 'light'
        });
    },
    props: {
        // 数据
        PlineData: {
            type: Object,
            default: function () {
                return {};
            }
        },
        // 是否多轴
        multiaxis: {
            type: Boolean,
            default: false
        },
        // 轴距等属性操作
        config: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    data() {
        return {
            showOption: {},
            data: {},
            optionPline: {}
        };
    },
    computed: {
        isAll: {
            get() {
                return this.PlineData;
            }
        }
    },
    watch: {
        PlineData: {
            immediate: true, // 立即执行
            handler(newVal) {
                if (this.multiaxis) {
                    this.axleFun('不同轴');
                }
            }
        },
        multiaxis: {
            handler(newVal) {
                if (this.multiaxis) {
                    this.axleFun('不同轴');
                } else {
                    this.axleFun('同轴');
                }
            }
        }
    },

    methods: {
        // 多轴线
        axleFun(val) {
            if (val === '同轴') {
                this.showOption = {};
            } else if (val === '不同轴') {
                let series = [];
                let yAxis = [];
                let offset = -(this.config.offset || 60); //轴距

                this.PlineData.series.forEach((value, index) => {
                    series.push({ yAxisIndex: index });
                    if (index % 2 == 0) {
                        offset = offset + (this.config.offset || 60);
                    }
                    yAxis.push({
                        name: value.name,
                        type: 'value',
                        offset: offset,
                        // nameGap: num1 % 2 ? nameGapa1 : nameGapa2, // 上下距离的位置
                        nameGap: 10,
                        position: index % 2 == 0 ? 'left' : 'right',
                        splitNumber: 10, // Y 轴分隔格数
                        splitLine: {
                            // Y 轴分隔线样式
                            show: false,
                            lineStyle: {
                                color: ['#f3f0f0'],
                                width: 2,
                                type: 'solid'
                            }
                        },
                        axisTick: {
                            //y轴刻度线
                            show: true
                        },
                        axisLine: {
                            //y轴
                            show: true
                        }
                    });
                });
                this.optionPline = {
                    series,
                    yAxis,
                    grid: {
                        containLabel: true,
                        top: '15%',
                        left: '10%',
                        right: '10%'
                    }
                    // 单参数
                    // legend: {
                    //     selectedMode: 'single',
                    // },
                };
                this.showOption = this.optionPline;
            }
        }
    },
    mounted() {
        // 监听图例
        // this.$refs.Pline.chart.on('legendselectchanged', (obj) => {
        //     console.log('obj111', obj);
        // });
    }
};
</script>
