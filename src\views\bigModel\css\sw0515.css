.sw0515-ultxt1 li {
  font-size: 16px;
  color: #0069ff;
  text-decoration: underline;
}
.sw0515-ultxt1 li + li {
  margin-top: 15px;
}

.sw0515-dlbx1 {
  background: #f8faff;
  border-radius: 5px;
  padding: 15px 20px;
}
.sw0515-dlbx1 dt {
  font-size: 16px;
  font-weight: bold;
  color: #3d3d3d;
}
.sw0515-dlbx1 dd {
  font-size: 16px;
  color: #0069ff;
  line-height: 1.8;
  padding-top: 8px;
  text-decoration: underline;
}

.sw0515-altbx1 {
  background: #fff;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 10px;
  position: relative;
}
.sw0515-altbx1:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: #eee;
}

.sw0515-ultbs1 {
  display: flex;
  align-items: center;
}
.sw0515-ultbs1 li {
  height: 36px;
  line-height: 36px;
  padding: 0 20px;
  background: rgba(153, 153, 153, 0.1);
  border-radius: 300px;
  font-size: 16px;
  color: #666;
  cursor: pointer;
}
.sw0515-ultbs1 li.on {
  background: rgba(0, 105, 255, 0.05);
  color: #0069ff;
}
.sw0515-ultbs1 li + li {
  margin-left: 20px;
}

.sw0515-notice1 {
  background: url(../images/sw0515_ic1.png) no-repeat;
  width: 18px;
  height: 23px;
  position: relative;
  cursor: pointer;
}
.sw0515-notice1.on {
  background-image: url(../images/sw0515_ic1_on.png);
}

.sw0515-clsbtn1 {
  background: url(../images/sw0515_ic2.png) no-repeat;
  width: 15px;
  height: 15px;
  cursor: pointer;
}

.sw0515-ulalt1 {
  position: absolute;
  left: 60px;
  bottom: 80%;
  width: 446px;
  height: 180px;
  background: #ffffff;
  box-shadow: 0px 8px 32px 0px rgba(51, 51, 51, 0.1);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #eeeeee;
  padding: 10px;
  box-sizing: border-box;
}
.sw0515-ulalt1 li {
  display: flex;
  align-items: center;
  height: 40px;
  border-radius: 8px 8px 8px 8px;
  font-size: 16px;
  color: #333;
  padding: 0 10px;
}
.sw0515-ulalt1 li.on {
  background: #f8faff;
}
.sw0515-ulalt1 li em {
  margin-right: 7px;
  height: 20px;
  background: rgba(0, 105, 255, 0.05);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #0069ff;
  padding: 0 10px;
  font-size: 14px;
  color: #0069ff;
  line-height: 20px;
}

.sw0515-dlalt1 {
  position: absolute;
  left: 100%;
  bottom: 0;
  width: 286px;
  height: 370px;
  background: linear-gradient(180deg, #EDF4FF 0%, #FFFFFF 100%);
  box-shadow: 0px 8px 32px 0px rgba(51, 51, 51, 0.1);
  border-radius: 16px 16px 16px 0px;
  border: 1px solid #EEEEEE;
  margin-left: 10px;
  padding: 0 20px;
  box-sizing: border-box;
}
.sw0515-dlalt1 dt {
  font-size: 16px;
  color: #3d3d3d;
  font-weight: bold;
}
.sw0515-dlalt1 dd {
  font-size: 16px;
  color: #3d3d3d;
  line-height: 1.8;
  padding-top: 10px;
}

.sw0515-suggestion {
  border-top: 1px solid rgba(53, 128, 255, 0.1);
  padding: 15px 0 20px;
  margin: 0 30px 0 20px;
}
.sw0515-suggestion.type2 ul li {
  background: rgba(153, 153, 153, 0.1);
}
.sw0515-suggestion h1 {
  font-size: 16px;
  color: #3d3d3d;
}
.sw0515-suggestion ul {
  margin-top: 15px;
  display: flex;
  align-items: center;
}
.sw0515-suggestion ul li {
  height: 36px;
  line-height: 36px;
  font-size: 16px;
  color: #666;
  padding: 0 20px;
  background: #FFFFFF;
  border-radius: 300px;
  cursor: pointer;
}
.sw0515-suggestion ul li.on {
  background: #0069FF;
  color: #fff;
}
.sw0515-suggestion ul li + li {
  margin-left: 20px;
}