/**
 * /*
 *
 * @format
 * @Author: Caijw
 * @LastEditors: Caijw
 * @Description: ajaxRequest 获取  (axios 重新封装了一遍，加上token和提示)
 * @Date: 2019-03-28 08:53:24
 * @LastEditTime: 2019-03-28 15:27:04
 */

import axios from 'axios';
import store from '../store';
import { getLocal } from './local';
import * as CONSTANTS from '_const/index';
// 当第一次请求 显示loading  剩下的时候就不调用了
// 当都请求完毕后 隐藏loading
class AjaxRequest {
    // baseURL
    constructor() {
        // 请求的路径
        // eslint-disable-next-line no-undef
        this.baseURL = ServerGlobalConstant.ctx;
        this.timeout = 300000; // 超时时间
        this.queue = {}; // 存放每次的请求 TODO 重复一样的请求会有问题 by 黄冠豪
    }
    merge(options) {
        return { ...options, baseURL: this.baseURL, timeout: this.timeout };
    }
    setInterceptor(instance, options) {
        //每次请求时 都会加一个loading效果
        // 更改请求头
        instance.interceptors.request.use((config) => {
            config.headers.Authorization = getLocal('token');
            if (Object.keys(this.queue).length === 0 && options.showLoading) {
                store.commit(CONSTANTS.SHOW_LOADING);
            }

            if (options.showLoading) {
                this.queue[options.url] = options.url;
            }
            return config;
        });
        // 如果上一个promise 返回了一个常量 会作为下一个promise的输入
        instance.interceptors.response.use(
            (res) => {
                if (options.showLoading) {
                    delete this.queue[options.url]; // 每次请求成功后 都删除队列里的路径
                }

                if (
                    Object.keys(this.queue).length === 0 &&
                    options.showLoading
                ) {
                    store.commit(CONSTANTS.HIDE_LOADING);
                }

                // 状态码拦截器判断，如果是000的话，就返回
                if (
                    res.data.status === CONSTANTS.STATUS_SUCCESS_CODE ||
                    res.data.code == 0 ||
                    res.data.status_code === '0' ||
                    res.data instanceof Array ||
                    res.data.list instanceof Array ||
                    res.data.status_code == 200
                ) {
                    return res.data;
                } else {
                    store.commit('errorMsg', {
                        msg: res.data.msg || '请求异常',
                        res
                    });
                    return Promise.reject(res.data);
                }
            },
            (error) => {
                delete this.queue[options.url]; // 每次请求成功后 都删除队列里的路径
                if (
                    Object.keys(this.queue).length === 0 &&
                    options.showLoading
                ) {
                    store.commit(CONSTANTS.HIDE_LOADING);
                }

                store.commit('errorMsg', {
                    msg: '请求异常',
                    error
                });
            }
        );
    }

    request(options) {
        options = Object.assign(
            {
                showLoading: true
            },
            options
        );
        // url,method
        let instance = axios.create(); // 通过axios库创建一个axios实例
        this.setInterceptor(instance, options);
        let config = this.merge(options);
        return instance(config); // axios执行后返回的是一个promise
    }
}
export default new AjaxRequest();
