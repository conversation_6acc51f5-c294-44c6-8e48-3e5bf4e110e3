<!-- @format -->
<!-- 污染物浓度变化 -->

<template>
    <div class="pollutant-concentration analy-box">
        <div class="rate-box">
            <div id="concentrationBar">
                <table
                    cellpadding="0"
                    class="pd-table1a"
                    v-if="data.tableList.length"
                >
                    <colgroup>
                        <col width="100" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                        <col width="3.1%" />
                    </colgroup>
                    <tbody>
                        <tr
                            v-for="(item, index) in data.tableList"
                            :key="index"
                        >
                            <td
                                class="x-td y-date"
                                v-if="timeType == 'year' && item.date < 12"
                            >
                                {{ item.date }}月
                            </td>
                            <td
                                class="x-td y-date"
                                v-if="timeType == 'year' && item.date == 12"
                                style="
                                    display: block;
                                    line-height: 55px;
                                    height: 65px;
                                "
                            >
                                {{ item.date }}月
                            </td>
                            <td
                                class="x-td y-date"
                                v-if="timeType == 'month'"
                                style="
                                    display: block;
                                    line-height: 55px;
                                    height: 100%;
                                "
                            >
                                {{ item.date }}月
                            </td>
                            <td
                                v-for="(items, indexs) in item.list"
                                :key="indexs"
                            >
                                <div
                                    class="td-p"
                                    :title="getValue(items.SYWRW)"
                                    :style="
                                        'background:' +
                                        getColorBg('AQI', items.AQI) +
                                        ';cursor: pointer;'
                                    "
                                >
                                    <div class="td">
                                        {{ getValueOf(items.AQI) }}
                                    </div>
                                    <div
                                        class="td"
                                        style="
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                            white-space: nowrap;
                                            width: 50px;
                                        "
                                    >
                                        {{ getValue(items.SYWRW) }}
                                    </div>
                                </div>
                                <div
                                    v-if="timeType == 'year' && item.date == 12"
                                    class="x-td x-date"
                                >
                                    {{ indexs + 1 }}日
                                </div>
                                <div
                                    v-if="timeType == 'month'"
                                    class="x-td x-date"
                                >
                                    {{ indexs + 1 }}日
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div v-if="data.fx" class="title_bar">{{ data.fx }}</div>
        </div>
    </div>
</template>

<script>
import util from './util.js';
export default {
    name: 'PollutantConcentration',
    props: {
        data: {
            //渲染的数据
            type: Array,
            default: function () {
                return [];
            }
        },
        timeType: {
            //日历类型   year 或 month
            type: String,
            default: 'month'
        }
    },
    computed: {
        getColorBg() {
            return function (name, value) {
                return util.getLevelPollution(name, value).color;
            };
        },
        getValue() {
            return function (value) {
                if (value) {
                    return util.replacePltName(value);
                } else {
                    return '-';
                }
            };
        },
        getValueOf() {
            return function (value) {
                if (value) {
                    return value;
                } else {
                    return '-';
                }
            };
        }
    }
};
</script>

<style lang="scss" scoped>
.td {
    text-align: center;
    padding: 2px 0;
}
.td-p {
    padding: 2px 0;
    height: 100%;
    max-height: 53px;
    box-sizing: border-box;
    // width: 50px;
}

.pd-table1a {
    margin: 10px 40px;
    margin-top: 0;
    width: initial;
}
.darkTheme .pd-table1a {
    border-color: 1px solid #ddd;
}

.pd-table1a tr {
    // border: 1px solid #fff;
}
.pd-table1a tr td {
    color: #fff;
    border: 1px solid #fff;
}

.darkTheme tr td {
    color: #fff;
    border: 1px solid rgb(0, 66, 128);
}

.x-td {
    text-align: center;
    color: #333;
    height: 53px;
    // line-height: 65px;
}

.darkTheme .x-td {
    color: #fff;
}

.pd-table1a tr .y-date {
    width: 50px;
    color: #333;
}

.darkTheme .pd-table1a tr .y-date {
    color: unset;
}

.title_bar {
    padding: 0 50px;
    font-size: 18px;
    text-indent: 2em;
}

.pollutant-concentration .rate-box {
    width: 100%;
}
</style>
