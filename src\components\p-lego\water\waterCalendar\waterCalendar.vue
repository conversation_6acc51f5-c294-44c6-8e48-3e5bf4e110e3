<!-- @format -->

<template>
    <div class="water-calendar">
        <p class="water-calendar__title">水质日历</p>
        <div class="water-calendar__selector">
            <el-date-picker
                style="width: 128px"
                v-model="dateTime"
                type="month"
                :clearable="false"
                placeholder="选择日期"
                format="YYYY-MM"
                value-format="YYYY-MM"
                @change="changeMonth"
            >
            </el-date-picker>
        </div>
        <BowoCalendar
            :date-time="dateTime"
            :list="list"
            :timeFieldKey="timeFieldKey"
            style="width: 800px"
            :renderItem="renderItem"
        >
        </BowoCalendar>
    </div>
</template>

<script>
import BowoCalendar from './BowoCalendar.vue';
export default {
    props: ['renderItem', 'strDate', 'list', 'timeFieldKey'],
    components: {
        BowoCalendar
    },
    computed: {
        dateTime: {
            get() {
                return this.strDate;
            },
            set(v) {
                this.$emit('update:strDate', v);
            }
        }
    },
    methods: {
        changeMonth(v) {
            this.$emit('change-time', v);
        }
    }
};
</script>

<style lang="scss">
.water-calendar {
    padding: 10px;

    &__title {
        font-size: 18px;
        line-height: 40px;
        padding-left: 12px;
        position: relative;
        text-align: left;
        margin-bottom: 10px;

        &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #0dc8ef;
        }
    }

    &__selector {
        position: relative;
        text-align: left;
        margin-bottom: 10px;
    }
}
</style>
