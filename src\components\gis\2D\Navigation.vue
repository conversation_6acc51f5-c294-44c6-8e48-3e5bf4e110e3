<!-- @format -->

<template>
    <ul class="zy-tools-gis">
        <li @click="gotoBack">
            <i class="tool-home" title="初始范围"></i>
        </li>
        <li @click="zoomOutClick">
            <i class="tool-zoomIn" title="放大地图"></i>
        </li>
        <li @click="zoomInClick">
            <i class="tool-zoomOut" title="缩小地图"></i>
        </li>
    </ul>
</template>

<script>
export default {
    name: 'MapTool',
    props: ['map', 'mapOption'],
    data() {
        return {};
    },
    components: {},
    computed: {},
    mounted() {},
    methods: {
        gotoBack() {
            let option = {};

            if (this.mapOption && this.mapOption.extent) {
                option.mapOptions = this.mapOption;
            } else {
                option = GisServerGlobalConstant.arcgis;
            }
            PowerGis.initExtent(option, this.map);
        },

        zoomOutClick() {
            PowerGis.mapControl(this.map, 'zoomIn');
        },

        zoomInClick() {
            PowerGis.mapControl(this.map, 'zoomOut');
        }
    },
    watch: {}
};
</script>

<style scoped></style>
