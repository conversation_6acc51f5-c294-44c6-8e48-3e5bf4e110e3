<!-- @format -->

<template>
    <div style="width: 100%; height: 100%">
        <canvas :width="canvas.width" :height="canvas.height" ref="canvasDiv">
        </canvas>
    </div>
</template>

<script>
export default {
    name: 'CircleProgress',
    props: {
        ringWidth: {
            type: Number,
            default: 5
        },
        ringColor: {
            type: String,
            default: 'rgba(0,225,225, 1)'
        },
        ringShadowColor: {
            type: String,
            default: 'rgba(225,225,225,0.8)'
        },
        progress: {
            type: Number,
            default: 50
        },
        max: {
            type: Number,
            default: 100
        }
    },

    data() {
        return {
            canvas: {
                width: 100,
                height: 100
            }
        };
    },

    mounted() {
        this.resetCanvasDimen();
        this.$nextTick(() => {
            this.draw();
        });
    },

    watch: {
        ringWidth: function (val) {
            this.redraw();
        },

        progress: function (val) {
            this.redraw();
        },

        ringColor: function (val) {
            this.redraw();
        },

        ringShadowColor: function (val) {
            this.redraw();
        }
    },

    methods: {
        resolveCanvasContext() {
            // return document.getElementById('canvasDiv').getContext('2d');

            return this.$refs.canvasDiv.getContext('2d');
        },

        draw() {
            if (!this.$refs.canvasDiv) {
                return;
            }
            let circle = this.fixCircleCenterAndRadius(this.$el);
            let context = this.resolveCanvasContext();
            context.lineWidth = this.ringWidth;
            this.drawShadowRing(context, circle);
            let angle = this.drawProgressRing(context, circle);
            this.drawRingEndDot(context, circle, angle);
        },

        redraw() {
            if (!this.$refs.canvasDiv) {
                return;
            }
            this.invalidDraw();
            this.draw();
        },

        invalidDraw() {
            let context = this.resolveCanvasContext();
            context.clearRect(
                0,
                0,
                this.$el.clientWidth,
                this.$el.clientHeight
            );
        },

        resetCanvasDimen() {
            this.canvas.width = this.$el.clientWidth;
            this.canvas.height = this.$el.clientHeight;
        },

        //确定圆的中心点及半径
        fixCircleCenterAndRadius(container) {
            let width = container.clientWidth;
            let height = container.clientHeight;
            let minEdgeLength = Math.min(width, height);
            return {
                x: width / 2,
                y: height / 2,
                radius: minEdgeLength / 2 - this.ringWidth * 2
            };
        },

        //绘制圆环背景色
        drawShadowRing(context, circle) {
            context.strokeStyle = this.ringShadowColor;
            context.beginPath();
            context.arc(circle.x, circle.y, circle.radius, 0, 2 * Math.PI);
            context.stroke();
            context.closePath();
        },

        //绘制圆环进度
        drawProgressRing(context, circle) {
            context.strokeStyle = this.ringColor;
            context.beginPath();
            let angle = (this.progress / this.max) * 2 + 1.5;
            context.arc(
                circle.x,
                circle.y,
                circle.radius,
                1.5 * Math.PI,
                angle * Math.PI
            );
            context.stroke();
            context.closePath();
            return angle;
        },

        drawRingEndDot(context, circle, angle) {
            let x = Math.floor(
                circle.x + circle.radius * Math.cos(angle * Math.PI)
            );
            let y = Math.floor(
                circle.y + circle.radius * Math.sin(angle * Math.PI)
            );
            context.beginPath();
            context.arc(x, y, this.ringWidth * 1.5, 0, 2 * Math.PI);
            context.fillStyle = this.ringColor;
            context.fill();
            context.closePath();
        }
    }
};
</script>

<style scoped></style>
