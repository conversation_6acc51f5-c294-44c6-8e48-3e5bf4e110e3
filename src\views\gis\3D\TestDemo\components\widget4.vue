<!-- @format -->
<!-- 线绘制 -->
<template>
    <div></div>
</template>

<script>
import PointUtil from '../utils/PointUtil';
export default {
    props: [],
    data() {
        return {
            map: null,
            pointUtil: null,
            layerID: '线图层'
        };
    },

    unmounted() {
        this.clear();
    },

    mounted() {
        this.initPage();
    },

    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.addLayer();
        },

        addLayer() {
            fetch('./gis/3D/data/hjyjkj.json')
                .then((res) => res.json())
                .then((json) => {
                    let params = {
                        id: this.layerID
                    };

                    //直接用面数据也可以绘制线
                    this.pointUtil.addLine(json.features, params);
                });
        },

        clear() {
            this.pointUtil.removeLayerByName(this.layerID);
        },

        pointClickHandle() {}
    }
};
</script>

<style scoped></style>
