<!-- @format  -->
<!-- 底图切换  -->
<template>
    <ul
        class="map-tabs-gis on"
        @mouseenter="mouseHandel('enter')"
        @mouseleave="mouseHandel('leave')"
    >
        <li
            v-for="(item, index) of arrMap"
            :key="index"
            v-show="showAll || item.label == selectMap"
            :class="{ cur: item.label == selectMap }"
            @click="changeMap(item)"
        >
            <img :src="'./gis/3D/images/common/' + item.minImg" alt="" />
            <p>{{ item.label }}</p>
        </li>
    </ul>
</template>

<script>
export default {
    name: 'MapSwitchTool',
    props: ['map'],
    data() {
        return {
            arrMap: [],
            selectMap: '',
            showAll: false
        };
    },
    components: {},
    computed: {},
    mounted() {
        this.arrMap = GisServerGlobalConstant.mapbox.basemaps.filter((item) => {
            return item.layerControl == true;
        });

        this.arrMap.map((item) => {
            if (item.visible) {
                this.selectMap = item.label;
            }
        });
    },
    methods: {
        mouseHandel(type) {
            if (type == 'enter') {
                this.showAll = true;
            } else {
                this.showAll = false;
            }
        },

        // 图层切换
        changeMap(item) {
            this.selectMap = item.label;

            //暂存业务图层数据
            let arrTempLayers = this.map.getStyle().layers.filter((layer) => {
                return layer.metadata && layer.metadata.isNotBaseMap;
            });

            let arrSource = [];

            for (let layer of arrTempLayers) {
                let source = this.map.getSource(layer.source);
                arrSource.push(source);
            }

            //切换地图
            for (let obj of this.arrMap) {
                if (obj.label == item.label) {
                    let baseUrl =
                        window.location.origin + window.location.pathname;
                    item.style.sprite = baseUrl + 'gis/3D/sprite/sprite';
                    item.style.glyphs =
                        './gis/3D/tilecache/mapbox_res/fonts/{fontstack}/{range}.pbf';

                    this.map.setStyle(item.style);
                }
            }

            GisServerGlobalConstant.mapbox.basemaps.map((objMap) => {
                if (objMap.label == this.selectMap) {
                    objMap.visible = true;
                } else {
                    objMap.visible = false;
                }
            });

            //重新加上业务图层
            for (let source of arrSource) {
                this.map.addSource(source.id, source._options);
            }

            for (let layer of arrTempLayers) {
                this.map.addLayer(layer);
            }

            this.$emit('baseMapChange');
        }
    },
    watch: {}
};
</script>

<style scoped></style>
