<!-- @format -->

<template>
    <div ref="chartdiv"></div>
</template>

<script>
import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import am4themes_animated from '@amcharts/amcharts4/themes/animated';
export default {
    name: 'normalPie',
    props: {
        data: {
            type: Array,
            default: function () {
                return []; //[{ num: 1512, name: '未分派', percent: 100 }]
            }
        }
    },
    watch: {
        data: 'renderPie'
    },
    mounted() {
        this.renderPie();
    },
    methods: {
        renderPie() {
            let data = this.data;
            // Themes begin
            am4core.useTheme(am4themes_animated);
            // Themes end

            let chart = am4core.create(
                this.$refs.chartdiv,
                am4charts.PieChart3D
            );
            chart.hiddenState.properties.opacity = 0; // this creates initial fade-in

            let newArr = [];
            data.forEach((item, index) => {
                let color;
                if (index == 0) {
                    color = am4core.color('#FEBB38');
                } else if (index == 1) {
                    color = am4core.color('#19C8FD');
                } else if (index == 2) {
                    color = am4core.color('#5438FB');
                } else if (index == 3) {
                    color = am4core.color('#C7C323');
                } else if (index == 4) {
                    color = am4core.color('#25C58F');
                }

                item.color = color;
                newArr.push(item);
            });

            chart.data = newArr;

            chart.innerRadius = am4core.percent(20);
            chart.depth = 20;
            // chart.legend = new am4charts.Legend();
            // 改变图例位置
            // chart.legend.position ='top'
            // 改变图例展示的值
            // chart.legend.valueLabels.template.text = "{NAME.''}";

            let series = chart.series.push(new am4charts.PieSeries3D());
            series.dataFields.value = 'num';
            series.dataFields.depthValue = 'num';
            series.dataFields.category = 'name';
            series.slices.template.cornerRadius = 5;
            // 改变颜色
            series.slices.template.propertyFields.fill = 'color';
            // 改变延伸线 展示 的值
            series.labels.template.text = '{category}: {num} {percent}%';

            // 去掉LOGO
            let eles = document.querySelectorAll('[aria-labelledby$=-title]');
            for (let i = 0; i < eles.length; i++) {
                eles[i].style.visibility = 'hidden';
            }
        }
    }
};
</script>

<style scoped></style>
