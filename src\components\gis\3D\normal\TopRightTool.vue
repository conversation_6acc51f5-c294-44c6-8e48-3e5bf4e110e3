<!-- @format -->

<template>
    <div>
        <!-- 导航 -->
        <Navigation :map="map"></Navigation>

        <!-- 指北针 -->
        <CompassControl :map="map"></CompassControl>

        <!-- 地图全屏 -->
        <FullScreen :map="map" :mapCls="mapCls"></FullScreen>

        <!-- 地图模式切换 -->
        <MapMode :map="map"></MapMode>

        <!-- 行政区地图 -->
        <MapXZQ :map="map" :dock="dock"></MapXZQ>

        <MapMeasure :map="map" :dock="dock"></MapMeasure>

        <!-- 图层管理 -->
        <!-- <LayerManagement :map="map" :dock="dock"></LayerManagement> -->

        <!-- 导出图片 -->
        <ExportMap :map="map"></ExportMap>
    </div>
</template>

<script>
import Navigation from '../Navigation';
import CompassControl from '../CompassControl';
import FullScreen from '../FullScreen';
import MapMode from '../MapMode';
import LayerManagement from '../LayerManagement';
import MapXZQ from '../MapXZQ';
import MapMeasure from '../MapMeasure';
import ExportMap from '../ExportMap.vue';

export default {
    name: 'TopRightTool',
    props: ['map', 'mapCls', 'dock'],
    data() {
        return {};
    },
    components: {
        Navigation,
        CompassControl,
        FullScreen,
        MapMode,
        LayerManagement,
        MapXZQ,
        MapMeasure,
        ExportMap
    },
    computed: {},
    mounted() {},
    methods: {},
    watch: {}
};
</script>

<style scoped></style>
