@font-face {
    font-family: "PMZD";
    src: url("../fonts/pmzd.woff2") format("woff2"), url("../fonts/pmzd.woff") format("woff");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "DIN-Bold";
    src: url("../fonts/DIN-Bold.woff2") format("woff2"), url("../fonts/DIN-Bold.woff") format("woff"), url("../fonts/DIN-Bold.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "DIN-Medium";
    src: url("../fonts/DIN-Medium.woff2") format("woff2"), url("../fonts/DIN-Medium.woff") format("woff"), url("../fonts/DIN-Bold.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
}

.shipei-wrap {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.yy-wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 1920px;
    height: 1080px;
    transform-origin: 0 0;
}

.yy-videobox {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    background: #fff;
    border-radius: 16px;
    z-index: 1000;
    padding: 30px;
    box-sizing: border-box;
}

.yy0424-cls {
    width: 45px;
    height: 45px;
    cursor: pointer;
    position: absolute;
    top: 104%;
    left: 50%;
    transform: translateX(-50%);

}

.yy0424-alert1 {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    z-index: 1000;
}

.yy-alert1 {
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    z-index: 1000;
    position: relative;
}

.yy0424-alert1 .yy-alert1+.yy-alert1 {
    margin-left: 13px;
}

.yy-alert1 .a-hd {
    height: 55px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    box-sizing: border-box;
}

.yy-alert1 .a-hd .tit {
    font-weight: 700;
    font-size: 18px;
    color: #333333;
    line-height: 55px;
}

.yy-alert1 .a-hd .close1 {
    width: 15px;
    height: 15px;
    background: url(../images/yy0424-cls2.png) no-repeat center center;
    background-size: 100% 100%;
    cursor: pointer;
}

.yy-alert1 .a-bd {
    position: absolute;
    left: 0;
    right: 0;
    top: 50px;
    bottom: 0;
    height: calc(100% - 50px);
    box-sizing: border-box;
}

.yy-selebox>label {
    font-size: 16px;
    color: #333;
    width: 90px;
    margin-right: 10px;
    height: 36px;
    line-height: 36px;
    float: left;
    text-align: right;
    vertical-align: middle;
    box-sizing: border-box;
    position: relative;
}

.yy-selebox .redx2 {
    color: #e44141;
    font-size: 16px;
    position: absolute;
    top: 0;
    left: 0;
}

.yy-selebox {
    margin-bottom: 24px;
}

.yy-selebox .redx {
    color: #e44141;
    font-size: 16px;
}

.yy-selebox .iptbox {
    width: 320px;
    line-height: 36px;
    position: relative;
    margin-left: 100px;
}

.yy-selebox .tskipt1 {
    width: 100%;
    height: 36px;
    border: solid 1px #ddd;
    padding-left: 12px;
    font-size: 16px;
    color: #333;
    box-sizing: border-box;
    border-radius: 8px;
    padding-right: 35px;
}

.yy-selebox .del-ic {
    width: 14px;
    height: 13px;
    display: inline-block;
    position: absolute;
    right: 10px;
    top: 11px;
    cursor: pointer;
}

.yy-selebox .error-tip {
    font-size: 14px;
    color: #f03939;
    padding-top: 7px;
    line-height: 1;
    cursor: pointer;
    position: absolute;
    top: 100%;
    left: 0;
}

.gap20 {
    height: 20px;
}

.passsafe {
    width: 85%;
}

.passsafe .itm {
    flex: 1;
    height: 10px;
    border-radius: 16px;
    background-color: #eee;
}

.passsafe .itm.red {
    background-color: #E94747;
}

.passsafe .itm.orange {
    background-color: #FAAD14;
}

.passsafe .itm.green {
    background-color: #09B361;
}

.passsafe .itm+.itm {
    margin-left: 10px;
}

.yy0424-qd {
    font-size: 16px;
    color: #333333;
}

.yy0424-btns {
    display: flex;
    align-items: center;
    position: absolute;
    right: 30px;
    bottom: 30px;
}

.yy-btn {
    width: 100px;
    display: inline-block;
    cursor: pointer;
    font-size: 16px;
    color: #333333;
    text-align: center;
    box-sizing: border-box;
    height: 36px;
    line-height: 36px;
    border-radius: 8px;
}

.yy-btn:hover {
    opacity: 0.8;
}

.yy-btn+.yy-btn {
    margin-left: 10px;
}

.yy-btn.bgbtn {
    background: #0069FF;
    border: none;
    color: #fff;
}

.yy-btn.bordbtn {
    border: 1px solid #ddd;
    background: none;
}

.yy0424-txtwrap {
    padding: 0 30px;
}

.yy0223-texta1 {
    width: 100%;
    display: block;
    resize: vertical;
    padding: 10px 15px;
    line-height: 1.5;
    box-sizing: border-box;
    font-size: 18px;
    color: #333;
    border: solid 1px #ddd;
    background-color: #fff;
    border-radius: 8px;
    word-break: break-all;
    resize: none;
}

.yy0223-texta1::-webkit-input-placeholder {
    color: 999;
}

.yy-selebox .tskipt1::-webkit-input-placeholder {
    color: 999;
}

.yy0223-texta1:focus {
    border-color: #0069FF;
}

.yy0223-texta1.error-border {
    border-color: #E94747;
}

.yy-selebox .tskipt1:focus {
    border-color: #0069FF;
}

.error-tip {
    font-size: 16px;
    color: #E94747;
    padding-top: 7px;
    line-height: 1;
    cursor: pointer;
    position: relative;
    top: 100%;
    left: 0;
}

.yy0424-tipbox {
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
}

.yy0424-rside {
    position: relative;
}

.yy0424-tick {
    width: 500px;
    margin: 0 auto;
    font-size: 16px;
    color: #333333;
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FFFFFF;
    box-shadow: 0px 8px 32px 0px rgba(51, 51, 51, 0.1);
    border-radius: 16px;
    padding: 0 20px;
    box-sizing: border-box;
}

.yy0424-tick .ic {
    margin-right: 8px;
}

.yy0424-erro {
    width: 350px;
    margin: 0 auto;
    font-size: 16px;
    color: #333333;
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FFFFFF;
    box-shadow: 0px 8px 32px 0px rgba(51, 51, 51, 0.1);
    border-radius: 16px;
    padding: 0 20px;
    box-sizing: border-box;
}

.yy0424-erro em {
    color: #3580FF;
    cursor: pointer;
}

.yy0424-erro .ic {
    margin-right: 8px;
}

.yy0424-cls3 {
    cursor: pointer;
}


.zy0315-play {
    height: 26px;
    display: flex;
    align-items: center;
    padding-top: 25px;
    padding-bottom: 25px;
}

.zy0315-play .ic {
    width: 26px;
    height: 26px;
    background: url(../images/yy0424-play.png) center no-repeat;
    cursor: pointer;
    margin-right: 10px;
}

.zy0315-play .bolang2 {
    display: flex;
    align-items: center;
}

.zy0315-play .bolang2 span {
    width: 4px;
    height: 8px;
    background: #666666;
    border-radius: 2px;
    margin-right: 4px;
    animation-name: audio-wave2;
    animation-duration: 1.2s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
    animation-play-state: running;
}

.zy0315-play.off .ic {
    background-image: url(../images/yy0424-pouse.png);
}

.zy0315-play.off .bolang2 span {
    animation-play-state: paused;
}

@keyframes audio-wave2 {
    0% {
        height: 8px;
        transform: translateY(0);
    }

    25% {
        height: 8px;
        transform: translateY(0);
    }

    50% {
        height: 20px;
        /* transform: translateY(0) scaleY(1); */
    }

    75% {
        height: 8px;
        transform: translateY(0);
    }

    100% {
        height: 8px;
        transform: translateY(0);
    }
}

.yy-desf {
    font-size: 16px;
    color: #3D3D3D;
}

.yy0424-pousebox {
    width: 120px;
    height: 33px;
    background: #FFFFFF;
    border-radius: 100px;
    text-align: center;
    padding-left: 16px;
    box-sizing: border-box;
    font-size: 16px;
    color: #3D3D3D;
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 170%;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
}

.yy0424-pousebox .ic {
    width: 17px;
    height: 17px;
    background: url(../images/yy-pouse2.png) center no-repeat;
    background-size: 100% 100%;
    margin-right: 7px;
}

.yy0424-bot {
    position: relative;
}