{"name": "app", "version": "0.1.0", "private": true, "scripts": {"fastInstall": "yarn install", "inject": "node ./node_modules/p-lego/bin/inject.js", "start": "vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dll": "webpack --config webpack.dll.config.js --mode production"}, "dependencies": {"@amcharts/amcharts4": "^4.10.34", "@cgcs2000/mapbox-gl": "^2.3.0", "@mapbox/togeojson": "^0.16.0", "@turf/turf": "^6.5.0", "PowerArcgis": "^1.2.3", "PowerMapbox": "^1.3.6", "axios": "^0.26.1", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.0", "driver.js": "^0.9.8", "echarts": "^5.4.0", "echarts-liquidfill": "^3.0.0", "echarts3": "yarn:echarts@3.3.2", "element-plus": "^2.2.26", "esri-loader": "^2.14.0", "highcharts": "^10.3.3", "highcharts-vue": "^1.4.0", "lodash": "^4.17.21", "mapbox-bowo": "^2.15.1", "mapbox-gl-draw-circle": "^1.1.2", "marked": "^15.0.6", "mitt": "^3.0.0", "mockjs": "^1.1.0", "p-charts": "^3.1.0", "p-lego": "0.0.4", "video.js": "^8.6.1", "vue": "^3.2.13", "vue-jsonp": "^2.0.0", "vue-router": "^4.0.3", "vue3-gemini-scrollbar": "^1.0.1", "vuex": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "clean-webpack-plugin": "^4.0.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.1.3", "less-loader": "^11.1.0", "postcss-preset-env": "^7.0.0", "prettier": "^2.4.1", "sass": "^1.32.7", "sass-loader": "^12.0.0", "thread-loader": "^3.0.4", "unplugin-element-plus": "^0.3.4", "webpack-cli": "^4.9.2"}}