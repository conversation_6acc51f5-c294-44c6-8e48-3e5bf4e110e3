/** @format */

import axios from '_u/ajaxRequest';
import axios2 from 'axios';
const BASE_URL = ServerGlobalConstant.dataUrl;

export const pwdLogin = (data) => {
    return axios.request({
        url: 'pwdLogin',
        method: 'get',
        params: data
    });
};

//获取知识库集合
export const getCommonCodesFromCache = (data) => {
    return axios.request({
        url:
            BASE_URL +
            '/platform/system/commoncodecontroller/getCommonCodesFromCache',
        method: 'post',
        data: data
    });
};

//获取历史记录List
export const getSessionData = (data) => {
    return axios.request({
        url: BASE_URL + '/gd/gpt/getSessionData',
        method: 'post',
        data: data
    });
};

//获取历史记录Detail
export const getChatData = (data) => {
    return axios.request({
        url: BASE_URL + '/gd/gpt/getChatData',
        method: 'post',
        data: data
    });
};

//保存当前对话
export const saveChat = (data) => {
    return axios.request({
        url: BASE_URL + '/gd/gpt/saveChat',
        method: 'post',
        data: data
    });
};
