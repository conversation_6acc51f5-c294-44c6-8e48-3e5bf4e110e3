<!-- @format -->

<template>
    <div class="gis-timeaxis">
        <img
            @click="playClick"
            v-show="!timeId && showPlay"
            src="../../../assets/gis/commom/images/dark/gis_plybtn.png"
            class="pd-play"
            alt="渲染失败"
        />

        <img
            v-show="timeId && showPlay"
            @click="pauseClick"
            src="../../../assets/gis/commom/images/dark/gis_pausebtn.png"
            class="pd-play"
            alt="渲染失败"
        />
        <!-- <div v-show="showDatePick">
                <el-date-picker
                    v-model="selectDate"
                    type="year"
                    placeholder="选择日期"
                    class="dateCls2"
                    :clearable="false"
                    value-format="YYYY"
                    format="YYYY"
                    @change="selectDateChanged"
                >
                </el-date-picker>
            </div> -->
        <ul class="pd-ulnumlst">
            <li
                v-for="(item, index) of arrDate"
                :key="index"
                :class="{
                    type1: item.level == '1',
                    type2: item.level == '2',
                    type3: item.level == '3',
                    type4: item.level == '4',
                    type5: item.level == '5',
                    type6: item.level == '6',
                    on: selectDate == item.sj,
                    cur: item.sfyj == '1'
                }"
                @click="itemClick(item, index)"
            >
                {{ item.sj }}<sup>{{ item.tipStr }}</sup>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    data() {
        return {
            selectDate: '2021', //当前选中的日历
            arrDate: [], //
            timeId: '',
            lastDate: '',

            showDatePick: false, // 是否显示日期控件
            showPlay: false, // 是否显示播放控件
            selectIndex: 0, //当前选中的序号
            maxIndex: 0, //最大索引号
            minIndex: 0, //最小索引号

            nums: 1, //数量
            interVal: 1 //播放间隔
        };
    },
    components: {},
    unmounted() {
        this.clearTime();
    },
    methods: {
        selectDateChanged() {
            this.intData();

            this.$emit('dateChange', this.selectDate, false);
        },

        //初始化timeline
        initTimeLine(option) {
            this.lastDate = option.lastDate;
            this.dateStr = option.selectDate;
            this.selectDate = option.selectDate;

            this.showDatePick = option.showDatePick;
            this.showPlay = option.showPlay;

            this.nums = option.nums;
            this.interVal = option.interVal;

            this.intData();

            //第一次请求数据
            this.$emit('dateChange', this.dateStr, false);
        },

        intData() {
            this.arrDate = [];

            let dd = Math.abs(this.nums);

            for (let i = dd; i >= 0; i--) {
                let sj = parseInt(this.selectDate) - i;
                this.arrDate.push({
                    sj: sj,
                    sfyj: '',
                    level: '',
                    tipStr: sj
                });

                if (this.selectDate == sj) {
                    this.selectIndex = dd - i;
                }
            }

            this.maxIndex = dd;
        },

        itemClick(item, index) {
            this.selectDate = item.sj;
            this.selectIndex = index;

            this.clearTime();

            this.$emit('dateChange', item.sj, false);
        },

        //暂停
        pauseClick() {
            this.clearTime();
        },

        //播放按钮点击
        playClick() {
            //如果正在播放，则停止
            if (this.timeId) {
                this.clearTime();
                return;
            }

            if (this.selectIndex == this.maxIndex) {
                this.selectIndex = this.minIndex;
                this.selectDate = this.arrDate[this.selectIndex].sj;
                this.$emit('dateChange', this.selectDate, true);
            }

            this.timeId = setInterval(() => {
                if (this.selectIndex >= this.maxIndex) {
                    this.clearTime();
                    return;
                }

                this.selectIndex++;
                this.selectDate = this.arrDate[this.selectIndex].sj;
                this.$emit('dateChange', this.selectDate, true);
            }, this.interVal * 1000);
        },

        //清除定时器
        clearTime() {
            if (this.timeId) {
                clearInterval(this.timeId);
                this.timeId = null;
            }
        }
    }
};
</script>

<style scoped>
.timeline.rightCls {
    right: 330px;
}

.pd-timeaxis {
    /* border: 1px solid #ccc;
    background: #fff; */
    height: 55px;
    display: flex;
    position: absolute;
    left: 10px;
    right: 10px;
    bottom: 10px;
}

.pd-play {
    margin: 11px 10px 0 9px;
    width: 16px;
    height: 16px;
    cursor: pointer;
    margin-top: 28px;
}

.pd-inpdate1a {
    height: 22px;
    line-height: 22px;
    border: 1px solid #40d9ec;
    font-size: 14px;
    color: #fff;
    text-indent: 5px;
    width: 113px;
    margin-top: 7px;
}

.pd-ulnumlst {
    flex: 1;
    display: flex;
    padding: 7px 10px 0;
}
.pd-ulnumlst li {
    flex: 1;
    height: 24px;
    background: rgba(0, 0, 0, 0.5);
    font-size: 14px;
    color: #fff;
    text-align: center;
    line-height: 24px;
    margin: 0 5px;
    position: relative;
    list-style: none;
}
.pd-ulnumlst li.grn {
    color: #2e2e2e;
    background: #24bd5d;
}
.pd-ulnumlst li.ylw {
    color: #2e2e2e;
    background: #D9BD23#d8bc37;
}
.pd-ulnumlst li sup {
    display: none;
    position: absolute;
    left: 50%;
    bottom: 100%;
    height: 26px;
    line-height: 26px;
    font-size: 14px;
    color: #fff;
    background: #3686e7;
    border-radius: 4px;
    padding: 0 10px;
    white-space: nowrap;
    margin-bottom: 8px;
    transform: translateX(-50%);
}

.pd-ulnumlst li sup:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 100%;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 5px 0 5px;
    border-color: #3686e7 transparent transparent transparent;
    transform: translateX(-50%);
}
.pd-ulnumlst li.on sup {
    display: block;
}
.pd-ulnumlst li.cur:after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    margin-top: 2px;
    height: 2px;
    background: #ff3838;
}

.type1 {
    background: #12bf59 !important;
}

.type2 {
    background: #d9bd23 !important;
}

.type3 {
    background: #fa7c00 !important;
}

.type4 {
    background: #f90000 !important;
}

.type5 {
    background: #96004b !important;
}
.type6 {
    background: #70001d !important;
}
</style>
