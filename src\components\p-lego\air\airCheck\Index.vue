<!-- @format -->

<template>
    <div style="width: 530px; height: 250px">
        <p-chart style="width: 530px; height: 250px" :option="option"></p-chart>
    </div>
</template>
<script>
export default {
    name: 'airCheck',
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        }
    },
    data() {
        let fontColor =
            window.localStorage.themeType === 'dark' ? '#fff' : '#333';
        return {
            option: {
                color: ['#3cefff'],
                tooltip: {},
                toolbox: { show: false },
                grid: {
                    top: 60,
                    containLabel: true,
                    bottom: 10
                },
                xAxis: [
                    {
                        type: 'category',
                        data: ['累计值', '目标值', '去年同期', '控制值'],
                        axisTick: {
                            alignWithLabel: true
                        },
                        nameTextStyle: {
                            color: 'white'
                        },
                        offset: 10,
                        axisLine: {
                            show: false,
                            lineStyle: {
                                fontSize: 16
                            }
                        },
                        axisLabel: {
                            textStyle: {
                                color: fontColor,
                                fontSize: 16
                            }
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            show: false,
                            textStyle: {
                                color: '#82b0ec'
                            },
                            formatter: '{value}'
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: false,
                            lineStyle: {
                                color: '#0c2c5a'
                            }
                        },
                        axisLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        name: '',
                        type: 'pictorialBar',
                        symbolSize: [30, 10],
                        symbolOffset: [0, -5],
                        symbolPosition: 'end',
                        z: 12,
                        label: {
                            normal: {
                                fontSize: 20,
                                color: fontColor,
                                show: true,
                                position: 'top',
                                formatter: '{c}'
                            }
                        },
                        data: [
                            {
                                value: 33, //累计值,
                                label: {
                                    formatter: this.chartpm(),
                                    rich: {
                                        pass: {
                                            backgroundColor: '#25bc5d',
                                            color: '#000',
                                            borderRadius: 15,
                                            padding: 5,
                                            width: 40,
                                            align: 'center'
                                        },
                                        unpass: {
                                            backgroundColor: '#ff3952',
                                            color: fontColor,
                                            borderRadius: 15,
                                            padding: 5,
                                            width: 40,
                                            align: 'center'
                                        },
                                        center: {
                                            align: 'center',
                                            fontSize: 20,
                                            color: fontColor
                                        }
                                    }
                                },
                                itemStyle: {
                                    normal: {
                                        color: new this.$echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: 'rgba(0,151,237,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: 'rgba(74,196,254,1)'
                                                }
                                            ],
                                            false
                                        )
                                    }
                                }
                            },
                            {
                                value: 35, //目标值
                                itemStyle: {
                                    color: new this.$echarts.graphic.LinearGradient(
                                        0,
                                        0,
                                        0,
                                        1,
                                        [
                                            {
                                                offset: 0,
                                                color: 'rgba(0,159,101,1)'
                                            },
                                            {
                                                offset: 1,
                                                color: 'rgba(83,255,193,1)'
                                            }
                                        ],
                                        false
                                    )
                                }
                            },
                            {
                                value: 29, //去年同期,
                                itemStyle: {
                                    normal: {
                                        color: new this.$echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: 'rgba(19,84,222,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: 'rgba(91,165,244,1)'
                                                }
                                            ],
                                            false
                                        )
                                    }
                                }
                            },
                            {
                                value: 31, //控制值,
                                itemStyle: {
                                    normal: {
                                        color: new this.$echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: 'rgba(191,162,0,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: 'rgba(252,234,86,1)'
                                                }
                                            ],
                                            false
                                        )
                                    }
                                }
                            }
                        ] //seriesDataTop,
                    },
                    {
                        name: '',
                        type: 'pictorialBar',
                        symbolSize: [30, 10],
                        symbolOffset: [0, 5],
                        z: 12,
                        data: [
                            {
                                value: 33, //累计值,
                                itemStyle: {
                                    normal: {
                                        color: new this.$echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: 'rgba(98,199,255,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: 'rgba(20,159,236,1)'
                                                }
                                            ]
                                        )
                                    }
                                }
                            },
                            {
                                value: 35, //目标值
                                itemStyle: {
                                    normal: {
                                        color: new this.$echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: 'rgba(0,180,115,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: 'rgba(82,224,185,1)'
                                                }
                                            ]
                                        )
                                    }
                                }
                            },
                            {
                                value: 29, //去年同期,
                                itemStyle: {
                                    normal: {
                                        color: new this.$echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: 'rgba(15,67,186,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: 'rgba(43,130,243,1)'
                                                }
                                            ]
                                        )
                                    }
                                }
                            },
                            {
                                value: 31, //控制值,
                                itemStyle: {
                                    normal: {
                                        color: new this.$echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: 'rgba(167,142,0,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: 'rgba(252,234,86,1)'
                                                }
                                            ]
                                        )
                                    }
                                }
                            }
                        ] //seriesDataBottom,
                    },
                    {
                        type: 'bar',
                        itemStyle: {},
                        barWidth: '30',
                        data: [
                            {
                                value: 33, //累计值,
                                itemStyle: {
                                    normal: {
                                        color: {
                                            x: 0,
                                            y: 0,
                                            x2: 0,
                                            y2: 1,
                                            type: 'linear',
                                            global: false,
                                            colorStops: [
                                                {
                                                    //第一节下面
                                                    offset: 0,
                                                    color: 'rgba(113,204,255,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#09a4f9'
                                                }
                                            ]
                                        }
                                    }
                                }
                            },
                            {
                                value: 35, //目标值
                                itemStyle: {
                                    normal: {
                                        color: {
                                            x: 0,
                                            y: 0,
                                            x2: 0,
                                            y2: 1,
                                            type: 'linear',
                                            global: false,
                                            colorStops: [
                                                {
                                                    //第一节下面
                                                    offset: 0,
                                                    color: 'rgba(17,228,151,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#00af85'
                                                }
                                            ]
                                        }
                                    }
                                }
                            },
                            {
                                value: 29, //去年同期,
                                itemStyle: {
                                    normal: {
                                        color: {
                                            x: 0,
                                            y: 0,
                                            x2: 0,
                                            y2: 1,
                                            type: 'linear',
                                            global: false,
                                            colorStops: [
                                                {
                                                    //第一节下面
                                                    offset: 0,
                                                    color: 'rgba(56,155,334,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#0636a3'
                                                }
                                            ]
                                        }
                                    }
                                }
                            },
                            {
                                value: 31, //控制值,
                                itemStyle: {
                                    normal: {
                                        color: {
                                            x: 0,
                                            y: 0,
                                            x2: 0,
                                            y2: 1,
                                            type: 'linear',
                                            global: false,
                                            colorStops: [
                                                {
                                                    //第一节下面
                                                    offset: 0,
                                                    color: 'rgba(244,211,32,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#a58d00'
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        ]
                    }
                ]
            }
        };
    },
    watch: {
        data: function (v) {
            console.log(v);
            this.getData();
        }
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            this.option.series.forEach((item) => {
                item.data.forEach((t, i) => {
                    t.value = this.data[i];
                });
            });
        },
        chartpm() {
            // 目标值<达标值
            let formatterRich = '{center|' + this.data[0] + '}\n';
            formatterRich +=
                parseInt(this.data[0]) < parseInt(this.data[1])
                    ? '{pass|达标}'
                    : '{unpass|未达标}';
            return formatterRich;
        }
    }
};
</script>
<style scoped></style>
