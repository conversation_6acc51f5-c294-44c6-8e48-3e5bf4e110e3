<!-- @format -->

<template>
    <div style="width: 100%; height: 100%">
        <!-- 操作模块 -->
        <div class="pad10" style="padding-top: 10px">
            <el-form
                :inline="true"
                :model="formInline"
                class="demo-form-inline"
                style="text-align: left; margin-left: 10px"
            >
                <el-form-item label="事件名称">
                    <el-input
                        v-model="formInline.user"
                        placeholder="事件名称"
                    ></el-input>
                </el-form-item>
                <el-form-item label="发生时间">
                    <el-date-picker
                        v-model="formInline.date"
                        type="date"
                        class="full-width-input"
                        clearable
                    ></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit">查询</el-button>
                    <el-button type="primary" @click="onAdd">新增</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 操作模块结束 -->
        <!-- 列表开始 -->
        <List :height="height" ref="listRegion" />
        <!-- 列表结束 -->

        <transition name="el-zoom-in-top">
            <!-- 新增模块 -->
            <Add ref="addRegion" />
            <!-- 新增模块结束 -->
        </transition>

        <transition name="el-zoom-in-top">
            <!--  编辑模块 -->
            <Edit ref="editRegion" />
            <!-- 编辑模块结束 -->
        </transition>
    </div>
</template>

<script>
import Add from './Add.vue';
import Edit from './Edit.vue';
import List from './List.vue';

import { getUsers } from '_a/user';
export default {
    components: {
        Add,
        List,
        Edit
    },
    data() {
        return {
            // 搜索的对象
            searchObj: {
                pageNum: 1,
                pageSize: 10,
                user: '',
                region: ''
            },
            formInline: {
                user: '',
                date: ''
            },
            height: 0,
            name: 'user'
        };
    },
    created() {
        let height = document.documentElement.clientHeight - 150;
        this.height = height;
    },
    mounted() {
        // 搜索列表
        this.fetchList();
    },
    methods: {
        // 改变搜索
        setSearch(payload) {
            this.searchObj = { ...this.searchObj, ...payload };
        },
        // 搜索列表
        fetchList() {
            getUsers(this.searchObj).then((res) => {
                // 设置列表数据
                this.$refs.listRegion.tableObj = res.data;
            });
        },
        onSubmit() {
            // 改变搜索
            this.setSearch(this.formInline);
            // 搜索列表
            this.fetchList();
            this.$message({
                showClose: true,
                message: '查询',
                type: 'success'
            });
        },
        onAdd() {
            // 显示新增
            this.$refs.addRegion.dialogFormVisible = true;
        }
    }
};
</script>

<style scoped></style>
