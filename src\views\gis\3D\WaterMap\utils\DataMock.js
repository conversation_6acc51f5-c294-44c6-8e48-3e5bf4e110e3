/** @format */

import dayjs from 'dayjs';
import { result } from 'lodash';
import Mock from 'mockjs';

// debugger;
Mock.setup({ timeout: 2000 });

//自动站数量统计
Mock.mock(RegExp('/water/gis/getZDZPointCount' + '.*'), 'get', (option) => {
    let result = {
        status: '000',
        msg: null,
        data: {
            JGJB: [
                {
                    MC: '总数',
                    NUM: 47,
                    DM: 'ZS',
                    TYPE: 'JGJB'
                },
                {
                    MC: '国考',
                    NUM: 10,
                    DM: 'GK',
                    TYPE: 'JGJB'
                },
                {
                    MC: '省考',
                    NUM: 13,
                    DM: 'SK',
                    TYPE: 'JGJB'
                },
                {
                    MC: '市考',
                    NUM: 8,
                    DM: 'SSK',
                    TYPE: 'JGJB'
                }
            ],
            STLX: [
                {
                    MC: '总数',
                    NUM: 47,
                    DM: 'ZS',
                    TYPE: 'STLX'
                },
                {
                    MC: '河流',
                    NUM: 35,
                    DM: 'HL',
                    TYPE: 'STLX'
                },
                {
                    MC: '湖库',
                    NUM: 12,
                    DM: 'HK',
                    TYPE: 'STLX'
                }
            ],
            DMSX: [
                {
                    MC: '总数',
                    NUM: 47,
                    DM: 'ZS',
                    TYPE: 'DMSX'
                },
                {
                    MC: '常规自动站',
                    NUM: 35,
                    DM: '常规自动站',
                    TYPE: 'DMSX'
                },
                {
                    MC: '重金属',
                    NUM: 10,
                    DM: '重金属',
                    TYPE: 'DMSX'
                }
            ]
        }
    };

    console.log('水环境站点数量接口');
    console.log(result);

    return result;
});

//手工站数量统计
Mock.mock(RegExp('/water/gis/getSGZPointCount' + '.*'), 'get', (option) => {
    let result = {
        status: '000',
        msg: null,
        data: {
            JGJB: [
                {
                    MC: '总数',
                    NUM: 280,
                    DM: 'ZS',
                    TYPE: 'JGJB'
                },
                {
                    MC: '国考',
                    NUM: 14,
                    DM: 'GK',
                    TYPE: 'JGJB'
                },
                {
                    MC: '省考',
                    NUM: 20,
                    DM: 'SK',
                    TYPE: 'JGJB'
                },
                {
                    MC: '市控',
                    NUM: 10,
                    DM: 'SSK',
                    TYPE: 'JGJB'
                }
            ],
            STLX: [
                {
                    MC: '总数',
                    NUM: 200,
                    DM: 'ZS',
                    TYPE: 'STLX'
                },
                {
                    MC: '河流',
                    NUM: 118,
                    DM: 'HL',
                    TYPE: 'STLX'
                },
                {
                    MC: '湖库',
                    NUM: 83,
                    DM: 'HK',
                    TYPE: 'STLX'
                }
            ]
        }
    };

    console.log('手工站站点数量接口');
    console.log(result);

    return result;
});

//污染源数量统计
Mock.mock(RegExp('/water/gis/getWryPointCount' + '.*'), 'get', (option) => {
    let result = {
        status: '000',
        msg: null,
        data: {
            WRY: [
                {
                    MC: '涉水企业',
                    NUM: 255,
                    DM: 'SSQY',
                    TYPE: 'WRY'
                },
                {
                    MC: '废水在线监控',
                    NUM: 22,
                    DM: 'FSZXJK',
                    TYPE: 'WRY'
                }
            ]
        }
    };

    console.log('污染源数量接口');
    console.log(result);

    return result;
});

//自动站点位
Mock.mock(RegExp('/water/gis/getZdjcStatiion' + '.*'), 'get', (option) => {
    let result = {
        status: '000',
        msg: null,
        data: [
            {
                JCFLMC: '河流',
                ZXBZMC: 'Ⅱ类',
                CDMC: '界标',
                DXMC: '太浦河',
                CBXM: null,
                WD: '31.017422',
                DMSX: '重金属',
                XH: '1',
                JCSJ: '2023-11-15 08:00:00',
                CWQI: null,
                CDDM: '1',
                SSXZQ: '汾湖高新区（黎里镇）',
                JGJB: 'GK',
                SFZX: '1',
                DXBH: '20230830224',
                XZQDM: '320509107',
                JD: '120.87426',
                JGJBMC: '国控',
                SZLB: 'Ⅰ类',
                SZLBBS: 1,
                SSJD: null
            },
            {
                JCFLMC: '河流',
                ZXBZMC: '',
                CDMC: '瓜泾口北',
                DXMC: '京杭运河',
                CBXM: null,
                WD: '31.201562',
                DMSX: '常规自动站',
                XH: '12',
                JCSJ: '2023-11-15 08:00:00',
                CWQI: '0.61',
                CDDM: '12',
                SSXZQ: '江陵街道',
                JGJB: 'SK',
                SFZX: '1',
                DXBH: '20230830225',
                XZQDM: '320509004',
                JD: '120.659507',
                JGJBMC: '省控',
                SZLB: 'Ⅲ类',
                SZLBBS: 3,
                SSJD: '320509004'
            },
            {
                JCFLMC: '河流',
                ZXBZMC: '',
                CDMC: '亭子港（二水厂）',
                DXMC: '吴江河桥',
                CBXM: null,
                WD: '31.0287',
                DMSX: '常规自动站',
                XH: '13',
                JCSJ: '2023-11-15 02:00:00',
                CWQI: null,
                CDDM: '13',
                SSXZQ: '横扇街道',
                JGJB: 'SSK',
                SFZX: '1',
                DXBH: '20230830223',
                XZQDM: '320509002',
                JD: '120.4989',
                JGJBMC: '市控',
                SZLB: 'Ⅰ类',
                SZLBBS: 1,
                SSJD: '320509002'
            },
            {
                JCFLMC: '河流',
                ZXBZMC: 'Ⅲ类',
                CDMC: '备用水源地',
                DXMC: '吴江河桥',
                CBXM: null,
                WD: '31.0363',
                DMSX: '常规自动站',
                XH: '19',
                JCSJ: '2023-11-15 08:30:00',
                CWQI: null,
                CDDM: '19',
                SSXZQ: '横扇街道',
                JGJB: 'SSK',
                SFZX: '1',
                DXBH: '20230830223',
                XZQDM: '320509002',
                JD: '120.4966',
                JGJBMC: '市控',
                SZLB: 'Ⅰ类',
                SZLBBS: 1,
                SSJD: '320509002'
            },
            {
                JCFLMC: '河流',
                ZXBZMC: 'Ⅲ类',
                CDMC: '太平桥',
                DXMC: '后市河',
                CBXM: null,
                WD: '30.895328',
                DMSX: '常规自动站',
                XH: '2',
                JCSJ: '2023-11-15 08:00:00',
                CWQI: '0.51',
                CDDM: '2',
                SSXZQ: '吴江高新区（盛泽镇）',
                JGJB: 'SK',
                SFZX: '1',
                DXBH: '20230830226',
                XZQDM: '320509103',
                JD: '120.707167',
                JGJBMC: '省控',
                SZLB: 'Ⅲ类',
                SZLBBS: 3,
                SSJD: null
            },
            {
                JCFLMC: '河流',
                ZXBZMC: 'Ⅲ类',
                CDMC: '庙港水源地',
                DXMC: '吴江河桥',
                CBXM: null,
                WD: '31.0091',
                DMSX: '常规自动站',
                XH: '20',
                JCSJ: '2023-11-15 08:30:00',
                CWQI: null,
                CDDM: '20',
                SSXZQ: '七都镇',
                JGJB: 'SSK',
                SFZX: '1',
                DXBH: '20230830223',
                XZQDM: '320509107',
                JD: '120.4628',
                JGJBMC: '市控',
                SZLB: 'Ⅰ类',
                SZLBBS: 1,
                SSJD: '320509107'
            },
            {
                JCFLMC: '河流',
                ZXBZMC: 'Ⅲ类',
                CDMC: '亭子港水源地',
                DXMC: '吴江河桥',
                CBXM: null,
                WD: '31.0421',
                DMSX: '常规自动站',
                XH: '21',
                JCSJ: '2023-11-15 08:30:00',
                CWQI: null,
                CDDM: '21',
                SSXZQ: '横扇街道',
                JGJB: 'SSK',
                SFZX: '1',
                DXBH: '20230830223',
                XZQDM: '320509002',
                JD: '120.4838',
                JGJBMC: '市控',
                SZLB: 'Ⅰ类',
                SZLBBS: 1,
                SSJD: '320509002'
            },
            {
                JCFLMC: '河流',
                ZXBZMC: 'Ⅲ类',
                CDMC: '金泽水库',
                DXMC: '吴江河桥',
                CBXM: null,
                WD: '31.02093067',
                DMSX: '常规自动站',
                XH: '22',
                JCSJ: '2023-11-15 06:00:00',
                CWQI: null,
                CDDM: '22',
                SSXZQ: '平望镇',
                JGJB: 'SYD',
                SFZX: '1',
                DXBH: '20230830223',
                XZQDM: '320509102',
                JD: '120.9421333',
                JGJBMC: '水源地',
                SZLB: 'Ⅰ类',
                SZLBBS: 1,
                SSJD: null
            },
            {
                JCFLMC: '河流',
                ZXBZMC: 'Ⅲ类',
                CDMC: '太浦闸（水位）',
                DXMC: '太浦河',
                CBXM: null,
                WD: '',
                DMSX: '3',
                XH: '23',
                JCSJ: null,
                CWQI: null,
                CDDM: '23',
                SSXZQ: '横扇街道',
                JGJB: 'SK',
                SFZX: '0',
                DXBH: '20230830224',
                XZQDM: '320509002',
                JD: '',
                JGJBMC: '省控',
                SZLB: null,
                SZLBBS: '',
                SSJD: '320509002'
            },
            {
                JCFLMC: '河流',
                ZXBZMC: 'Ⅲ类',
                CDMC: '平望新运河大桥',
                DXMC: '頔塘河',
                CBXM: null,
                WD: '30.977479',
                DMSX: '重金属',
                XH: '25',
                JCSJ: '2023-11-15 06:00:00',
                CWQI: null,
                CDDM: '25',
                SSXZQ: '平望镇',
                JGJB: 'GK',
                SZLB: null,
                SZLBBS: '',
                SSJD: '320509002'
            }
        ]
    };

    console.log('自动站点位接口');
    console.log(result);

    return result;
});

//手工站点位
Mock.mock(RegExp('/water/gis/getSgjcStatiion' + '.*'), 'get', (option) => {
    let result = {
        status: '000',
        msg: null,
        data: [
            {
                JCFLMC: '湖库',
                ZYWRW: '',
                ZXBZMC: 'Ⅲ类',
                CDMC: '白蚬湖',
                DXMC: '白蚬湖',
                CBXM: '',
                WD: '30.910147',
                DMSX: null,
                XH: '20230810001_2023',
                JCSJ: '2023-09-30 00:00:00',
                SSXZQ: '吴江区',
                JGJB: 'GK',
                DXBH: '20230830229',
                XZQDM: '320509',
                JD: '120.541349',
                JGJBMC: null,
                SZLB: 'Ⅲ类',
                SZLBBS: 3,
                DWDM: '20230830001',
                SSJD: null
            },

            {
                JCFLMC: '湖库',
                ZYWRW: '',
                ZXBZMC: 'Ⅲ类',
                CDMC: '汪鸭潭中心',
                DXMC: '南参漾中心',
                CBXM: '',
                WD: '31.078296',
                DMSX: null,
                XH: '20230810010_2023',
                JCSJ: '2023-09-30 00:00:00',
                SSXZQ: '吴江区',
                JGJB: 'GK',
                DXBH: '20231011009',
                XZQDM: '320509',
                JD: '120.737398',
                JGJBMC: '区控',
                SZLB: 'Ⅱ类',
                SZLBBS: 2,
                DWDM: '20230830010',
                SSJD: null
            },

            {
                JCFLMC: '河流',
                ZYWRW: '',
                ZXBZMC: 'Ⅲ类',
                CDMC: '牛头湖东南道路桥',
                DXMC: '西大港',
                CBXM: '',
                WD: '30.985002',
                DMSX: null,
                XH: '20230830001_2023',
                JCSJ: '2023-09-30 00:00:00',
                SSXZQ: '吴江区',
                JGJB: 'SSK',
                DXBH: '20230830637',
                XZQDM: '320509',
                JD: '120.70202',
                JGJBMC: '区控',
                SZLB: 'Ⅲ类',
                SZLBBS: 3,
                DWDM: '20230830001',
                SSJD: null
            },
            {
                JCFLMC: '河流',
                ZYWRW: '',
                ZXBZMC: 'Ⅲ类',
                CDMC: '江浙交界（水泥厂）',
                DXMC: '紫荇塘',
                CBXM: '',
                WD: '30.761211',
                DMSX: null,
                XH: '20230830002_2023',
                JCSJ: '2023-09-30 00:00:00',
                SSXZQ: '吴江区',
                JGJB: 'SSK',
                DXBH: '20230830638',
                XZQDM: '320509',
                JD: '120.503773',
                JGJBMC: '区控',
                SZLB: 'Ⅲ类',
                SZLBBS: 3,
                DWDM: '20230830002',
                SSJD: null
            },
            {
                JCFLMC: '河流',
                ZYWRW: '',
                ZXBZMC: 'Ⅲ类',
                CDMC: '紫荇塘北（科欧污水厂取水口）',
                DXMC: '紫荇塘',
                CBXM: '',
                WD: '30.839623',
                DMSX: null,
                XH: '20230830003_2023',
                JCSJ: '2023-09-30 00:00:00',
                SSXZQ: '吴江区',
                JGJB: 'SSK',
                DXBH: '20230830638',
                XZQDM: '320509',
                JD: '120.521976',
                JGJBMC: '区控',
                SZLB: 'Ⅲ类',
                SZLBBS: 3,
                DWDM: '20230830003',
                SSJD: null
            },
            {
                JCFLMC: '河流',
                ZYWRW: '',
                ZXBZMC: 'Ⅲ类',
                CDMC: '麻溪桥',
                DXMC: '清溪河（麻溪桥）',
                CBXM: '',
                WD: '30.869935',
                DMSX: null,
                XH: '20230830004_2023',
                JCSJ: '2023-09-30 00:00:00',
                SSXZQ: '吴江区',
                JGJB: 'SSK',
                DXBH: '20230830639',
                XZQDM: '320509',
                JD: '120.531038',
                JGJBMC: '区控',
                SZLB: 'Ⅲ类',
                SZLBBS: 3,
                DWDM: '20230830004',
                SSJD: null
            },
            {
                JCFLMC: '河流',
                ZYWRW: '',
                ZXBZMC: 'Ⅲ类',
                CDMC: '西二路桥',
                DXMC: '清溪河（麻溪桥）',
                CBXM: '',
                WD: '30.876291',
                DMSX: null,
                XH: '20230830005_2023',
                JCSJ: '2023-09-30 00:00:00',
                SSXZQ: '吴江区',
                JGJB: 'SSK',
                DXBH: '20230830639',
                XZQDM: '320509',
                JD: '120.625852',
                JGJBMC: '区控',
                SZLB: 'Ⅲ类',
                SZLBBS: 3,
                DWDM: '20230830005',
                SSJD: null
            }
        ]
    };

    console.log('手工站点位接口');
    console.log(result);

    return result;
});

//污染源点位
Mock.mock(RegExp('/water/gis/getWryjbxx' + '.*'), 'get', (option) => {
    let result = {
        status: '000',
        msg: null,
        data: [
            {
                CORPNAME: '梅伟',
                TELEPHONE: '15396854321',
                WRYMC: '苏州衣涵羊毛衫整烫厂',
                WRYDZ: '吴江区松陵镇横扇旗北民营区（北横村八组）',
                CODE_REGION: '320509002',
                TYSHXYDM: '9132050932391187X9',
                WD: '31.027961',
                SFZDY: '0',
                REGIONNAME: '横扇街道',
                QYLX: 'PWLX_SSSQ_SSQY',
                WRYBH: '9132050932391187X901LS',
                QYMC: '苏州衣涵羊毛衫整烫厂',
                JD: '120.555977'
            },
            {
                CORPNAME: ' 钱卫荣 ',
                TELEPHONE: ' 0512-63833322',
                WRYMC: '吴江市荣凤喷织厂',
                WRYDZ: '南麻镇永平村',
                CODE_REGION: '320509105',
                TYSHXYDM: '913205097406874777',
                WD: '30.887343',
                SFZDY: '0',
                REGIONNAME: '盛泽镇',
                QYLX: 'PWLX_SSSQ_SSQY',
                WRYBH: '91320509740687477701LS',
                QYMC: '吴江市荣凤喷织厂',
                JD: '120.575434'
            },
            {
                CORPNAME: '冯金荣',
                TELEPHONE: '13962500281',
                WRYMC: '吴江市浩锦纺织有限公司',
                WRYDZ: '吴江平望镇梅堰梅龙路',
                CODE_REGION: '320509104',
                TYSHXYDM: '91320509767386842U',
                WD: '30.982165',
                SFZDY: '0',
                REGIONNAME: '平望镇',
                QYLX: 'PWLX_SSSQ_SSQY',
                WRYBH: '91320509767386842U01LS',
                QYMC: '吴江市浩锦纺织有限公司',
                JD: '120.595207'
            },
            {
                CORPNAME: '沈建峰',
                TELEPHONE: '0512-63088196',
                WRYMC: '苏州盛泽镇坛丘喷织污水处理有限公司（自来水厂站）',
                WRYDZ: '苏州市吴江区盛泽镇亭心村',
                CODE_REGION: '320509105',
                TYSHXYDM: '91320509MA1MUQ5F8E',
                WD: '30.907230',
                SFZDY: '1',
                REGIONNAME: '盛泽镇',
                QYLX: 'PWLX_SSSQ_SSQY',
                WRYBH: '91320509MA1MUQ5F8E01LS',
                QYMC: '苏州盛泽镇坛丘喷织污水处理有限公司（自来水厂站）',
                JD: '120.601609'
            },
            {
                CORPNAME: '沈建峰',
                TELEPHONE: '0512-63088196',
                WRYMC: '苏州盛泽镇坛丘喷织污水处理有限公司（自来水厂站）',
                WRYDZ: '苏州市吴江区盛泽镇亭心村',
                CODE_REGION: '320509105',
                TYSHXYDM: '91320509MA1MUQ5F8E',
                WD: '30.907230',
                SFZDY: '1',
                REGIONNAME: '盛泽镇',
                QYLX: 'PWLX_SSSQ_SSQY',
                WRYBH: '91320509MA1MUQ5F8E01LS',
                QYMC: '苏州盛泽镇坛丘喷织污水处理有限公司（自来水厂站）',
                JD: '120.601609'
            },
            {
                CORPNAME: '徐海珍',
                TELEPHONE: '63776976',
                WRYMC: '吴江市友联纺织有限公司',
                WRYDZ: '震泽镇外商投资开发区',
                CODE_REGION: '320509108',
                TYSHXYDM: '91320509752031563C',
                WD: '30.918912',
                SFZDY: '0',
                REGIONNAME: '震泽镇',
                QYLX: 'PWLX_SSSQ_SSQY',
                WRYBH: '91320509752031563C01LS',
                QYMC: '吴江市友联纺织有限公司',
                JD: '120.515654'
            },
            {
                CORPNAME: '潘红怡',
                TELEPHONE: '0512-63788700',
                WRYMC: '苏州欣亚电器有限公司',
                WRYDZ: '苏州市吴江区震泽镇开发区',
                CODE_REGION: '320509108',
                TYSHXYDM: '9132050974480962X0',
                WD: '30.918912',
                SFZDY: '0',
                REGIONNAME: '震泽镇',
                QYLX: 'PWLX_SSSQ_SSQY',
                WRYBH: '9132050974480962X001LS',
                QYMC: '苏州欣亚电器有限公司',
                JD: '120.515654'
            },
            {
                CORPNAME: '潘红怡',
                TELEPHONE: '0512-63788700',
                WRYMC: '苏州欣亚电器有限公司',
                WRYDZ: '苏州市吴江区震泽镇开发区',
                CODE_REGION: '320509108',
                TYSHXYDM: '9132050974480962X0',
                WD: '30.918912',
                SFZDY: '0',
                REGIONNAME: '震泽镇',
                QYLX: 'PWLX_SSSQ_SSQY',
                WRYBH: '9132050974480962X001LS',
                QYMC: '苏州欣亚电器有限公司',
                JD: '120.515654'
            }
        ]
    };

    console.log('污染源点位接口');
    console.log(result);

    return result;
});
