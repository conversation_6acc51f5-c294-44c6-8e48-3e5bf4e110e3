<!-- @format -->
<!-- tab切换 -->

<template>
    <div class="p-lego-tab">
        <ul class="fl" :class="[`${type}`]">
            <li
                :class="{ on: item.value === tabVal }"
                v-for="(item, index) in tabList"
                :key="index"
                @click="tabChange(item)"
            >
                {{ item.label }}
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    name: '',
    props: {
        tabList: {
            type: Array,
            default: () => {
                return [];
            }
        },
        defaultVal: {
            type: String,
            default: () => {
                return '';
            }
        },
        type: {
            type: String,
            default: () => {
                return 'type1';
            }
        }
    },

    data() {
        return {
            tabVal: ''
        };
    },
    mounted() {
        this.tabVal = this.defaultVal;
    },
    methods: {
        tabChange(v) {
            if (this.tabVal !== v.value) {
                this.tabVal = v.value;
                this.$emit('tabChange', v);
            }
        }
    }
};
</script>

<style lang="scss">
// 色值根据项目而定，这里只做参考
.darkTheme {
    --tab-base-color: #0582d6;
    --tab-border-color: #0582d6;
    --tab-font-color: #fff;
    --tab-font-on-color: #fff;
}
.lightTheme {
    --tab-base-color: #008aff;
    --tab-border-color: #eee;
    --tab-font-color: #333;
    --tab-font-on-color: #fff;
}
</style>

<style lang="scss" scoped>
.p-lego-tab {
    .type1 {
        display: flex;
        border: 1px solid var(--tab-border-color);
        border-radius: 4px;
        overflow: hidden;
        li {
            padding: 0 20px;
            font-size: 16px;
            color: var(--tab-font-color);
            line-height: 34px;
            cursor: pointer;
            &.on {
                color: var(--tab-font-on-color);
                background: var(--tab-base-color);
            }
            & + li {
                border-left: 1px solid var(--tab-border-color);
            }
        }
    }

    .type2 {
        display: flex;
        align-items: center;
        li {
            font-size: 16px;
            color: var(--tab-font-color);
            position: relative;
            cursor: pointer;
            & + li {
                padding-left: 25px;
                &:before {
                    content: '';
                    position: absolute;
                    left: 12px;
                    top: 50%;
                    width: 2px;
                    height: 16px;
                    background: var(--tab-border-color);
                    margin-top: -8px;
                }
            }
            &.on {
                color: var(--tab-base-color);
            }
        }
    }

    .type3 {
        display: flex;
        margin: 0 -7px;
        li {
            border: 1px solid var(--tab-border-color);
            height: 35px;
            width: 80px;
            line-height: 35px;
            margin: 0 7px;
            font-size: 16px;
            color: var(--font-color);
            cursor: pointer;
            text-align: center;
            border-radius: 3px;
            &.on {
                color: var(--tab-font-on-color);
                background: var(--tab-base-color);
                border-color: var(--tab-border-color);
            }
        }
    }
}

.fl {
    float: left;
}
</style>
