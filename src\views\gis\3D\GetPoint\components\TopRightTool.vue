<!-- @format -->

<template>
    <div>
        <!-- 导航 -->
        <Navigation :map="map"></Navigation>

        <!-- 指北针 -->
        <CompassControl :map="map"></CompassControl>

        <!-- 地图全屏 -->
        <FullScreen :map="map" :mapCls="mapCls"></FullScreen>

        <!-- 地图模式切换 -->
        <MapMode :map="map"></MapMode>
    </div>
</template>

<script>
import Navigation from '@/components/gis/3D/Navigation';
import CompassControl from '@/components/gis/3D/CompassControl';
import FullScreen from '@/components/gis/3D/FullScreen';
import MapMode from '@/components/gis/3D/MapMode';

export default {
    name: 'TopRightTool',
    props: ['map', 'mapCls', 'dock'],
    data() {
        return {};
    },
    components: {
        Navigation,
        CompassControl,
        FullScreen,
        MapMode
    },
    computed: {},
    mounted() {},
    methods: {},
    watch: {}
};
</script>

<style scoped></style>
