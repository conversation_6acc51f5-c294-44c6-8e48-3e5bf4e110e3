<!-- @format -->

<template>
    <div>
        <table cellpadding="0" class="pd-tablelst1">
            <colgroup>
                <col width="30%" />
                <col width="40%" />
                <col width="30%" />
            </colgroup>
            <thead>
                <tr>
                    <td>日期</td>
                    <td>AQI</td>
                    <td>首要污染物</td>
                </tr>
            </thead>
        </table>
        <table cellpadding="0" class="pd-tablelst1">
            <colgroup>
                <col width="30%" />
                <col width="40%" />
                <col width="30%" />
            </colgroup>
            <tbody>
                <tr v-for="item in data" :key="item">
                    <td>{{ item.date }}</td>
                    <td>
                        <i
                            :style="{
                                background: getColor(item.aqiMax)
                            }"
                            >{{ item.aqiMax }}</i
                        >~<i
                            :style="{
                                background: getColor(item.aqimin)
                            }"
                            >{{ item.aqimin }}</i
                        >
                    </td>
                    <td>
                        {{
                            item.maxItem == 'O3_8H'
                                ? 'O₃'
                                : replacePltName(item.maxItem) || '-'
                        }}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
import util from './util';
export default {
    name: 'airNextDay',
    props: {
        data: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    methods: {
        getColor(value) {
            return util.getLevelPollution('AQI', value).color;
        },
        replacePltName(value) {
            value = value || '';
            let labelObj = {
                'PM2.5': 'PM₂.₅',
                PM25: 'PM₂.₅',
                PM10: 'PM₁₀',
                O3: 'O₃',
                NO2: 'NO₂',
                SO2: 'SO₂'
            };
            return value.replace(/[A-Z]+[0-9]+\.*[0-9]*/g, function () {
                return labelObj[arguments[0]] || arguments[0];
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.pd-tablelst1 {
    width: 100%;
}

.pd-tablelst1 tr td {
    font-size: 14px;
    // color: #333;
    text-align: center;
    height: 50px;
}
table {
    display: table;
    margin: 0;
    border: none;
}
th,
td {
    border: none;
}
.pd-tablelst1 thead tr td {
    // background: url(./thdbg.png) repeat-x;
    background: var(--el-border-color);
    height: 50px;
    // color: #666;
}

.pd-tablelst1 tbody tr td i {
    display: inline-block;
    font-size: 16px;
    color: white;
    width: 30px;
    height: 18px;
    line-height: 18px;
    margin: 0 5px;
}

.pd-tablelst1 tbody tr td sub {
    font-size: 14px;
    vertical-align: baseline;
}
</style>
