<!-- @format -->

<template>
    <div
        :style="{
            width: option.width,
            height: option.height,
            background: option.backgroundColor
        }"
    >
        <div class="pd-tit1" style="padding-bottom: 10px">
            <span>水环境质量排名</span>
            <ul class="pd-ultbs12 blue-theme" style="margin-left: auto">
                <li :class="1 == top ? 'on' : ''" @click="changeTop(1)">
                    设区市
                </li>
                <li :class="2 == top ? 'on' : ''" @click="changeTop(2)">
                    县级城市
                </li>
            </ul>
        </div>
        <div v-show="top == 1" class="table-main">
            <table class="zy-table4">
                <colgroup>
                    <col width="53" />
                    <col width="53" />
                    <col width="300" />
                    <col />
                </colgroup>
                <tr v-for="(item, index) in gkList" :key="index">
                    <td>
                        <p :class="getnoClass(index)">{{ index + 1 }}</p>
                    </td>
                    <td>
                        <p>{{ item.ssxzq }}</p>
                    </td>
                    <td>
                        <div class="bar">
                            <div
                                class="bili"
                                :style="
                                    'width: ' + (item.cwqi / gkzdz) * 100 + '%'
                                "
                            ></div>
                        </div>
                    </td>
                    <td>
                        <p class="p2">{{ item.cwqi }}</p>
                    </td>
                </tr>
            </table>
            <div class="nomsg" v-if="gkList.length == 0">暂无数据</div>
        </div>
        <div v-if="top == 2" class="table-main">
            <table class="zy-table4">
                <colgroup>
                    <col width="53" />
                    <col width="53" />
                    <col width="300" />
                    <col />
                </colgroup>
                <tr v-for="(item, index) in skList" :key="index">
                    <td>
                        <p :class="getnoClass(index)">{{ index + 1 }}</p>
                    </td>
                    <td>
                        <p class="name-txt" :title="item.ssxzq">
                            {{ item.ssxzq }}
                        </p>
                    </td>
                    <td>
                        <div class="bar">
                            <div
                                class="bili"
                                :style="
                                    'width: ' + (item.cwqi / gkzdz) * 100 + '%'
                                "
                            ></div>
                        </div>
                    </td>
                    <td>
                        <p class="p2">{{ item.cwqi }}</p>
                    </td>
                </tr>
            </table>
            <div class="nomsg" v-if="skList.length == 0">暂无数据</div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            gkList: [],
            skList: [],
            topList: [
                { name: '设区市', value: 1 },
                { name: '县级城市', value: 2 }
            ],
            top: 1,
            gkzdz: 0,
            skzdz: 0
        };
    },
    props: {
        data: {
            type: Object
        },
        option: {
            default: function () {
                return {
                    width: '780px',
                    height: '300px',
                    backgroundColor: '#024a83'
                };
            },
            type: Object
        }
    },
    mounted() {
        this.getData();
    },
    methods: {
        changeTop(e) {
            this.top = e;
        },

        getnoClass(e) {
            if (e <= 2) {
                let x = parseInt(e) + 1;
                return 'index no' + x;
            } else {
                return 'index';
            }
        },
        getData() {
            this.gkList = this.data.gkList;
            this.skList = this.data.skList;
            this.skzdz = this.data.skzdz;
            this.gkzdz = this.data.gkzdz;
        }
    }
};
</script>

<style scoped>
table {
    border: none;
}
table tr {
    border: none;
}
table tr td {
    border: none;
}
li {
    list-style: none;
}
tr:nth-child(2n) {
    background: none;
}
.table-main {
    overflow-y: scroll;
    height: 80%;
    width: 100%;
}
.zy-table4 {
    width: 100%;
}
.zy-table4 td {
    height: 20px;
    text-align: center;
    padding: 0 2px;
}
.zy-table4 p {
    font-size: 14px;
    color: #fff;
    text-align: center;
    line-height: 36px;
}
.zy-table4 .index {
    width: 53px;
    height: 26px;
    display: inline-block;
    line-height: 26px;
}
.zy-table4 .index.no1 {
    background: url(./images/no1.png);
    text-indent: -999em;
    overflow: hidden;
}
.zy-table4 .index.no2 {
    background: url(./images/no2.png);
    text-indent: -999em;
    overflow: hidden;
}
.zy-table4 .index.no3 {
    background: url(./images/no3.png);
    text-indent: -999em;
    overflow: hidden;
}
.zy-table4 .p2 {
    font-size: 16px;
    color: #fff;
    font-family: 'DIN-Medium', sans-serif;
    padding-left: 10px;
}
.zy-table4 .bar {
    width: 100%;
    height: 10px;
    background-color: rgba(0, 255, 17, 0.1);
    margin-top: 7px;
}
.zy-table4 .bar .bili {
    width: 80%;
    height: 3px;
    background: url(./images/bar-green.png) 0 0 no-repeat;
    background-size: 100% 100%;
    position: relative;
    max-width: 100%;
}

.tablelist::-webkit-scrollbar {
    display: none;
}
.name-txt {
    white-space: nowrap;
    display: inline-block;
    width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
}

.pd-tit1 {
    display: flex;
    align-items: center;
}
.pd-tit1 span {
    font-size: 16px;
    color: #0bb1ff;
    background: url(./images/titic1.png) no-repeat left center;
    padding-left: 26px;
}
.pd-ultbs12 {
    display: flex;
}
.pd-ultbs12.blue-theme li {
    border: 1px solid #0bb2ff;
    line-height: 28px;
    background: none;
    font-size: 16px;
    color: #fff;
    padding: 0 12px;
}
.pd-ultbs12.blue-theme li.on {
    background: #0bb2ff;
    color: #031522;
    border-color: #0bb2ff;
}
.nomsg {
    font-size: 16px;
    color: #aaa;
    text-align: center;
    line-height: 40px;
}
</style>
