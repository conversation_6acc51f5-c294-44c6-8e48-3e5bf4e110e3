<!-- @format -->

<template>
    <dl class="sw0811-dlbx5">
        <dt class="flx1 ac" style="padding-left: 15px">
            <label class="sw0811-check1" @click="allLayerClick()"
                ><input
                    type="checkbox"
                    :checked="checkAll"
                    disabled
                    style="pointer-events: none"
                /><strong>污染源</strong></label
            >
        </dt>
        <dd class="sw0811-checkbox1 yy0812-checkbox1">
            <label
                class="sw0811-check1"
                v-for="(item, index) of zts"
                :key="index"
                @click="ztLayerClick(item)"
                ><input
                    type="checkbox"
                    :checked="item.selected"
                    disabled
                    style="pointer-events: none"
                /><span>{{ item.name }}（{{ item.total }}）</span></label
            >
        </dd>
    </dl>
</template>

<script>
import { getWryjbxx, getWryPointCount } from '@/api/gis/3D/WaterMap';
import PointUtil from '../utils/PointUtil';
export default {
    data() {
        return {
            map: null,
            result: {}, //专题数据集合
            checkAll: false,
            pointUtil: null,
            tjData: null,
            zts: [
                // {
                //     name: '涉水企业',
                //     selected: false,
                //     DM: 'SSQY',
                //     total: 0
                // },
                // {
                //     name: '废水在线监控',
                //     selected: false,
                //     DM: 'FSZXJK',
                //     total: 0
                // },
                // {
                //     name: '排口',
                //     selected: false,
                //     DM: 'PKXX',
                //     total: 0
                // }
            ]
        };
    },
    props: [],
    inject: ['pointClickHandle', 'refreshSearchData'],
    unmounted() {
        this.clearAll();
    },
    mounted() {
        this.getPointCount();
        this.initPage();
    },
    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(
                this.map,
                this.pointClickHandle,
                this.mouseOverHandle,
                this.mouseOutHandle
            );
        },

        getPointCount() {
            let param = {};

            getWryPointCount(param).then((res) => {
                this.tjData = res.data;
            });
        },

        //专题点击
        allLayerClick() {
            this.checkAll = !this.checkAll;

            for (let o of this.zts) {
                o.selected = this.checkAll;
            }

            if (this.checkAll) {
                this.getAllData();
            } else {
                this.clearAll();
            }
        },

        //子专题点击
        ztLayerClick(obj) {
            obj.selected = !obj.selected;

            //根据子级的选中状态，设置父级的选中状态
            this.checkAll = this.zts.some((item) => {
                return item.selected;
            });

            if (obj.selected) {
                this.getData(obj);
            } else {
                this.clear(obj.name);
            }
        },

        //根据专题加载数据
        getData(obj) {
            // switch (obj.name) {
            //     case '工业源':
            //         this.getWryData(obj);
            //         break;
            //     case '扬尘源':
            //         this.getYCYData(obj);
            //         break;
            //     default:
            //         break;
            // }

            if (this.result[obj.name]) {
                this.addPointToMap(obj.name);
                return;
            }
            let param = {
                FL: obj.DM
            };

            getWryjbxx(param).then((res) => {
                this.result[obj.name] = res.data.map((item) => {
                    return item;
                });

                this.addPointToMap(obj.name);
            });
        },

        //地图上绘制点位
        addPointToMap(type) {
            this.pointUtil.addImgPoint(this.result[type], { id: type });

            this.setSearchData(type, this.result[type]);
        },

        //请求所有选中专题的数据
        getAllData() {
            for (let obj of this.zts) {
                if (obj.selected) {
                    this.getData(obj);
                }
            }
        },

        //清理图层
        clearAll() {
            for (let item of this.zts) {
                this.clear(item.name);
            }
        },

        clear(layerId) {
            this.pointUtil.removeLayerByName(layerId);
            this.setSearchData(layerId, []);
        },

        //设置搜索框数据
        setSearchData(layerId, arr) {
            let data = {};
            data[layerId] = arr;
            this.refreshSearchData(data);
        }
    },

    watch: {
        tjData: {
            deep: true,
            handler(val) {
                this.allTotal = 0;
                let tempArr = [];
                for (let item of val.WRY) {
                    if (item.DM == 'ZS') {
                        this.allTotal = item.NUM;
                    } else {
                        tempArr.push({
                            name: item.MC,
                            selected: false,
                            total: item.NUM,
                            DM: item.DM
                        });
                    }
                }

                this.zts = tempArr;
            }
        }
    }
};
</script>

<style></style>
