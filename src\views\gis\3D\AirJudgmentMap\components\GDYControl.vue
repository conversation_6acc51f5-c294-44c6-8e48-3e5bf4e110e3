<!-- @format -->

<!-- 固定源  -->
<!-- @format -->
<template>
    <dd>
        <label class="sw0721-check1" @click="allLayerClick()"
            ><input type="checkbox" :checked="checkAll" disabled /><span
                >固定源</span
            ></label
        >
        <div class="gap"></div>
        <div class="sw0726-pdl15">
            <label
                class="sw0721-check1"
                v-for="(item, index) of zts"
                :key="index"
                @click="ztLayerClick(item)"
                ><input
                    type="checkbox"
                    :checked="item.selected"
                    style="pointer-events: none"
                    disabled
                /><span>{{ item.name }}({{ item.total }})</span></label
            >
        </div>
    </dd>
</template>

<script>
export default {
    data() {
        return {
            checkAll: true,
            zts: [
                {
                    name: '在线监测企业',
                    selected: true,
                    dm: 'zxjc',
                    total: 0
                },
                {
                    name: '涉气重点源',
                    selected: true,
                    dm: 'sqzdy',
                    total: 0
                }
            ]
        };
    },
    props: ['zbwrytj', 'paramsData'],
    unmounted() {},
    mounted() {},
    methods: {
        //专题点击
        allLayerClick() {
            this.checkAll = !this.checkAll;

            for (let o of this.zts) {
                o.selected = this.checkAll;

                PowerGL.setLayerVisible(this.$parent.map, o.name, o.selected);
            }
        },

        //子专题点击
        ztLayerClick(obj) {
            obj.selected = !obj.selected;

            //根据子级的选中状态，设置父级的选中状态
            this.checkAll = this.zts.some((item) => {
                return item.selected;
            });

            PowerGL.setLayerVisible(this.$parent.map, obj.name, obj.selected);
        }
    },

    watch: {
        zbwrytj: {
            handler(newVal) {
                if (newVal) {
                    for (let item of this.zts) {
                        item.total = newVal[item.dm] || 0;
                    }
                }
            },
            deep: true
        }
    }
};
</script>

<style scoped>
.sw0726-pdl15 label + label {
    margin-top: 10px;
}
</style>
