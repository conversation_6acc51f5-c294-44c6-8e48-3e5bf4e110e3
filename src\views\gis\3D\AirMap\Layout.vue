<!-- @format -->
<!-- 业务主要入口 -->
<template>
    <div>
        <div class="gis-option-wrap">
            <!-- 大气点位 -->
            <AirControl
                :curentTime="curentTime"
                @yzChange="yzChangeHandle"
                :showAno="showAno"
                ref="childAir"
            ></AirControl>
            <div class="gap"></div>

            <WryPointControl></WryPointControl>

            <div class="gap"></div>
            <dl class="gis-dloption1">
                <dt>专题图层</dt>
                <dd>
                    <ul class="pd-ullst3">
                        <li @click="showAno = !showAno">
                            <i class="swfic" :class="{ on: showAno }"></i
                            ><span>站点名称</span>
                        </li>
                        <!-- 风场 -->
                        <WindControl
                            :curentTime="curentTime"
                            :map="map"
                            :isOpen="isOpen"
                        ></WindControl>

                        <!-- 插值 -->
                        <InterpolationControl
                            :curentTime="curentTime"
                            :map="map"
                            :selectYZ="selectYZ"
                        ></InterpolationControl>
                    </ul>
                </dd>
            </dl>
        </div>

        <!-- 测试用 -->
        <changeStyle></changeStyle>

        <!-- 搜索框 -->
        <SearchTool
            class="gis-search"
            :arrSearch="arrSearch"
            :map="map"
            dock="left"
        ></SearchTool>

        <!-- 图例 -->
        <LegendTool
            class="gis-legend"
            :arrLegend="arrLegend"
            :expand="true"
        ></LegendTool>

        <!-- 时间轴 -->
        <TimeLine
            class="footer"
            ref="childTimeLine"
            @dateChange="dateChangeHandle"
        ></TimeLine>
    </div>
</template>
<script>
import { getPointHourMaxDate } from '@/api/gis/3D/AirMap/index';
import AirControl from './components/KQZD/AirControl.vue';
import WryPointControl from './components/WryPointControl';
import InterpolationControl from './components/InterpolationControl.vue';
import WindControl from './components/WindControl.vue';

import SearchTool from '@/components/gis/3D/SearchTool';
import TimeLine from '@/components/gis/common/TimeLineHour.vue';
import LegendTool from '@/components/gis/3D/LegendTool';

import changeStyle from './components/changeStyle.vue';

import DataMock from './utils/DataMock'; //mock数据
export default {
    data() {
        return {
            showAno: true, //是否显示站点名称
            curentTime: '',
            selectYZ: 'AQI',
            showPanel: false,
            arrLegend: [], //图例
            arrSearch: [], //搜索框数据
            objSearch: {},
            isOpen: false //默认是否展示风场
        };
    },
    provide() {
        return {
            pointClickHandle: this.pointClickHandle,
            refreshSearchData: this.refreshSearchData
        };
    },
    props: [],
    unmounted() {},
    components: {
        AirControl,
        WryPointControl,
        InterpolationControl,
        SearchTool,
        TimeLine,
        LegendTool,
        WindControl,
        changeStyle
    },
    mounted() {
        this.arrLegend = [
            {
                title: '空气站',
                data: [
                    { name: '优', url: 'c-1.png' },
                    { name: '良', url: 'c-2.png' },
                    { name: '轻度污染', url: 'c-3.png' },
                    { name: '中度污染', url: 'c-4.png' },
                    { name: '重度污染', url: 'c-5.png' },
                    { name: '严重污染', url: 'c-6.png' }
                ]
            },
            {
                title: '工业源',
                data: [
                    { name: '正常', url: 'FQ-ZC.png' },
                    { name: '超标', url: 'FQ-CB.png' },
                    { name: '异常', url: 'FQ-WSJ.png' }
                ]
            },
            {
                title: '污染源',
                data: [{ name: '扬尘源', url: 'YCY-ZC.png' }]
            }
        ];
        this.initPage();
    },
    methods: {
        //页面初始化
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.map = window.glMap;

            this.initEvent();

            this.getPointHourMaxDate();
        },

        //请求最新时间
        getPointHourMaxDate() {
            getPointHourMaxDate({}).then((res) => {
                this.curentTime = res.data.JCSJ;
                this.initTimeLine();
            });
        },

        //定义地图事件
        initEvent() {
            let self = this;
            // this.map.on('zoomend', (evt) => {});
        },

        //空气因子选择改变
        yzChangeHandle(val) {
            this.selectYZ = val;
        },

        //地图点位点击事件
        pointClickHandle(type, item) {
            console.log('点击了地图：' + type);
            this.$emit('mapEvent', type, item);
        },

        //进度条初始化
        initTimeLine() {
            let option = {
                lastDate: this.curentTime,
                selectDate: this.curentTime,
                showDatePick: true,
                showPlay: true
            };
            this.$refs.childTimeLine.initTimeLine(option);
        },

        //时间轴时间改变事件
        dateChangeHandle(selectDate, isPlay) {
            // console.log(selectDate);
            this.curentTime = selectDate;
        },

        //前端右侧面板
        showPanleChangeHandle(showPanel) {
            this.showPanel = showPanel;
        },

        //刷新查询框的数据
        refreshSearchData(data) {
            Object.assign(this.objSearch, data);

            this.arrSearch = [];
            for (let key in this.objSearch) {
                for (let item of this.objSearch[key]) {
                    let obj = {
                        name: item.CDMC || item.QYMC || item.NAME,
                        type: key,
                        JD: item.JD,
                        WD: item.WD
                    };

                    this.arrSearch.push(obj);
                }
            }
        }
    }
};
</script>

<style>
.gis-tuli .item .rp p img {
    max-width: 20px;
}

@import './css/style.css';
</style>

<style scoped>
@import '~_as/gis/3D/BasicFramework/css/style.css';

.gis-search {
    position: absolute;
    top: 80px;
    left: 240px;
}

.gis-legend {
    position: absolute;
    left: 10px;
    bottom: 100px;
    height: 95px !important;
}

.footer {
    position: fixed;
    bottom: 10px;
    left: 15px;
    right: 40px;
}
</style>
