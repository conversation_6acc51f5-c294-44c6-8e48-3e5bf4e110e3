/** @format */

import { createStore } from 'vuex';
import { ElMessage } from 'element-plus';

export default createStore({
    modules: {},
    state: {
        isShowLoading: false
    },
    mutations: {
        showLoading(state) {
            // 显示loading
            state.isShowLoading = true;
        },
        hideLoading(state) {
            // 影藏loading
            state.isShowLoading = false;
        },
        errorMsg(msg, error) {
            ElMessage({
                showClose: true,
                message: error.msg,
                type: 'error'
            });
        }
    },
    // 存放着接口调用
    actions: {}
});
