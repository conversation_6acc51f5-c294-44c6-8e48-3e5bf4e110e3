#!usr/bin/env node

const path = require('path')
const fs = require('fs')

/**
 * 在指定目录下查找指定文件名所在目录
 */
const resolveFileDir = (dir, fileName) => {
	let children = fs.readdirSync(dir)
	let found = null
	for(let child of children) {
		let absolutePath = path.resolve(dir, child)
		let stats = fs.statSync(absolutePath)
		if(stats.isDirectory() && child === 'src') {
			found = resolveFileDir(absolutePath, fileName)
			if(found) {
				found = absolutePath
			}
		} else {
			if(fileName === child) {
				found = dir
			}
		}
		if(found) {
			break
		}
	}
	return found
}

/**
 * 拷贝文件夹到指定文件夹下
 */
const copyDir = (source, target) => {
	let sourceDirName = path.basename(source)
    let targetDirName = path.basename(target)
    let targetPath = targetDirName === sourceDirName ? target : path.resolve(target, sourceDirName)
	checkAndCreateDir(targetPath)
	let children = fs.readdirSync(source)
	children.forEach(file => {
		let sourceFilePath = path.resolve(source, file)
		let fileStats = fs.statSync(sourceFilePath)
		if(fileStats.isDirectory()) {
			copyDir(sourceFilePath, targetPath)
		} else {
			copyFile(sourceFilePath, targetPath)
		}
	})
}

/**
 * 拷贝文件到指定路径（路径或文件全路径）
 */
const copyFile = (source, target) => {
	let targetStats = fs.statSync(target)
	let targetPath = target
	if(targetStats.isDirectory()) {
		checkAndCreateDir(target)
		let sourceFileName = path.basename(source)
		targetPath = path.resolve(target, sourceFileName)
	}
	let readStream = fs.createReadStream(source)
	let writeStream = fs.createWriteStream(targetPath)
	readStream.pipe(writeStream)
}

/**
 * 检查文件夹是否存在，如果不存在，递归创建文件夹
 */
const checkAndCreateDir = (dir) => {
	if(fs.existsSync(dir)) {
		return
	}
	recursiveMkdir(dir)
}

/**
 * 递归创建文件夹
 */
const recursiveMkdir = (dir) => {
	let parentDir = path.dirname(dir)
	if(!fs.existsSync(parentDir)) {
		recursiveMkdir(parentDir)
	}
	fs.mkdirSync(dir)
}

/**
 * 递归删除文件夹
 */
const recursiveDeleteDir = (dir, deleteSelf = true) => {
	if(!fs.existsSync(dir)) {
		return
	}
	let children = fs.readdirSync(dir)
	children.forEach(child => {
		let fullPath = path.resolve(dir, child)
		let childStats = fs.statSync(fullPath)
		if(childStats.isDirectory()) {
			recursiveDeleteDir(fullPath)
		} else {
			fs.unlinkSync(fullPath)
		}
	})
	if(deleteSelf) {
		fs.rmdirSync(dir)
	}
}

module.exports = {
	resolveFileDir,
	copyDir,
	copyFile,
	checkAndCreateDir,
	recursiveMkdir,
    recursiveDeleteDir
}