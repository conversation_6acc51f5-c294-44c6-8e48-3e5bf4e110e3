<!-- @format -->

<!--  -->
<template>
    <div class="">
        <p-pie
            :data="pieData"
            :config="{
                type: 'ring',
                color: 'merge1n2WaterGradesColor',
                showLegend: true,
                showLabel: false,
                isShowInnerShadow: false,
                legendOrient: 'vertical',
                legendTextStyle: {
                    rich: {
                        name: { width: 60 },
                        value: { width: 50 }
                    }
                }
            }"
            :option="finalOption"
            style="width: 100%; height: 100%"
        ></p-pie>
    </div>
</template>

<script>
const lightColor = [
    '#6ebffb',
    '#a9df34',
    '#40c057',
    '#5a7fe8',
    '#ffea00',
    '#13c9d9',
    '#00a497',
    '#5363c5',
    '#218ede',
    '#f39800',
    '#4262d9',
    '#9799f3',
    '#0fd0b7',
    '#ffd351'
];
const darkColor = [
    '#2ad9ff',
    '#e9c613',
    '#26d267',
    '#f68b17',
    '#fc4a4a',
    '#4d76eb',
    '#00e1c4',
    '#9465f4',
    '#c0f02f',
    '#06a4ff'
];
export default {
    //import引入的组件需要注入到对象中才能使用
    name: 'airPlatformType',
    components: {},
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        },
        option: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    data() {
        const fontColor =
            window.localStorage.themeType === 'dark' ? '#fff' : '#333';
        const color =
            window.localStorage.themeType === 'dark' ? darkColor : lightColor;
        return {
            pieData: [],
            defaultOption: {
                color,
                series: [
                    {
                        center: ['25%', '50%']
                    }
                ],
                legend: [
                    {
                        show: true,
                        top: 'center',
                        itemWidth: 12,
                        itemHeight: 12,
                        icon: 'rect',
                        left: '50%',
                        padding: 0,
                        textStyle: {
                            color: fontColor,
                            rich: { name: { width: 60 }, value: { width: 50 } }
                        },
                        right: '5%',
                        orient: 'vertical',
                        data: [],
                        formatter: (name) => {
                            return 'name';
                        },
                        rich: {
                            name: { width: 80 },
                            rate: { width: 50 }
                        }
                    },
                    {
                        show: true,
                        top: 'center',
                        itemWidth: 12,
                        itemHeight: 12,
                        icon: 'rect',
                        left: 'auto',
                        padding: 0,
                        textStyle: {
                            color: fontColor,
                            rich: { name: { width: 60 }, value: { width: 50 } }
                        },
                        right: '5%',
                        orient: 'vertical',
                        data: [],
                        formatter: (name) => {
                            return 'name';
                        },
                        rich: {
                            name: { width: 80 },
                            rate: { width: 50 }
                        }
                    }
                ]
            },
            finalOption: {}
        };
    },
    computed: {},
    watch: {
        data: function (v) {
            this.getData();
        },
        option: function (v) {
            this.getData();
        }
    },
    created() {},
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            this.pieData = [];
            let sum = 0;
            this.data.forEach((obj) => {
                sum += Number(obj.value);
            });
            this.finalOption = {
                ...this.defaultOption,
                ...this.option
            };

            sum = sum || 1;
            let legendData = [];
            this.data.forEach((obj, i) => {
                legendData.push(obj.name);
                this.pieData.push({
                    name: obj.name,
                    value: obj.value,
                    legendName:
                        '{name|' +
                        obj.name +
                        '} {value|' +
                        ((100 * Number(obj.value)) / sum).toFixed(0) +
                        '%}'
                });
            });
            this.finalOption.legend[0].formatter = (name) => {
                let rate = '';
                this.data.forEach((obj, i) => {
                    if (name == obj.name) {
                        rate = ((100 * Number(obj.value)) / sum).toFixed(0);
                    }
                });
                return '{name|' + name + '}{rate|' + rate + '%}';
            };
            this.finalOption.legend[1].formatter = (name) => {
                let rate = '';
                this.data.forEach((obj, i) => {
                    if (name == obj.name) {
                        rate = ((100 * Number(obj.value)) / sum).toFixed(0);
                    }
                });
                return '{name|' + name + '}{rate|' + rate + '%}';
            };
            this.finalOption.legend[0].data = legendData.slice(0, 4);
            this.finalOption.legend[1].data = legendData.slice(4);
        }
    }
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
