#!usr/bin/env node
/** @format */

const path = require('path');
const fs = require('fs');

const fileUtil = require('./util/file.js');

/**
 * 查找main.js所在目录
 */
const findMainJsDir = () => {
    let projectDir = path.resolve('./');
    return fileUtil.resolveFileDir(projectDir, 'main.js') || null;
};

/**
 * 从根目录拷贝bowo-form库到项目源代码目录
 */
const copyFormModuleIntoSourceDir = () => {
    let mainDir = findMainJsDir();
    if (mainDir === null) {
        console.log(`未查找到main.js所在目录`);
        return;
    }

    let projectDir = path.resolve('./');
    if (mainDir === projectDir) {
        console.log(`当前项目是标准uni-app结构，无需注入包`);
        return;
    }

    let sourcePath = `${projectDir}/node_modules/p-lego/`;
    let targetPath = `${mainDir}/components/`;
    fileUtil.copyDir(sourcePath, targetPath);
};

//执行copy动态表单module代码到非uni-app标准项目src目录
copyFormModuleIntoSourceDir();
