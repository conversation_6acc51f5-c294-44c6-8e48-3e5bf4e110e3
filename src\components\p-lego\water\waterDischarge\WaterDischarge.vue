<!-- @format -->
<template>
    <div>
        <div
            :style="{
                height: option.height,
                width: option.width,
                backgroundColor: option.backgroundColor
            }"
            ref="dashboardEchart"
        ></div>
    </div>
</template>

<script>
export default {
    props: {
        data: Object,
        option: {
            // 配置宽高
            type: Object,
            default() {
                return {
                    height: '400px',
                    width: '800px'
                };
            }
        }
    },

    data() {
        return {};
    },

    created() {},

    mounted() {
        this.setDashboardEchart(this.data);
    },

    methods: {
        // 配置总量仪运行情况图
        setDashboardEchart(data) {
            let option = {
                title: [
                    {
                        text:
                            '{name|' +
                            data[0].name +
                            '}\n{val|' +
                            data[0].value +
                            '吨' +
                            '}',
                        x: '15%',
                        bottom: 0,
                        textStyle: {
                            rich: {
                                name: {
                                    fontSize: 16,
                                    width: 80,
                                    align: 'center',
                                    // padding: [0, 0, 0, 0],
                                    fontWeight: '400',
                                    color: '#fff'
                                },
                                val: {
                                    fontSize: 20,
                                    fontWeight: '400',
                                    width: 80,
                                    align: 'center',
                                    color: '#32d6ff',
                                    padding: [0, 0, 10, 0]
                                }
                            }
                        }
                    },
                    {
                        text:
                            '{name|' +
                            data[1].name +
                            '}\n{val|' +
                            data[1].value +
                            '吨' +
                            '}',
                        x: '45%',
                        bottom: 0,
                        textStyle: {
                            rich: {
                                name: {
                                    fontSize: 16,
                                    width: 80,
                                    align: 'center',
                                    // padding: [0, 0, 0, 0],
                                    fontWeight: '400',
                                    color: '#fff'
                                },
                                val: {
                                    fontSize: 20,
                                    fontWeight: '400',
                                    width: 80,
                                    align: 'center',
                                    color: '#32d6ff',
                                    padding: [0, 0, 10, 0]
                                }
                            }
                        }
                    },
                    {
                        text:
                            '{name|' +
                            data[2].name +
                            '}\n{val|' +
                            data[2].value +
                            '吨' +
                            '}',
                        x: '75%',
                        bottom: 0,
                        textStyle: {
                            rich: {
                                name: {
                                    fontSize: 16,
                                    width: 80,
                                    align: 'center',
                                    // padding: [0, 0, 0, 0],
                                    fontWeight: '400',
                                    color: '#fff'
                                },
                                val: {
                                    fontSize: 20,
                                    fontWeight: '400',
                                    width: 80,
                                    align: 'center',
                                    color: '#32d6ff',
                                    padding: [0, 0, 10, 0]
                                }
                            }
                        }
                    }
                ],
                tooltip: {
                    show: false
                },
                series: [
                    // 中间仪表盘
                    {
                        type: 'gauge',
                        center: ['50%', '55%'], // 默认全局居中
                        radius: '90%',
                        splitNumber: 5, //刻度数量
                        min: 0,
                        max: 100,
                        startAngle: 200,
                        endAngle: -20,
                        clockwise: true,
                        axisLine: {
                            show: true,
                            lineStyle: {
                                width: 5,
                                shadowBlur: 0,
                                color: [[1, '#32a3ff']]
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: '#309cf3',
                                width: 1
                            },
                            length: 15,
                            splitNumber: 10
                        },
                        splitLine: {
                            show: true,
                            length: 20,
                            lineStyle: {
                                color: '#32a3ff'
                            }
                        },
                        axisLabel: {
                            distance: 10,
                            textStyle: {
                                color: '#fff',
                                fontSize: '16'
                                // fontWeight: 'bold',
                            }
                        },
                        pointer: {
                            //仪表盘指针
                            show: 1,
                            width: 4
                        },
                        itemStyle: {
                            normal: {
                                color: '#32a3ff'
                            }
                        },
                        detail: {
                            show: true,
                            formatter: '{value}%',
                            offsetCenter: [0, '45%'],
                            textStyle: {
                                fontSize: 30
                            }
                        },
                        data: [
                            {
                                name: '',
                                value: data[1].ratio || 0
                            }
                        ]
                    },

                    // 左侧仪表盘
                    {
                        type: 'gauge',
                        center: ['22%', '55%'], // 默认全局居中
                        radius: '80%',
                        splitNumber: 5, //刻度数量
                        min: 0,
                        max: 100,
                        endAngle: 45,
                        clockwise: true,
                        axisLine: {
                            show: true,
                            lineStyle: {
                                width: 5,
                                shadowBlur: 0,
                                color: [[1, '#32a3ff']]
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: '#309cf3',
                                width: 1
                            },
                            length: 15,
                            splitNumber: 10
                        },
                        splitLine: {
                            show: true,
                            length: 20,
                            lineStyle: {
                                color: '#32a3ff'
                            }
                        },
                        axisLabel: {
                            distance: 10,
                            textStyle: {
                                color: '#fff',
                                fontSize: '16'
                                // fontWeight: 'bold',
                            }
                        },
                        pointer: {
                            //仪表盘指针
                            show: 1,
                            width: 4
                        },
                        itemStyle: {
                            normal: {
                                color: '#32a3ff'
                            }
                        },
                        detail: {
                            show: true,
                            formatter: '{value}%',
                            offsetCenter: [0, '45%'],
                            textStyle: {
                                fontSize: 30
                            }
                        },
                        data: [
                            {
                                name: '',
                                value: data[0].ratio || 0
                            }
                        ]
                    },

                    // 右侧仪表盘
                    {
                        type: 'gauge',
                        center: ['80%', '55%'], // 默认全局居中
                        radius: '80%',
                        splitNumber: 5, //刻度数量
                        min: 0,
                        max: 100,
                        startAngle: 140,
                        endAngle: -45,
                        clockwise: true,
                        axisLine: {
                            show: true,
                            lineStyle: {
                                width: 5,
                                shadowBlur: 0,
                                color: [[1, '#32a3ff']]
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: '#309cf3',
                                width: 1
                            },
                            length: 15,
                            splitNumber: 10
                        },
                        splitLine: {
                            show: true,
                            length: 20,
                            lineStyle: {
                                color: '#32a3ff'
                            }
                        },
                        axisLabel: {
                            distance: 10,
                            textStyle: {
                                color: '#fff',
                                fontSize: '16'
                                // fontWeight: 'bold',
                            }
                        },
                        pointer: {
                            //仪表盘指针
                            show: 1,
                            width: 4
                        },
                        itemStyle: {
                            normal: {
                                color: '#32a3ff'
                            }
                        },
                        detail: {
                            show: true,
                            formatter: '{value}%',
                            offsetCenter: [0, '45%'],
                            textStyle: {
                                fontSize: 30
                            }
                        },
                        data: [
                            {
                                name: '',
                                value: data[2].ratio || 0
                            }
                        ]
                    }
                ]
            };

            let myChart = this.$echarts.init(this.$refs.dashboardEchart);
            myChart.setOption(option, true);
        }
    }
};
</script>
<style scoped></style>
