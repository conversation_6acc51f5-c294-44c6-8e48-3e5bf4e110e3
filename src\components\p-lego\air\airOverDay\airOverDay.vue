<!-- @format -->

<template>
    <div style="width: 258px; height: 210px; background: #004280">
        <p-chart style="width: 258px; height: 210px" :option="option"></p-chart>
    </div>
</template>
<script>
export default {
    name: 'airOverDay',
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        }
    },
    data() {
        return {
            option: {
                color: ['#3cefff'],
                tooltip: {},
                toolbox: { show: false },
                grid: {
                    top: 30,
                    containLabel: true,
                    bottom: 10,
                    left: 0,
                    right: 0
                },
                xAxis: [
                    {
                        type: 'category',
                        data: ['累计超标天数', '剩余可超标天数'],
                        position: 'top',
                        axisTick: {
                            alignWithLabel: true
                        },
                        nameTextStyle: {
                            color: '#FFF'
                        },
                        offset: 5,
                        axisLine: {
                            show: false,
                            lineStyle: {
                                fontSize: 16
                            }
                        },
                        axisLabel: {
                            textStyle: {
                                fontSize: 16,
                                color: '#FFF'
                            },
                            interval: 0,
                            formatter: '{value} \n'
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            show: false,
                            textStyle: {
                                color: '#FFF'
                            },
                            formatter: '{value}'
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: false,
                            lineStyle: {
                                color: '#0c2c5a'
                            }
                        },
                        axisLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        type: 'bar',
                        itemStyle: {},
                        barWidth: '20',
                        label: {
                            normal: {
                                fontSize: 16,
                                show: true,
                                color: '#FFF',
                                position: 'top',
                                formatter: '{c}天'
                            }
                        },
                        data: [
                            {
                                value: 0,
                                itemStyle: {
                                    normal: {
                                        color: {
                                            x: 0,
                                            y: 0,
                                            x2: 0,
                                            y2: 1,
                                            type: 'linear',
                                            global: false,
                                            colorStops: [
                                                {
                                                    //第一节下面
                                                    offset: 0,
                                                    color: 'rgba(247,116,88,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#F3425A'
                                                }
                                            ]
                                        }
                                    }
                                }
                            },
                            {
                                value: 0,
                                itemStyle: {
                                    normal: {
                                        color: {
                                            x: 0,
                                            y: 0,
                                            x2: 0,
                                            y2: 1,
                                            type: 'linear',
                                            global: false,
                                            colorStops: [
                                                {
                                                    //第一节下面
                                                    offset: 0,
                                                    color: 'rgba(78,215,253,1)'
                                                },
                                                {
                                                    offset: 1,
                                                    color: '#06C7CC'
                                                }
                                            ]
                                        }
                                    }
                                }
                            }
                        ]
                    }
                ]
            }
        };
    },
    watch: {
        data: function (v) {
            this.getData();
        }
    },
    mounted() {
        this.getData();
    },
    methods: {
        getData() {
            this.option.series.forEach((item) => {
                item.data.forEach((t, i) => {
                    t.value = this.data[i];
                });
            });
        }
    }
};
</script>
