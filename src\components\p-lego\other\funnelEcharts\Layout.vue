<!-- @format -->

<template>
    <div style="width: 100%; height: 100%" ref="funnelEcharts"></div>
</template>
<script>
export default {
    data() {
        return {};
    },
    props: {
        data: {
            type: Object
        }
    },
    mounted() {
        this.getEcharts();
    },
    watch: {
        data: {
            deep: true,
            immediate: true,
            handler: function (New, Old) {
                this.$nextTick(() => {
                    this.getEcharts();
                });
            }
        }
    },
    methods: {
        getEcharts() {
            let myChart = this.$echarts.init(this.$refs.funnelEcharts);

            // 从大到小排序
            let array = [];

            array = this.data.LIST.sort((a, b) => {
                return b.value - a.value;
            });

            let data = array;

            let option = {
                color: this.data.color,
                series: [
                    {
                        top: 10,
                        bottom: 20,
                        type: 'funnel',
                        left: 60,
                        right: 60,
                        minSize: 150,
                        label: {
                            show: true,
                            fontSize: '40',
                            position: 'inside',
                            formatter: '{b}{c}'
                        },
                        data: data
                    }
                ]
            };

            myChart.setOption(option);
        }
    }
};
</script>
<style scoped></style>
