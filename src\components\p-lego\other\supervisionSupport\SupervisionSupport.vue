<!-- @format -->

<!--  -->
<template>
    <div class="rp">
        <div class="zy-cell cell4">
            <div class="hd">
                <p class="til">监督帮扶</p>
            </div>
            <div class="bd">
                <div class="gap"></div>
                <div class="gap"></div>
                <ul class="zy-list6">
                    <li>
                        <div class="tu">
                            <img src="./images/list6-li1.png" alt="" />
                        </div>
                        <div class="rp">
                            <p class="p1">开展轮次</p>
                            <p class="p2">{{ jdbfLcZx.KZLC_SL }}</p>
                        </div>
                    </li>
                    <li>
                        <div class="tu">
                            <img src="./images/list6-li2.png" alt="" />
                        </div>
                        <div class="rp">
                            <p class="p1">累计天数</p>
                            <p class="p2">{{ jdbfLcZx.LJTS }}</p>
                        </div>
                    </li>
                    <li>
                        <div class="tu">
                            <img src="./images/list6-li3.png" alt="" />
                        </div>
                        <div class="rp">
                            <p class="p1">专项</p>
                            <p class="p2">{{ jdbfLcZx.KZZX_SL }}</p>
                        </div>
                    </li>
                    <li>
                        <div class="tu">
                            <img src="./images/list6-li4.png" alt="" />
                        </div>
                        <div class="rp">
                            <p class="p1">城市</p>
                            <p class="p2">{{ jdbfLcZx.CSSL }}</p>
                        </div>
                    </li>
                    <li>
                        <div class="tu">
                            <img src="./images/list6-li5.png" alt="" />
                        </div>
                        <div class="rp">
                            <p class="p1">检查企业</p>
                            <p class="p2">
                                {{ jdbfLcZx.JCQY_JS }}
                                <span>家</span>
                            </p>
                        </div>
                    </li>
                    <li>
                        <div class="tu">
                            <img src="./images/list6-li6.png" alt="" />
                        </div>
                        <div class="rp">
                            <p class="p1">帮扶人员</p>
                            <p class="p2">
                                {{ jdbfLcZx.BFRY_SL }}<span>人</span>
                            </p>
                        </div>
                    </li>
                    <li>
                        <div class="tu">
                            <img src="./images/list6-li7.png" alt="" />
                        </div>
                        <div class="rp">
                            <p class="p1">发现问题</p>
                            <p class="p2">
                                {{ jdbfLcZx.FXWT_GS }}<span>个</span>
                            </p>
                        </div>
                    </li>
                    <li>
                        <div class="tu">
                            <img src="./images/list6-li8.png" alt="" />
                        </div>
                        <div class="rp">
                            <p class="p1">已销号</p>
                            <p class="p2">
                                {{ jdbfLcZx.YXH_GS }}
                                <span>个</span>
                            </p>
                        </div>
                    </li>
                    <li>
                        <div class="tu">
                            <img src="./images/list6-li8.png" alt="" />
                        </div>
                        <div class="rp">
                            <p class="p1">已上报未销号</p>
                            <p class="p2">
                                {{ jdbfLcZx.WXH_GS }}
                                <span>个</span>
                            </p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {};
    },
    props: {
        jdbfLcZx: {
            type: Object,
            default() {
                return {
                    //监督帮扶轮次专项
                    KZLC_SL: '1', //开展轮次
                    LJTS: '23', // 累计天数
                    KZZX_SL: '23', // 专项
                    CSSL: '23', // 城市
                    JCQY_JS: '23', // 检查企业
                    BFRY_SL: '23', // 帮扶人员
                    FXWT_GS: '23', //    发现问题
                    YXH_GS: '23', //    已销号
                    WXH_GS: '23' //    已上报未销号
                };
            }
        }
    },
    mounted() {},

    methods: {}
};
</script>
<style scoped>
.zy-cell {
    border: 1px solid #0a428a;
    position: relative;
    margin-bottom: 15px;
    background-color: rgba(9, 44, 89, 0.8);
}

.zy-cell.cell1 {
    width: 616px;
    height: 207px;
    box-sizing: border-box;
}

.zy-cell.cell2 {
    height: 177px;
    box-sizing: border-box;
}

.zy-cell.cell3 {
    width: 616px;
    height: 427px;
    box-sizing: border-box;
}

.zy-cell.cell4 {
    width: 616px;
    height: 399px;
    box-sizing: border-box;
}

.zy-cell::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    width: 18px;
    height: 18px;
    background: url(./images/cell-before.png);
}

.zy-cell::after {
    content: '';
    position: absolute;
    bottom: -1px;
    right: -1px;
    width: 18px;
    height: 18px;
    background: url(./images/cell-fater.png);
}

.zy-cell .hd {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    padding-top: 12px;
}

.zy-cell .hd .til {
    font-size: 24px;
    color: #32d6ff;
    line-height: 32px;
    font-family: 'pmzd';
}

.zy-list6 {
    display: flex;
    flex-wrap: wrap;
}

.zy-list6 li {
    width: 33.33%;
    box-sizing: border-box;
    display: flex;
    margin-top: 60px;
}

.zy-list6 li:nth-of-type(1),
.zy-list6 li:nth-of-type(2),
.zy-list6 li:nth-of-type(3) {
    margin-top: 0;
}

.zy-list6 li .tu {
    padding-left: 20px;
}

.zy-list6 li .rp {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 20px;
}

.zy-list6 li .p1 {
    font-size: 16px;
    color: #ddd;
    line-height: 22px;
}

.zy-list6 li .p2 {
    font-size: 24px;
    color: #32d6ff;
    line-height: 33px;
    font-family: 'DIN-Regular';
}

.zy-list6 li .p2 span {
    font-size: 16px;
    vertical-align: baseline;
}
</style>
