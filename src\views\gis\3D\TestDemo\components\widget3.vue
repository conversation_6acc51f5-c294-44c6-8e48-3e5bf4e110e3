<!-- @format -->
<!--绘制面与标注 -->
<template>
    <div></div>
</template>

<script>
import PointUtil from '../utils/PointUtil';
export default {
    props: [],
    data() {
        return {
            map: null,
            pointUtil: null,
            layerID: '面图层',
            layerID2: '文本注记'
        };
    },

    unmounted() {
        this.clear();
    },

    mounted() {
        this.initPage();
    },

    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.addLayer();
        },

        //热力图
        addLayer() {
            fetch('./gis/3D/data/hjyjkj.json')
                .then((res) => res.json())
                .then((json) => {
                    let params = {
                        id: this.layerID
                    };

                    this.pointUtil.addPolygon(json.features, params);

                    let datas = [];

                    for (let item of json.features) {
                        let obj = item.properties;

                        obj.JD = item.geometry.coordinates[0][0][0];
                        obj.WD = item.geometry.coordinates[0][0][1];

                        datas.push(obj);
                    }

                    let params2 = {
                        id: this.layerID2,
                        mapType: 'dark'
                    };

                    this.pointUtil.addTextPoint(datas, params2);
                });
        },

        clear() {
            this.pointUtil.removeLayerByName(this.layerID);
            this.pointUtil.removeLayerByName(this.layerID2);
        },

        pointClickHandle() {}
    }
};
</script>

<style scoped></style>
