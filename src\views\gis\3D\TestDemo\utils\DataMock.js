/** @format */

import dayjs from 'dayjs';
import { result } from 'lodash';
import Mock from 'mockjs';

// debugger;
Mock.setup({ timeout: 2000 });

//空气站点数量
Mock.mock(RegExp('/air/gis/getAirTotal' + '.*'), 'get', (option) => {
    const { data } = Mock.mock({
        data: [
            {
                name: '国控站',
                fiterType: '1',
                selected: true,
                'total|1-20': 1 //生成1 到20的随机数
            },
            {
                name: '省控站',
                fiterType: '2',
                selected: true,
                'total|1-20': 1 //生成1 到20的随机数
            },
            {
                name: '市控站',
                fiterType: '3',
                selected: true,
                'total|1-20': 1 //生成1 到20的随机数
            }
        ]
    });

    let result = {
        data: data,
        status: '000'
    };

    console.log('空气站点数量接口');
    console.log(result);

    return result;
});

//空气站最新时间
Mock.mock(RegExp('/air/gis/getPointHourMaxDate' + '.*'), 'get', (option) => {
    let result = {
        data: {
            JCSJ: dayjs(new Date()).format('YYYY-MM-DD HH')
        },
        status: '000'
    };

    console.log('空气站最新时间接口');
    console.log(result);

    return result;
});

//空气站点
Mock.mock(RegExp('/air/gis/getAirStationBaseInfo' + '.*'), 'get', (option) => {
    let center = GisServerGlobalConstant.mapbox.mapBoxOption.center;
    const { data } = Mock.mock({
        'data|5-10': [
            {
                SSQX: '421303',
                SFYJ: 0,
                YJID: '',
                O3: 82,
                PM25: 49,
                CDMC: '市委党校',
                CO: 0.6,
                WD: center[1],
                SYWRW: '',
                NO2: 2,
                JCSJ: '2023-07-10 13:00:00',
                KQZLLB: '优',
                CDDM: '421300051',
                'JGJB|1': ['1', '2', '3'],
                SSXZQ: '随州市',
                SO2: 7,
                'AQI|1-300': 1,
                PM10: 31,
                JD: center[0],
                JGJBMC: '国控'
            }
        ]
    });

    for (let item of data) {
        item.JD = center[0] + (Math.random() - 0.5) * 0.25;
        item.WD = center[1] + (Math.random() - 0.5) * 0.25;
    }

    let result = {
        data: data,
        status: '000'
    };

    console.log('空气站数据接口');
    console.log(result);

    return result;
});

//工业源
Mock.mock(RegExp('/air/gis/getEnterpriseBaseInfo' + '.*'), 'get', (option) => {
    let center = GisServerGlobalConstant.mapbox.mapBoxOption.center;
    const { data } = Mock.mock({
        'data|50-100': [
            {
                SSQX: '420581',
                FRMC: '@cname',
                SFZDQY: '0',
                QYGM: '',
                WD: '30.250528',
                HBRLXDH: '15926699138',
                JCSJ: dayjs(new Date()).format('YYYY-MM-DD HH'),
                WRYBH: '1653308345554038957056',
                'SFCB|1': ['1', '0'],
                QYMC: '泰山石膏有限公司',
                HYDM: 'F',
                QYDZ: '',
                HBLXR: '艾斌',
                JD: '111.537778',
                SFYC: '0',
                HYLX: 'F-批发和零售业'
            }
        ]
    });

    for (let item of data) {
        item.JD = center[0] + (Math.random() - 0.5) * 0.5;
        item.WD = center[1] + (Math.random() - 0.5) * 0.5;
    }

    console.log('工业源数据接口');
    console.log(result);

    return {
        data: data,
        status: '000'
    };
});

//扬尘源
Mock.mock(RegExp('/air/gis/getDustingBaseInfo' + '.*'), 'get', (option) => {
    let center = GisServerGlobalConstant.mapbox.mapBoxOption.center;
    const { data } = Mock.mock({
        'data|50-100': [
            {
                WINDDIRECTION: '西南',
                WINDSPEED: '0.2m/s',
                HUMID: '63.1%',
                XZQMC: '远安县',
                WD: '31.1964',
                NAME: '远安县谢家垭硅业有限责任公司覃家湾石英砂岩矿',
                TSP: 'N/Aμg/m³',
                PRESSURE: '94.8hPa',
                CODE: '3859b877-7eeb-45c7-95a5-d7c333858956',
                JCSJ: dayjs(new Date()).format('YYYY-MM-DD HH'),
                TEMP: '-3.63℃',
                'PM₁₀': '25μg/m³',
                NOTE: '环保扬尘站',
                ADDRESS: '',
                'PM₂.₅': '7μg/m³',
                XZQDM: '420525',
                JD: '111.47',
                ZT: '1',
                TYPE: '1'
            }
        ]
    });

    for (let item of data) {
        item.JD = center[0] + (Math.random() - 0.5) * 0.5;
        item.WD = center[1] + (Math.random() - 0.5) * 0.5;
    }

    let result = {
        data: data,
        status: '000'
    };

    console.log('扬尘源数据接口');
    console.log(result);

    return result;
});

//污染源-数量统计
Mock.mock(RegExp('/air/gis/getAirSourceTotal' + '.*'), 'get', (option) => {
    const { data } = Mock.mock({
        data: [
            {
                name: '工业源',
                fiterType: 'GYY',
                'total|1-100': 1 //生成1 到20的随机数
            },
            {
                name: '扬尘源',
                fiterType: 'YCY',
                'total|1-100': 1 //生成1 到20的随机数
            }
        ]
    });

    let result = {
        data: data,
        status: '000'
    };

    console.log('污染源数量统计接口');
    console.log(result);

    return result;
});
