<!-- @format -->

<template>
    <div>
        <div class="gap"></div>
        <ul class="air-pollution-trend">
            <li
                class="air-pollution-trend-li"
                v-for="item in pollutionArr"
                :key="item.value"
                :class="{ on: item.value == pollutionVal }"
                @click="pollutionChange(item.value, item.unit)"
            >
                {{ item.name }}
            </li>
        </ul>
        <div class="gap"></div>
        <p-line
            :data="lineData"
            :config="{
                color: ['#24BD5D', '#D8BC37', '#FF00CC', '#F60000', '#0099FF'],
                unit: pollutionUnit
            }"
            :option="lineOption"
            style="width: 100%; height: 300px"
        ></p-line>
    </div>
</template>

<script>
import echartsUtils from './echartsUtils';
export default {
    name: 'airPollutionTrend',
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        },
        format: {
            type: String,
            default: 'YYYY-MM-DD HH'
        }
    },
    data() {
        return {
            pollutionArr: [
                { name: 'AQI', value: 'AQI', unit: '' },
                { name: 'PM₂.₅', value: 'PM25', unit: 'μg/m³' },
                { name: 'PM₁₀', value: 'PM10', unit: 'μg/m³' },
                { name: 'SO₂', value: 'SO2', unit: 'μg/m³' },
                { name: 'NO₂', value: 'NO2', unit: 'μg/m³' },
                { name: 'O₃', value: 'O3', unit: 'μg/m³' },
                { name: 'CO', value: 'CO', unit: 'mg/m³' }
            ],
            pollutionVal: 'AQI',
            pollutionUnit: '',
            lineData: {
                xAxis: [],
                series: []
            },
            lineOption: {
                grid: {
                    left: '8%',
                    right: '8%'
                },
                xAxis: {
                    axisLabel: {
                        formatter: (val) => {
                            if (this.format) {
                                return this.$dayjs(val).format(this.format);
                            } else {
                                return val;
                            }
                        }
                    }
                },
                tooltip: {
                    formatter: (val) => {
                        let str = '';
                        val.map((item) => {
                            str += `<div style="display:flex;align-items: center;justify-content: space-between;">${item.marker}<div style="text-align:right">${item.seriesName}：<b>${item.value}</b> ${this.pollutionUnit}</div></div>`;
                        });
                        return `${val[0].name}</br>${str}`;
                    }
                }
            }
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            this.lineData = echartsUtils.createLineBarOptionDefault(this.data, {
                x: 'XAXIS',
                data: this.pollutionVal,
                name: 'NAME'
            });
        },
        pollutionChange(type, unit) {
            if (this.pollutionVal != type) {
                this.pollutionVal = type;
                this.pollutionUnit = unit;
                this.init();
            }
        }
    }
};
</script>
<style lang="less">
.darkTheme {
    --air-pollution-trend-li-color: #fff;
    --air-pollution-trend-li-broder: 1px solid #2083f3;
}
.lightTheme {
    --air-pollution-trend-li-color: #333;
    --air-pollution-trend-li-broder: 1px solid #bcbcbc;
}
</style>
<style lang="less" scoped>
.air-pollution-trend {
    text-align: center;
    font-family: 'DIN-Medium';
    & .air-pollution-trend-li {
        display: inline-block;
        width: 58px;
        height: 23px;
        line-height: 23px;
        text-align: center;
        font-size: 14px;

        color: var(--air-pollution-trend-li-color);
        border: var(--air-pollution-trend-li-broder);
        cursor: pointer;
    }
    & .air-pollution-trend-li + .air-pollution-trend-li {
        cursor: pointer;
        margin-left: 10px;
    }
    & .air-pollution-trend-li.on {
        background: #0181dc;
        color: #fff;
        border-color: #0181dc;
    }
}
</style>
