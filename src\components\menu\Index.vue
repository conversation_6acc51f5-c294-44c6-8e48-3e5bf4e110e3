<!-- @format -->

<template>
    <div class="tac">
        <el-menu
            :default-active="this.$route.path"
            router
            class="el-menu-vertical-demo"
        >
            <template v-for="m in menuList">
                <el-menu-item
                    :index="m.LJDZ"
                    :key="m.LJDZ"
                    v-if="m.children.length == 0"
                >
                    <!-- <i class="el-icon-location"></i> -->
                    {{ m.CDMC }}
                </el-menu-item>
                <ReSub :m="m" :key="m.LJDZ" v-else></ReSub>
            </template>
        </el-menu>
    </div>
</template>

<script>
import ReSub from './ReSub';
export default {
    data() {
        return {
            defaultOpen: []
        };
    },
    props: ['menuList'],
    components: {
        ReSub
    }
};
</script>

<style>
.el-menu {
    border: none;
}
</style>
