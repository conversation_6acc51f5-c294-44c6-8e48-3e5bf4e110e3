<!-- @format -->

<template>
    <div class="water-sort">
        <p-pie
            :option="pieOpt"
            :data="pieData"
            :config="{
                color: ['#44c5fd', '#73bb31', '#eebd15', '#f88e17', '#ee3b5b']
            }"
            style="width: 100%; height: 100%"
        ></p-pie>
        <div class="water-sort-rate">
            <span>优良率：&nbsp;&nbsp;</span>

            <span
                :style="[{ color: '#11b8f5' }]"
                style="font-size: 20px; font-weight: 600; margin-left: -10px"
                >{{ yll }}%</span
            >
        </div>
    </div>
</template>

<script>
export default {
    name: 'waterSortConstitute',
    props: {
        data: {
            //饼图数据
            type: Array,
            default: function () {
                return [];
            }
        },
        yll: {
            //优良率
            type: String,
            default: function () {
                return '';
            }
        }
    },
    data() {
        return {
            pieOpt: {
                series: []
            },
            pieData: []
        };
    },
    watch: {
        data: 'myCharts'
    },
    mounted() {
        this.myCharts();
    },
    methods: {
        myCharts() {
            this.pieData = [];
            this.data.forEach((item) => {
                this.pieData.push({
                    name: `${item.name} ${item.value}个`,
                    num: item.value,
                    value: item.percent
                });
            });
            this.pieOpt = {
                title: {
                    left: '45%',
                    top: '80%',
                    subtext: '',
                    textStyle: {
                        color: '#666',
                        fontWeight: 400,
                        fontSize: 16
                    },
                    subtextStyle: {
                        color: '#666',
                        fontWeight: 400,
                        fontSize: 16
                    }
                },
                legend: {
                    bottom: 0,
                    left: null,
                    right: 20,
                    top: 'center',
                    orient: 'vertical',
                    icon: 'rect',
                    itemWidth: 16,
                    itemHeight: 16,
                    itemGap: 10,
                    height: 230,
                    textStyle: {
                        fontSize: 16
                    }
                },
                toolbox: {
                    show: false
                },
                tooltip: {
                    formatter: function (obj) {
                        let data = obj.data;
                        let name = data.name.split(' ')[0];
                        return `${name}：${data.num}个, ${data.value}%`;
                    }
                }
            };
        }
    },
    computed: {
        // 优良率字体动态颜色;
        calWaterRateColor() {
            let color = [
                '#44c5fd',
                '#73bb31',
                '#eebd15',
                '#f88e17',
                '#ee3b5b',
                '#999999'
            ];
            switch (true) {
                case this.yll <= 20: {
                    return color[4];
                }
                case this.yll <= 40: {
                    return color[3];
                }
                case this.yll <= 60: {
                    return color[2];
                }
                case this.yll <= 80: {
                    return color[1];
                }
                case this.yll <= 100: {
                    return color[0];
                }
                default: {
                    return color[color.length - 1];
                }
            }
        }
    }
};
</script>

<style scoped lang="less">
.water-sort {
    position: relative;
    &-rate {
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        text-align: center;
        font-size: 16px;
        height: 30px;
        margin-top: -20px;
    }
}
</style>
