/** @format */

import axios from '_u/ajaxRequest';

const BASE_URL = ServerGlobalConstant.dataUrl;

//公共代码集
export const getCommonCodesFromCache = (data) => {
    return axios.request({
        method: 'post',
        url:
            BASE_URL +
            '/platform/system/commoncodecontroller/getCommonCodesFromCache',
        data: data
    });
};

export const queryAdministrativeRegionByCondition = (data) => {
    return axios.request({
        method: 'post',
        url:
            BASE_URL +
            '/platform/system/administrativeregioncontroller/queryAdministrativeRegionByCondition',
        data: data
    });
};

export const getUnreadList = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/getNotReadKnowledgeList',
        data: data
    });
};

// 知识库查询列表接口
export const getKnowledgeList = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/getKnowledgeList',
        data: data
    });
};

//全文检索
export const getContent = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/getContent',
        data: data,
        showLoading: false
    });
};
// 知识收藏取消接口
export const cancelCollectKnowledge = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/cancelCollectKnowledge',
        data: data
    });
};
// 知识收藏接口
export const collectKnowledge = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/collectKnowledge',
        data: data
    });
};

// 知识分享取消接口
export const cancelShareKnowledge = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/cancelShareKnowledge',
        data: data
    });
};
// 知识分享接口
export const shareKnowledge = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/shareKnowledge',
        data: data
    });
};

// 水印
export const getWaterMark = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/getWaterMark',
        data: data
    });
};

// 学习记录
export const recordClassHours = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/recordClassHours',
        data: data,
        showLoading: false
    });
};

// 保存视频播放进度
export const recordVideoProgress = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/recordVideoProgress',
        data: data,
        showLoading: false
    });
};

export const updateDjcs = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/updateDjcs',
        data: data,
        showLoading: false
    });
};

// 查询视频播放进度
export const getVideoProgress = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/ZskController/getVideoProgress',
        data: data
    });
};

// 年监测人数分析（表格)
export const jcrsStatistics = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/gd/GrjlStatisticsController/jcrsStatistics',
        data: data
    });
};

// 年监测人数趋势分析（曲线图）
export const jcryQsStatistics = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/gd/GrjlStatisticsController/jcryQsStatistics',
        data: data
    });
};

// 个人剂量分析
export const grjlAvgStatistics = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/gd/GrjlStatisticsController/grjlAvgStatistics',
        data: data
    });
};

// 剂量区间分布分析
export const grjlQjStatistics = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/gd/GrjlStatisticsController/grjlQjStatistics',
        data: data
    });
};

// 任务剂量分析
export const grjlStatistics = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/gd/GrjlStatisticsController/grjlStatistics',
        data: data
    });
};

// 监管执法工作量分析
export const zfjlStatistics = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/gd/GrjlStatisticsController/zfjlStatistics',
        data: data
    });
};
