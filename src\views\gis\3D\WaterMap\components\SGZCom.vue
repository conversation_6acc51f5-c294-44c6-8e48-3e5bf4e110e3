<!-- @format -->

<template>
    <div class="sw0811-dlbx5">
        <p class="yy0812-tctit">手工监测</p>
        <dt class="flx1 ac" style="padding-left: 15px">
            <label class="sw0811-check1" @click="allLayerClick"
                ><input
                    type="checkbox"
                    disabled
                    style="pointer-events: none"
                    :checked="checkAll"
                /><strong>监管级别（{{ allTotal }}）</strong></label
            >
        </dt>
        <dd class="sw0811-checkbox1 yy0812-checkbox1">
            <label
                class="sw0811-check1"
                v-for="(item, index) of zts"
                :key="index"
                @click="ztLayerClick(item)"
                ><input
                    type="checkbox"
                    :checked="item.selected"
                    disabled
                    style="pointer-events: none"
                /><span>{{ item.name }}（{{ item.total }}）</span></label
            >
        </dd>

        <STLXCom :tjData="tjData" @stlxChange="stlxChangeHandle"></STLXCom>
    </div>
</template>

<script>
import { getSgjcStatiion, getSGZPointCount } from '@/api/gis/3D/WaterMap';
import STLXCom from './STLXCom';
import PointUtil from '../utils/PointUtil';
export default {
    data() {
        return {
            jgjbArr: [],
            zts: [
                // {
                //     name: '国控站',
                //     fiterType: '1',
                //     selected: true,
                //     total: 0,
                //     DM: 'SG-GKZ'
                // },
                // {
                //     name: '省控站',
                //     fiterType: '2',
                //     selected: true,
                //     total: 0,
                //     DM: 'SG-SKZ'
                // }
            ],

            allTotal: 0,
            checkAll: false, //全部选中

            arrResult: null,
            layerName: '断面',
            map: null,
            tjData: null,
            arrSTLX: ['河流', '湖库'], //水体类型
            pointSize: 'min',

            style: '1'
        };
    },
    inject: ['pointClickHandle', 'refreshSearchData'],
    props: ['showName'],
    components: { STLXCom },
    unmounted() {
        this.clear();

        this.map.off('zoomend', this.zoomendHandle);
    },
    mounted() {
        this.getPointCount();
        this.initPage();
    },
    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.getSelectJGJB();

            this.getData();

            this.initEvent();
        },

        initEvent() {
            this.map.on('zoomend', this.zoomendHandle);
        },

        zoomendHandle() {
            if (this.showName) {
                let zoom = this.map.getZoom();
                if (zoom > 13 && this.pointSize == 'min') {
                    this.pointSize = 'max';
                } else if (zoom < 13 && this.pointSize == 'max') {
                    this.pointSize = 'min';
                }
            } else {
            }
        },

        //专题点击
        allLayerClick() {
            this.checkAll = !this.checkAll;

            for (let o of this.zts) {
                o.selected = this.checkAll;
            }

            this.getSelectJGJB();

            this.addPointToMap();
        },

        //子专题点击
        ztLayerClick(obj) {
            obj.selected = !obj.selected;

            //根据子级的选中状态，设置父级的选中状态
            this.checkAll = this.zts.some((item) => {
                return item.selected;
            });

            this.getSelectJGJB();

            this.addPointToMap();
        },

        //获取选中的监管级别
        getSelectJGJB() {
            let arrTemp = this.zts.filter((item) => {
                return item.selected;
            });

            this.jgjbArr = arrTemp.map((item) => {
                return item.fiterType;
            });
        },

        //点位数量
        getPointCount() {
            let param = {};

            getSGZPointCount(param).then((res) => {
                this.tjData = res.data;
            });
        },

        //查询点位数据
        getData() {
            if (this.arrResult) {
                this.addPointToMap();
                return;
            }
            getSgjcStatiion({}).then((res) => {
                this.arrResult = res.data.map((item) => {
                    item.SZLBBS = PowerGL.getWaterDJByLevel(item.SZLB);
                    return item;
                });

                this.addPointToMap();
            });
        },

        //地图上绘制点位
        addPointToMap() {
            if (!this.arrResult) {
                return;
            }
            let arrTemp = this.filerData();

            this.clear();
            if (this.style == '0') {
                this.pointUtil.addImgPoint(arrTemp, { id: this.layerName });
            } else {
                let params = {
                    id: this.layerName,
                    showAno: this.showName,
                    style: this.style, //样式
                    disablePopup: false,
                    disableClick: false
                };

                this.pointUtil.addCustomHtmlLayer(arrTemp, params);
            }

            this.setSearchData(this.layerName, arrTemp);
        },

        //设置搜索框数据
        setSearchData(layerId, arr) {
            let data = {};
            data[layerId] = arr;
            this.refreshSearchData(data);
        },

        clear() {
            if (this.pointUtil) {
                this.pointUtil.removeLayerByName(this.layerName);
                this.pointUtil.clear(this.layerName);
            }
        },

        filerData() {
            let arrTemp = this.arrResult.filter((item) => {
                if (item.JGJB && item.JGJB.includes(',')) {
                    let flag = false;

                    let temp = item.JGJB.split(',');

                    for (let str of temp) {
                        if (this.jgjbArr.includes(str)) {
                            flag = true;
                            break;
                        }
                    }

                    return flag && this.arrSTLX.includes(item.JCFLMC);
                } else {
                    return (
                        this.jgjbArr.includes(item.JGJB) &&
                        this.arrSTLX.includes(item.JCFLMC)
                    );
                }
            });

            return arrTemp;
        },

        stlxChangeHandle(data) {
            this.arrSTLX = data;
        },
        test(style) {
            this.style = style;

            this.addPointToMap();
        }
    },
    watch: {
        tjData: {
            deep: true,
            handler(val) {
                this.allTotal = 0;
                let tempArr = [];
                for (let item of val.JGJB) {
                    if (item.DM == 'ZS') {
                        this.allTotal = item.NUM;
                    } else {
                        tempArr.push({
                            name: item.MC,
                            fiterType: item.DM,
                            selected: this.checkAll,
                            total: item.NUM,
                            DM: item.DM
                        });
                    }
                }

                this.zts = tempArr;

                this.getSelectJGJB();
            }
        },

        arrSTLX(val) {
            this.addPointToMap();
        },
        showName(val) {
            this.addPointToMap();
        }
    }
};
</script>

<style></style>
