<!-- @format -->

<template>
    <ul class="zy-tools-gis">
        <li @click="gotoBack">
            <i class="tool-home" title="初始范围"></i>
        </li>
        <li @click="zoomInClick">
            <i class="tool-zoomIn" title="放大地图"></i>
        </li>
        <li @click="zoomOutClick">
            <i class="tool-zoomOut" title="缩小地图"></i>
        </li>
    </ul>
</template>

<script>
export default {
    name: 'MapTool',
    props: ['map'],
    data() {
        return {};
    },
    components: {},
    computed: {},
    mounted() {},
    methods: {
        gotoBack() {
            let option = PowerGL.deepClone(
                GisServerGlobalConstant.mapbox.mapBoxOption
            );

            delete option.minZoom;

            this.map.flyTo(option);
        },

        zoomOutClick() {
            this.map.zoomOut();
        },

        zoomInClick() {
            this.map.zoomIn();
        }
    },
    watch: {}
};
</script>

<style scoped></style>
