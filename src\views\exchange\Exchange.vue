<!-- @format -->

<template>
    <div
        class="sw1212-wrap"
        style="background: #fff; width: 100%; height: 100%; overflow: auto"
    >
        <!-- <div class="sw1212-header">
            <img
                src="@/assets/exam/images/sw1212_logo.png"
                class="sw1212-logo"
            />
        </div> -->
        <div class="sw1212-container flx1" style="top: 0">
            <!-- <div class="sw1212-lside"></div> -->
            <div class="sw1212-rmain" style="margin-left: 0">
                <div class="sw1212-topbar">
                    <dl>
                        <!-- <dt>知识库</dt> -->
                        <dd>
                            <input
                                type="text"
                                class="sw1212-inptxt1"
                                placeholder="请输入咨询标题进行查询"
                                v-model="serchText"
                                @keyup.enter="serch"
                            />
                            <button
                                type="button"
                                class="sw1212-btn1"
                                @click="serch"
                            >
                                搜索
                            </button>
                            <button
                                style="margin-left: 10px; border-radius: 5px"
                                type="button"
                                class="sw1212-btn1"
                                @click="openAddPop"
                            >
                                新增
                            </button>
                        </dd>
                    </dl>
                </div>
                <div class="sw1212-botbar" style="padding: 0 24px">
                    <el-scrollbar :height="`calc(100vh - 230px)`">
                        <ul
                            class="sw0130-ullst1"
                            v-infinite-scroll="load"
                            :infinite-scroll-distance="50"
                            infinite-scroll-immediate
                        >
                            <li
                                v-for="(item, index) in list"
                                :key="index"
                                @click="showPop(item)"
                            >
                                <h1>
                                    <strong>{{ item.ZXBT }}</strong>
                                    <!-- <ul class="sw0130-ulbx1">
                                        <li>
                                            <h2>被收藏</h2>
                                            <p>1250</p>
                                        </li>
                                        <li>
                                            <h2>被浏览</h2>
                                            <p>12546</p>
                                        </li>
                                    </ul> -->
                                </h1>
                                <p>
                                    {{ item.ZXXQ }}
                                </p>
                                <!-- <ul class="sw0130-ulbx2">
                                    <li class="on">
                                        <i class="i1">赞同 {{ item.ZTZS }}</i>
                                    </li>
                                    <li>
                                        <i class="i2"
                                            >不赞同 {{ item.BZTZS }}</i
                                        >
                                    </li>
                                    <li class="nbg">
                                        <i class="i3">1086 条评论</i>
                                    </li>
                                </ul> -->
                            </li>
                            <div
                                style="
                                    display: flex;
                                    justify-content: center;
                                    margin-top: 10px;
                                    font-size: 16px;
                                "
                            >
                                {{ bottomText }}
                            </div>
                        </ul>
                    </el-scrollbar>
                </div>
            </div>
        </div>
    </div>
    <ExchangePop
        ref="ExchangePop"
        v-if="isShowPop"
        @close="isShowPop = false"
        :clickData="clickData"
    ></ExchangePop>
    <el-dialog
        class="dialog_custom"
        v-model="show"
        title="新增"
        :width="1400"
        :before-close="closePreviewDialog"
    >
        <iframe
            v-if="show"
            :src="listUrl"
            frameborder="0"
            style="width: 100%; height: 700px"
        ></iframe>
    </el-dialog>
</template>

<script>
import { getLynrList, analysiscontroller } from '@/api/exchange.js';
import ExchangePop from './ExchangePop.vue';
import CryptoJS from 'crypto-js';
export default {
    components: {
        ExchangePop
    },
    data() {
        return {
            listUrl: '',
            show: false,
            isShowPop: false,
            serchText: '',
            isLastPage: false,
            pageSize: 10,
            pageNum: 1,
            list: [],
            bottomText: '下滑加载更多',
            clickData: {}
        };
    },

    created() {},
    mounted() {
        this.analysiscontroller();
        window.addEventListener(
            'message',
            (e) => {
                if (e.data.source !== 'react-devtools-content-script') {
                    this.closePreviewDialog();
                    this.serch();
                }
            },
            false
        );
    },
    beforeUnmount() {
        window.removeEventListener(
            'message',
            function (msg) {
                // 输出
                console.log(msg);
            },
            false
        );
    },
    methods: {
        analysiscontroller() {
            this.bottomText = '加载中....';
            analysiscontroller({
                xh: '1704957274604076857344',
                urlParams: {
                    isImmediatelyQuery: true,
                    isView: '',
                    multi: true
                },
                pageSize: this.pageSize,
                pageNum: this.pageNum,
                executeQuery: {
                    conditions: [
                        {
                            logic: 'AND',
                            column: 'ZXBT',
                            operate: 'LIKE',
                            value: `%${this.serchText}%`
                        }
                    ]
                },
                extendParam: {},
                staticCondition: []
            }).then((res) => {
                this.list.push(...res.list);
                this.isLastPage = res.isLastPage;
                if (res.isLastPage) {
                    this.bottomText = '已无更多数据';
                } else {
                    this.bottomText = '下滑加载更多';
                }
            });
        },
        serch() {
            this.list = [];
            this.pageNum = 1;
            this.analysiscontroller();
        },
        showPop(item) {
            this.clickData = item;
            this.isShowPop = true;
        },

        openAddPop(item, type) {
            this.listUrl = `/stfs_gd/dynamicform/viewresolvercontroller/render/2024010919221972fb286c43e743f79532f9323dc4fcb9`;
            setTimeout(() => {
                this.show = true;
            }, 100);
        },

        closePreviewDialog() {
            this.show = false;
        },

        load(e) {
            if (!this.isLastPage) {
                this.pageNum++;
                this.analysiscontroller();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.sw1212-dlbx2 dd h1 {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.sw1212-ultbs1 li {
    cursor: pointer;
}
.sw1212-ultbs1 {
    justify-content: flex-start;
}
.pagination-block {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.sw1212-tablelst1 tr:hover {
    background: #e4f5ff;
}

.sw1212-dlbx2 dt .img {
    width: 100%;
    height: 100%;
}
</style>
