<!-- @format -->
<!-- 
    @Time: 2022/11/28
    @Author: maliang
    @Des: 弹框
 -->
<template>
    <div class="bowo-dialog" v-if="visiable">
        <div class="bowo-dialog__mask" v-show="showMask" @click="close"></div>
        <div class="bowo-dialog__wrap" :style="dialogStyle">
            <i class="bowo-dialog__close" @click="close"></i>
            <div class="bowo-dialog__title">
                <span v-if="!$slots.title">{{ title }}</span>
                <slot name="title" />
            </div>
            <div class="bowo-dialog__body">
                <slot />
            </div>
            <div class="bowo-dialog__footer" v-if="$slots.footer">
                <slot name="footer" />
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        // 弹框显示状态
        visiable: Boolean,
        // 遮罩层
        showMask: {
            type: Boolean,
            default: true
        },
        // 弹框标题
        title: {
            type: String,
            default: '弹框'
        },
        // 设置弹框样式 width、height、left、right等
        styleConfig: Object
    },
    computed: {
        dialogStyle() {
            // 默认居中
            const defaultStyle = {
                width: '800px',
                height: '500px',
                left: '50%',
                top: '100px',
                transform: 'translateX(-50%)'
            };
            return Object.assign(defaultStyle, this.styleConfig || {});
        }
    },
    mounted() {
        console.log(111);
    },
    methods: {
        close() {
            this.$emit('update:visiable', false);
            this.$emit('close');
        }
    }
};
</script>

<style lang="scss" scoped>
.bowo-dialog {
    --dialog-bg: #075a9e;
    --title-color: #fff;
    --title-bg: #065fa5;

    // 遮罩层
    &__mask {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        z-index: 999;
    }

    // 弹框容器
    &__wrap {
        z-index: 1000;
        position: fixed;
        box-sizing: border-box;
        background-color: var(--dialog-bg);
    }

    &__title {
        height: 50px;
        line-height: 50px;
        font-size: 20px;
        box-sizing: border-box;
        padding: 0 20px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: var(--title-color);
        background-color: var(--title-bg);
    }

    &__body {
        padding: 0 20px;
    }

    &__footer {
        height: 80px;
        box-sizing: border-box;
        padding: 15px 20px;
    }

    &__close {
        position: absolute;
        top: 17px;
        right: 17px;
        width: 16px;
        height: 16px;
        cursor: pointer;
        background: url(./close.png) no-repeat;
    }
}
</style>
