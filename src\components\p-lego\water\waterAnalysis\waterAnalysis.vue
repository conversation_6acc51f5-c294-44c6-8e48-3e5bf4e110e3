<!-- @format -->

<!-- @format 水质日历-->

<template>
    <div class="pd-panel1">
        <div class="gap"></div>
        <div class="gap"></div>
        <div class="con">
            <ul class="pd-ultbs1">
                <li
                    v-for="(item, index) in type"
                    :key="index"
                    :class="typeVal == item.value ? 'on' : ''"
                    @click="changeType(item.value)"
                >
                    {{ item.label }}
                </li>
            </ul>
            <img src="./images/se2.png" class="fr" />
            <div class="derr">
                <table class="zy-table">
                    <colgroup>
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                        <col width="100" />
                    </colgroup>
                    <thead>
                        <th>
                            <div class="th1">
                                <span class="s1">区县</span>
                                <span class="s2">月份</span>
                                <svg width="100%" height="100%">
                                    <line
                                        x1="0"
                                        y1="0"
                                        x2="110"
                                        y2="40"
                                        style="stroke: #034d70; stroke-width: 1"
                                    />
                                </svg>
                            </div>
                        </th>
                        <th v-for="item in month" :key="item">
                            {{ item }}
                        </th>
                    </thead>
                </table>
                <div
                    class="tablelist"
                    :style="'height:' + data.scrollHeight + 'px'"
                    v-if="data.tableData.length"
                >
                    <table class="zy-table">
                        <colgroup>
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                            <col width="100" />
                        </colgroup>
                        <tbody>
                            <tr
                                v-for="(item, index) in data.tableData"
                                :key="index"
                                class="td"
                            >
                                <td>
                                    {{ item.SSXZQ }}
                                </td>
                                <td :class="changeColor(item.month_01).cls">
                                    {{ changeColor(item.month_01).ext }}
                                </td>
                                <td :class="changeColor(item.month_02).cls">
                                    {{ changeColor(item.month_02).ext }}
                                </td>
                                <td :class="changeColor(item.month_03).cls">
                                    {{ changeColor(item.month_03).ext }}
                                </td>
                                <td :class="changeColor(item.month_04).cls">
                                    {{ changeColor(item.month_04).ext }}
                                </td>
                                <td :class="changeColor(item.month_05).cls">
                                    {{ changeColor(item.month_05).ext }}
                                </td>
                                <td :class="changeColor(item.month_06).cls">
                                    {{ changeColor(item.month_06).ext }}
                                </td>
                                <td :class="changeColor(item.month_07).cls">
                                    {{ changeColor(item.month_07).ext }}
                                </td>
                                <td :class="changeColor(item.month_08).cls">
                                    {{ changeColor(item.month_08).ext }}
                                </td>
                                <td :class="changeColor(item.month_09).cls">
                                    {{ changeColor(item.month_09).ext }}
                                </td>
                                <td :class="changeColor(item.month_10).cls">
                                    {{ changeColor(item.month_10).ext }}
                                </td>
                                <td :class="changeColor(item.month_11).cls">
                                    {{ changeColor(item.month_11).ext }}
                                </td>
                                <td :class="changeColor(item.month_12).cls">
                                    {{ changeColor(item.month_12).ext }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="nomsg" v-if="!data.tableData.length">暂无数据</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            type: [
                { label: '达标率', value: 'DBL' },
                { label: '优良率', value: 'YLL' },
                { label: '劣Ⅴ占比', value: 'LWZB' }
            ],
            typeVal: 'DBL',
            month: [
                '1月',
                '2月',
                '3月',
                '4月',
                '5月',
                '6月',
                '7月',
                '8月',
                '9月',
                '10月',
                '11月',
                '12月'
            ]
        };
    },
    computed: {
        changeColor() {
            let that = this;
            return function (count) {
                if (that.typeVal == 'DBL') {
                    if (count >= 0 && count < 20) {
                        return { cls: 'b5', ext: count + '%' };
                    } else if (count >= 20 && count < 40) {
                        return { cls: 'b4', ext: count + '%' };
                    } else if (count >= 40 && count < 60) {
                        return { cls: 'b3', ext: count + '%' };
                    } else if (count >= 60 && count < 80) {
                        return { cls: 'b2', ext: count + '%' };
                    } else if (count >= 80 && count <= 100) {
                        return { cls: 'b1', ext: count + '%' };
                    } else {
                        return { cls: 'b', ext: count };
                    }
                } else {
                    if (count >= 0 && count < 20) {
                        return { cls: 'b5', ext: count + '%' };
                    } else if (count >= 20 && count < 40) {
                        return { cls: 'b1', ext: count + '%' };
                    } else if (count >= 40 && count < 60) {
                        return { cls: 'b3', ext: count + '%' };
                    } else if (count >= 60 && count < 80) {
                        return { cls: 'b4', ext: count + '%' };
                    } else if (count >= 80 && count <= 100) {
                        return { cls: 'b2', ext: count + '%' };
                    } else {
                        return { cls: 'b', ext: count };
                    }
                }
            };
        }
    },
    props: {
        data: {
            type: Object
        }
    },
    methods: {
        changeType(e) {
            this.typeVal = e;
        }
    }
};
</script>

<style lang="scss" scoped>
.nomsg {
    font-size: 16px;
    color: #aaa;
    text-align: center;
    line-height: 40px;
}

.pd-ultbs1 li + li {
    margin-left: 14px;
}
.pd-ultbs1.tac {
    float: none;
    display: flex;
    justify-content: center;
}
.pd-ultbs1.tac li {
    float: none;
}

.pd-dlghd {
    height: 40px;
    background: #3686e7;
}

tr:nth-child(2n) {
    background: none;
}

.pd-dlghd span {
    float: left;
    font-size: 16px;
    color: #fff;
    padding-left: 15px;
    line-height: 40px;
}

.td {
    cursor: pointer;
}
.tablelist {
    height: 100%;
    overflow-y: scroll;
}
.pd-ultbs1 {
    display: flex;
    float: left;
    li {
        color: #fff;
        margin-right: 16px !important;
        margin-left: 0 !important;
        font-size: 16px;
        margin: 0 18px;
        padding: 0 5px;
        cursor: pointer;
        float: left;
        line-height: 30px;
        background: #01496c;
        border-radius: 4px;
    }
    li.on {
        color: #fff;
        background: #03a1c6;
    }
}
.derr {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding-top: 50px;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}

.zy-table {
    width: 100%;
    border: 1px solid #034d70;
    // margin: 10px;
}

.zy-table th {
    border: 1px solid #034d70;
    text-align: center;
    background: #014566;
    height: 40px;
    font-size: 14px;
    font-weight: 400 !important;
    color: #fff !important;
}

.zy-table td.b {
    background-color: #bfbfbf;
}

.zy-table {
    width: 100%;
    border: 1px solid #034d70;
    // margin: 10px;
}

.zy-table th {
    border: 1px solid #034d70;
    text-align: center;
    background: #014566;
    height: 40px;
    font-size: 14px;
    font-weight: 400 !important;
    color: #fff !important;
}

.zy-table .th1 {
    position: relative;
    height: 40px;
    width: 100%;
}

.zy-table .th1 .s1 {
    position: absolute;
    bottom: 5px;
    left: 5px;
    font-size: 14px;
    color: #fff;
    line-height: 16px;
}

.zy-table .th1 .s2 {
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 14px;
    line-height: 16px;
}

.zy-table .th1 svg {
    position: absolute;
    top: 0;
    left: 0;
}

.zy-table td {
    font-size: 14px;
    color: #fff;
    height: 80px;
    box-sizing: border-box;
    border-bottom: 1px solid #034d70;
    border-right: 1px solid #034d70;
    text-align: center;
    position: relative;
}

.zy-table td .box,
.zy-table td .box2 {
    padding: 9px 0;
    height: 100%;
}

.zy-table td.b1 {
    background-color: #11b8f5;
}

.zy-table td.b2 {
    background-color: #41ea7d;
}

.zy-table td.b3 {
    background-color: #f03762;
}

.zy-table td.b4 {
    background-color: #f0a32b;
}

.zy-table td.b5 {
    background-color: #f0e22b;
}

.zy-table td .box p {
    font-size: 16px;
    color: #fff;
    line-height: 22px;
}

.zy-table td .box2 p {
    color: #fff;
}
.pd-panel1 {
    background: #033c58;
    padding: 15px;
}
</style>
