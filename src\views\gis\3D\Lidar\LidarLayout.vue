<!-- @format -->
<!-- 白色底雷达 -->
<template>
    <el-container
        class="main lidarmode"
        direction="vertical"
        style="height: 100%"
    >
        <el-main class="gisMain" style="padding: 0px; overflow: hidden">
            <div style="width: 100%; height: 100%">
                <MapboxMap
                    :mapOption="mapOption"
                    @onMapLoaded="onMapLoadedHandle"
                ></MapboxMap>
            </div>

            <!-- 业务面板 -->
            <LidarList
                style="top: 10px; bottom: 10px; height: unset; left: 10px"
                :map="map"
            ></LidarList>
        </el-main>
    </el-container>
</template>
<script>
import MapboxMap from '@/components/gis/3D/MapBoxGLMap';
import LidarList from './components/LidarList.vue';

export default {
    created() {},
    data() {
        return {
            mapOption: {},
            map: null,
            skin: '',
            showPanel: false,
            mapCls: '.gisMain'
        };
    },
    unmounted() {
        this.clear();
    },
    components: {
        MapboxMap,
        LidarList
    },
    mounted() {
        document.getElementsByTagName('body')[0].className = 'lightTheme';
    },
    methods: {
        //地图加载完成时触发
        onMapLoadedHandle(map) {
            this.map = map;
        },

        //销毁
        clear() {}
    }
};
</script>

<style>
@import '~_as/gis/commom/reset.css';
@import '~_as/gis/commom/mapCommon.css';

.lidarmode .lidarList {
    background: rgb(255 255 255/95) !important;
    border-radius: 0px !important;
}
.lidarmode .lidarList .pd-ul1 li,
.lidarList .pd-timelst li,
.lidarList .condition {
    color: #333 !important;
}

.lidarmode .qj-pd-ullst1 li + li {
    border-top: 2px dashed #ccc;
}

.lidarmode .lidarList .pd-timelst {
    height: unset;
    position: absolute;
    top: 250px;
    bottom: 80px;
}

.lidarmode .lidarList .pd-ul1 li.on {
    background-color: #409eff !important;
    color: #fff !important;
    border: 1px solid #409eff !important;
}

.lidarmode .scale1 {
    background: #a6c7e9 !important;
}

.lidarmode .lidarList .pd-modhd {
    background: #409eff !important;
}
</style>

<style scoped></style>
