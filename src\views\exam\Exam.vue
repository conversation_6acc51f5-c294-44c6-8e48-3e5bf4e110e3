<!-- @format -->

<template>
    <div id="exam_page" class="sw1212-wrap" style="background: #f1f3f7;width:100%">
        <!-- <div class="sw1212-dlghd">
            <strong>考试</strong>
            <img src="@/assets/exam/images/sw1212_cls2.png" class="icon" />
        </div> -->
        <div class="sw1212-dlgbd" style="padding: 16px 16px 0; top: 0">
            <div class="sw1212-mod">
                <div class="gap"></div>
                <div class="gap"></div>
                <dl class="sw1212-dlbx4">
                    <dt>{{ title || '-' }}</dt>
                    <dd>
                        <p class="bar" style="flex: 1; width: 0">
                            <b
                                :style="{
                                    width:
                                        (questions.filter(
                                            (item) => !!item.answer
                                        ).length *
                                            100) /
                                            questions.length +
                                        '%'
                                }"
                            ></b>
                        </p>
                        <span
                            ><i
                                >已完成
                                {{
                                    questions.filter((item) => !!item.answer)
                                        .length
                                }}题</i
                            >/共<i>{{ questions.length }}</i
                            >题</span
                        >
                    </dd>
                </dl>
                <div class="gap"></div>
                <div class="gap"></div>
            </div>
            <div class="gap15"></div>
            <div class="flx1" style="height: calc(100% - 152px)">
                <div style="flex: 1; width: 0">
                    <div
                        class="sw1212-mod"
                        style="
                            height: calc(100% - 100px);
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                        "
                        v-if="questions[currentIndex]"
                    >
                        <dl class="sw1212-dlbx5" style="margin-top: 30px">
                            <dt>
                                第{{ currentIndex + 1 }}题
                                <i
                                    >（{{
                                        getTypeName(questions[currentIndex])
                                    }}）：</i
                                >
                            </dt>
                            <dd>
                                <div class="gap15"></div>
                                <div class="gap"></div>
                                <h1>
                                    {{ currentIndex + 1 }}、{{
                                        currentQuestion.name
                                    }}。
                                </h1>
                                <div class="gap15"></div>
                                <el-image
                                    fit="object-fit"
                                    style="width: 400px; height: 200px"
                                    v-for="(
                                        picTtem, picIndex
                                    ) in currentQuestion.tmFile"
                                    :key="picTtem"
                                    preview-teleported
                                    :initial-index="picIndex"
                                    :src="'/stfs_gd' + picTtem.path"
                                    :preview-src-list="
                                        returnImgUrl(currentQuestion.tmFile)
                                    "
                                >
                                </el-image>
                                <div class="gap15"></div>
                                <div
                                    class="sw1212-radiolst1"
                                    v-if="
                                        currentQuestion.type === 'DXT' ||
                                        currentQuestion.type === 'PDT'
                                    "
                                >
                                    <div>
                                        <el-radio-group
                                            style="
                                                display: flex;
                                                flex-direction: column;
                                                align-items: flex-start;
                                            "
                                            v-model="currentAnswer"
                                            @change="answerChanged"
                                        >
                                            <div
                                                v-for="(
                                                    item, index
                                                ) in currentQuestion.choice"
                                                :key="item.xh"
                                            >
                                                <el-radio
                                                    :label="item.xh"
                                                    size="large"
                                                    ><p style="font-size: 16px">
                                                        {{ item.text }}
                                                    </p>
                                                </el-radio>
                                                <div>
                                                    <el-image
                                                        fit="object-fit"
                                                        style="
                                                            width: 300px;
                                                            height: 200px;
                                                            margin: 10px;
                                                        "
                                                        v-for="(
                                                            picTtem, picIndex
                                                        ) in item.xxFile"
                                                        :key="picTtem"
                                                        preview-teleported
                                                        :initial-index="
                                                            picIndex
                                                        "
                                                        :src="
                                                            '/stfs_gd' +
                                                            picTtem.path
                                                        "
                                                        :preview-src-list="
                                                            returnImgUrl(
                                                                item.xxFile
                                                            )
                                                        "
                                                    >
                                                    </el-image>
                                                </div>
                                            </div>
                                        </el-radio-group>
                                    </div>
                                </div>
                                <div
                                    class="sw1212-radiolst1"
                                    v-if="currentQuestion.type === 'FXT'"
                                >
                                    <!-- <div
                                        v-for="(
                                            item, index
                                        ) in currentQuestion.choice"
                                        :key="item.xh"
                                    >
                                        <label
                                            class="sw1212-radio1"
                                            style="cursor: pointer"
                                        >
                                            <el-checkbox
                                                v-model="item.checked"
                                                @change="selectAnswer(item)"
                                            />
                                            <span>{{ item.text }}</span>
                                        </label>
                                        <div
                                            class="gap"
                                            v-if="
                                                index !==
                                                currentQuestion.choice.length -
                                                    1
                                            "
                                        ></div>
                                        <div
                                            class="gap"
                                            v-if="
                                                index !==
                                                currentQuestion.choice.length -
                                                    1
                                            "
                                        ></div>
                                    </div> -->
                                    <div>
                                        <el-checkbox-group
                                            style="
                                                display: flex;
                                                flex-direction: column;
                                                align-items: flex-start;
                                            "
                                            v-model="currentAnswer"
                                            @change="answerChanged"
                                        >
                                            <div
                                                v-for="(
                                                    item, index
                                                ) in currentQuestion.choice"
                                                :key="item.xh"
                                            >
                                                <el-checkbox
                                                    :label="item.xh"
                                                    size="large"
                                                    ><p style="font-size: 16px">
                                                        {{ item.text }}
                                                    </p>
                                                </el-checkbox>
                                                <div>
                                                    <el-image
                                                        fit="object-fit"
                                                        style="
                                                            width: 300px;
                                                            height: 200px;
                                                            margin: 10px;
                                                        "
                                                        v-for="(
                                                            picTtem, picIndex
                                                        ) in item.xxFile"
                                                        :key="picTtem"
                                                        preview-teleported
                                                        :initial-index="
                                                            picIndex
                                                        "
                                                        :src="
                                                            '/stfs_gd' +
                                                            picTtem.path
                                                        "
                                                        :preview-src-list="
                                                            returnImgUrl(
                                                                item.xxFile
                                                            )
                                                        "
                                                    >
                                                    </el-image>
                                                </div>
                                            </div>
                                        </el-checkbox-group>
                                    </div>
                                </div>
                            </dd>
                            <!-- <div class="gap" style="height: 300px"></div> -->
                            <!-- <dt>正确答案：C&emsp;&emsp;&emsp;我的答案：C</dt> -->
                        </dl>

                        <!-- <div
                            class="sw1212-askcon"
                            style="margin-bottom: 30px"
                            v-if="currentQuestion.ztjx"
                        >
                            <h1>解析：</h1>
                            <p v-html="currentQuestion.ztjx"></p>
                        </div> -->
                    </div>
                    <div class="gap15"></div>
                    <ul class="sw1212-ulpage1">
                        <li
                            @click="prev()"
                            :style="{
                                color: currentIndex === 0 ? '#eee' : '#467DDC'
                            }"
                        >
                            上一题
                        </li>
                        <li
                            @click="next()"
                            :style="{
                                color:
                                    currentIndex === questions.length - 1
                                        ? '#eee'
                                        : '#467DDC'
                            }"
                        >
                            下一题
                        </li>
                    </ul>
                    <div class="gap15"></div>
                </div>
                <div style="width: 400px; margin-left: 14px">
                    <div class="sw1212-mod">
                        <ul class="sw1212-ulbx5">
                            <!-- <li>
                                <img
                                    src="@/assets/exam/images/sw1212_ic1d.png"
                                    alt=""
                                />
                                <p>暂停</p>
                            </li> -->
                            <li>
                                <h1>{{ remainTime || '' }}</h1>
                                <p>剩余时间</p>
                            </li>
                        </ul>
                    </div>
                    <div class="gap15"></div>
                    <div class="sw1212-mod" style="height: calc(100% - 115px)">
                        <div class="gap15"></div>
                        <div class="flx1 ac">
                            <strong class="sw1212-txt1"
                                >答题卡
                                <!-- <i style="color: #467ddc">0</i>/6 -->
                            </strong>
                        </div>
                        <div class="gap"></div>
                        <div
                            class="gap"
                            style="border-top: 1px solid #eeeeee"
                        ></div>
                        <p
                            class="sw1212-txt3"
                            v-if="
                                questions.filter((item) => item.type === 'DXT')
                                    .length
                            "
                        >
                            单选题
                        </p>
                        <div class="gap15"></div>
                        <ul class="sw1212-ulbx6">
                            <li
                                :class="[
                                    { cur: questionIndex === currentIndex },
                                    { done: question.answer }
                                ]"
                                v-for="(
                                    question, questionIndex
                                ) in questions.filter(
                                    (item) => item.type === 'DXT'
                                )"
                                :key="questionIndex"
                                @click="currentIndex = questionIndex"
                            >
                                {{ questionIndex + 1 }}
                            </li>
                            <!-- <li class="on"></li> -->
                        </ul>
                        <div class="gap15"></div>
                        <div class="gap15"></div>
                        <p
                            class="sw1212-txt3"
                            v-if="
                                questions.filter((item) => item.type === 'FXT')
                                    .length
                            "
                        >
                            多选题
                        </p>
                        <div class="gap15"></div>
                        <ul class="sw1212-ulbx6">
                            <li
                                :class="[
                                    {
                                        cur:
                                            questionIndex ===
                                            currentIndex -
                                                questions.filter(
                                                    (item) =>
                                                        item.type === 'DXT'
                                                ).length
                                    },
                                    { done: question.answer }
                                ]"
                                v-for="(
                                    question, questionIndex
                                ) in questions.filter(
                                    (item) => item.type === 'FXT'
                                )"
                                :key="questionIndex"
                                @click="
                                    currentIndex =
                                        questionIndex +
                                        questions.filter(
                                            (item) => item.type === 'DXT'
                                        ).length
                                "
                            >
                                {{ questionIndex + 1 }}
                            </li>
                        </ul>
                        <div class="gap15"></div>
                        <div class="gap15"></div>
                        <p
                            class="sw1212-txt3"
                            v-if="
                                questions.filter((item) => item.type === 'PDT')
                                    .length
                            "
                        >
                            判断题
                        </p>
                        <div class="gap15"></div>
                        <ul class="sw1212-ulbx6">
                            <li
                                :class="[
                                    {
                                        cur:
                                            questionIndex ===
                                            currentIndex -
                                                questions.filter(
                                                    (item) =>
                                                        item.type === 'DXT'
                                                ).length -
                                                questions.filter(
                                                    (item) =>
                                                        item.type === 'FXT'
                                                ).length
                                    },
                                    { done: question.answer }
                                ]"
                                v-for="(
                                    question, questionIndex
                                ) in questions.filter(
                                    (item) => item.type === 'PDT'
                                )"
                                :key="questionIndex"
                                @click="
                                    currentIndex =
                                        questionIndex +
                                        questions.filter(
                                            (item) => item.type === 'DXT'
                                        ).length +
                                        questions.filter(
                                            (item) => item.type === 'FXT'
                                        ).length
                                "
                            >
                                {{ questionIndex + 1 }}
                            </li>
                        </ul>
                        <div class="gap" style="height: 50px"></div>
                        <ul class="sw1212-ulbx7">
                            <li>
                                <img
                                    src="@/assets/exam/images/sw1212_ic3d.png"
                                    alt=""
                                />已做
                            </li>
                            <li>
                                <img
                                    src="@/assets/exam/images/sw1212_ic4d.png"
                                    alt=""
                                />未做
                            </li>
                            <li style="visibility: hidden">
                                <img
                                    src="@/assets/exam/images/sw1212_ic2d.png"
                                    alt=""
                                />标记
                            </li>
                        </ul>
                        <div class="gap15"></div>
                        <ul class="sw1212-ulbtn1">
                            <!-- <li>保存进度，下次继续</li> -->
                            <li
                                class="on"
                                @click="uninstallListener(), closeExam()"
                            >
                                我要交卷
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog
            v-model="showConfirmFullScreen"
            title="提示"
            width="30%"
            top="30vh"
            :show-close="false"
        >
            <span class="v-tips">{{
                `考试期间需保持全屏且禁止切换窗口,${
                    numberOfViolations > 0
                        ? '已违规' + numberOfViolations + '次,'
                        : ''
                }违规超过三次将自动交卷！`
            }}</span>
            <template #footer>
                <span class="dialog-footer">
                    <el-button
                        type="primary"
                        @click="
                            enterScreen();
                            showConfirmFullScreen = false;
                        "
                    >
                        我已知晓
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <el-dialog
            class="dialog_custom"
            v-model="showExamResult"
            title="考试结束"
            width="30%"
            top="30vh"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="exam_result">
                <h1>考试结果</h1>
                <div>
                    <span>总得分：</span>
                    <span>{{ examResult?.score ?? '-' }}</span>
                </div>
                <div>
                    <span>题目数量：</span
                    ><span>{{ examResult?.allTopic ?? '-' }}</span>
                </div>
                <div>
                    <span>答对数量：</span
                    ><span>{{ examResult?.rightTopic ?? '-' }}</span>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    queryKsstList,
    submitExamination,
    getExaminationJbxx,
    finishExamination
} from '@/api/exam.js';
export default {
    name: 'Exam',
    data() {
        return {
            title: '',
            showConfirmFullScreen: false,
            numberOfViolations: -1,
            questions: [],
            currentAnswer: null,
            currentIndex: 0,
            examEndingTime: null,
            remainTime: '',
            examResult: {},
            showExamResult: false
        };
    },
    computed: {
        currentQuestion() {
            return this.questions[this.currentIndex];
        },
        remainSeconds() {
            if (this.examEndingTime) {
                return this.$dayjs(this.examEndingTime).diff(this.$dayjs());
            } else {
                return null;
            }
        }
    },

    created() {
        this.getQuestions();
        this.installListener();
    },
    unmounted() {
        this.uninstallListener();
    },
    watch: {
        currentIndex: {
            handler() {
                if (this.currentQuestion.type === 'FXT') {
                    this.currentAnswer = this.currentQuestion.answer
                        ? this.currentQuestion.answer.split(',')
                        : [];
                } else {
                    this.currentAnswer = this.currentQuestion.answer
                        ? this.currentQuestion.answer
                        : '';
                }
            }
        },
        remainSeconds() {
            this.setRemainTime();
        }
    },
    mounted() {
        this.showFullScreenTips();
    },
    methods: {
        installListener() {
            // 监听document visibilityState变化事件（用于监听页面是否被隐藏）
            document.addEventListener(
                'visibilitychange',
                this.onVisibilityChange
            );
            window.addEventListener('resize', this.check);
        },
        uninstallListener() {
            // 解除监听事件
            window.removeEventListener('resize', this.check);
            document.removeEventListener(
                'visibilitychange',
                this.onVisibilityChange
            );
        },
        showFullScreenTips() {
            this.numberOfViolations += 1;
            if (this.numberOfViolations > 3) {
                this.showConfirmFullScreen = false;
                this.uninstallListener();
                alert('违规超过三次，自动交卷！');
                this.closeExam();
                return;
            }
            getExaminationJbxx({
                SJXH: this.$route.params.id
            }).then((res) => {
                this.numberOfViolations = Number(res.data_json.DCCS);
                this.examEndingTime = res.data_json.KSJSSJ;
                this.showConfirmFullScreen = true;
            });
        },
        //设置剩余时间
        setRemainTime() {
            let _remainSeconds = Math.floor(this.remainSeconds / 1000);
            setInterval(() => {
                _remainSeconds -= 1;
                let seconds = _remainSeconds % 60;
                let minutes = Math.floor(_remainSeconds / 60) % 60;
                let hours = Math.floor(_remainSeconds / 3600);
                this.remainTime = `${hours}小时${minutes}分钟${seconds}秒`;
            }, 1000);
        },
        //浏览器进入全屏
        enterScreen() {
            let element = document.getElementById('exam_page'); //设置后就是 id==exam_page 的容器全屏
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.webkitRequestFullScreen) {
                element.webkitRequestFullScreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                // IE11
                element.msRequestFullscreen();
            }
        },
        check() {
            if (document.documentElement.clientHeight < window.innerHeight) {
                console.log('退出全屏');
                this.showFullScreenTips();
            } else {
                console.log('进入全屏');
            }
        },
        onVisibilityChange() {
            if (!document.hidden) {
                console.log('页面由隐藏状态切换到了活动状态');
                this.showFullScreenTips();
            }
        },
        getQuestions() {
            this.title = this.$route.query.BT;
            queryKsstList({
                SJXH: this.$route.params.id
            }).then((res) => {
                this.questions = [
                    ...(res.data_json[0].DXT || []),
                    ...(res.data_json[0].FXT || []),
                    ...(res.data_json[0].PDT || [])
                ];
                //初始化答案
                this.currentAnswer = this.currentQuestion?.answer;
            });
        },
        submitExamination() {
            submitExamination({
                id: this.currentQuestion.id,
                value:
                    this.currentQuestion.type === 'FXT'
                        ? this.currentAnswer.join(',')
                        : this.currentAnswer
            }).then((res) => {
                this.currentQuestion.answer =
                    this.currentQuestion.type === 'FXT'
                        ? this.currentAnswer.join(',')
                        : this.currentAnswer;
            });
        },
        closeExam() {
            //结束考试
            finishExamination({
                SJXH: this.$route.params.id
            }).then((res) => {
                this.examResult = res.data_json;
                this.showExamResult = true;
            });
        },
        closePage() {
            window.close();
        },
        answerChanged() {
            this.submitExamination();
        },
        prev() {
            if (this.currentIndex !== 0) {
                this.currentIndex -= 1;
            }
        },
        next() {
            if (this.currentIndex !== this.questions.length - 1) {
                this.currentIndex += 1;
            }
        },
        getTypeName(item) {
            switch (item.type) {
                case 'DXT':
                    return '单选题';
                case 'FXT':
                    return '多选题';
                case 'PDT':
                    return '判断题';
            }
        },
        returnImgUrl(list) {
            let arr = [];
            list.map((e) => {
                arr.push('/stfs_gd' + e.path);
            });
            return arr;
        }
    }
};
</script>

<style lang="scss" scoped>
.v-tips {
    font-size: 18px;
    margin: 15px;
    display: block;
}
::v-deep.el-radio,
::v-deep.el-checkbox {
    height: auto;
    white-space: unset;
    margin-bottom: 10px;
}

.exam_result {
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    h1 {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    div {
        font-size: 20px;
        font-weight: bold;
        line-height: 30px;
        width: 200px;
        display: flex;
        justify-content: space-between;
    }
    div:nth-of-type(3) {
        margin-bottom: 20px;
    }
}
</style>
<style>
.dialog_custom .el-dialog__body {
    height: calc(100% - 44px);
    width: 100%;
    padding: 0;
}
</style>
