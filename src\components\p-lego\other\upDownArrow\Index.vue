<!-- @format -->

<template>
    <div class="arrow-content">
        <img class="arrow-image" v-if="value != 0" :src="image" />
        <span v-if="value == 0" :style="{ fontSize: zeroFontSize }">{{
            zeroText
        }}</span>
    </div>
</template>

<script>
import downRedImage from './images/down-red.png';
import upRedImage from './images/up-red.png';
import downGreenImage from './images/down-green.png';
import upGreenImage from './images/up-green.png';

export default {
    props: {
        type: {
            // rate 数值大于0 绿色向上箭头 数值小于0红色向下箭头
            //pollutant 数值大于0 红色向上箭头 数值小于0绿色向下箭头
            default: 'rate',
            type: String
        },
        value: {
            default: '1',
            type: String
        },
        zeroText: {
            //当值为0时显示的内容
            default: '持平',
            type: String
        },
        zeroFontSize: {
            //当值为0时显示的字体大小
            default: '12px',
            type: String
        }
    },
    data() {
        return {
            upGreen: upGreenImage,
            downRed: downRedImage,
            upRed: upRedImage,
            downGreen: downGreenImage,
            image: upGreenImage
        };
    },
    watch: {
        type: 'changeImage',
        value: 'changeImage'
    },
    mounted() {
        this.changeImage();
    },
    methods: {
        changeImage() {
            if (this.type == 'rate') {
                if (this.value > 0) {
                    this.image = this.upGreen;
                } else {
                    this.image = this.downRed;
                }
            } else {
                if (this.value > 0) {
                    this.image = this.upRed;
                } else {
                    this.image = this.downGreen;
                }
            }
        }
    }
};
</script>

<style lang="less" scoped>
.arrow-content {
    display: inline-block;
}
</style>
