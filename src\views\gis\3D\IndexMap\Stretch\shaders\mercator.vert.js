/** @format */

export default `#ifdef GL_FRAGMENT_PRECISION_HIGH
	precision highp float;
#else
	precision mediump float;
#endif

const float PI = 3.14159265;

uniform mat4 u_matrix;

attribute vec2 position;

float mercatorXfromLng(float lng ) {
    return (180.0 + lng) / 360.0;
	// return 0.5 + lng/360.0;
}

float mercatorYfromLat(float lat) { 
  return (180.0 - (180.0 / PI * log(tan(PI / 4.0 + lat * PI / 360.0)))) / 360.0;
// return  (180.0 - (180.0 / PI * log(tan(PI / 4.0 + (90.0-lat) * PI / 360.0)))) / 360.0;
// return  (180.0 / PI * log(tan(PI / 4.0 + (90.0-lat) * PI / 360.0))) / 360.0-0.035;// 形状方向对了，但是纬度被拉伸了几乎一倍
	// return 0.5 - lat / 360.0;
}

/** EPSG:4326转EPSG:3857角度版
const radians = Math.PI/180;
const x = R*lng*radians;
const y = R*Math.log(Math.tan((Math.PI*0.25) + (0.5* lat * radians)))

*/

void main(){

	vec2 pos = position; 

	float x = mercatorXfromLng(pos.x);
	float y = mercatorYfromLat(pos.y);

	gl_Position = u_matrix * vec4( x, y , 0.0 ,1.0 );

}`;
