<!-- @format -->

<template>
    <div :style="{ width: option.width, height: option.height }">
        <p-chart
            style="width: 100%; height: 100%"
            :option="chartOption"
        ></p-chart>
    </div>
</template>
<script>
export default {
    props: {
        data: {
            type: Array,
            default() {
                return [];
            }
        },
        option: {
            type: Object,
            default() {
                return {
                    width: '800px',
                    height: '600px'
                };
            }
        }
    },
    data() {
        return {
            chartOption: {}
        };
    },
    mounted() {
        this.setChart();
    },
    watch: {
        data() {
            this.setChart();
        }
    },
    methods: {
        setChart() {
            if (!this.data) {
                this.chartOption = {};
                return;
            }
            let legend = this.data.map((res) => {
                return res.ZJMC;
            });
            const that = this;
            this.chartOption = {
                polar: {
                    radius: ['55%', '50%'],
                    center: ['25%', '50%']
                },
                legend: {
                    orient: 'vertical',
                    left: '55%',
                    top: 'middle',
                    data: legend,
                    formatter: function (name) {
                        let item = that.data.find((item) => {
                            return item.ZJMC == name;
                        });
                        return [
                            '{a|' + name + '}',
                            '{b|' + item.BFB + '}',
                            '{c|' + item.SL + '个}'
                        ].join('');
                    },
                    textStyle: {
                        rich: {
                            a: {
                                fontSize: 16,
                                align: 'left',
                                width: 80
                            },
                            b: {
                                fontSize: 16,
                                align: 'center',
                                width: 70
                            },
                            c: {
                                fontSize: 16,
                                align: 'left',
                                padding: [0, 0, 0, 30]
                            }
                        }
                    }
                },
                title: {
                    show: false
                },
                angleAxis: {
                    max: 1,
                    show: false,
                    startAngle: 0
                },
                radiusAxis: {
                    type: 'category',
                    show: true,
                    axisLabel: { show: false },
                    axisLine: { show: false },
                    axisTick: { show: false }
                },
                series: [
                    // 外层刻度
                    {
                        hoverAnimation: false,
                        type: 'pie',
                        z: 2,
                        data: [],
                        radius: ['25%', '65%'],
                        center: ['25%', '50%'],
                        zlevel: -2,
                        label: {
                            normal: {
                                position: 'inside',
                                show: false
                            }
                        }
                    },
                    // 内层饼图
                    {
                        type: 'pie',
                        radius: ['0%', '0%'],
                        center: ['25%', '50%'],
                        data: [],
                        labelLine: {
                            length: 40
                        },
                        // 提示线设置
                        label: {
                            show: false
                        }
                    }
                ]
            };

            let colors = [
                '#FFA117',
                '#BA40FF',
                '#FD3833',
                '#2DB5FF',
                '#48D711',
                '#f76363'
            ];
            let chartData = this.data.map((item, i) => {
                let ration = item.BFB ? item.BFB.match(/(.*)%$/)[1] / 100 : '';
                return {
                    name: item.ZJMC,
                    data: item.SL,
                    ratio: ration,
                    color: colors[i]
                };
            });
            let total = 0;
            this.data.forEach((item) => {
                total += item.SL;
            });
            chartData.map((item) => {
                if (item.data > 0) {
                    let num = Math.round((item.data / total) * 100);
                    for (let i = 0; i < num; ++i) {
                        this.chartOption.series[0].data.push(
                            {
                                value: 2, // 有颜色部分的宽度
                                name: item.name,
                                ratio: item.ratio,
                                itemStyle: {
                                    normal: {
                                        color: item.color
                                    }
                                }
                            },
                            {
                                value: 1, // 无颜色部分的宽度
                                name: '',
                                itemStyle: {
                                    normal: {
                                        label: {
                                            show: false
                                        },
                                        labelLine: {
                                            show: false
                                        },
                                        color: 'rgba(0, 0, 0, 0)',
                                        borderColor: 'rgba(0, 0, 0, 0)',
                                        borderWidth: 0
                                    }
                                }
                            }
                        );
                    }
                }
                // 内层饼图数据
                this.chartOption.series[1].data.push({
                    hoverOffset: 1,
                    value: item.data,
                    itemStyle: { color: item.color }
                });
            });
        }
    }
};
</script>
<style scoped></style>
