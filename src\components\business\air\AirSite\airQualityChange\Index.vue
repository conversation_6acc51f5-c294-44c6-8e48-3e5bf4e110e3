<!-- @format -->

<template>
    <div>
        <div class="gap"></div>
        <ul class="quality-change">
            <li
                class="quality-change-li"
                v-for="item in pollutionArr"
                :key="item.value"
                :class="{ on: item.value == pollutionVal }"
                @click="pollutionChange(item.value, item.unit)"
            >
                {{ item.name }}
            </li>
        </ul>
        <p-bar
            :option="barOption"
            :data="barData"
            :config="{
                unit: pollutionUnit
            }"
            style="width: 100%; height: 240px"
        ></p-bar>
    </div>
</template>

<script>
import util from './util';
import echartsUtils from './echartsUtils';
export default {
    name: 'air-quality-change',
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        },
        format: {
            type: String,
            default: 'YYYY-MM-DD HH'
        }
    },
    data() {
        return {
            pollutionArr: [
                { name: 'AQI', value: 'AQI', unit: '' },
                { name: 'PM₂.₅', value: 'PM25', unit: 'μg/m³' },
                { name: 'PM₁₀', value: 'PM10', unit: 'μg/m³' },
                { name: 'SO₂', value: 'SO2', unit: 'μg/m³' },
                { name: 'NO₂', value: 'NO2', unit: 'μg/m³' },
                { name: 'O₃', value: 'O3', unit: 'μg/m³' },
                { name: 'CO', value: 'CO', unit: 'mg/m³' }
            ],
            pollutionVal: 'AQI',
            pollutionUnit: '',
            barData: {},
            barOption: {
                legend: {
                    show: false
                },
                grid: {
                    left: '8%',
                    right: '8%'
                },
                xAxis: {
                    axisLabel: {
                        formatter: (val) => {
                            if (this.format) {
                                return this.$dayjs(val).format(this.format);
                            } else {
                                return val;
                            }
                        }
                    }
                },
                tooltip: {
                    formatter: (val) => {
                        let str = '';
                        val.map((item) => {
                            str += `<div style="display:flex;align-items: center;justify-content: space-between;">${item.marker}<div style="text-align:right"><b>${item.value}</b> ${this.pollutionUnit}</div></div>`;
                        });
                        return `${val[0].name}</br>${str}`;
                    }
                }
            }
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        getLevelPollution(pollutionName, value) {
            let color = '';
            let color1 = '';
            let txt = '';
            let num0, num1, num2, num3, num4, num5, num6;
            let result = util.getlevelValueByPollution(pollutionName);
            num0 = result[0];
            num1 = result[1];
            num2 = result[2];
            num3 = result[3];
            num4 = result[4];
            num5 = result[5];
            num6 = result[6];

            if (value > num0 && value <= num1) {
                color = '#31e1ad';
                color1 = '#18b553';
            } else if (value >= num1 && value <= num2) {
                color = '#31e1ad';
                color1 = '#18b553';
            } else if (value > num2 && value <= num3) {
                color = '#a7d91f';
                color1 = '#d8bc37';
            } else if (value > num3 && value <= num4) {
                color = '#fadf2b';
                color1 = '#f87c12';
            } else if (value > num4 && value <= num5) {
                color = '#e86e15';
                color1 = '#f60000';
            } else if (value > num5 && value <= num6) {
                color = '#d717c3';
                color1 = '#94004b';
            } else if (value > num6) {
                color = '#f9356c';
                color1 = '#6f001f';
            } else {
                color = '#666666';
                color1 = '#666666';
            }
            return { color: color, color1: color1 };
        },
        init() {
            if (this.data.length === 0) {
                this.barData = {};
            }
            let newChart = [];
            let data = echartsUtils.createLineBarOptionDefault([this.data], {
                name: '',
                x: 'JCSJ',
                data: this.pollutionVal.toUpperCase()
            });
            data.series[0].data.forEach((item) => {
                let ccObj = this.getLevelPollution(this.pollutionVal, item);
                let obj = {
                    value: item,
                    itemStyle: {
                        normal: {
                            color: new this.$echarts.graphic.LinearGradient(
                                0,
                                0,
                                0,
                                1,
                                [
                                    { offset: 0, color: ccObj.color },
                                    { offset: 1, color: ccObj.color1 }
                                ]
                            )
                        }
                    }
                };
                newChart.push(obj);
            });
            this.barData = {
                xAxis: data.xAxis,
                series: [{ data: newChart }]
            };
        },
        pollutionChange(type, unit) {
            if (this.pollutionVal != type) {
                this.pollutionVal = type;
                this.pollutionUnit = unit;
                this.init();
            }
        }
    }
};
</script>
<style lang="less">
.darkTheme {
    --quality-change-li-color: #fff;
    --quality-change-li-broder: 1px solid #2083f3;
}
.lightTheme {
    --quality-change-li-color: #333;
    --quality-change-li-broder: 1px solid #bcbcbc;
}
</style>
<style lang="less" scoped>
.quality-change {
    text-align: center;
    font-family: 'DIN-Medium', sans-serif;
    & .quality-change-li {
        display: inline-block;
        width: 58px;
        height: 23px;
        line-height: 23px;
        text-align: center;
        font-size: 14px;
        color: var(--quality-change-li-color);
        border: var(--quality-change-li-broder);
        cursor: pointer;
    }
    & .quality-change-li + .quality-change-li {
        cursor: pointer;
        margin-left: 10px;
    }
    & .quality-change-li.on {
        background: #0181dc;
        color: #fff;
        border-color: #0181dc;
    }
}
</style>
