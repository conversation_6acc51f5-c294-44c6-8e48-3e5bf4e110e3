<!-- @format -->

<template>
    <div class="water-quelity-trend">
        <ul class="water-quelity-trend-list">
            <li
                :class="{ cur: 'level' === cur }"
                @click="
                    cur = 'level';
                    unit = '';
                "
            >
                <div>{{ latestData.level || '-' }}</div>
                <div
                    :style="{
                        background: colorObj[latestData.level]
                    }"
                >
                    水质
                </div>
            </li>
            <li
                :class="{ cur: 'cwqi' === cur }"
                @click="
                    cur = 'cwqi';
                    unit = '';
                "
            >
                <div>{{ latestData.cwqi || '-' }}</div>
                <div
                    :style="{
                        background: colorObj[latestData.level]
                    }"
                >
                    CWQI
                </div>
            </li>

            <li
                v-for="(item, idx) in pltItems"
                :key="idx"
                :class="{ cur: item.code === cur }"
                @click="
                    cur = item.code;
                    unit = item.unit;
                "
            >
                <div>
                    {{ latestData[item.code] || '-' }}{{ item.unit || '' }}
                </div>
                <div
                    :style="{
                        background: colorObj[latestData[item.code + '_level']]
                    }"
                >
                    {{ item.name }}
                </div>
            </li>
        </ul>

        <div class="water-quelity-trend-chart">
            <p-line
                style="width: 100%; height: 100%"
                :data="chartData"
                :option="chartOpt"
            ></p-line>
            <ul :style="labelStyle" v-show="'level' === cur">
                <li
                    v-for="item in levelArr"
                    :key="item"
                    :style="{
                        background: colorObj[item]
                    }"
                ></li>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        data: {},
        pltItems: {}
    },
    data() {
        return {
            cur: 'level',
            unit: '',
            //水质对应颜色
            colorObj: {
                Ⅰ类: '#44c5fd',
                Ⅱ类: '#51a5fd',
                Ⅲ类: '#73bb31',
                Ⅳ类: '#eebd15',
                Ⅴ类: '#f88e17',
                劣Ⅴ类: '#ee3b5b'
            },
            //水质级别
            levelArr: ['Ⅰ类', 'Ⅱ类', 'Ⅲ类', 'Ⅳ类', 'Ⅴ类', '劣Ⅴ类']
        };
    },
    computed: {
        //图表边距 用于对齐水质类别颜色对照区
        chartGrid() {
            return {
                top: this.unit ? 30 : 10,
                left: 60,
                bottom: 30,
                right: 20
            };
        },
        //最新时间数据
        latestData() {
            if (!this.data || !this.data.length) {
                return {};
            }
            return this.data[this.data.length - 1];
        },
        chartData() {
            let data = this.data || [];
            let cur = this.cur;
            let x = [];
            let s = [];
            data.forEach((v) => {
                x.push(v.date);
                s.push(v[cur]);
            });
            return {
                xAxis: x,
                series: [
                    {
                        data: s
                    }
                ]
            };
        },
        chartOpt() {
            let cur = this.cur;
            // let colorObj = this.colorObj;
            return {
                tooltip: {
                    /* formatter: function (arr) {
                        let data = arr[0];
                        let color = colorObj[data.data];
                        return `${data.axisValue}<br><span style="color: ${color}">${data.data}</span>`;
                    } */
                },
                grid: {
                    ...this.chartGrid,
                    containLabel: false
                },
                yAxis: {
                    type: cur === 'level' ? 'category' : 'value',
                    data: cur === 'level' ? this.levelArr : null,
                    name: this.unit || '',
                    splitLine: {
                        // show: false
                    }
                },
                xAxis: {
                    boundaryGap: true,
                    axisLabel: {
                        formatter: function (v) {
                            return v.split(' ')[1];
                        }
                    }
                },
                series: [
                    {
                        smooth: false,
                        symbolSize: 8
                    }
                ]
            };
        },
        //水质类别颜色对照区位置样式
        labelStyle() {
            let grid = this.chartGrid;
            return {
                left: grid.left + 'px',
                top: grid.top + 'px',
                bottom: grid.bottom + 'px'
            };
        }
    }
};
</script>

<style lang="less" scoped>
.water-quelity-trend {
    width: 100%;
    height: 300px;
    padding-top: 10px;

    &-list {
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
        & > li {
            cursor: pointer;
            border: 1px solid var(--el-border-color);
            white-space: nowrap;
            &.cur {
                border: 1px solid var(--el-color-primary);
            }
            & > div {
                padding: 0 10px;
                line-height: 28px;
                text-align: center;

                &:first-child {
                    border-bottom: 1px solid var(--el-border-color);
                }

                &:last-child {
                    background: #999;
                    color: #000;
                }

                &.water-quelity-trend-cwqi {
                    background: var(--el-color-primary);
                }
            }
        }
    }
    &-chart {
        height: calc(100% - 90px);
        margin-top: 10px;
        position: relative;
        & > ul {
            position: absolute;
            display: flex;
            flex-direction: column-reverse;
            & > li {
                width: 10px;
                flex: 1;
            }
        }
    }
}
</style>
