<!-- @format -->

<template>
    <!-- 企业信息 -->
    <div class="hour-box-wrapper">
        <p class="zy-til1 ic4" style="padding: 0 32px">近24小时振动数据</p>
        <div class="gap"></div>

        <div class="gap"></div>
        <div class="zy-line jb ac" style="padding: 0 14px">
            <p class="zy-time">
                <!-- {{datetimerange[0]}}~{{datetimerange[1]}} -->
                <uni-datetime-picker
                    @change="change"
                    disabled
                    v-model="datetimerange"
                    type="datetimerange"
                    rangeSeparator="至"
                    :hide-second="true"
                    :clear-icon="false"
                />
            </p>
            <!-- <i class="ic-more"></i> -->
        </div>
        <div class="gap"></div>
        <ul class="zy-data4" style="padding: 0 14px">
            <li class="li1">启动</li>
            <li class="li2">停止</li>
            <li class="li3">离线</li>
        </ul>
        <div class="gap"></div>
        <!-- :style="{'height': hourBoxHeight}" -->
        <div
            class="time-data-wrap"
            style="
                position: relative;
                overflow-y: auto;
                overflow-x: hidden;
                background-color: white;
            "
            :style="{ height: hourBoxHeight }"
            id="table"
        >
            <div class="mark">2</div>
            <div class="left">
                <div class="th" style="display: flex; margin: 0 2px; top: 0">
                    <div style="" class="week"></div>
                </div>
                <div class="tb" style="display: flex; flex-direction: column">
                    <div
                        v-for="(row, index) in hoursData"
                        :key="index"
                        class="time-24"
                        style="text-align: center"
                    >
                        {{ row.key }}
                    </div>
                </div>
            </div>
            <div class="right" style="position: relative">
                <div
                    class="th"
                    ref="th"
                    id="th"
                    style="display: flex; margin: 0 2px; top: 0"
                >
                    <div
                        v-for="(item, index) in header"
                        :key="index"
                        class="th-item"
                        @click="toEquipmentInfo(item)"
                        style="
                            width: 200px;
                            background-color: #e4eaff;
                            color: #4874ff;
                            border-radius: 5px;
                            padding-left: 5px;
                        "
                    >
                        {{ item && item.SBMC.slice(0, 4) }}
                        <img
                            src="./images/chan.png"
                            mode=""
                            style="margin-left: 8px"
                            v-if="item.SBLX == '生产'"
                        />
                        <img
                            src="./images/zhi.png"
                            mode=""
                            style="margin-left: 8px"
                            v-if="item.SBLX == '治污'"
                        />
                        <img
                            src="./images/bei.png"
                            mode=""
                            style="margin-left: 8px"
                            v-if="item.ZBGX == '备'"
                        />
                        <img
                            src="./images/yong.png"
                            mode=""
                            style="margin-left: 8px"
                            v-if="item.ZBGX == '主'"
                        />
                    </div>
                </div>
                <div
                    class="tb"
                    id="tb"
                    @scroll="scrollEvent()"
                    ref="table"
                    style="display: flex; flex-direction: column"
                >
                    <div
                        v-for="(row, index) in hoursData"
                        :key="index"
                        style="display: flex"
                        class="tr"
                    >
                        <div
                            v-for="(item, index) in row.hours"
                            :key="index"
                            class="hours-data status td"
                            :class="
                                item && item.val == '0'
                                    ? 'c-gray'
                                    : item && item.val == '1'
                                    ? 'c-red'
                                    : 'c-green'
                            "
                        ></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        data: {}
    },
    watch: {
        data(newValue) {
            console.log(newValue);
            this.scxList = newValue.scxList;
            if (this.scxList && this.scxList.length > 0) {
                this.currentscx = this.scxList[0].SCXID;
                this.currentscxname = this.scxList[0].SCXMC;

                this.initXssj();
            }
        }
    },
    data() {
        return {
            scxList: [],
            currentscx: '',

            currentscxname: '',
            datetimerange: [
                this.$dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm'),
                this.$dayjs().format('YYYY-MM-DD HH:mm')
            ],
            header: [],
            hoursData: [],
            hourBoxHeight: 'auto'
        };
    },

    mounted() {},
    methods: {
        // 小时数据
        initXssj() {
            let res = this.data.hoursData;
            const qtztMap = { 0: '离线', 1: '停止', 2: '启动', '': '' };
            let scsbList = res.scxssjList;
            let zwxssjList = res.zwxssjList;

            let allequipment = scsbList.concat(zwxssjList);

            let columns = [];
            let columns1 = {};
            let colIndex = 1;
            let data = [];

            allequipment.forEach((item) => {
                //
                if (!columns1[item.SBMC]) {
                    columns1[item.SBMC] = colIndex++;
                }
                let index = columns.findIndex((items) => {
                    return items.SBMC == item.SBMC;
                });
                if (index == -1) {
                    columns.push({
                        SBMC: item.SBMC,
                        SBLX: item.SBLX,
                        ZBGX: item.ZBGX,
                        SBID: item.SBID,
                        IMEI: item.IMEI
                    });
                }

                let hours = item.SJSJ && item.SJSJ.slice(11, 16);
                if (!data[hours]) {
                    data[hours] = {
                        key: item.SJSJ && item.SJSJ.slice(11, 16),
                        hours: []
                    };
                }
                let curColIndex = columns1[item.SBMC] - 1;

                data[hours]['hours'][curColIndex] = {
                    val: item.QTZT,
                    sbmc: item.SBMC,
                    label: qtztMap[item.QTZT]
                };
            });
            console.log(data);
            // 表头数据
            this.header = columns;
            this.hoursData = [];
            // 解决视图不更新
            for (let i in data) {
                this.hoursData.push(data[i]);
            }
        },
        // 生产线点击
        changescx(val) {
            this.currentscx = val.SCXID;
            this.currentscxname = val.SCXMC;
            this.initXssj();
        },
        // 时间控件change事件
        change(v) {
            this.datetimerange = v;
            this.initXssj();
        },
        handleRect() {
            const self = this;
            this.$nextTick(() => {
                setTimeout(function () {
                    let queryDom = uni.createSelectorQuery().in(self); // 使用api并绑定当前组件
                    let promiseTop = new Promise((resolve) => {
                        queryDom
                            .select('.hour-box-wrapper')
                            .boundingClientRect((res) => {
                                let top = res.top;
                                // console.log(res)

                                resolve(top);
                            })
                            .exec();
                    });

                    let promiseBottom = new Promise((resolve) => {
                        queryDom
                            .select('.time-data-wrap')
                            .boundingClientRect((res) => {
                                let bottom = res.top;
                                // console.log(res)

                                resolve(bottom);
                            })
                            .exec();
                    });

                    Promise.all([promiseTop, promiseBottom]).then((res) => {
                        console.log(self.screenHeight, res);

                        // let hourBoxHeight = (self.screenHeight - (res[1] - res[0]) - self.headerHeight);

                        // if(!plus.navigator.hasNotchInScreen()){
                        // 	hourBoxHeight = hourBoxHeight - plus.navigator.getStatusbarHeight();
                        // }

                        let hourBoxHeight =
                            self.scrollHeight - (res[1] - res[0]);
                        self.hourBoxHeight = hourBoxHeight + 'px';
                    });
                }, 500);
            });
        },
        scrollEvent() {
            // 头部随滚动条滚动
            this.$refs.th.style.transform = `translate(-${this.$refs.table.scrollLeft}px)`;
        }
    }
};
</script>
<style scoped lang="scss">
.zy-til1 {
    font-size: 13px;
    color: #333;
    line-height: 31px;
    padding-left: 20px;
}

.zy-til1.ic4 {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAACi0lEQVRYhe2Yy2sUQRCHv7gbE6IgIiQRXxBQUNHLJpCo4OMi3sSD4EUC6iIR9WIQgx7ywFdUNBeff4Ae1IviQXwgCB68ieAbkSiKiohEMK5BSmqldp1JepmZHg/7u0z3bnXXR9V091TX5PN5UtIQsA04BhwIQ5iUFh2wGagH9gNHw4zSBDxk2t3AYJBRJpfL+UMq1X3trdLnMmAqcNNaZQMGtgHrgcXAFA+g34EGbe8BCsC+IMBZwHlgnQeo8bQXOAl8sIAtwB1gbspwosfA52InqwvlooH7qkv/BvAlYZhFwCWT4mfAWk3zX8CN+t6hYV0OvEwYTLQQuGDgngNrgHfWSKLXafq7PMGJLgNN2n6hcG/LjQSwVduymq56ghNl9CkBWQ0Mm/92AyPAKQGcoT9+An5GdNoILHC0lXdtO9BeBifq0tR3xXmSzAFeAU+BTQ72r4GzGphy1Ws/GyfgUrOxr4hr0qTO4pq4JkrzY8FJVcCoqgJGlW/AycBOYIPrgKAP1iS1Azih83cADyby5TuC8027xWVAJYBSOxyp4KyNRa4prgWu6wEuh/tKX4CuEWwwH5bTE+T5R9VtJqqqgFHlCjhm2r8i2NiSohBiUyJXwG9a2IuuhNg81JJxFLgWYiNF2Q/gPXDPxXElR52UhdO0sA/SR2AeUKcVWZDuaqk54hrBrKYjY8rA8RQGV1TBwfFEc2AyOyqNN9qZCTQ7DE5aTcoiGhbAW4a6/z8A7DPZvC1Qx01atgLnUoqk+DwDFC/N5dUbknfwCdBj7onlYnuLWZE+VKv3k3ZXkbvrR8VVPKhGveZKbra/4JVIsikch6VjiQ/qte9pvcII22yTkPgSn5LiJcDAHyfAb9ezbJvgBr+0AAAAAElFTkSuQmCC)
        0 center no-repeat;
    background-size: 11px;
    background-position: 14px center;
}

.zy-tabs1 {
    flex-wrap: wrap;
}

.zy-tabs1 li {
    position: relative;
    width: 84px;
    height: 28px;
    padding: 0 5px;
    background-color: #eeeeee;
    border-radius: 28px;
    font-size: 12px;
    color: #666;
    text-align: center;
    line-height: 1;
    margin-right: 11px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 4px;
}

.zy-tabs1 li.cur {
    background-color: #4874ff;
    color: #fff;
}

.zy-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.zy-data4 {
    display: flex;
    width: 144px;
    border-radius: 2px;
    overflow: hidden;
}

.zy-data4 li {
    flex: 1;
    height: 22px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    line-height: 22px;
}

.zy-data4 .li1 {
    background-color: #7dcd27;
}

.zy-data4 .li2 {
    background-color: #ff7054;
}

.zy-data4 .li3 {
    background-color: #888888;
}

.time-data-wrap {
    display: flex;
    width: 100vw;
    position: relative;
    overflow: hidden auto;
    background-color: white;
    height: auto;
}

.time-data-wrap .left {
    width: 120px;
    position: relative;
    z-index: 1;
}

.time-data-wrap .right .tb {
    width: calc(100vw - 70px);
    overflow: auto;
}

.time-data-wrap .right .tb div.tr {
    float: left;
    height: 23px;
    box-sizing: border-box;
    margin: 4px 2px;
}

.hours-data {
    width: 96px;
    border-radius: 1px;
    flex-shrink: 0;
}

.c-red {
    background-color: #ff7048;
}
.c-gray {
    background-color: #888888;
}
.c-green {
    background-color: #7dcd27;
}
.on {
    color: #007aff;
}

/* #ifdef APP-PLUS */
// .time-data-wrap .left div {
// 	width: 120px;
// 	height: 48px;
// 	box-sizing: border-box;
// 	background-color: #fff;
// 	margin: 10px 0;
// 	border: 1px solid #0000FF;
// }
/* #endif */
/* #ifdef H5 */
// .time-data-wrap .left div {
// 	width: 120px;
// 	height: 48px;
// 	box-sizing: border-box;
// 	background-color: #fff;
// 	margin: 8px 0;
// }
/* #endif */

.time-data-wrap .left .tb div {
    float: left;
    height: 23px;
    box-sizing: border-box;
    margin: 4px 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.time-data-wrap .right .tb div.td {
    margin: 0 2px;
}

.th {
    position: sticky;
    top: 120px;
}
.th div {
    width: 200px;
    white-space: nowrap;
    margin: 10px 5px;
    overflow: hidden;
    height: 48px;
    display: flex;
    align-items: center;
}

.mark {
    background-color: #fff;
    color: #fff;
    width: 28px;
    height: 40vh;
    right: 0;
    z-index: 666;
    position: absolute;
}

.th-item {
    box-sizing: border-box;
}
.th-item img {
    height: 16px;
    width: 16px;
}
</style>
