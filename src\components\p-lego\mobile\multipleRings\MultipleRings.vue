<!-- @format -->
<template>
    <p-chart
        :option="opt"
        style="width: 100%; height: 100%"
    ></p-chart>
</template>


<script>
const _COLOR = [
    '#9799f3',
    '#0fd0b7',
    '#ffd351',
    '#6ebffb',
    '#f56b6d',
    '#40c057',
    '#6a89e2',
    '#ff8e43'
];
export default {
    name: 'MultipleRings',
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        }
    },
    data() {
        return {
            theme:'dark',
            opt: {}
        };
    },
    watch: {
        data: 'getChart',
        option: 'getChart'
    },
    mounted() {
        this.theme = window.localStorage.getItem('themeType') || 'dark';
        this.getChart();
    },
    methods: {
        getChart() {
            let textColor = this.theme === 'light'?'#999':'#fff'
            let centerList = [
                ['50%', '30%'],['80%', '30%'],['50%', '75%'],['80%', '75%']
            ]
            let _series = []
            this.data.forEach((item,index) => {
                _series.push(
                    {   
                        type: 'pie',
                        radius: ['30%', '40%'],
                        center: centerList[index],
                        data: item.list,
                        labelLine: { show: false },
                        label: {
                            position: 'center',
                            formatter: `${item.name} \n ${item.total}`,
                            fontSize: 18,
                            lineHeight: 28,
                            color: textColor,
                        },
                    },
                )
            })
            this.opt = {
                color: _COLOR,
                legend: {
                    top: '50%',
                    itemWidth: 12,
                    itemHeight: 12,
                    icon: 'rect',
                    left: '10%',
                    padding: 0,
                    textStyle: {
                        color: textColor,
                        fontSize: 16,
                        padding: [2, 0, 0, 0]
                    },
                    orient: 'vertical'
                },
                tooltip: {
                    backgroundColor: 'rgba(2, 115, 194, 0.8)',
                    borderColor: '#0bb2ff',
                    textStyle: { color: '#fff' }
                },
                series:_series
            };
        }
    }
};
</script>


<style scoped></style>


