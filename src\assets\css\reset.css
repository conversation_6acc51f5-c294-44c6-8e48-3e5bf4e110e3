@charset "utf-8";

/* -- Reset -- */
body,ul,ol,dl,dd,h1,h2,h3,h4,h5,h6,p,form,input,textarea,select,button{
  --font-size: 14px;
  margin:0;
  padding:0;
  font-family:'Microsoft YaHei',SimSun,Arial,Helvetica,sans-serif;
}
ul,ol{list-style-type:none;}
table {border-collapse: collapse; border-spacing: 0;}
img{border:0 none;}
em,i{font-style:normal;}
a:link{color:#4F14F7;text-decoration: none;}
a:visited{color:#551a8b;}
a:hover{color:#FF9900;text-decoration: underline;}
a:active{color: #cc0000;}

/* -- Common style -- */
.dn{display:none;}
.db{display:block;}
.fl{float:left;}
.fr{float:right;}
.rel{position: relative;}
.abs{position: absolute;}
.gap{height: 10px;width: 100%;}
.auto{margin: 0 auto;}
.clear{clear: both;}
.clearfix:after{content: "\200B";display: block;height: 0;clear: both;}
.clearfix{*zoom: 1;} 

/*栅格系统*/
.raster{padding: 0 10px; margin: 0 auto;}
.row{margin: 0 -5px;}
.row:before, .row:after{content: ''; display: table; clear: both;}
.col{min-height: 1px; float: left; position: relative; padding: 0 5px; box-sizing: border-box;}
.col-1{width: 8.333333%;}
.col-2{width: 16.666666%;}
.col-3{width: 25%;}
.col-4{width: 33.333333%;}
.col-5{width: 41.666666%;}
.col-6{width: 50%;}
.col-7{width: 58.333333%;}
.col-8{width: 66.666666%;}
.col-9{width: 75%;}
.col-10{width: 83.333333%;}
.col-11{width: 91.666666%;}
.col-12{width: 100%;}

/* 表单输入框 */
.input-block {
  display: inline-block;
  position: relative;
  vertical-align: middle;
}
.input-block > .input-label {
  display: inline-block;
  max-width: 100%;
}
.input-block > .input-txt {
  border: none;
  padding: 6px;
  outline: none;
}

/* 按钮 */
.btn {
  display: inline-block;
  border: none;
  outline: none;
  cursor: pointer;
}

/* 图标 */
.icon {
  display: inline-block;
  cursor: pointer;
  background-position-x: center;
  background-position-y: center;
  background-repeat: no-repeat;
  vertical-align: middle;
}

/* 遮罩层 */
.mask {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 900;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .5);
}

/*滚动条样式*/

::-webkit-scrollbar-track-piece {
  background: transparent;
}

::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-thumb {
  background: #999;
}
