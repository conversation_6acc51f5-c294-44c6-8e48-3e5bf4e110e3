<!-- @format -->

<template>
    <div ref="analysis" class="chart"></div>
</template>
<script>
import * as echarts from 'echarts';

export default {
    props: {
        data: {
            type: Object,
            default: () => {
                return {
                    color: ['#04CD83', '#189CFC', '#FFBC20', '#F6382B'],
                    yAxisData: ['VOCS', 'SO2', 'NOX', 'COD', 'NH3'],
                    series: [
                        {
                            name: '剩余总量',
                            data: [10, 20, 15, 50, 30]
                        },
                        {
                            name: '减排目标量',
                            data: [10, 20, 15, 50, 30]
                        },
                        {
                            name: '预支量',
                            data: [10, 20, 15, 50, 30]
                        },
                        {
                            name: '支出量',
                            data: [10, 20, 15, 50, 30]
                        }
                    ],
                    barBorderRadius: [0, 30, 30, 0] //圆角大小
                };
            }
        }
    },
    data() {
        return {};
    },
    mounted() {
        this.setCharts();
    },
    methods: {
        setCharts() {
            let myChart = echarts.init(this.$refs.analysis);
            let that = this;
            let series = [];

            this.data.series.forEach((e, index) => {
                if (index === this.data.series.length - 1) {
                    // 如果是最后一项，添加圆角
                    series.push({
                        name: e.name,
                        type: 'bar',
                        stack: 'total',
                        label: {
                            show: false
                        },
                        barWidth: '20',
                        data: e.data,
                        itemStyle: {
                            normal: {
                                barBorderRadius: [0, 30, 30, 0] //圆角大小
                            }
                        }
                    });
                } else {
                    series.push({
                        name: e.name,
                        type: 'bar',
                        stack: 'total',
                        label: {
                            show: false
                        },
                        barWidth: '20',
                        data: e.data
                    });
                }
            });

            let option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        // Use axis to trigger tooltip
                        type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
                    }
                },
                legend: {
                    top: '20',
                    textStyle: {
                        color: '#fff'
                    }
                },
                color: that.data.color,
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLabel: {
                        margin: 10,
                        color: '#fff',
                        textStyle: {
                            fontSize: 14
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#397488'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#397488'
                        }
                    }
                },
                yAxis: {
                    type: 'category',
                    axisLabel: {
                        margin: 10,
                        color: '#fff',
                        textStyle: {
                            fontSize: 14
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#397488'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    data: that.data.yAxisData
                },
                series: series
            };
            myChart.setOption(option);
        }
    }
};
</script>
<style scoped>
.chart {
    width: 100%;
    height: 100%;
}
</style>
