/** @format */

/**
 * addImgPoint  图片点位
 * addCustomHtmlLayer(data, params) 添加html
 * addCirclePoint(data, params) 简单圆点符号
 * addTextPoint(data, params) 文本
 * addLine(features, params) 线
 * addPolygon(features, params) 面
 * addHeatData(data, params)  热力图
 * addExtrusionData(featureCollection, params) 拉伸面
 * addCylinderData(datas, params) 立体柱子
 * addGeojsonData(featureCollection, params) geojson数据
 * addAirRoundData(obj, fxlx, distance, fxjd, option) 周边分析
 * addTransportData(data, params) 获取echart 转移图
 * getlngLat(properties) 获取经纬度
 * getBuffer(geojson, params)  获取缓冲结果
 * areaFilter(polygon, data) 数据过滤
 * toGeojson(datas, type) 拼接geojson 数据
 * removeLayerByName(name) 移除图层
 * clear(name) 清理marker
 */

import BasePointUtil from '@/components/gis/3D/utils/BasePointUtil';
class PointUtil extends BasePointUtil {
    constructor(map, callback) {
        super(map, callback);
        this.map = map;
        let arr = [
            // {
            //     name: 'gyy-01',
            //     url: './gis/3D/images/iconLibrary/qhj/GYY.png'
            // }
        ];
        this.loadImages(arr);
    }

    // mapboxgl.Popup参数 可选择是否覆盖的方法
    getPopupOption(id, properties) {
        let option = {
            offset: [0, -20],
            className: 'mapbox-tooltip'
        };

        return option;
    }
    // mapboxgl.Popup.HTML 需要覆盖的方法
    getPopupStr(id, properties) {
        let html = '';
        switch (id) {
            case '水质自动站':
                break;
        }

        return html;
    }

    // marker.option 可选择是否覆盖方法
    getMarkerOption(id, properties) {
        return { className: 'coustom-marker', offset: [0, 20] };
    }

    // 生成marker.element
    getMarkerHtml(properties, params) {
        let el = document.createElement('div');
        el.className = 'air-marker';
        let template = ``;
        switch (params.id) {
            case '手工断面':
                template = ` `;
                break;
        }
        el.innerHTML = template;
        return el;
    }

    // 获取点位数据 layout属性
    getSymbolLayout(layerId) {
        let layout = {};

        switch (layerId) {
            case '手工断面':
                // layout = {
                //     'icon-image': [
                //         'coalesce',
                //         ['image', ['concat', 'DBS', ['get', 'SZLBBS']]],

                //         ['image', 'DBS']
                //     ],
                //     'icon-size': ['step', ['zoom'], 0.7, 10, 0.8, 13, 0.9]
                // };

                break;
        }

        layout['icon-allow-overlap'] = true;

        return layout;
    }

    // 获取点位数据 paint属性
    getSymbolPaint(id) {
        let paint = {};
        return paint;
    }
}
export default PointUtil;
