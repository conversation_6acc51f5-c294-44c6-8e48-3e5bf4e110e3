<!-- @format -->

<template>
    <div class="modal-bg">
        <div class="modal-container">
            <div class="modal-main">
                <AirQualityCard
                    :data="data"
                    :show="show"
                    :pollutant="pollutant"
                    v-if="data.data"
                ></AirQualityCard>
                <div v-if="!data.data" class="modal-main_lose">
                    抱歉，该天还未统计空气质量数据
                </div>
                <!-- <slot></slot> -->
            </div>
            <div class="modal-footer">
                <!-- <div @click="hideModal">取消</div> -->
            </div>
        </div>
    </div>
</template>

<script>
import AirQualityCard from './AirQualityCard.vue';
export default {
    name: 'CalendarDialog',
    components: {
        AirQualityCard
    },
    props: {
        show: {
            type: Boolean,
            default: false
        },
        pollutant: {
            type: String,
            default: 'AQI'
        },
        title: {
            type: String,
            default: '空气质量详情'
        },
        data: {
            type: Object,
            default: function () {
                return [];
            }
        }
    },
    data() {
        return {
            x: 0,
            y: 0,
            node: null,
            isCanMove: false
        };
    }
};
</script>

<style scoped>
.modal-container {
    width: 400px;
    border-radius: 4px;
    background: #fff;
    z-index: 100;
    overflow: hidden;
    box-shadow: 0px 0px 5px 2px #888;
}

.modal-header {
    height: 46px;
    background: #409eff;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: center;
}
.modal-footer button {
    width: 100px;
}
.modal-main {
    width: 100%;
}
.modal-header_title {
    padding-left: 20px;
}

.modal-header_close {
    padding-right: 20px;
    cursor: pointer;
}

.modal-main_lose {
    height: 60px;
    font-size: 16px;
    text-align: center;
}
</style>
