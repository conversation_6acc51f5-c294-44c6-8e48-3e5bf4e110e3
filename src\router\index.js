/** @format */

import { createRouter, createWebHashHistory } from 'vue-router';
import bigScreenRoutes from './app/app-routes.js';
const routes = [
    {
        path: '/',
        name: 'index',
        redirect: '/knowledge'
    },
    {
        path: '/exam/:id',
        name: 'exam',
        component: () => import('_v/exam/Exam.vue')
    },
    {
        path: '/exercise/:id',
        name: 'exercise',
        component: () => import('_v/exam/Exercise.vue')
    },
    {
        path: '/exerciseResult/:id',
        name: 'exerciseResult',
        component: () => import('_v/exam/ExerciseResult.vue')
    },
    {
        path: '/knowledge',
        name: 'knowledge',
        component: () => import('_v/knowledge/Knowledge.vue')
    },
    {
        path: '/staging',
        name: 'staging',
        component: () => import('_v/staging/Staging.vue')
    },
    {
        path: '/exchange',
        name: 'exchange',
        component: () => import('_v/exchange/Exchange.vue')
    },
    {
        path: '/annual',
        name: 'annual',
        component: () => import('_v/backPage/Annual.vue')
    },
    {
        path: '/dose',
        name: 'dose',
        component: () => import('_v/backPage/Dose.vue')
    },
    {
        path: '/doseSpread',
        name: 'doseSpread',
        component: () => import('_v/backPage/DoseSpread.vue')
    },
    {
        path: '/doseTask',
        name: 'doseTask',
        component: () => import('_v/backPage/DoseTask.vue')
    },
    {
        path: '/supervise',
        name: 'supervise',
        component: () => import('_v/backPage/Supervise.vue')
    },
    {
        path: '/bigModel',
        name: 'bigModel',
        component: () => import('_v/bigModel/Layout.vue')
    },
    {
        path: '/app',
        name: 'app',
        redirect: '/app/event',
        // route level code-splitting
        // this generates a separate chunk (about.[hash].js) for this route
        // which is lazy-loaded when the route is visited.
        component: () =>
            import(/* webpackChunkName: "list" */ '../views/app/Layout.vue'),
        children: bigScreenRoutes
    }
];

const router = createRouter({
    history: createWebHashHistory(),
    routes
});

export default router;
