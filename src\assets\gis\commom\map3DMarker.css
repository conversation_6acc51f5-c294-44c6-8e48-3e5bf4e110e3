/* 地图上标签组件的样式*/

/* ------------------------------------大气点位样式------------------------------------------------ */

.air-effect1 {
    text-align: center;
    margin: 0px;
    padding: 0;
}

.air-effect1 dt {
    opacity: 0.8;
    height: 33px;
    border-radius: 300px;
    border: 1px solid transparent;
    font-size: 18px;
    font-weight: bold;
    color: #081e21;
    text-align: center;
    padding: 0 10px;
    line-height: 33px;
    position: relative;
}

.air-effect1 dt:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 100%;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 10px 0 10px;
    border-color: transparent transparent transparent transparent;
    margin-left: -10px;
}

.air-effect1 dd {
    position: relative;
    height: 68px;
    background-repeat: no-repeat;
    background-position: center bottom;
}

.air-effect1 dd:before {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -2px;
    width: 23px;
    height: 17px;
    margin-left: -11.5px;
    border-radius: 50%;
    animation: ease-in-out ballBig1a-air 2s .1s infinite;
    z-index: 1;
}

.air-effect1 dd:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -8px;
    width: 39px;
    height: 29px;
    margin-left: -19.5px;
    border-radius: 50%;
    animation: ease-in-out ballBig1a-air 2s infinite;
}

/* 无数据 */
.air-effect1.dj0 dt {
    background: #CCCCCC;
    border-color: #e2dede;
}

.air-effect1.dj0 dt:after {
    content: '';
    border-top-color: #CCCCCC;
}

.air-effect1.dj0 dd {
    background-image: url(./images/dj0.png);
}

.air-effect1.dj0 dd:before {
    content: '';
    background: rgba(204, 204, 204, 0.6);
}

.air-effect1.dj0 dd:after {
    content: '';
    background: rgba(204, 204, 204, .16);
    border: 1px solid #CCCCCC;
}

/* 优 */
.air-effect1.dj1 dt {
    background: #2DB62D;
    border-color: #65e765;
}

.air-effect1.dj1 dt:after {
    content: '';
    border-top-color: #2DB62D;
}

.air-effect1.dj1 dd {
    background-image: url(./images/dj1.png);
}

.air-effect1.dj1 dd:before {
    content: '';
    background: rgba(45, 182, 45, .4);
}

.air-effect1.dj1 dd:after {
    content: '';
    background: rgba(45, 182, 45, .16);
    border: 1px solid #2DB62D;
}

/* 良 */
.air-effect1.dj2 dt {
    background: #CBCC33;
    border-color: #eeee68;
}

.air-effect1.dj2 dt:after {
    content: '';
    border-top-color: #CBCC33;
}

.air-effect1.dj2 dd {
    background-image: url(./images/dj2.png);
}

.air-effect1.dj2 dd:before {
    content: '';
    background: rgba(203, 204, 51, .4);
}

.air-effect1.dj2 dd:after {
    content: '';
    background: rgba(203, 204, 51, .16);
    border: 1px solid #CBCC33;
}

/* 轻度 */
.air-effect1.dj3 dt {
    background: #FF7E00;
    border-color: #f1a355;
}

.air-effect1.dj3 dt:after {
    content: '';
    border-top-color: #FF7E00;
}

.air-effect1.dj3 dd {
    background-image: url(./images/dj3.png);
}

.air-effect1.dj3 dd:before {
    content: '';
    background: rgba(255, 126, 0, .4);
}

.air-effect1.dj3 dd:after {
    content: '';
    background: rgba(255, 126, 0, .16);
    border: 1px solid #FF7E00;
}

/* 中度 */
.air-effect1.dj4 dt {
    background: #FF0000;
    border-color: #e95959;
}

.air-effect1.dj4 dt:after {
    content: '';
    border-top-color: #FF0000;
}

.air-effect1.dj4 dd {
    background-image: url(./images/dj4.png);
}

.air-effect1.dj4 dd:before {
    content: '';
    background: rgba(255, 0, 0, .4);
}

.air-effect1.dj4 dd:after {
    content: '';
    background: rgba(255, 0, 0, .16);
    border: 1px solid #FF0000;
}


/* 重度 */
.air-effect1.dj5 dt {
    background: #99004C;
    border-color: #ec3591;
}

.air-effect1.dj5 dt:after {
    content: '';
    border-top-color: #99004C;
}

.air-effect1.dj5 dd {
    background-image: url(./images/dj5.png);
}

.air-effect1.dj5 dd:before {
    content: '';
    background: rgba(153, 0, 76, .4);
}

.air-effect1.dj5 dd:after {
    content: '';
    background: rgba(153, 0, 76, .16);
    border: 1px solid #99004C;
}



/* 严重 */
.air-effect1.dj6 dt {
    background: #7E0023;
    border-color: #cf2354;
}

.air-effect1.dj6 dt:after {
    content: '';
    border-top-color: #7E0023;
}

.air-effect1.dj6 dd {
    background-image: url(./images/dj6.png);
}

.air-effect1.dj6 dd:before {
    content: '';
    background: rgba(126, 0, 35, .4);
}

.air-effect1.dj6 dd:after {
    content: '';
    background: rgba(126, 0, 35, .16);
    border: 1px solid #7E0023;
}


@keyframes ballBig1a-air {
    0% {
        transform: scale(1);
        opacity: 0
    }

    80% {
        transform: scale(2);
        opacity: 1
    }

    100% {
        transform: scale(2);
        opacity: 0
    }
}

/* 效果2：真气网，小圆点 */
.air-effect2 {
    width: 14px;
    height: 14px;
    box-shadow: 0 0 6px #666;
    border-radius: 50%;
    margin-left: -7px; 
    margin-top: -7px;
}  

.air-effect2.circlelevel0 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#c6c6c6 0,#999 100%,#afafaf 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#c6c6c6,#999,#afafaf)
}

.air-effect2.circlelevel1 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#8dc670 0,#390 100%,#60af38 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#8dc670,#390,#60af38)
}

.air-effect2.circlelevel2 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#f6eb8b 0,#efdc31 100%,#f3e45e 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#f6eb8b,#efdc31,#f3e45e)
}

.air-effect2.circlelevel3 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#f5b270 0,#ee7600 100%,#f29438 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#f5b270,#ee7600,#f29438)
}

.air-effect2.circlelevel4 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#f85c49 0,#ff401a 100%,#ff5c49 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#f85c49,#ff401a,#ff5c49)
}

.air-effect2.circlelevel5 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#e38d8d 0,#cd3333 100%,#d86060 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#e38d8d,#cd3333,#d86060)
}

.air-effect2.circlelevel6 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#cd8888 0,brown 100%,#b95959 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#cd8888,brown,#b95959)
}


/* 效果3：真气网，背景加文字 */


.air-effect3{
    margin-left: -17.5px;
    margin-top: -11px;
    width: 35px;
    height: 22px;
}

.air-effect3 .marker_marker_1AA {
    width: 36px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    border-radius: 4px;
    color:black;
    font-size:16px;
}

.air-effect3 .marker_marker_1AA .marker_arrow_18U {
    width: 0;
    height: 0;
    border: 6px solid transparent;
    position: absolute;
    margin-left: -6px;
    left: 10%;
    top: 12px;
 
}

.air-effect3 .marker_name{
    padding: auto 10px;
    height:22px;
    text-align:center;
    line-height:22px;
    border: 1px solid #ddd;
    border-radius: 2px;
    background: rgba(255,255,255,0.8);
    margin-top:6px;
    color: #666;
}


/* 效果4：图片加地图闪烁 */
.air-effect4 {
   
}

.air-effect4  .air-effect4-dipan {
    position: absolute;
    bottom: -15px;
    left: -2px;
}

.air-effect4-dipan ellipse {
    transform-origin: center;
    animation: aireffect4animation 1.5s linear infinite;
    transform: scale(1);
}

.air-effect4-dipan .e1 {
    transform-origin: center center;
    animation: aireffect4animation 2.5s linear infinite;
    transform: scale(0.7);
    opacity: 0.3;
}

.air-effect4-dipan .e2 {
    transform-origin: center center;
    animation: aireffect4animation 2.5s linear 0.8s infinite;
    transform: scale(0.5);
    opacity: 1;
}

@keyframes aireffect4animation {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}



.air-effect5 {
    /* position: absolute; */
}

.air-effect5 h1:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 100%;
    width: 19px;
    height: 20px;
    margin-left: -9.5px;
}

.air-effect5 h1 {
    height: 34px;
    display: flex;
    align-items: center;
    border: 1px solid transparent;
    padding-left: 1px;
    position: relative;
}

.air-effect5 h1 p {
    flex: 1;
    font-size: 18px;
    font-family: "ALM", sans-serif;
    text-align: center;
    line-height: 32px;
    background: #fff;
    min-width: 90px;
    padding: 0 10px;
    box-sizing: border-box;
}

.air-effect5 h1 i {
    font-size: 18px;
    color: #fff;
    font-family: "ALM", sans-serif;
    width: 57px;
    text-align: center;
    line-height: 32px;
    white-space: nowrap;
}

.air-effect5 .air-effect5-dipan {
    position: relative;
    top: 10px;
    display: flex;
    justify-content: center;
}

.air-effect5 .air-effect5-dipan ellipse {
    transform-origin: center;
    animation: air-effect5-dipan 1.5s linear infinite;
    transform: scale(1);
}

.air-effect5 .dipan .e1 {
    transform-origin: center center;
    animation: air-effect5-dipan 2.5s linear infinite;
    transform: scale(0.7);
    opacity: 0.3;
}

.air-effect5 .air-effect5-dipan .e2 {
    transform-origin: center center;
    animation: air-effect5-dipan 2.5s linear 0.8s infinite;
    transform: scale(0.5);
    opacity: 1;
}

.air-effect5 .air-effect5-dipan .e3 {
    transform-origin: center center;
    animation: air-effect5-dipan 2.5s linear 1.6s infinite;
    transform: scale(0.3);
    opacity: 0.3;
}

.air-effect5.type0 h1 {
    border-color: #999;
    background: rgba(153, 153, 153, .5);
}

.air-effect5.type0 h1 p {
    color: #999;
}

.air-effect5.type0 h1:after {
    content: '';
    background: url(./images/sw20_arwbt0.png);
}

.air-effect5.type1 h1 {
    border-color: #24bd5d;
    background: rgba(36, 189, 93, .5);
}

.air-effect5.type1 h1 p {
    color: #24bd5d;
}

.air-effect5.type1 h1:after {
    content: '';
    background: url(./images/sw20_arwbt1.png);
}

.air-effect5.type2 h1 {
    border-color: #d8bc37;
    background: rgba(216, 188, 55, .5);
}

.air-effect5.type2 h1 p {
    color: #d8bc37;
}

.air-effect5.type2 h1:after {
    content: '';
    background: url(./images/sw20_arwbt2.png);
}

.air-effect5.type3 h1 {
    border-color: #f87c12;
    background: rgba(248, 124, 18, .5);
}

.air-effect5.type3 h1 p {
    color: #f87c12;
}

.air-effect5.type3 h1:after {
    content: '';
    background: url(./images/sw20_arwbt3.png);
}

.air-effect5.type4 h1 {
    border-color: #f60000;
    background: rgba(246, 0, 0, .5);
}

.air-effect5.type4 h1 p {
    color: #f60000;
}

.air-effect5.type4 h1:after {
    content: '';
    background: url(./images/sw20_arwbt4.png);
}

.air-effect5.type5 h1 {
    border-color: #94004b;
    background: rgba(148, 0, 75, .5);
}

.air-effect5.type5 h1 p {
    color: #94004b;
}

.air-effect5.type5 h1:after {
    content: '';
    background: url(./images/sw20_arwbt5.png);
}

.air-effect5.type6 h1 {
    border-color: #6f001f;
    background: rgba(111, 0, 31, .5);
}

.air-effect5.type6 h1 p {
    color: #6f001f;
}

.air-effect5.type6 h1:after {
    content: '';
    background: url(./images/sw20_arwbt6.png);
}


@keyframes air-effect5-dipan {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }

    50% {
        opacity: 0.8;
    }

    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* 样式6 */

.air-effect6 {
    position: static;
}
.air-effect6 > h1 {
    white-space: nowrap;
    width: auto;
    background-size: 100% 50px;
    padding: 1px 10px 0 10px;

    height: 50px;
    font-size: 16px;
    line-height: 30px;
}

.air-effect6 > h1 i {
    padding: 0 5px;
    background-color: #fff;
    font-size: 14px;
}

.air-effect6.kqzl-0 > h1 {
    background: url('./images/kqzdz-0.png') no-repeat center/100% 50px;
}
.air-effect6.kqzl-1 > h1 {
    background: url('./images/kqzdz-1.png') no-repeat center/100% 50px;
}
.air-effect6.kqzl-2 > h1 {
    background: url('./images/kqzdz-2.png') no-repeat center/100% 50px;
}
.air-effect6.kqzl-3 > h1 {
    background: url('./images/kqzdz-3.png') no-repeat center/100% 50px;
}
.air-effect6.kqzl-4 > h1 {
    background: url('./images/kqzdz-4.png') no-repeat center/100% 50px;
}
.air-effect6.kqzl-5 > h1 {
    background: url('./images/kqzdz-5.png') no-repeat center/100% 50px;
}
.air-effect6.kqzl-6 > h1 {
    background: url('./images/kqzdz-6.png') no-repeat center/100% 50px;
}



.air-effect6 .air-effect6-dipan {
    position: relative;
    top: 0px;
    display: flex;
    justify-content: center;
}

.air-effect6 .air-effect6-dipan ellipse {
    transform-origin: center;
    animation: air-effect6-dipan 1.5s linear infinite;
    transform: scale(1);
}

.air-effect6 .air-effect6-dipan .e1 {
    transform-origin: center center;
    animation: air-effect6-dipan 2.5s linear infinite;
    transform: scale(0.7);
    opacity: 0.3;
}

.air-effect6 .air-effect6-dipan .e2 {
    transform-origin: center center;
    animation: air-effect6-dipan 2.5s linear 0.8s infinite;
    transform: scale(0.5);
    opacity: 1;
}

.air-effect6 .air-effect6-dipan .e3 {
    transform-origin: center center;
    animation: air-effect6-dipan 2.5s linear 1.6s infinite;
    transform: scale(0.3);
    opacity: 0.3;
}


@keyframes air-effect6-dipan {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}


/* ------------------------------------大气点位样式 end------------------------------------------------ */



/* ------------------------------------ 水质点位  -------------------------------------------------- */

.water-effect1 {
    text-align: center;
    margin: 0px;
    padding: 0;
}

.water-effect1 dt {
    opacity: 0.8;
    height: 33px;
    border-radius: 300px;
    border: 1px solid transparent;
    font-size: 18px;
    font-weight: bold;
    color: #081e21;
    text-align: center;
    padding: 0 10px;
    line-height: 33px;
    position: relative;
}

.water-effect1 dt:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 100%;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 10px 0 10px;
    border-color: transparent transparent transparent transparent;
    margin-left: -10px;
}

.water-effect1 dd {
    position: relative;
    height: 68px;
    background-repeat: no-repeat;
    background-position: center bottom;
}

.water-effect1 dd:before {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -2px;
    width: 23px;
    height: 17px;
    margin-left: -11.5px;
    border-radius: 50%;
    animation: ease-in-out ballBig1a-air 2s .1s infinite;
    z-index: 1;
}

.water-effect1 dd:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -8px;
    width: 39px;
    height: 29px;
    margin-left: -19.5px;
    border-radius: 50%;
    animation: ease-in-out ballBig1a-air 2s infinite;
}

/* 无数据 */
.water-effect1.dj0 dt {
    background: #CCCCCC;
    border-color: #e2dede;
}

.water-effect1.dj0 dt:after {
    content: '';
    border-top-color: #CCCCCC;
}

.water-effect1.dj0 dd {
    background-image: url(./images/szlb0.png);
}

.water-effect1.dj0 dd:before {
    content: '';
    background: rgba(204, 204, 204, 0.6);
}

.water-effect1.dj0 dd:after {
    content: '';
    background: rgba(204, 204, 204, .16);
    border: 1px solid #CCCCCC;
}



.water-effect1.dj1 dt {
    background: #44C5FD;
    border-color: #19b1f2;
}

.water-effect1.dj1 dt:after {
    content: '';
    border-top-color: #44C5FD;
}

.water-effect1.dj1 dd {
    background-image: url(./images/szlb1.png);
}

.water-effect1.dj1 dd:before {
    content: '';
    background: rgba(68,197,253, .4);
}

.water-effect1.dj1 dd:after {
    content: '';
    background: rgba(68,197,253, .16);
    border: 1px solid #44C5FD;
}



/* 良 */
.water-effect1.dj2 dt {
    background: #51A5FD;
    border-color: #3a95f8;
}

.water-effect1.dj2 dt:after {
    content: '';
    border-top-color: #51A5FD;
}

.water-effect1.dj2 dd {
    background-image: url(./images/szlb2.png);
}

.water-effect1.dj2 dd:before {
    content: '';
    background: rgba(81,165,253, .4);
}

.water-effect1.dj2 dd:after {
    content: '';
    background: rgba(81,165,253, .16);
    border: 1px solid #51A5FD;
}


.water-effect1.dj3 dt {
    background: #6AB437;
    border-color: #5ab51e;
}

.water-effect1.dj3 dt:after {
    content: '';
    border-top-color: #6AB437;
}

.water-effect1.dj3 dd {
    background-image: url(./images/szlb3.png);
}

.water-effect1.dj3 dd:before {
    content: '';
    background: rgba(106,180,55, .4);
}

.water-effect1.dj3 dd:after {
    content: '';
    background: rgba(106,180,55, .16);
    border: 1px solid #6AB437;
}

.water-effect1.dj4 dt {
    background: #E8BE31;
    border-color: #e7b71a;
}

.water-effect1.dj4 dt:after {
    content: '';
    border-top-color: #E8BE31;
}

.water-effect1.dj4 dd {
    background-image: url(./images/szlb4.png);
}

.water-effect1.dj4 dd:before {
    content: '';
    background: rgba(232,190,49, .4);
}

.water-effect1.dj4 dd:after {
    content: '';
    background: rgba(232,190,49 .16);
    border: 1px solid #E8BE31;
}



/* 重度 */
.water-effect1.dj5 dt {
    background: #F88E17;
    border-color: #f4870a;
}

.water-effect1.dj5 dt:after {
    content: '';
    border-top-color: #F88E17;
}

.water-effect1.dj5 dd {
    background-image: url(./images/szlb5.png);
}

.water-effect1.dj5 dd:before {
    content: '';
    background: rgba(248,142,23, .4);
}

.water-effect1.dj5 dd:after {
    content: '';
    background: rgba(248,142,23, .16);
    border: 1px solid #F88E17;
}


/* 严重 */
.water-effect1.dj6 dt {
    background: #EE3B5B;
    border-color: #EE3B5B;
}

.water-effect1.dj6 dt:after {
    content: '';
    border-top-color: #EE3B5B;
}

.water-effect1.dj6 dd {
    background-image: url(./images/szlb6.png);
}

.water-effect1.dj6 dd:before {
    content: '';
    background: rgba(238,59,91, .4);
}

.water-effect1.dj6 dd:after {
    content: '';
    background: rgba(238,59,91, .16);
    border: 1px solid #EE3B5B;
}


@keyframes ballBig1a-water {
    0% {
        transform: scale(1);
        opacity: 0
    }

    80% {
        transform: scale(2);
        opacity: 1
    }

    100% {
        transform: scale(2);
        opacity: 0
    }
}


/* 效果2：真气网，小圆点 */
.water-effect2 {
    width: 14px;
    height: 14px;
    box-shadow: 0 0 6px #666;
    border-radius: 50%;
    margin-left: -7px; 
    margin-top: -7px;
}  

.water-effect2.circlelevel0 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#c6c6c6 0,#999 100%,#afafaf 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#c6c6c6,#999,#afafaf)
}

.water-effect2.circlelevel1 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#75d2fa 0,#44C5FD 100%,#75d2fa 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#75d2fa,#44C5FD,#75d2fa)
}

.water-effect2.circlelevel2 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#82bbf7 0,#51A5FD 100%,#82bbf7 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#82bbf7,#51A5FD,#82bbf7)
}

.water-effect2.circlelevel3 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#7fb45b 0,#6AB437 100%,#7fb45b 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#7fb45b,#6AB437,#7fb45b)
}

.water-effect2.circlelevel4 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#e3c35a 0,#E8BE31 100%,#e3c35a 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#e3c35a,#E8BE31,#e3c35a)
}

.water-effect2.circlelevel5 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#f4aa56 0,#F88E17 100%,#f4aa56 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#f4aa56,#F88E17,#f4aa56)
}

.water-effect2.circlelevel6 {
    background-image: radial-gradient(7px 7px 45deg,circle cover,#f16881 0,#EE3B5B 100%,#ee506d 0);
    background-image: -webkit-radial-gradient(7px 7px,circle cover,#f16881,#EE3B5B,#ee506d)
}


/* 效果3：真气网，背景加文字 */


.water-effect3{
    margin-left: -17.5px;
    margin-top: -11px;
    width: 35px;
    height: 22px;
}

.water-effect3 .marker_marker_1AA {
    width: 36px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    border-radius: 4px;
    color:black;
    font-size:16px;
}

.water-effect3 .marker_marker_1AA .marker_arrow_18U {
    width: 0;
    height: 0;
    border: 6px solid transparent;
    position: absolute;
    margin-left: -6px;
    left: 10%;
    top: 12px;
 
}

.water-effect3 .marker_name{
    padding: auto 10px;
    height:22px;
    text-align:center;
    line-height:22px;
    border: 1px solid #ddd;
    border-radius: 2px;
    background: rgba(255,255,255,0.8);
    margin-top:6px;
    color: #666;
}



/* 效果4：图片加地图闪烁 */
.water-effect4 {
   
}

.water-effect4  .water-effect4-dipan {
    position: absolute;
    bottom: -15px;
    left: -2px;
}

.water-effect4-dipan ellipse {
    transform-origin: center;
    animation: watereffect4animation 1.5s linear infinite;
    transform: scale(1);
}

.water-effect4-dipan .e1 {
    transform-origin: center center;
    animation: watereffect4animation 2.5s linear infinite;
    transform: scale(0.7);
    opacity: 0.3;
}

.water-effect4-dipan .e2 {
    transform-origin: center center;
    animation: watereffect4animation 2.5s linear 0.8s infinite;
    transform: scale(0.5);
    opacity: 1;
}

@keyframes watereffect4animation {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}



/* 水质点位样式5 -- 宜昌项目 */
.water-effect5  {
}

.water-effect5 h1:after {
    content: '';
    position: absolute;
    left: 50%;
    top: 100%;
    width: 19px;
    height: 20px;
    margin-left: -9.5px; 
}

.water-effect5 h1 {
    height: 34px;
    display: flex;
    align-items: center;
    border: 1px solid transparent;
    padding-left: 1px;
    position: relative;
}

.water-effect5 h1 p {
    flex: 1;
    font-size: 18px;
    font-family: "ALM", sans-serif;
    text-align: center;
    line-height: 32px;
    background: #fff;
    min-width: 90px;
    padding: 0 10px;
    box-sizing: border-box;
}

.water-effect5 h1 i {
    font-size: 18px;
    color: #fff;
    font-family: "ALM", sans-serif;
    width: 57px;
    text-align: center;
    line-height: 32px;
    white-space: nowrap;
}

.water-effect5 .water-effect5-dipan {
    position: relative;
    top: 10px;
    display: flex;
    justify-content: center;
}

.water-effect5 .water-effect5-dipan ellipse {
    transform-origin: center;
    animation: water-effect5-dipan  1.5s linear infinite;
    transform: scale(1);
}

.water-effect5 .water-effect5-dipan .e1 {
    transform-origin: center center;
    animation: water-effect5-dipan  2.5s linear infinite;
    transform: scale(0.7);
    opacity: 0.3;
}

.water-effect5 .water-effect5-dipan .e2 {
    transform-origin: center center;
    animation: water-effect5-dipan  2.5s linear 0.8s infinite;
    transform: scale(0.5);
    opacity: 1;
}

.water-effect5 .water-effect5-dipan .e3 {
    transform-origin: center center;
    animation: water-effect5-dipan  2.5s linear 1.6s infinite;
    transform: scale(0.3);
    opacity: 0.3;
}

.water-effect5.type1 h1 {
    border-color: #44c5fd;
    background: rgba(68, 197, 253, .5);
}

.water-effect5.type1 h1 p {
    color: #44c5fd;
}

.water-effect5.type1 h1:after {
    content: '';
    background: url(./images/sw20_arwbt7.png);
}

.water-effect5.type2 h1 {
    border-color: #51a5fd;
    background: rgba(81, 165, 253, .5);
}

.water-effect5.type2 h1 p {
    color: #51a5fd;
}

.water-effect5.type2 h1:after {
    content: '';
    background: url(./images/sw20_arwbt8.png);
}

.water-effect5.type3 h1 {
    border-color: #24bd5d;
    background: rgba(36, 189, 93, .5);
}

.water-effect5.type3 h1 p {
    color: #24bd5d;
}

.water-effect5.type3 h1:after {
    content: '';
    background: url(./images/sw20_arwbt1.png);
}

.water-effect5.type4 h1 {
    border-color: #eebd15;
    background: rgba(238, 189, 21, .5);
}

.water-effect5.type4 h1 p {
    color: #eebd15;
}

.water-effect5.type4 h1:after {
    content: '';
    background: url(./images/sw20_arwbt9.png);
}

.water-effect5.type5 h1 {
    border-color: #f88e17;
    background: rgba(248, 142, 23, .5);
}

.water-effect5.type5 h1 p {
    color: #f88e17;
}

.water-effect5.type5 h1:after {
    content: '';
    background: url(./images/sw20_arwbt10.png);
}

.water-effect5.type6 h1 {
    border-color: #ee3b5b;
    background: rgba(238, 59, 91, .5);
}

.water-effect5.type6 h1 p {
    color: #ee3b5b;
}

.water-effect5.type6 h1:after {
    content: '';
    background: url(./images/sw20_arwbt11.png);
}

@keyframes water-effect5-dipan {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }

    50% {
        opacity: 0.8;
    }

    100% {
        transform: scale(1);
        opacity: 0;
    }
}


/* 样式6 */

.water-effect6 {
    position: static;
}
.water-effect6 > h1 {
    white-space: nowrap;
    width: auto;
    background-size: 100% 50px;
    padding: 1px 10px 0 10px;

    height: 50px;
    font-size: 16px;
    line-height: 30px;
}

.water-effect6 > h1 i {
    padding: 0 5px;
    background-color: #fff;
    font-size: 14px;
}

.water-effect6.szlb-0 > h1 {
    background: url('./images/szlb-0.png') no-repeat center/100% 50px;
}
.water-effect6.szlb-1 > h1 {
    background: url('./images/szlb-1.png') no-repeat center/100% 50px;
}
.water-effect6.szlb-2 > h1 {
    background: url('./images/szlb-2.png') no-repeat center/100% 50px;
}
.water-effect6.szlb-3 > h1 {
    background: url('./images/szlb-3.png') no-repeat center/100% 50px;
}
.water-effect6.szlb-4 > h1 {
    background: url('./images/szlb-4.png') no-repeat center/100% 50px;
}
.water-effect6.szlb-5 > h1 {
    background: url('./images/szlb-5.png') no-repeat center/100% 50px;
}
.water-effect6.szlb-6 > h1 {
    background: url('./images/szlb-6.png') no-repeat center/100% 50px;
}



.water-effect6 .water-effect6-dipan {
    position: relative;
    top: 0px;
    display: flex;
    justify-content: center;
}

.water-effect6 .water-effect6-dipan ellipse {
    transform-origin: center;
    animation: water-effect6-dipan 1.5s linear infinite;
    transform: scale(1);
}

.water-effect6 .water-effect6-dipan .e1 {
    transform-origin: center center;
    animation: water-effect6-dipan 2.5s linear infinite;
    transform: scale(0.7);
    opacity: 0.3;
}

.water-effect6 .water-effect6-dipan .e2 {
    transform-origin: center center;
    animation: water-effect6-dipan 2.5s linear 0.8s infinite;
    transform: scale(0.5);
    opacity: 1;
}

.water-effect6 .water-effect6-dipan .e3 {
    transform-origin: center center;
    animation: water-effect6-dipan 2.5s linear 1.6s infinite;
    transform: scale(0.3);
    opacity: 0.3;
}


@keyframes water-effect6-dipan {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}


/* ------------------------------------水质点位 end -------------------------------------------------- */
