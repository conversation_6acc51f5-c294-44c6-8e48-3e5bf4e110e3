/** @format */

let mapboxKey = [
    'pk.eyJ1Ijoia2V5aWRkcmQiLCJhIjoiY2xuaTc1cDNtMGpjcjJycWlhODgxNGd0bCJ9.fDHGo5442q8tBUnTEdt6gg', //一部
    'pk.eyJ1IjoiaHN6bWFwIiwiYSI6ImNsbmk5OWN6cTAwYnUya2xzcGtzMTF1YjIifQ.XDuCt7RW_LgsPWkUlSRnnw', //二部
    'pk.eyJ1Ijoia2V5aWRyIiwiYSI6ImNrb3Y5eDhwNDA2ZW0yd2s1bDcwbzlrbnUifQ.3t_ALTZvKnRBnkF5QopZEA', //执法
    'pk.eyJ1IjoiZGVmZnNmZiIsImEiOiJjbG5pOGg0dWUwanJjMnJxaTkxdnZrZW9rIn0.EaZwQDAp_HbnDjv_BF7hiw', //环境质量
    'pk.eyJ1Ijoia2V5aWRkcnJyIiwiYSI6ImNsbmk4dXkwaDBqdmYycnFpemk1NHByOXYifQ.0gjdqehXadxzxvYJmrNkxg', //北京分公司
    'pk.eyJ1IjoiaHpxMTIxMiIsImEiOiJjbG4wNGhya2sxNnUyMmxsYzZuMG1zazF1In0.V4yHFtlKITpG-pEu7KU_jw' //待定
];

// eslint-disable-next-line
var GisServerGlobalConstant = {
    ProjectTiandituKey: ServerGlobalConstant.ProjectTiandituKey, //天地图key

    is3D: ServerGlobalConstant.isLoad3DMap, //是否引入3D相关参数
    is2D: ServerGlobalConstant.isLoad2DMap, //是否引入2D相关参数

    common: {
        needSaveMap: true, //是否需要地图导出
        needFullscreen: true, //是否需要全屏
        needPbf: false, //pbf数据转成geojson
        needShapefile: false, //解析shapefile 数据
        needGeotiff: false, //解析geotiff 数据
        needKriging: false, //克里金差值
        needEchart3: false,
        needMav: false,

        needD3: false, //是否需要d3，svg差值用到
        needXmltojson: false, //xmltojosn库， 用于EsrilocalLayer、esri本地切片
        needArcgisFrontWind: false, //arcgis前端风场
        needArcgisWind: true, //arcgis风场

        needMapboxWind: true, //mapbox风场
        needMapboxDraw: true, //是否需要标绘

        needScan: false, //是否需要扫描
        need3DBoundaryLine: true //是否需要立体边界 ，如果设置为true,需要替换 ./gis/3D/data/xzq.json 数据
    },

    arcgis: {
        needToken: false, //是否需要请求arcgis token
        mapOptions: {
            extent: {
                // xmax: 12932925.17384548,
                // xmin: 12345887.622540222,
                // ymax: 3531211.6879894473,
                // ymin: 3250840.1074962597,

                xmax: 13068108.501170566,
                xmin: 12534578.043740036,
                ymax: 3595383.359256417,
                ymin: 3315012.33950639,
                spatialReference: {
                    wkid: 102100
                }
            },
            minZoom: 2,
            maxZoom: 18,
            logo: false
        },
        basemaps: [
            {
                label: '天地图',
                type: 'tianditumtimage',
                visible: true,
                layerControl: true,
                minImg: 'map-caise.png'
            }
        ],
        thematicmap: {},
        arcgisApiHost: ServerGlobalConstant.arcgisApiHost //兼容封装库
    },
    mapbox: {
        needToken: false,
        mapBoxOption: {
            accessToken: '',
            //   'pk.eyJ1IjoidGVhbWVuY2FjaGUiLCJhIjoiY2txZzcxa2NrMHB4eTJ1bnphem9lazZrYiJ9.bQ9wCbRR64Iaxgk4-l2JCw',

            //限制地图在固定的区域
            // maxBounds: [
            //     [119.77011007581652, 30.542393038695934],
            //     [121.76934017890662, 31.45115550705424]
            // ],
            //中心点配置
            center: [120.57282044325768, 30.971725224623682],

            //默认级别
            zoom: 10.5,
            //倾斜角设置，默认40~60
            pitch: 40,
            //默认地图的最小等级，当前视图范围内显示完行政区划边界小两个级别
            minZoom: 9,
            //最大级别，限制地图无限放大
            maxZoom: 18,
            //旋转角度
            bearing: 0,
            //地图样式对象
            style: ServerGlobalConstant.isLoad3DMap ? xzqht : ''
        },
        basemaps: [], //显示在图层切换的组件中
        thematicmap: {},
        czExt: {
            xmin: 120.350986,
            ymin: 30.760181,
            xmax: 120.898986,
            ymax: 31.228181,

            xNum: 274,
            yNum: 234
        }
    }
};
