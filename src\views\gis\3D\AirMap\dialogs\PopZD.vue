<!-- @format -->
<!--空气站提示框 -->
<template>
    <div
        class="yy-mapalert1"
        style="height: 205px; width: 500px; transform: translate(-50%, 0)"
    >
        <div class="flx1 ac jb hdbox" style="margin-bottom: -10px">
            <h1 class="t1">
                {{ selectItem.CDMC
                }}<i class="yygkbtn">{{ selectItem.JGJBMC || '' }}</i>
            </h1>
            <p class="time1">
                更新时间：{{
                    selectItem.JCSJ ? selectItem.JCSJ.substring(0, 13) : '--'
                }}
            </p>
        </div>
        <img
            src="../css/images/yy-alinebg.png"
            style="width: 460px"
            alt=""
            class="yy-alinebg"
        />
        <div class="bdbox">
            <div class="gap"></div>
            <dl class="sw0630-dlbx2" style="height: 120px">
                <dt style="width: 200px" :class="'air-back-dj' + aqiLevel">
                    <h1>
                        AQI: <i>{{ selectItem.AQI || '' }}</i>
                    </h1>
                    <h2>
                        <p style="padding-left: 0">
                            <span style="font-size: 24px">{{
                                selectItem.KQZLLB || ''
                            }}</span>
                            <span v-show="selectItem.SYWRW"
                                >首污：{{ selectItem.SYWRW || '' }}</span
                            >
                        </p>
                    </h2>
                </dt>
                <dd>
                    <ul>
                        <li
                            style="width: 77px"
                            :class="'air-border-dj' + pm25Level"
                        >
                            <h1 :class="'air-back-dj' + pm25Level">
                                PM<sub>2.5</sub>
                            </h1>
                            <p>{{ selectItem.PM25 || '' }}</p>
                        </li>
                        <li
                            style="width: 77px"
                            :class="'air-border-dj' + pm10Level"
                        >
                            <h1 :class="'air-back-dj' + pm10Level">
                                PM<sub>10</sub>
                            </h1>
                            <p>{{ selectItem.PM10 || '' }}</p>
                        </li>
                        <li
                            style="width: 77px"
                            :class="'air-border-dj' + o3Level"
                        >
                            <h1 :class="'air-back-dj' + o3Level">
                                O<sub>3</sub>
                            </h1>
                            <p>{{ selectItem.O3 || '' }}</p>
                        </li>
                        <li
                            style="width: 77px"
                            :class="'air-border-dj' + so2Level"
                        >
                            <h1 :class="'air-back-dj' + so2Level">
                                SO<sub>2</sub>
                            </h1>
                            <p>{{ selectItem.SO2 || '' }}</p>
                        </li>
                        <li
                            style="width: 77px"
                            :class="'air-border-dj' + no2Level"
                        >
                            <h1 :class="'air-back-dj' + no2Level">
                                NO<sub>2</sub>
                            </h1>
                            <p>{{ selectItem.NO2 || '' }}</p>
                        </li>
                        <li
                            style="width: 77px"
                            :class="'air-border-dj' + coLevel"
                        >
                            <h1 :class="'air-back-dj' + coLevel">CO</h1>
                            <p>{{ selectItem.CO || '' }}</p>
                        </li>
                    </ul>
                </dd>
            </dl>
        </div>
    </div>
</template>

<script>
export default {
    props: ['selectItem'],
    data() {
        return {
            aqiLevel: '1',
            pm25Level: '1',
            pm10Level: '1',
            o3Level: '1',
            so2Level: '1',
            no2Level: '1',
            coLevel: '1'
        };
    },
    mounted() {
        setTimeout(() => {
            this.initData();
        }, 200);
    },
    methods: {
        initData() {
            this.aqiLevel = PowerGL.getAirDJByLevel(
                PowerGL.getLevelByWrw('AQI', this.selectItem, 1)
            );

            this.pm25Level = PowerGL.getAirDJByLevel(
                PowerGL.getLevelByWrw('PM25', this.selectItem, 1)
            );

            this.pm10Level = PowerGL.getAirDJByLevel(
                PowerGL.getLevelByWrw('PM10', this.selectItem, 1)
            );

            this.o3Level = PowerGL.getAirDJByLevel(
                PowerGL.getLevelByWrw('O3', this.selectItem, 1)
            );

            this.so2Level = PowerGL.getAirDJByLevel(
                PowerGL.getLevelByWrw('SO2', this.selectItem, 1)
            );

            this.no2Level = PowerGL.getAirDJByLevel(
                PowerGL.getLevelByWrw('NO2', this.selectItem, 1)
            );

            this.coLevel = PowerGL.getAirDJByLevel(
                PowerGL.getLevelByWrw('CO', this.selectItem, 1)
            );
        }
    },
    computed: {}
};
</script>

<style></style>
