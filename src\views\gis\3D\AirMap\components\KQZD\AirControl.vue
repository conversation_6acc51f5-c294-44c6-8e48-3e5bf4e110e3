<!-- @format -->
<!-- 大气站点组件 -->
<template>
    <div>
        <!-- 大气因子切换 -->
        <FactorCheck @factorChange="factorChangeHandle"></FactorCheck>
        <div class="gap"></div>
        <!-- 大气监管级别 -->
        <JGJBCheck :map="map" @jgjbChange="jgjbChangeHandle"></JGJBCheck>
    </div>
</template>

<script>
import {
    getAirStationBaseInfo //查询空气站信息
} from '@/api/gis/3D/AirMap/index';
import JGJBCheck from './JGJBCheck.vue';
import FactorCheck from './FactorCheck.vue';
import PointUtil from '../../utils/PointUtil';
export default {
    data() {
        return {
            layerId: '大气站点',
            selectYZ: 'AQI',
            jgjbArr: null, //监管级别
            style: '1'
        };
    },
    inject: ['pointClickHandle', 'refreshSearchData'],
    mounted() {
        this.initPage();
    },
    unmounted() {
        this.clear();
    },
    components: {
        JGJBCheck,
        FactorCheck
    },
    props: ['curentTime', 'showAno'],
    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);
        },

        //监管级别统计
        jgjbChangeHandle(data) {
            this.jgjbArr = data;
        },
        // 大气因子改变
        factorChangeHandle(yz) {
            this.selectYZ = yz;
        },

        //查询空气站点接口
        getAirStationBaseInfo() {
            let param = {};

            if (this.curentTime) {
                param.jcsj = this.curentTime;
            }

            getAirStationBaseInfo(param).then((res) => {
                this.jcdArr = res.data.map((item) => {
                    item.AQILevel = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('AQI', item, 1)
                    );

                    item.PM25Level = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('PM25', item, 1)
                    );

                    item.PM10Level = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('PM10', item, 1)
                    );

                    item.O3Level = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('O3', item, 1)
                    );

                    item.SO2Level = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('SO2', item, 1)
                    );

                    item.NO2Level = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('NO2', item, 1)
                    );

                    item.COLevel = PowerGL.getAirDJByLevel(
                        PowerGL.getLevelByWrw('CO', item, 1)
                    );
                    return item;
                });

                this.filterData();
            });
        },

        //过滤点位
        filterData() {
            if (this.jcdArr) {
                let arr = this.jcdArr.filter((item) => {
                    //需要考虑一个站点，多个监管级别的情况，代码暂时未实现
                    return this.jgjbArr.includes(item.JGJB.toString());
                });

                this.clear();

                if (this.style == '0') {
                    this.pointUtil.addImgPoint(arr, {
                        id: this.layerId,
                        selectYZ: this.selectYZ
                        // layerOption: {
                        //     minzoom: 12
                        // }
                    });
                } else {
                    let params = {
                        id: this.layerId,
                        selectYZ: this.selectYZ,
                        showAno: this.showAno,
                        style: this.style, //样式
                        disablePopup: false,
                        disableClick: false
                    };

                    this.pointUtil.addCustomHtmlLayer(arr, params);
                }

                this.setSearchData(this.layerId, arr);
            }
        },

        test(style) {
            this.style = style;
            this.filterData();
        },

        //设置搜索框数据
        setSearchData(layerId, arr) {
            let data = {};
            data[layerId] = arr;
            this.refreshSearchData(data);
        },

        //清理空气站点
        clear() {
            if (this.pointUtil) {
                this.pointUtil.removeLayerByName(this.layerId);
                this.pointUtil.clear(this.layerId);
            }
        }
    },

    computed: {
        listenParam() {
            let selectYZ = this.selectYZ;
            let showAno = this.showAno;
            let jgjbArr = this.jgjbArr;
            return { selectYZ, showAno, jgjbArr };
        }
    },

    watch: {
        curentTime(newVal, oldVal) {
            this.getAirStationBaseInfo();
        },

        //多参数监听
        listenParam: {
            // immediate: true,
            deep: true,
            handler(val, oldVal) {
                if (this.jcdArr) {
                    this.filterData();
                }
            }
        }
    }
};
</script>

<style></style>
