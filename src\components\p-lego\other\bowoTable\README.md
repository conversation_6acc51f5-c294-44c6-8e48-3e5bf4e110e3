### 版本说明
vue^3.0.0、element-plus^2.1.7，(最好使用这个)
vue^3.2.13、element-plus^1.0.2-beta.28
以上版本均能使用
### props选项说明
```jsx
{
    culumns: Array, //表头
    /**
    * field: 映射字段
    * title: 表头标题
    * align: 对齐方式 同el-table align
    * fixed: 固定位置 同el-table fixed
    * type为operation(操作) fixed:right
    * width: 列宽  同el-table width
    * minWidth: 最小列宽   同el-table 
    * formatter: 格式化数据
    * headerRender: 自定义头部渲染
    * render: 自定义表格单元格渲染
    * */
   styleConfig: Object, //样式配置
   config: Object, //配置项
   tableData: Array, //表数据
   checkedIds: Array, //勾选的主键集合
   scrollConfig: Object, //滚动配置
   setOrder: Function, //自定义序号函数
}
```

### events事件说明
```jsx
'on-selectList' //监听已选择列表事件
'update:checkedIds' //监听已勾选勾选的主键事件
```


### 基本使用
```vue
<template>
    <BowoTableView
        :config="{
            maxHeight: '300px',
            showOrder: false, // 是否显示序号
            showCheckbox: false, // 是否显示勾选框
            multiple: false // 是否多选
        }"
        :styleConfig="{
            headerBgColor: 'rgba(2, 115, 194, 0.2)', // 表头背景色
            headerFontColor: '#fff', // 表头字体颜色
            cellHeight: '60px', // 单元格高度
            bodyBgColor: 'transparent', // tbody背景色
            bodyFontColor: '#fff', // tbody字体颜色
            borderColor: 'rgba(52,151,228,0.3)', // 边框颜色
            hoverBgColor: 'transparent', // hover背景色
            fixedBgColor: 'transparent' // 固定列背景色
        }"
        :columns="columns"
        :tableData="tableData"          
    />
</template>
<script>
import BowoTableView from '@/components/bowoTable/BowoTableView.vue';

export default {
    components:{ BowoTableView },
    data(){
        return {
            columns: [
                {
                    title: '姓名',
                    field: 'name',
                    width: '100px'
                },
                {
                    title: '年龄',
                    field: 'age',
                    width: '100px'
                },
                {
                    title: '地址',
                    field: 'address',
                    render(h, params) {
                        return h(
                            'span',
                            {
                                onClick: () => {
                                    console.log('点击事件', params);
                                }
                            },
                            'hello，我是通过render函数渲染的，点我试试'
                        );
                    }
                }
            ],
            tableData: [
                {
                    id: '1',
                    name: '姓名1',
                    age: '20',
                    address: 'xxxx'
                },
                {
                    id: '2',
                    name: '姓名2',
                    age: '20',
                    address: 'xxxx'
                },
                {
                    id: '3',
                    name: '姓名3',
                    age: '20',
                    address: 'xxxx'
                },
                {
                    id: '4',
                    name: '姓名4',
                    age: '20',
                    address: '岳麓区'
                },
                {
                    id: '5',
                    name: '姓名5',
                    age: '20',
                    address: 'xxxx'
                },
                {
                    id: '6',
                    name: '姓名6',
                    age: '20',
                    address: 'xxxx'
                },
                {
                    id: '7',
                    name: '姓名4',
                    age: '20',
                    address: 'xxxx'
                },
                {
                    id: '8',
                    name: '姓名5',
                    age: '20',
                    address: 'xxxx'
                },
                {
                    id: '9',
                    name: '姓名6',
                    age: '20',
                    address: 'xxxx'
                },
                {
                    id: '10',
                    name: '姓名10',
                    age: '20',
                    address: 'xxxx'
                }
            ],
        }
    }
}
</script>
```

### 选择功能（支持单选，多选）
```vue
<template>
    <BowoTableView
        :config="{
            maxHeight: '300px',
            showOrder: true, // 是否显示序号
            showCheckbox: true, // 是否显示勾选框
            multiple: false // 是否多选
        }"
        :styleConfig="{
            headerBgColor: '#f3f8ff', // 表头背景色
            headerFontColor: '#48658e', // 表头字体颜色
            cellHeight: '50px', // 单元格高度
            bodyBgColor: '#fff', // tbody背景色
            bodyFontColor: '#333', // tbody字体颜色
            borderColor: '#eee', // 边框颜色
            hoverBgColor: 'transparent', // hover背景色
            fixedBgColor: 'transparent' // 固定列背景色
        }"
        :columns="columns"
        :tableData="tableData"
        :checkedIds="checkedIds"
        @on-selectList="test"
    />
</template>

<script>
import BowoTableView from '@/components/bowoTable/BowoTableView.vue';
export default {
    components: { BowoTableView },
    data(){
        return {
            columns: [],
            tableData: [],
            checkedIds: [],
        }
    },
    methods:{
        test(v){
            console.log('on-selectList--',v)
        }
    }
}
</script>
```
### 表格+分页+自增序号
```vue
<template>
    <BowoTable
        :api="requestApi"
        :config="{
            maxHeight: '500px',
            showOrder: true, // 是否显示序号
            showCheckbox: false // 是否显示勾选框
        }"
        :isAutoLoad="true"
        ref="bowoTable"
        :columns="columns"
        useIncreOrder
    />
</template>

<script>
import BowoTable from '@/components/bowoTable/index.vue';
import { testApi } from '_a/test.js'
export default {
    components:{ BowoTable },
    data(){
        return {
            requestApi: testApi,
            columns: []
        }
    }
}
</script>
```


### 自动滚动,不丢失事件
```vue
<template>
    <BowoTableView
        :config="{
            maxHeight: '300px',
            showOrder: false, // 是否显示序号
            showCheckbox: false, // 是否显示勾选框
            multiple: false // 是否多选
        }"
        :styleConfig="{
            headerBgColor: '#f3f8ff', // 表头背景色
            headerFontColor: '#48658e', // 表头字体颜色
            cellHeight: '50px', // 单元格高度
            bodyBgColor: '#fff', // tbody背景色
            bodyFontColor: '#333', // tbody字体颜色
            borderColor: '#eee', // 边框颜色
            hoverBgColor: 'transparent', // hover背景色
            fixedBgColor: 'transparent' // 固定列背景色
        }"
        :columns="columns"
        :tableData="tableData"
        :scrollConfig="{
            autoPlay: true, // 开启自动滚动
            limitMoveNum: 10 // 开始无缝滚动的数据量
        }"
    />
</template>

<script>
import BowoTableView from '@/components/bowoTable/BowoTableView.vue';
export default {
    components:{ BowoTableView },
    data(){
        return {
            columns: [],
            tableData: []
        }
    }
}
</script>
```