/** @format */

import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';

import installElementPlus from './plugins/element';
import { VueJsonp } from 'vue-jsonp';
// 基于elementUI封装的业务ui
// import pUI from 'p-ui';
// import 'p-ui/dist/pUi.css';

// p-charts vue3版本
import pCharts from 'p-charts';

// 滚动美化
import GeminiScrollbar from 'vue3-gemini-scrollbar';

import dayjs from 'dayjs';
const app = createApp(App);
app.config.globalProperties.$dayjs = dayjs;
//后续使用的时候 this.$dayjs(new Date()).add(-1, 'day').format('YYYY-MM-DD HH:mm:ss');

// if (ServerGlobalConstant.isLoad3DMap) {
// }

import mapboxgl from 'mapbox-bowo';
import 'mapbox-bowo/dist/mapbox-gl.css';
window.mapboxgl = mapboxgl;

// mapboxgl.config.API_URL = '';

// 加载wgs84 与 cgcs2000 坐标系的底图，需要用到这个库
// import '@cgcs2000/mapbox-gl/dist/mapbox-gl.css';
// import mapboxgl from '@cgcs2000/mapbox-gl';
// window.mapboxgl = mapboxgl;

import PowerMapbox from 'PowerMapbox';
window.PowerGL = PowerMapbox;

import PowerArcgis from 'PowerArcgis';
window.PowerGis = PowerArcgis;

import 'PowerArcgis/css/pointStyle.css';

mapboxgl.config.MAX_PARALLEL_IMAGE_REQUESTS = 256; //请求底图请求慢，造成阻塞的问题

import * as echarts from 'echarts';
window.echarts = echarts;

app.use(store)
    .use(router)
    .use(installElementPlus)
    .use(GeminiScrollbar)
    // .use(pUI)
    .use(pCharts)
    .mount('#app');
