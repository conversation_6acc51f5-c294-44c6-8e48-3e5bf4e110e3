<!-- @format -->

<template>
    <div class="zy-cell cell2">
        <div class="hd">
            <p class="til">日常执法</p>
        </div>
        <div class="bd">
            <div class="gap"></div>
            <div class="gap"></div>
            <ul class="zy-list2">
                <li>
                    <div class="tu">
                        <img src="./images/zhifa-li1.png" alt="" />
                    </div>
                    <div class="rp">
                        <p class="p1">出动人次</p>
                        <p class="p2">
                            {{ fomate3Number(rczfData.CDRC) }}
                            <span>次</span>
                        </p>
                        <p class="p3" v-if="dateType != 'ZDY'">
                            <span>同比</span>
                            <span
                                :class="[
                                    rczfData.CDRC_TB > 0 ? 'add' : 'reduce'
                                ]"
                                >{{ rczfData.CDRC_TB || '-' }}%</span
                            >
                        </p>
                    </div>
                </li>
                <li>
                    <div class="tu">
                        <img src="./images/zhifa-li2.png" alt="" />
                    </div>
                    <div class="rp">
                        <p class="p1">出动人数</p>
                        <p class="p2">
                            {{ fomate3Number(rczfData.CDRS) }}
                            <span>人</span>
                        </p>
                        <p class="p3" v-if="dateType != 'ZDY'">
                            <span>同比</span>
                            <span
                                :class="[
                                    rczfData.CDRS_TB > 0 ? 'add' : 'reduce'
                                ]"
                                >{{ rczfData.CDRS_TB || '-' }}%</span
                            >
                        </p>
                    </div>
                </li>
                <li>
                    <div class="tu">
                        <img src="./images/zhifa-li3.png" alt="" />
                    </div>
                    <div class="rp">
                        <p class="p1">检查家次</p>
                        <p class="p2">
                            {{ fomate3Number(rczfData.JCJC) }}
                            <span>家</span>
                        </p>
                        <p class="p3" v-if="dateType != 'ZDY'">
                            <span>同比</span>
                            <span
                                :class="[
                                    rczfData.JCJC_TB > 0 ? 'add' : 'reduce'
                                ]"
                                >{{ rczfData.JCJC_TB || '-' }}%</span
                            >
                        </p>
                    </div>
                </li>
                <li>
                    <div class="tu">
                        <img src="./images/zhifa-li4.png" alt="" />
                    </div>
                    <div class="rp">
                        <p class="p1">发现问题</p>
                        <p class="p2">
                            {{ fomate3Number(rczfData.FXWT) }}
                            <span>个</span>
                        </p>
                        <p class="p3" v-if="dateType != 'ZDY'">
                            <span>同比</span>
                            <span
                                :class="[
                                    rczfData.FXWT_TB > 0 ? 'add' : 'reduce'
                                ]"
                                >{{ rczfData.FXWT_TB || '-' }}%</span
                            >
                        </p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
// 日常执法
export default {
    props: {
        // rczfData: {
        //     type: Object
        // }
    },
    created() {},
    data() {
        return {
            rczfData: {
                CDRC: '2006040',
                CDRC_TB: -62.82,
                CDRS: '54523',
                CDRS_TB: -33.04,
                FXWT: '111805',
                FXWT_TB: -66.69,
                JCJC: '864155',
                JCJC_TB: -60.17
            }
        };
    },
    mounted() {},
    methods: {
        fomate3Number: function (e) {
            if (!e) return '0';
            let intPart = Number(e).toFixed(0); // 获取整数部分
            let intPartFormat = intPart
                .toString()
                .replace(/(\d)(?=(?:\d{3})+$)/g, '$1,'); // 将整数部分逢三一断
            let floatPart = '.00'; // 预定义小数部分
            let value2Array = e.toString().split('.');
            // =2表示数据有小数位
            if (value2Array.length === 2) {
                floatPart = value2Array[1].toString(); // 拿到小数部分
                if (floatPart.length === 1) {
                    // 补0
                    return intPartFormat + '.' + floatPart + '0';
                } else {
                    return intPartFormat + '.' + floatPart;
                }
            } else {
                return intPartFormat;
            }
        }
    }
};
</script>

<style scoped>
.zy-cell {
    border: 1px solid #0a428a;
    position: relative;
    margin-bottom: 15px;
    background-color: rgba(9, 44, 89, 0.8);
}

.zy-cell.cell2 {
    height: 177px;
    box-sizing: border-box;
}

.zy-cell2 {
    flex: 1;
    background: url(./images/line1.png) right top repeat-y;
}

.zy-cell2 .hd {
    padding: 8px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.zy-cell2 .hd p {
    font-size: 24px;
    color: #32d6ff;
    line-height: 32px;
    font-family: 'pmzd';
}

.zy-cell2 .hd p span {
    font-size: 16px;
    vertical-align: baseline;
    font-family: 'Microsoft YaHei', SimSun, Arial, Helvetica, sans-serif;
}

.zy-list2 {
    display: flex;
}

.zy-list2 li {
    flex: 1;
    display: flex;
    background: url(./images/line1.png) right top no-repeat;
}

.zy-list2 li:last-child {
    background: none;
}

.zy-list2 li .tu {
    padding-left: 22px;
}

.zy-list2 li .rp {
    height: 90px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 23px;
}

.zy-list2 li .p1 {
    font-size: 16px;
    color: #ddd;
    line-height: 18px;
}

.zy-list2 li .p2 {
    font-size: 24px;
    color: #32d6ff;
    line-height: 24px;
    font-family: 'DIN-Regular';
}

.zy-list2 li .p2 span {
    font-size: 16px;
    vertical-align: baseline;
}

.zy-list2 li .p3 {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.zy-list2 li .p3 span {
    font-size: 16px;
    color: #ddd;
    line-height: 18px;
    margin-right: 5px;
}

.zy-list2 li .p3 .add {
    color: #f64141;
    padding-right: 28px;
    background: url(./images/add.png) right center no-repeat;
}

.zy-list2 li .p3 .reduce {
    color: #22df7b;
    padding-right: 28px;
    background: url(./images/reduce.png) right center no-repeat;
}
.zy-cell .hd .til {
    font-size: 24px;
    color: #32d6ff;
    line-height: 32px;
    font-family: 'pmzd';
}
</style>
