function shipei(dom) {
    detectZoom();
    window.onresize = function() {
        detectZoom();
    }

    function detectZoom() {
        dom.css({
            width: '1920px',
            height: '1080px'
        })
        var ratio = window.innerWidth / window.innerHeight;
        var biaozhun = 1920 / 1080;
        var scale = 1;
        if (ratio > biaozhun) {
            scale = window.innerHeight / 1080;
            dom.css({
                'transform': "scale(" + scale + ") ",
                'transform-origin': "0 0",
                'width': window.innerWidth / scale
            });
        } else {
            scale = window.innerWidth / 1920;
            dom.css({
                'transform': "scale(" + scale + ") ",
                'transform-origin': "0 0",
                'height': window.innerHeight / scale
            });
        }
    };
}