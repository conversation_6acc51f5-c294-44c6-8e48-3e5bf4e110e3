<!-- @format -->
<!-- 3d饼图 -->
<template>
    <div class="pie">
        <!-- 图表容器 -->
        <div class="pie-chart" ref="chartdiv"></div>

        <!-- 图例 -->
        <div
            v-if="finalOption.showLegend && data && data.length"
            class="pie-legend"
        >
            <div
                v-for="(item, i) in dataItems"
                :key="i"
                class="pie-legend-item"
                @click="toggleSlice(i)"
                @mouseover="hoverSlice(i)"
                @mouseout="blurSlice(i)"
            >
                <div
                    :style="{ background: finalOption.color[i] }"
                    class="pie-legend-item-marker"
                ></div>
                <div
                    class="pie-legend-item-value"
                    v-html="legendFormat(item)"
                ></div>
            </div>
        </div>

        <!-- 无数据效果 -->
        <div class="pie-no-data" v-if="!data || !data.length">
            <img src="./nodata.png" alt="" />
            <p class="pie-no-data-txt">暂无数据</p>
        </div>
    </div>
</template>

<script>
import * as am4core from '@amcharts/amcharts4/core';
import * as am4charts from '@amcharts/amcharts4/charts';
import am4themes_animated from '@amcharts/amcharts4/themes/animated';
const lightColor = [
    '#6ebffb',
    '#a9df34',
    '#40c057',
    '#5a7fe8',
    '#ffea00',
    '#13c9d9',
    '#00a497',
    '#5363c5',
    '#218ede',
    '#f39800',
    '#4262d9',
    '#9799f3',
    '#0fd0b7',
    '#ffd351'
];
const darkColor = [
    '#2ad9ff',
    '#e9c613',
    '#26d267',
    '#f68b17',
    '#fc4a4a',
    '#4d76eb',
    '#00e1c4',
    '#9465f4',
    '#c0f02f',
    '#06a4ff'
];

export default {
    name: 'pie',
    props: {
        data: {
            type: Array,
            default: function () {
                return [];
            }
        },
        option: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    watch: {
        option: 'renderChartMethod',
        data: 'renderChartMethod'
    },

    data() {
        const color =
            window.localStorage.themeType === 'dark' ? darkColor : lightColor;

        return {
            defaultOption: {
                angle: 45, // 角度
                alignLabels: false, // 是否显示长的线
                setScroll: false, // 提示框是否自动轮播
                innerRadius: 40, // 中间圆圈大小
                showLegend: true, //是否展示图例
                showLabel: false, //是否展示label
                legendFormat: `{category}：{value.value}（{value.percent.formatNumber('#.00')}%）`, //label格式化
                depth: 40, //饼图深度
                color //颜色配置
            },
            finalOption: {},
            chart: {},
            series2: {
                dataItems: []
            },
            dataItems: [],
            timer: null,
            index: 0
        };
    },
    created() {},
    mounted() {
        this.renderChartMethod();
    },
    methods: {
        legendFormat(item) {
            if (typeof this.finalOption.legendFormat === 'function') {
                return this.finalOption.legendFormat(item);
            }
            return `${item.category}&nbsp;&nbsp;&nbsp;${item.value}&nbsp;&nbsp;&nbsp;${item.percent}%`;
        },
        toggleSlice(item) {
            let slice = this.series2.dataItems.getIndex(item);
            if (slice.visible) {
                slice.hide();
            } else {
                slice.show();
            }
        },
        hoverSlice(item) {
            let slice = this.series2.slices.getIndex(item);
            slice.isHover = true;
        },
        blurSlice(item) {
            let slice = this.series2.slices.getIndex(item);
            slice.isHover = false;
        },
        // 是否设置滚动
        renderChartMethod() {
            this.finalOption = {
                ...this.defaultOption,
                ...this.option
            };
            clearInterval(this.timer);
            this.timer = null;
            let self = this;
            let resData = JSON.parse(JSON.stringify(this.data));
            let newData = [];
            if (this.finalOption.setScroll && this.data.length) {
                resData.forEach((item) => {
                    newData.push({
                        pulled: false,
                        name: item.name,
                        value: item.value
                    });
                });
                newData[0].pulled = true;
                this.timer = setInterval(() => {
                    let isFlag = false;
                    newData.forEach((item) => {
                        item.pulled = false;
                    });
                    newData[self.index].pulled = true;
                    self.series2.slices.getIndex(self.index).isHover = true;
                    self.index++;
                    if (self.index == newData.length) {
                        self.index = 0;
                    }
                    this.renderChartVal(newData);
                }, 2000);
                this.renderChart(resData);
            } else {
                this.renderChart(resData);
            }
        },
        renderChartVal(data) {
            this.chart.data = data;
        },
        renderChart(data) {
            // let data = this.data;
            let self = this;
            am4core.ready(function () {
                am4core.useTheme(am4themes_animated);
                // Themes end

                let chart = am4core.create(
                    self.$refs.chartdiv,
                    am4charts.PieChart3D
                );

                chart.hiddenState.properties.opacity = 0; // this creates initial fade-in

                chart.data = data;
                self.chart = chart;
                chart.innerRadius = am4core.percent(
                    self.finalOption.innerRadius
                );
                chart.depth = self.finalOption.depth;
                chart.angle = self.finalOption.angle;

                let series2 = chart.series.push(new am4charts.PieSeries3D());
                self.series2 = series2;
                series2.colors.list = self.finalOption.color.map((v) => {
                    return am4core.color(v);
                });

                series2.alignLabels = self.finalOption.alignLabels;

                //label设置
                series2.labels.template.disabled = !self.finalOption.showLabel;
                // label的值
                if (self.finalOption.showLabel) {
                    series2.labels.template.text =
                        self.finalOption.legendFormat;
                    //label的颜色
                    series2.labels.template.fill =
                        window.localStorage.themeType === 'dark'
                            ? '#fff'
                            : '#333';
                }

                // series2.tooltip.fontSize = '14'; //tooltip的字体大小
                series2.tooltip.autoTextColor = false;
                series2.tooltip.label.fill = '#fff';

                series2.slices.template.tooltipText =
                    "{category}：{value.value}（{value.percent.formatNumber('#.00')}%）";
                series2.dataFields.value = 'value';
                series2.dataFields.depthValue = 'value';
                series2.dataFields.category = 'name';

                let sum = 0;
                data.forEach((v) => {
                    sum += isNaN(v.value) ? 0 : Number(v.value);
                });

                //数据都是0的时候默认会不展示，不知道怎么配置， 先搞个替代效果：放个假数据并把tooltip中数据写死为0
                if (sum === 0) {
                    chart.data = data.map((v) => {
                        return {
                            name: v.name,
                            value: 50
                        };
                    });
                    series2.slices.template.tooltipText = '{category}：0（0%）';
                }
                series2.slices.template.propertyFields.isActive = 'pulled';
                series2.slices.template.cornerRadius = 5;
                let eles = document.querySelectorAll(
                    '[aria-labelledby$=-title]'
                );
                for (let i = 0; i < eles.length; i++) {
                    eles[i].style.visibility = 'hidden';
                }

                chart.events.on('ready', function (event) {
                    // populate our custom legend when chart renders
                    // let chart = self.chart;
                    let series2 = self.series2;
                    // chart.customLegend = self.$refs.legend;
                    // self.series2.dataItems = series2.dataItems;
                    self.dataItems = [];
                    series2.dataItems.each(function (row, i) {
                        let percent =
                            Math.round(row.values.value.percent * 100) / 100;
                        let value = row.value;
                        self.dataItems.push({
                            percent: sum === 0 ? 0 : percent,
                            value: sum === 0 ? 0 : value,
                            category: row.category
                        });
                    });
                });
            });
        }
    }
};
</script>

<style scoped lang="less">
.pie {
    display: flex;
    width: 100%;
    height: 330px;
    position: relative;
    &-chart {
        flex: 1;
        height: 100%;
    }
    &-legend {
        width: 51%;
        height: 100%;
        float: left;
        text-align: left;
        display: flex;
        flex-direction: column;
        justify-content: center;

        &-item {
            display: flex;
            margin: 10px 0 10px 10px;
            font-weight: normal;
            cursor: pointer;
            font-family: 'DIN-Bold';
            width: 100%;
            align-items: center;

            &-value {
                display: flex;
                align-items: center;
            }
            &-marker {
                display: inline-block;
                width: 14px;
                height: 14px;
                margin-right: 10px;
            }
            &.disabled .legend-marker {
                opacity: 0.5;
                background: #ddd;
            }
        }
    }

    &-no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        &-txt {
            opacity: 0.8;
        }
    }
}
</style>
