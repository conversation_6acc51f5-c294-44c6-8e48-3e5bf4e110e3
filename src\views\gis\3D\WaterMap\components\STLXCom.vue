<!-- @format -->

<template>
    <div>
        <dt class="flx1 ac" style="padding-left: 15px">
            <label class="sw0811-check1" @click="allLayerClick"
                ><input
                    type="checkbox"
                    disabled
                    style="pointer-events: none"
                    :checked="checkAll"
                /><strong>水体类型（{{ allTotal }}）</strong></label
            >
        </dt>
        <dd class="sw0811-checkbox1 yy0812-checkbox1">
            <label
                class="sw0811-check1"
                v-for="(item, index) of zts"
                :key="index"
                @click="ztLayerClick(item)"
                ><input
                    type="checkbox"
                    :checked="item.selected"
                    disabled
                    style="pointer-events: none"
                /><span>{{ item.name }}（{{ item.total }}）</span></label
            >
        </dd>
    </div>

    <!-- <dl class="sw0811-dlbx5">
        <dt class="flx1 ac" style="padding-left: 15px">
            <label class="sw0811-check1" @click="allLayerClick"
                ><input
                    type="checkbox"
                    disabled
                    style="pointer-events: none"
                    :checked="checkAll"
                /><strong>水体类型（{{ allTotal }}）</strong></label
            >
        </dt>
        <dd class="sw0811-checkbox1 yy0812-checkbox1">
            <label
                class="sw0811-check1"
                v-for="(item, index) of zts"
                :key="index"
                @click="ztLayerClick(item)"
                ><input
                    type="checkbox"
                    :checked="item.selected"
                    disabled
                    style="pointer-events: none"
                /><span>{{ item.name }}（{{ item.total }}）</span></label
            >
        </dd>
    </dl> -->
</template>

<script>
export default {
    data() {
        return {
            stlxArr: [],

            zts: [
                // {
                //     name: '河流',
                //     fiterType: '河流',
                //     selected: true,
                //     total: 0,
                //     DM: 'HL'
                // },
                // {
                //     name: '湖库',
                //     fiterType: '湖库',
                //     selected: true,
                //     total: 0,
                //     DM: 'HK'
                // }
            ],

            allTotal: 0,

            checkAll: true //全部选中
        };
    },
    props: ['tjData'],
    methods: {
        //专题点击
        allLayerClick() {
            this.checkAll = !this.checkAll;

            for (let o of this.zts) {
                o.selected = this.checkAll;
            }

            this.getSelectZT();
        },

        //子专题点击
        ztLayerClick(obj) {
            obj.selected = !obj.selected;

            //根据子级的选中状态，设置父级的选中状态
            this.checkAll = this.zts.some((item) => {
                return item.selected;
            });

            this.getSelectZT();
        },

        //获取选中的监管级别
        getSelectZT() {
            let arrTemp = this.zts.filter((item) => {
                return item.selected;
            });

            this.stlxArr = arrTemp.map((item) => {
                return item.fiterType;
            });

            this.$emit('stlxChange', this.stlxArr);
        }
    },
    watch: {
        tjData: {
            deep: true,
            handler(val) {
                if (val && this.zts.length == 0) {
                    this.allTotal = 0;

                    let tempArr = [];
                    for (let item of val.STLX) {
                        if (item.DM == 'ZS') {
                            this.allTotal = item.NUM;
                        } else {
                            tempArr.push({
                                name: item.MC,
                                fiterType: item.MC,
                                selected: this.checkAll,
                                total: item.NUM,
                                DM: item.DM
                            });
                        }
                    }

                    this.zts = tempArr;
                } else {
                    for (let item of val.STLX) {
                        if (item.DM == 'ZS') {
                            this.allTotal = item.NUM;
                        }
                    }

                    for (let obj of this.zts) {
                        let arr = val.STLX.filter((oo) => {
                            return oo.DM == obj.DM;
                        });
                        if (arr.length >= 0) {
                            obj.total = arr[0].NUM;
                        }
                    }
                }
            }
        }
    }
};
</script>

<style></style>
