/** @format */

let DATA_URL2 = '/hjbh/LIDARController';
import axios from '_u/ajaxRequest';

// 雷达-最新时间
export const lidarMaxTime = (data) => {
    return axios.request({
        url: DATA_URL2 + '/get/maxTime',
        method: 'GET',
        params: data
    });
};

// 雷达-记录列表
export const lidarDataTimeList = (data) => {
    return axios.request({
        url: DATA_URL2 + '/get/LIDAR/DataTime',
        method: 'GET',
        params: data
    });
};

// 雷达-雷达数据
export const lidarData = (data) => {
    return axios.request({
        url: DATA_URL2 + '/get/LIDAR/data',
        method: 'GET',
        params: data
    });
};
