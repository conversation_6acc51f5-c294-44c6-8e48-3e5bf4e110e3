<!-- @format -->

<template>
    <div>
        <!-- 导航 -->
        <Navigation :map="map" :mapOption="mapOption"></Navigation>

        <!-- 地图测量 -->
        <MapMeasure :map="map"></MapMeasure>

        <!-- 地图全屏 -->
        <FullScreen :map="map" :htmlRoot="htmlRoot"></FullScreen>
    </div>
</template>

<script>
import Navigation from '@/components/gis/2D/Navigation.vue';
import MapMeasure from '@/components/gis/2D/MapMeasure';
import FullScreen from '@/components/gis/2D/FullScreen';

export default {
    name: 'TopRightTool',
    props: ['map', 'mapOption', 'dock', 'htmlRoot'],
    data() {
        return {};
    },
    components: {
        Navigation,
        MapMeasure,
        FullScreen
    },
    computed: {},
    mounted() {},
    methods: {},
    watch: {}
};
</script>

<style scoped></style>
