<!-- @format -->

<template>
    <el-container class="main" direction="vertical" style="height: 100%">
        <el-main class="gisMain" style="padding: 0px; overflow: hidden">
            <div style="width: 100%; height: 100%">
                <MapboxMap
                    :mapOption="mapOption"
                    @onMapLoaded="onMapLoadedHandle"
                ></MapboxMap>
            </div>

            <!--  基础工具 (右上) -->
            <TopRightTool
                class="baseTool"
                :map="map"
                :mapCls="mapCls"
                :class="{ off: !showPanel }"
                :dock="toolDock"
            ></TopRightTool>

            <!-- 右下角 -->
            <!-- <BottomRightTool
                class="bottomRightToolCls"
                :class="{ off: !showPanel }"
            ></BottomRightTool> -->

            <!-- 业务面板 -->
            <BasicMain
                :map="map"
                @mapEvent="mapEventHandle"
                ref="childMain"
            ></BasicMain>
        </el-main>
    </el-container>
</template>
<script>
import MapboxMap from '@/components/gis/3D/MapBoxGLMap';
import BottomRightTool from '@/components/gis/3D/normal/BottomRightTool';
import TopRightTool from '@/components/gis/3D/normal/TopRightTool';
import BasicMain from './BasicMain';

export default {
    name: 'BasicFrameworkLayout', //基础框架源码
    created() {},
    data() {
        return {
            mapOption: {},
            map: null,

            toolDock: 'right',

            showPanel: false,
            mapCls: '.gisMain'
        };
    },
    unmounted() {},
    components: {
        MapboxMap,
        TopRightTool,
        BottomRightTool,
        BasicMain
    },
    mounted() {},
    methods: {
        //地图加载完成时触发
        onMapLoadedHandle(map) {
            this.map = map;
        },

        //地图事件：与前端交互
        mapEventHandle(type, item) {
            //type：mapClick(地图点位点击)、
            this.$emit('mapEvent', type, item);
        },

        //地图交互方法：供前端调用
        mapFuc(type, item) {
            switch (type) {
                case 'RPanel': //右侧面板
                    this.showPanel = item;
                    // this.$refs.childMain.showPanleChangeHandle(this.showPanel);
                    break;

                case 'pointTo': //定位(item中必须包含JD、WD字段)
                    PowerGL.pointTo(this.map, item, 14);
                    break;
            }
        }
    }
};
</script>

<style>
@import '~_as/gis/commom/mapCommon.css';
@import '~_as/gis/commom/mapTool.css';
@import '~_as/gis/3D/BasicFramework/css/style.css';
</style>

<style scoped>
.baseTool {
    position: absolute;
    top: 90px;
    right: inherit;
    bottom: inherit;
}

.baseTool.off {
    right: 10px;
}

.bottomRightToolCls {
    position: absolute;
    bottom: 10px;
    right: 520px;
}

.bottomRightToolCls.off {
    right: 10px;
}
</style>
