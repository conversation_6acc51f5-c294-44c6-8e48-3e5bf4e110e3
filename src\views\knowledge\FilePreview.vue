<!-- @format -->

<template>
    <el-dialog
        class="dialog_custom"
        v-if="isVideo"
        v-model="show"
        title="查看"
        :width="1024"
        :before-close="closePreviewDialog"
    >
        <!-- <iframe
            :src="url"
            frameborder="0"
            style="width: 100%; height: 576px"
        ></iframe> -->

        <video-player
            v-if="videoDataReady && show"
            :options="videoOptions"
            :watermark="watermark"
            :videoProgress="videoProgress"
        />
    </el-dialog>

    <el-dialog
        class="dialog_custom"
        v-else
        v-model="show"
        title="查看"
        :fullscreen="true"
        :before-close="closePreviewDialog"
    >
        <iframe
            id="myIframe"
            v-if="show"
            :src="url"
            frameborder="0"
            allowfullscreen
            style="width: 100%; height: 100%"
        ></iframe>
    </el-dialog>

    <el-dialog
        v-model="showTips"
        title="提示"
        width="30%"
        top="30vh"
        :show-close="false"
    >
        <span class="v-tips">{{
            isSwitchOut
                ? '检测到页面切换到后台，已暂停学习'
                : '您还在屏幕前吗？'
        }}</span>
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    @click="
                        startRecord();
                        resetTimeout();
                        showTips = false;
                    "
                >
                    {{ isSwitchOut ? '继续学习' : '是的' }}
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import VideoPlayer from './VideoPlayer.vue';
import {
    recordClassHours,
    recordVideoProgress,
    getVideoProgress
} from '@/api/knowledge';
export default {
    name: 'FilePreview',
    components: {
        VideoPlayer
    },
    props: ['url', 'fileInfo', 'isVideo', 'watermark', 'previewData'],
    inject: ['closePreview'],
    provide() {
        return {
            startRecord: this.startRecord,
            stopRecord: this.stopRecord,
            progressChange: this.progressChange,
            recordVideoWatchTimes: this.recordVideoWatchTimes
        };
    },
    data() {
        return {
            show: false,
            videoDataReady: false,
            videoOptions: {
                autoplay: false,
                controls: true,
                errorDisplay: false,
                preload: 'auto', // 自动加载
                sources: []
            },
            startTime: null,
            readTime: 0,
            recordId: '',
            timer: null, //学习记录定时器
            documentReadingTimer: null, //阅读离开检测定时器
            isSwitchOut: false, //true 切换至后台暂停提示 false定时暂停提示
            showTips: false,
            videoProgressId: '',
            videoProgress: 0
        };
    },
    created() {},
    unmounted() {},
    watch: {
        show() {
            if (this.show) {
                this.startTime = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
                if (this.isVideo) {
                    this.getVideoWatchTimes();
                    this.videoOptions.sources = [
                        {
                            src: `${ServerGlobalConstant.videoStreamUrl}${this.fileInfo.WJID}`,
                            type: 'video/mp4'
                        }
                    ];
                    this.videoDataReady = true;
                } else {
                    this.$nextTick(() => {
                        // this.installListener();
                    });
                    this.startRecord();
                }
            } else {
                this.stopRecord();
                this.startTime = null;
                this.readTime = 0;
                this.recordId = '';
            }
        }
    },
    mounted() {},
    methods: {
        onVisibilityChange() {
            if (document.hidden) {
                this.isSwitchOut = true;
                this.showTips = true;
            }
        },
        installListener() {
            this.listenDocumentReading();

            // 监听document visibilityState变化事件（用于监听页面是否被隐藏）
            document.addEventListener(
                'visibilitychange',
                this.onVisibilityChange
            );
        },
        uninstallListener() {
            this.removeListenDocumentReading();

            // 解除监听事件
            document.removeEventListener(
                'visibilitychange',
                this.onVisibilityChange
            );
        },
        //监听阅读文档或图片鼠标不动，移动、滚动重置倒计时
        listenDocumentReading() {
            this.resetTimeout();
            let iframe = document.getElementById('myIframe');
            iframe.addEventListener('load', () => {
                iframe.contentWindow.addEventListener(
                    'scroll',
                    this.resetTimeout
                );
                iframe.contentWindow.addEventListener(
                    'mousemove',
                    this.resetTimeout
                );
            });
        },
        removeListenDocumentReading() {
            clearTimeout(this.documentReadingTimer);
            let iframe = document.getElementById('myIframe');
            iframe.contentWindow.removeEventListener(
                'scroll',
                this.resetTimeout
            );
            iframe.contentWindow.removeEventListener(
                'mousemove',
                this.resetTimeout
            );
        },
        resetTimeout() {
            clearTimeout(this.documentReadingTimer);
            this.documentReadingTimer = setTimeout(() => {
                this.stopRecord();
                this.showTips = true;
            }, 180000);
        },
        progressChange(progress) {
            this.videoProgress = progress;
        },
        showPreviewDialog() {
            this.show = true;
        },
        closePreviewDialog() {
            if (!this.isVideo) {
                // this.uninstallListener();
            }
            this.show = false;
            this.closePreview();
        },

        startRecord() {
            let step = 0.5; //单次0.5分钟
            this.timer = setInterval(() => {
                this.readTime += step;
                if (this.readTime % 0.5 == 0) {
                    this.recordClassHours();
                }
                if (this.isVideo) {
                    this.recordVideoWatchTimes();
                }
            }, 60000 * step);
        },
        stopRecord() {
            clearInterval(this.timer);
        },
        getVideoWatchTimes() {
            getVideoProgress({
                ZSKBH: this.previewData.XH
            }).then((res) => {
                if (res.result.BFJZ) {
                    this.videoProgress = Number(res.result.BFJZ);
                    this.videoProgressId = res.result.XH;
                }
            });
        },
        recordVideoWatchTimes(progress) {
            recordVideoProgress({
                ZSKBH: this.previewData.XH,
                XH: this.videoProgressId,
                BFJZ: progress || this.videoProgress
            }).then((res) => {
                if (!this.videoProgressId) {
                    this.videoProgressId = res.result;
                }
            });
        },
        recordClassHours() {
            recordClassHours({
                ZSKBH: this.previewData.XH || '',
                XH: this.recordId || '',
                XXSJ: this.startTime,
                XXSC: this.readTime
            }).then((res) => {
                if (res.result) this.recordId = res.result;
            });
        }
    }
};
</script>

<style>
.dialog_custom .el-dialog__body {
    height: calc(100% - 44px);
    width: 100%;
    padding: 0;
}
</style>
