/** @format */

import mercatorVert from './shaders/mercator.vert.js';
import mercatorFrag from './shaders/mercator.frag.js';
import stretchYVert from './shaders/stretchY.vert.js';
import stretchYFrag from './shaders/stretchY.frag.js';
import blendVert from './shaders/blend.vert.js';
import blendFrag from './shaders/blend.frag.js';
// import * as earcut from 'earcut';
import {
    createShader,
    createProgram,
    createTexture,
    bindTexture,
    createBuffer,
    createIndexBuffer,
    bindAttribute,
    bindAttribute2,
    bindFramebuffer
} from './util/ShaderUtil';

export default class Stretch {
    constructor(option) {
        this.gl = null;
        this.time = null;
        this.lngLat = null;
        this.vertices = null;
        this.mercatorProgram = null;
        this.stretchYProgram = null;
        this.trianglesIndexes = null;
        this.coordinatesData = null;
        this.framebuffer = null;
        this.texture1 = null;
        this.texture2 = null;
        this.stretchHeight = 20; //拉伸高度pix
        this.color = [0, 73, 79, 255];
        this.offset = 2; //拉伸偏移pix
        Object.assign(this, option);
    }

    init(gl) {
        this.gl = gl;
        this.framebuffer = gl.createFramebuffer();
        this.mercatorProgram = createProgram(gl, mercatorVert, mercatorFrag);
        let fragStr = stretchYFrag
            .replace('{stretch_height}', this.stretchHeight)
            .replace('{stretch_offset}', this.offset);
        let wrapper = createProgram(gl, stretchYVert, fragStr);
        wrapper['vertexBuffer'] = createBuffer(
            gl,
            new Float32Array([-1, -1, 1, -1, 1, 1, -1, 1])
        );
        wrapper['uvBuffer'] = createBuffer(
            gl,
            new Float32Array([0, 0, 1, 0, 1, 1, 0, 1])
        );
        wrapper['numComponents'] = 2;
        wrapper['uvComponents'] = 2;
        wrapper['indexBuffer'] = createIndexBuffer(
            gl,
            new Uint16Array([0, 1, 3, 1, 3, 2])
        );
        wrapper['indexLength'] = 6;
        this.stretchYProgram = wrapper;

        this.texture1 = createTexture(
            gl,
            gl.LINEAR,
            null,
            gl.drawingBufferWidth,
            gl.drawingBufferHeight
        );
        this.texture2 = createTexture(
            gl,
            gl.LINEAR,
            null,
            gl.drawingBufferWidth,
            gl.drawingBufferHeight
        );
        wrapper = createProgram(gl, blendVert, blendFrag);
        wrapper['vertexBuffer'] = createBuffer(
            gl,
            new Float32Array([-1, -1, 1, -1, 1, 1, -1, 1])
        );
        wrapper['uvBuffer'] = createBuffer(
            gl,
            new Float32Array([0, 0, 1, 0, 1, 1, 0, 1])
        );
        wrapper['numComponents'] = 2;
        wrapper['uvComponents'] = 2;
        wrapper['indexBuffer'] = createIndexBuffer(
            gl,
            new Uint16Array([0, 1, 3, 1, 3, 2])
        );
        wrapper['indexLength'] = 6;
        this.blendProgram = wrapper;

        console.log('Stretch');
    }
    //
    initData(coordinates) {
        // 使用插值点来生成曲线路径
        const smoothness = 0.004; // 插值的平滑度，根据需要调整
        // const interpolatedCoordinates = this.getBezierPoints(
        //     coordinates[0],
        //     smoothness
        // );
        let data = earcut.flatten(coordinates);
        this.coordinatesData = data;
        this.trianglesIndexes = earcut(data.vertices, null, data.dimensions);

        this.initDraw();
    }

    initDraw() {
        let wrapper = this.mercatorProgram;
        wrapper['vertexBuffer'] = createBuffer(
            this.gl,
            new Float32Array(this.coordinatesData.vertices)
        );
        wrapper['indexBuffer'] = createIndexBuffer(
            this.gl,
            new Uint16Array(this.trianglesIndexes)
        );
        wrapper['numComponents'] = this.coordinatesData.dimensions;
        wrapper['indexLength'] = this.trianglesIndexes.length;
    }

    // 首先定义一个函数用于计算贝塞尔曲线的插值点
    getBezierPoints(points, smoothness) {
        const interpolatedPoints = [];

        // 添加起点
        interpolatedPoints.push(points[0]);

        for (let i = 0; i < points.length - 2; i++) {
            const pointA = points[i];
            const pointB = points[i + 1];
            const pointC = points[i + 2];

            // 计算贝塞尔曲线的插值点
            for (let t = 0; t <= 1; t += smoothness) {
                const x =
                    (1 - t) * (1 - t) * pointA[0] +
                    2 * (1 - t) * t * pointB[0] +
                    t * t * pointC[0];
                const y =
                    (1 - t) * (1 - t) * pointA[1] +
                    2 * (1 - t) * t * pointB[1] +
                    t * t * pointC[1];

                interpolatedPoints.push([x, y]);
            }
        }
        return interpolatedPoints;
    }

    render(matrix, map) {
        bindFramebuffer(this.gl, this.framebuffer, this.texture1);
        this.gl.clearColor(0, 0, 0, 0);
        this.gl.clear(this.gl.COLOR_BUFFER_BIT);
        this.renderMercator(matrix, map);

        bindFramebuffer(this.gl, this.framebuffer, this.texture2);
        this.gl.clearColor(0, 0, 0, 0);
        this.gl.clear(this.gl.COLOR_BUFFER_BIT);
        this.renderStretchY(matrix, map);

        bindFramebuffer(this.gl, null);
        this.renderBlend(matrix, map);

        // this.renderMercator(matrix, map);
    }

    renderMercator(matrix, map) {
        let gl = this.gl;
        let wrapper = this.mercatorProgram;
        gl.useProgram(wrapper.program);
        gl.uniformMatrix4fv(wrapper.u_matrix, false, matrix);
        gl.bindBuffer(gl.ARRAY_BUFFER, wrapper.vertexBuffer);
        bindAttribute(
            gl,
            wrapper.vertexBuffer,
            wrapper.position,
            wrapper.numComponents
        );
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, wrapper.indexBuffer);
        gl.drawElements(
            gl.TRIANGLES,
            wrapper.indexLength,
            gl.UNSIGNED_SHORT,
            0
        );
    }

    renderStretchY(matrix, map) {
        let gl = this.gl;
        let wrapper = this.stretchYProgram;
        gl.useProgram(wrapper.program);
        gl.bindBuffer(gl.ARRAY_BUFFER, wrapper.vertexBuffer);
        bindAttribute(
            gl,
            wrapper.vertexBuffer,
            wrapper.a_Position,
            wrapper.numComponents
        );
        gl.bindBuffer(gl.ARRAY_BUFFER, wrapper.uvBuffer);
        bindAttribute(gl, wrapper.uvBuffer, wrapper.a_UV, wrapper.uvComponents);
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, wrapper.indexBuffer);

        gl.uniform1f(wrapper.tex_height, gl.drawingBufferHeight);
        // gl.uniform1f(wrapper.u_stretch_height, this.stretchHeight);
        bindTexture(gl, this.texture1, 0);
        gl.uniform1i(wrapper.u_Texture, 0);
        gl.drawElements(
            gl.TRIANGLES,
            wrapper.indexLength,
            gl.UNSIGNED_SHORT,
            0
        );
    }

    renderBlend(matrix, map) {
        let gl = this.gl;
        let wrapper = this.blendProgram;
        gl.useProgram(wrapper.program);
        gl.bindBuffer(gl.ARRAY_BUFFER, wrapper.vertexBuffer);
        bindAttribute(
            gl,
            wrapper.vertexBuffer,
            wrapper.a_Position,
            wrapper.numComponents
        );
        gl.bindBuffer(gl.ARRAY_BUFFER, wrapper.uvBuffer);
        bindAttribute(gl, wrapper.uvBuffer, wrapper.a_UV, wrapper.uvComponents);
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, wrapper.indexBuffer);

        let color = this.color.map((v) => v / 255);
        gl.uniform4f(wrapper.u_Color, ...color);

        bindTexture(gl, this.texture2, 0);
        gl.uniform1i(wrapper.u_Texture, 0);
        bindTexture(gl, this.texture1, 1);
        gl.uniform1i(wrapper.u_TextureOrigin, 1);
        gl.drawElements(
            gl.TRIANGLES,
            wrapper.indexLength,
            gl.UNSIGNED_SHORT,
            0
        );
    }
}
