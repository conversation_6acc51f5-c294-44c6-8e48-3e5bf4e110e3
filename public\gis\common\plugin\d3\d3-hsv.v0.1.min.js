// https://d3js.org/d3-hsv/ Version 0.1.0. Copyright 2017 <PERSON>.
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("d3-color")):"function"==typeof define&&define.amd?define(["exports","d3-color"],n):n(t.d3=t.d3||{},t.d3)}(this,function(t,n){"use strict";function i(t){if(t instanceof e)return new e(t.h,t.s,t.v,t.opacity);t instanceof n.rgb||(t=n.rgb(t));var i=t.r/255,r=t.g/255,o=t.b/255,s=Math.min(i,r,o),h=Math.max(i,r,o),u=h-s,a=NaN,c=u/h,f=h;return u&&(a=i===h?(r-o)/u+6*(r<o):r===h?(o-i)/u+2:(i-r)/u+4,a*=60),new e(a,c,f,t.opacity)}function r(t,n,r,o){return 1===arguments.length?i(t):new e(t,n,r,null==o?1:o)}function e(t,n,i,r){this.h=+t,this.s=+n,this.v=+i,this.opacity=+r}function o(t,i,r,e,o){return n.rgb(255*(t+e),255*(i+e),255*(r+e),o)}function s(t,n){return function(i){return t+i*n}}function h(t,n){var i=n-t;return i?s(t,i>180||i<-180?i-360*Math.round(i/360):i):c(isNaN(t)?n:t)}function u(t){return function(n,i){var e=t((n=r(n)).h,(i=r(i)).h),o=f(n.s,i.s),s=f(n.v,i.v),h=f(n.opacity,i.opacity);return function(t){return n.h=e(t),n.s=o(t),n.v=s(t),n.opacity=h(t),n+""}}}var a=e.prototype=r.prototype=Object.create(n.color.prototype);a.constructor=e,a.brighter=function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new e(this.h,this.s,this.v*t,this.opacity)},a.darker=function(t){return t=null==t?.7:Math.pow(.7,t),new e(this.h,this.s,this.v*t,this.opacity)},a.rgb=function(){var t=isNaN(this.h)?0:this.h%360+360*(this.h<0),n=isNaN(this.h)||isNaN(this.s)?0:this.s,i=this.v,r=this.opacity,e=i*n,s=e*(1-Math.abs(t/60%2-1)),h=i-e;return t<60?o(e,s,0,h,r):t<120?o(s,e,0,h,r):t<180?o(0,e,s,h,r):t<240?o(0,s,e,h,r):t<300?o(s,0,e,h,r):o(e,0,s,h,r)},a.displayable=function(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.v&&this.v<=1&&0<=this.opacity&&this.opacity<=1};var c=function(t){return function(){return t}},f=function(t,n){var i=n-t;return i?s(t,i):c(isNaN(t)?n:t)},p=u(h),v=u(f);t.hsv=r,t.interpolateHsv=p,t.interpolateHsvLong=v,Object.defineProperty(t,"__esModule",{value:!0})});