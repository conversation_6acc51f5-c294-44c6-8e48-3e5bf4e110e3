<!-- @format -->
<!-- 拉升面 -->
<template>
    <div></div>
</template>

<script>
import PointUtil from '../utils/PointUtil';
export default {
    props: [],
    data() {
        return {
            map: null,
            pointUtil: null,
            layerID: '拉升面'
        };
    },

    unmounted() {
        this.clear();
    },

    mounted() {
        this.initPage();
    },

    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.addLayer();
        },

        addLayer() {
            fetch('./gis/3D/data/hjyjkj.json')
                .then((res) => res.json())
                .then((json) => {
                    let params = {
                        id: this.layerID,
                        beforeId: '',
                        disableClick: true,
                        disablePopup: true
                    };

                    this.pointUtil.addExtrusionData(json, params);
                });
        },

        clear() {
            this.pointUtil.removeLayerByName(this.layerID);
        },

        pointClickHandle() {}
    }
};
</script>

<style scoped></style>
