<!-- @format -->

<template>
    <div
        class="sw1212-wrap"
        style="background: #fff; width: 100%; height: 100%"
    >
        <div>
            <!-- <div class="sw1212-header">
                <img
                    src="@/assets/exam/images/sw1212_logo.png"
                    class="sw1212-logo"
                />
            </div> -->
            <div style="padding: 20px; top: 0">
                <!-- 年监测人数分析 -->
                <Annual v-if="type == 'Annual'" />
                <!-- 个人剂量分析 -->
                <Dose v-if="type == 'Dose'" />
                <!-- 个人剂量分布 -->
                <DoseSpread v-if="type == 'DoseSpread'" />
                <!-- 任务剂量分析 -->
                <DoseTask v-if="type == 'DoseTask'" />
                <!-- 监管执法工作量分析 -->
                <Supervise v-if="type == 'Supervise'" />
            </div>
        </div>
    </div>
</template>

<script>
import Annual from './Annual.vue';
import Dose from './Dose.vue';
import DoseSpread from './DoseSpread.vue';
import DoseTask from './DoseTask.vue';
import Supervise from './Supervise.vue';
export default {
    components: {
        Annual,
        Dose,
        DoseSpread,
        DoseTask,
        Supervise
    },
    provide() {
        return {};
    },
    data() {
        return {
            type: ''
        };
    },
    created() {
        //
    },
    computed: {},
    mounted() {
        this.type = this.$route.query.type;
    },
    methods: {}
};
</script>

<style lang="scss" scoped></style>
