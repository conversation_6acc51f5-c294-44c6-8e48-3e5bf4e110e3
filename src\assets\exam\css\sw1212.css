@font-face {
  font-family: "DIN-Medium";
  src: url("../fonts/DIN-Medium.woff2") format("woff2"), url("../fonts/DIN-Medium.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "DIN-Bold";
  src: url("../fonts/DIN-Bold.woff2") format("woff2"), url("../fonts/DIN-Bold.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "DingTalk-JinBuTi";
  src: url("../fonts/DingTalk-JinBuTi.woff2") format("woff2"), url("../fonts/DingTalk-JinBuTi.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}
.gap15 {
  height: 15px;
}

.gap16 {
  height: 16px;
}

.sw1212-wrap {
  width: 1920px;
  height: 1080px;
  position: absolute;
  left: 0;
  top: 0;
}

.sw1212-header {
  height: 60px;
  background: #467ddc;
}

.sw1212-logo {
  margin: 10px 18px;
}

.sw1212-container {
  position: absolute;
  left: 0;
  right: 0;
  top: 60px;
  bottom: 0;
  background-color: #eeecec;
}

.sw1212-lcon {
  width: 66.6%;
}

.sw1212-rcon {
  flex: 1;
  margin-left: 20px;
}

.sw1212-banner {
  background: url(../images/sw1212_banner.png) no-repeat;
  height: 171px;
  position: relative;
  padding: 0 65px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-size: cover;
  background-position: center 0;
  border-radius: 10px;
}
.sw1212-banner .sw1212-inpsrh1 {
  width: 619px;
  height: 50px;
  background: rgba(255, 255, 255, 0.45) url(../images/sw1212_ic3.png) no-repeat 17px center;
  border-radius: 300px;
  font-size: 16px;
  color: #333;
  text-indent: 38px;
}
.sw1212-banner .sw1212-inpsrh1::-moz-placeholder {
  color: #8591a5;
}
.sw1212-banner .sw1212-inpsrh1::placeholder {
  color: #8591a5;
}
.sw1212-banner h1 {
  font-size: 30px;
  color: #171717;
  font-weight: bold;
  padding-left: 10px;
  padding-bottom: 15px;
}
.sw1212-banner sup {
  position: absolute;
  right: 23px;
  top: 23px;
  display: flex;
  align-items: center;
}
.sw1212-banner sup em {
  width: 1px;
  height: 12px;
  background: #4a4a4a;
  margin: 0 10px;
}
.sw1212-banner sup i {
  font-size: 20px;
  color: #171717;
}
.sw1212-banner sup i.i1 {
  font-family: "DIN-Medium";
  /* background: url(../images/sw1212_ic1.png) no-repeat left center; */
  padding-left: 20px;
}
.sw1212-banner sup i.i2 {
  background: url(../images/sw1212_ic2.png) no-repeat left center;
  padding-left: 20px;
}

.sw1212-mod {
  background: #ffffff;
  border-radius: 12px;
  padding: 0 16px;
}

.sw1212-txt1 {
  font-size: 23px;
  color: #171717;
}

.sw1212-ullst1 li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 46px;
  padding-left: 20px;
  cursor: pointer;
}
.sw1212-ullst1 li span {
  font-size: 20px;
  color: #666;
  position: relative;
  padding-left: 12px;
}
.sw1212-ullst1 li span.ico::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -3px;
  width: 6px;
  height: 6px;
  background: #c4c4c4;
  border-radius: 50%;
}
.sw1212-ullst1 li + li {
  border-top: 1px solid #eee;
}
.sw1212-ullst1 li.on {
  background: url(../images/sw1212_ic4.png) no-repeat left center;
}
.sw1212-ullst1 li.on span {
  color: #333;
}
.sw1212-ullst1 li.on span::before {
  content: "";
  background: #467ddc;
}

.sw1212-slideleft1 {
  position: relative;
}
.sw1212-slideleft1 .hd .prev,
.sw1212-slideleft1 .hd .next {
  position: absolute;
  top: 30%;
  width: 28px;
  height: 28px;
  cursor: pointer;
}
.sw1212-slideleft1 .hd .prev {
  left: 0;
}
.sw1212-slideleft1 .hd .prev::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  width: 94px;
  height: 94px;
  background: url(../images/sw1212_prev.png) no-repeat;
  margin: -47px 0 0 -47px;
}
.sw1212-slideleft1 .hd .next {
  right: 0;
}
.sw1212-slideleft1 .hd .next::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  width: 94px;
  height: 94px;
  background: url(../images/sw1212_next.png) no-repeat;
  margin: -47px 0 0 -47px;
}
.sw1212-slideleft1 .bd {
  padding: 0 30px;
  overflow: hidden;
  margin-right: 40px;
}

.sw1212-ullst2 li {
  float: left;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 250px;
  height: 69px;
  background: #fcf7ef;
  border-radius: 4px;
  border: 1px solid #f9e3c1;
  padding: 0 20px;
  font-size: 18px;
  color: #3d3d3d;
  line-height: 69px;
  position: relative;
  margin: 11px 10px;
}

.sw1212-ullst2 li .text {
  width: 100%;
  height: 100%;
  line-height: 33px;
  font-size: 18px;
  color: #3d3d3d;
  display: -webkit-box !important;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sw1212-ullst2 li sup {
  position: absolute;
  right: -11px;
  top: -11px;
  cursor: pointer;
  background: url(../images/sw1212_cls1.png) no-repeat;
  width: 22px;
  height: 22px;
}

.sw1212-ullst3 {
  display: flex;
  justify-content: space-between;
}
.sw1212-ullst3 li {
  width: 166px;
  height: 217px;
  background: url(../images/sw1212_bg1.png) no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
/* .sw1212-ullst3 li.on {
  transform: translateY(-15px);
  background-image: url(../images/sw1212_bg1_on.png);
} */
.sw1212-ullst3 li:hover {
  transform: translateY(-15px);
  background-image: url(../images/sw1212_bg1_on.png);
}
.sw1212-ullst3 li h1 {
  font-size: 23px;
  color: #333;
}
.sw1212-ullst3 li p {
  font-size: 20px;
  color: #467ddc;
  margin-top: 15px;
}
.sw1212-ullst3 li p i {
  font-size: 32px;
  font-family: "DIN-Bold";
}

.sw1212-ulbx1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.sw1212-ulbx1 li {
  width: calc(50% - 8px);
  height: 95px;
  background: #f6f8fe;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  box-sizing: border-box;
}
.sw1212-ulbx1 li dl dt {
  font-size: 23px;
  color: #666;
}
.sw1212-ulbx1 li dl dd {
  font-size: 16px;
  margin-top: 10px;
}
.sw1212-ulbx1 li dl dd i {
  font-size: 25px;
  font-family: "DIN-Bold";
}

.sw1212-ulbx2 {
  display: flex;
  flex-wrap: wrap;
  /* margin-left: -13%; */
  margin-top: -20px;
}
.sw1212-ulbx2 li {
  width: 153px;
  padding-left: 10%;
  padding-top: 20px
}
.sw1212-ulbx2 li .img {
  width: 153px;
  height: 184px;
}
.sw1212-ulbx2 li p {
  font-size: 20px;
  color: #333;
  padding-top: 8px;
}

.sw1212-lside {
  width: 250px;
  background: #fff;
}

.sw1212-rmain {
  flex: 1;
  margin-left: 17px;
  background: #fff;
}

.sw1212-topbar {
  background: url(../images/sw1212_banner2.png) no-repeat;
  height: 220px;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.sw1212-topbar.type2 {
  height: 130px;
  background-size: cover;
}
.sw1212-topbar dl {
  width: 860px;
}
.sw1212-topbar dl dt {
  font-size: 46px;
  font-family: "DingTalk-JinBuTi";
  color: #467ddc;
}
.sw1212-topbar dl dd {
  height: 58px;
  display: flex;
  align-items: center;
  margin-top: 14px;
}
.sw1212-topbar dl dd .sw1212-inptxt1 {
  width: 751px;
  height: 58px;
  background: rgba(255, 255, 255, 0.85) url(../images/sw1212_ic3.png) no-repeat 12px center;
  border-radius: 6px 0px 0px 6px;
  font-size: 16px;
  color: #333;
  text-indent: 36px;
}
.sw1212-topbar dl dd .sw1212-inptxt1::-moz-placeholder {
  color: #999;
}
.sw1212-topbar dl dd .sw1212-inptxt1::placeholder {
  color: #999;
}
.sw1212-topbar dl dd .sw1212-btn1 {
  width: 108px;
  height: 58px;
  background: #467ddc;
  border-radius: 0px 6px 6px 0px;
  font-size: 18px;
  color: #fff;
  cursor: pointer;
}

.sw1212-botbar {
  padding: 0 30px;
}

.sw1212-dlbx1 {
  padding: 18px 0;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: flex-start;
}
.sw1212-dlbx1 dt {
  font-size: 16px;
  color: #3d3d3d;
  font-weight: bold;
}
.sw1212-dlbx1 dd {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  margin-left: -54px;
  padding-right: 50px;
  margin-top: -15px;
}
.sw1212-dlbx1 dd span {
  font-size: 16px;
  color: #333333;
  cursor: pointer;
  margin-left: 54px;
  margin-top: 15px;
}
.sw1212-dlbx1 dd span.on {
  color: #467ddc;
}

.sw1212-ultbs1 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.sw1212-ultbs1.type2 {
  justify-content: start;
}
.sw1212-ultbs1 li {
  font-size: 16px;
  color: #333;
  border-bottom: 4px solid transparent;
  padding-bottom: 8px;
}
.sw1212-ultbs1 li + li {
  margin-left: 58px;
}
.sw1212-ultbs1 li.on {
  color: #467ddc;
  font-weight: bold;
  border-bottom-color: #467ddc;
}

.sw1212-ultbs2 {
  display: flex;
  align-items: center;
}
.sw1212-ultbs2.abs {
  right: 0;
  top: 5px;
}
.sw1212-ultbs2 li {
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.sw1212-ultbs2 li.li1 {
  background: url(../images/sw1212_ic1a.png) no-repeat;
}
.sw1212-ultbs2 li.li1.on {
  background-image: url(../images/sw1212_ic1a_on.png);
}
.sw1212-ultbs2 li.li2 {
  background: url(../images/sw1212_ic2a.png) no-repeat;
}
.sw1212-ultbs2 li.li2.on {
  background-image: url(../images/sw1212_ic2a_on.png);
}
.sw1212-ultbs2 li + li {
  margin-left: 15px;
}

.sw1212-cardbx1 {
  padding: 0 3%;
}
.sw1212-cardbx1 .row {
  margin: 0 -20px;
}
.sw1212-cardbx1 .row .col {
  width: 20%;
  padding: 0 20px;
}

.sw1212-dlbx2 {
  height: 266px;
  border-radius: 4px;
  border: 1px solid #eee;
}
.sw1212-dlbx2 dt {
  border-bottom: 1px solid #eee;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 172px;
}
.sw1212-dlbx2 dt sup.star {
  position: absolute;
  right: 15px;
  top: 12px;
  width: 16px;
  height: 16px;
  background: url(../images/sw1212_ic3a.png) no-repeat;
  cursor: pointer;
}
.sw1212-dlbx2 dt sup.star.on {
  background-image: url(../images/sw1212_ic4a.png);
}
.sw1212-dlbx2 dt sup.share {
    position: absolute;
    right: 40px;
    top: 12px;
    width: 16px;
    height: 16px;
    background: url(../images/unshare.png) no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }
  .sw1212-dlbx2 dt sup.share.on {
    background-image: url(../images/shared.png);
    background-size: 100% 100%;
  }
.sw1212-dlbx2 dt .img {
  width: 106px;
  height: 128px;
}
.sw1212-dlbx2 dd {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 15px;
}
.sw1212-dlbx2 dd h1 {
  font-size: 18px;
  color: #333;
  font-weight: bold;
}
.sw1212-dlbx2 dd p {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
}
.sw1212-dlbx2 dd p span {
  font-size: 14px;
  /* color: #999; */
}
.sw1212-dlbx2 dd p i {
  width: 73px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: 300px;
  font-size: 14px;
}
.sw1212-dlbx2 dd p i.type1 {
  color: #467ddc;
  background: #f3f7ff;
}
.sw1212-dlbx2 dd p i.type2 {
  color: #41b037;
  background: #e9f4e8;
}
.sw1212-dlbx2 dd p i.type3 {
  color: #4649dc;
  background: #e6e7fa;
}

.sw1212-tablelst1 {
  width: 100%;
}
.sw1212-tablelst1 td {
  font-size: 18px;
  color: #333;
  padding: 15px;
  border-bottom: 1px solid #eee;
}
.sw1212-tablelst1 td.gray span {
  color: #666;
}
.sw1212-tablelst1 td .type {
  display: inline-block;
  width: 73px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-size: 14px;
  color: #467ddc;
  background: #f3f7ff;
  border-radius: 300px;
}
.sw1212-tablelst1 td .star {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url(../images/sw1212_ic3a.png) no-repeat;
  cursor: pointer;
}
.sw1212-tablelst1 td .star.on {
  background-image: url(../images/sw1212_ic4a.png);
}
.sw1212-tablelst1 td .share {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url(../images/unshare.png) no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }
  .sw1212-tablelst1 td .share.on {
    background-image: url(../images/shared.png);
    background-size: 100% 100%;

  }

.sw1212-data1 h1 {
  font-size: 24px;
  color: #333;
  font-weight: bold;
}
.sw1212-data1 p {
  font-size: 40px;
  font-family: "DIN-Bold";
  color: #467ddc;
  padding-top: 8px;
}

.sw1212-ulbx3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f1f3f7;
  padding-right: 18px;
}
.sw1212-ulbx3 li {
  width: 311px;
  height: 120px;
  display: flex;
  align-items: center;
  padding-left: 30px;
  box-sizing: border-box;
}
.sw1212-ulbx3 li .icon {
  width: 70px;
  height: 70px;
}
.sw1212-ulbx3 li strong {
  font-size: 20px;
  color: #fff;
  margin-left: 22px;
}
.sw1212-ulbx3 li.li1 {
  background: url(../images/sw1212_bg1a.png) no-repeat;
}
.sw1212-ulbx3 li.li2 {
  background: url(../images/sw1212_bg2a.png) no-repeat;
}
.sw1212-ulbx3 li.li3 {
  background: url(../images/sw1212_bg3a.png) no-repeat;
}
.sw1212-ulbx3 li.li4 {
  background: url(../images/sw1212_bg4a.png) no-repeat;
}
.sw1212-ulbx3 li.li5 {
  background: url(../images/sw1212_bg5a.png) no-repeat;
}

.sw1212-dlghd {
  height: 50px;
  background: #467DDC;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}
.sw1212-dlghd strong {
  font-size: 20px;
  color: #fff;
}

.sw1212-dlgbd {
  position: absolute;
  left: 0;
  top: 50px;
  right: 0;
  bottom: 0;
}

.sw1212-hdpic {
  width: 130px;
  height: 130px;
}

.sw1212-dlbx3 {
  flex: 1;
  margin-left: 20px;
}
.sw1212-dlbx3 dt {
  font-size: 22px;
  color: #3D3D3D;
  font-weight: bold;
}
.sw1212-dlbx3 dd {
  padding-top: 5px;
}

.sw1212-table1 {
  width: 100%;
}
.sw1212-table1 td {
  padding: 5px 0;
}
.sw1212-table1 td span {
  font-size: 16px;
  color: #666;
  padding-left: 25px;
}
.sw1212-table1 td span i {
  color: #467DDC;
}
.sw1212-table1 td span.ic1 {
  background: url(../images/sw1212_ic1c.png) no-repeat left center;
}
.sw1212-table1 td span.ic2 {
  background: url(../images/sw1212_ic2c.png) no-repeat left center;
}
.sw1212-table1 td span.ic3 {
  background: url(../images/sw1212_ic3c.png) no-repeat left center;
}
.sw1212-table1 td span.ic4 {
  background: url(../images/sw1212_ic4c.png) no-repeat left center;
}
.sw1212-table1 td span.ic5 {
  background: url(../images/sw1212_ic5c.png) no-repeat left center;
}
.sw1212-table1 td span.ic6 {
  background: url(../images/sw1212_ic6c.png) no-repeat left center;
}
.sw1212-table1 td span.ic7 {
  background: url(../images/sw1212_ic7c.png) no-repeat left center;
}
.sw1212-table1 td span.ic8 {
  background: url(../images/sw1212_ic8c.png) no-repeat left center;
}
.sw1212-table1 td span.ic9 {
  background: url(../images/sw1212_ic9c.png) no-repeat left center;
}
.sw1212-table1 td span.ic10 {
  background: url(../images/sw1212_ic10c.png) no-repeat left center;
}
.sw1212-table1 td span.ic11 {
  background: url(../images/sw1212_ic11c.png) no-repeat left center;
}
.sw1212-table1 td span.ic12 {
  background: url(../images/sw1212_ic12c.png) no-repeat left center;
}

.sw1212-tablelst2 {
  width: 100%;
}
.sw1212-tablelst2 th, .sw1212-tablelst2 td {
  font-size: 16px;
  color: #333;
  text-align: center;
  height: 50px;
}
.sw1212-tablelst2 th {
  background: #eee;
  font-weight: bold;
}
.sw1212-tablelst2 td {
  border-bottom: 1px solid #eee;
}
.sw1212-tablelst2 td.z1 {
  font-family: "DIN-Medium";
}

.sw1212-txt2 {
  font-size: 16px;
  color: #467DDC;
}

.sw1212-ulaxis1 li {
  position: relative;
  padding-bottom: 30px;
}
.sw1212-ulaxis1 li:first-child::before {
  content: "";
  top: 10px;
}
.sw1212-ulaxis1 li::before {
  content: "";
  position: absolute;
  left: 6.5px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: #467DDC;
}
.sw1212-ulaxis1 li h1 {
  font-size: 18px;
  color: #333;
  font-family: "DIN-Medium";
  position: relative;
  padding-left: 25px;
}
.sw1212-ulaxis1 li h1::after {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 14px;
  height: 14px;
  background: #FFFFFF;
  border-radius: 50%;
  border: 1px solid #467DDC;
  margin-top: -7px;
  box-sizing: border-box;
}
.sw1212-ulaxis1 li p {
  font-size: 16px;
  color: #3D3D3D;
  background: #F3F7FF;
  border-radius: 4px;
  padding: 10px;
  margin-left: 25px;
  margin-top: 15px;
}

.sw1212-dlbx4 dt {
  font-size: 30px;
  color: #3D3D3D;
  text-align: center;
  font-weight: bold;
}
.sw1212-dlbx4 dd {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
.sw1212-dlbx4 dd .bar {
  width: 975px;
  height: 10px;
  background: #F0F0F0;
  border-radius: 300px;
}
.sw1212-dlbx4 dd .bar b {
  background: #467DDC;
  border-radius: 300px;
  display: block;
  height: 100%;
}
.sw1212-dlbx4 dd span {
  font-size: 20px;
  color: #3D3D3D;
  margin-left: 20px;
}
.sw1212-dlbx4 dd span i {
  color: #467DDC;
}

.sw1212-dlbx5 {
  padding: 0 20px;
}
.sw1212-dlbx5 dt {
  font-size: 20px;
  color: #3D3D3D;
  font-weight: bold;
}
.sw1212-dlbx5 dt i {
  color: #467DDC;
}
.sw1212-dlbx5 dd h1 {
  font-size: 24px;
  color: #3D3D3D;
  font-weight: bold;
}

.sw1212-radiolst1 {
  padding-left: 10px;
}

.sw1212-radio1 {
  display: flex;
  align-items: center;
}
.sw1212-radio1 input[type=radio] {
  width: 16px;
  height: 16px;
  background: url(../images/sw1212_radioic.png) no-repeat;
}
.sw1212-radio1 input[type=radio]:checked {
  background-image: url(../images/sw1212_radioicon.png);
}
.sw1212-radio1 span {
  font-size: 16px;
  color: #3D3D3D;
  padding-left: 10px;
}

.sw1212-askcon {
  background: #F6F6F6;
  padding: 18px;
  margin: 0 10px;
}
.sw1212-askcon h1 {
  font-size: 20px;
  color: #3D3D3D;
  font-weight: bold;
}
.sw1212-askcon p {
  font-size: 16px;
  color: #3D3D3D;
  line-height: 1.8;
  padding-top: 10px;
}

.sw1212-ulpage1 {
  display: flex;
  justify-content: space-between;
}
.sw1212-ulpage1 li {
  width: calc(50% - 7px);
  border-radius: 12px;
  background: #fff;
  line-height: 80px;
  font-size: 26px;
  color: #467DDC;
  cursor: pointer;
  text-align: center;
}

.sw1212-ulbx5 {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 95px;
}
.sw1212-ulbx5 li {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.sw1212-ulbx5 li h1 {
  font-size: 18px;
  color: #3D3D3D;
  font-family: "DIN-Medium";
}
.sw1212-ulbx5 li p {
  font-size: 18px;
  color: #666;
  padding-top: 5px;
}

.sw1212-txt3 {
  font-size: 18px;
  color: #3D3D3D;
}

.sw1212-ulbx6 {
  display: flex;
  flex-wrap: wrap;
  margin-left: -23px;
  margin-top: -16px;
}
.sw1212-ulbx6 li {
  width: 30px;
  height: 30px;
  background: #FFFFFF;
  border-radius: 50%;
  border: 1px solid #DDDDDD;
  font-size: 16px;
  color: #999;
  text-align: center;
  line-height: 30px;
  font-family: "Arial";
  box-sizing: border-box;
  margin-left: 23px;
  margin-top: 16px;
  cursor: pointer;
}
.sw1212-ulbx6 li.done {
  border-color: #467DDC;
  color: #467DDC;
}
.sw1212-ulbx6 li.cur{
    border-color: #467DDC;
    background-color: #467DDC;
    color: #fff;
}
.sw1212-ulbx6 li.on {
  border-color: #e66363;
  background: url(../images/sw1212_ic2d.png) no-repeat center;
}

.sw1212-ulbx7 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.sw1212-ulbx7 li {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #999;
}
.sw1212-ulbx7 li img {
  margin-right: 8px;
}

.sw1212-ulbtn1 li {
  height: 36px;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #467DDC;
  font-size: 16px;
  color: #467DDC;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
}
.sw1212-ulbtn1 li + li {
  margin-top: 16px;
}
.sw1212-ulbtn1 li.on {
  background: #467DDC;
  color: #fff;
}



.sw0130-ullst1 > li {
  padding: 10px 0 20px;
  border-bottom: 1px solid #eee;
}
.sw0130-ullst1 > li > h1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.sw0130-ullst1 > li > h1 strong {
  font-size: 20px;
  color: #333;
}
.sw0130-ullst1 > li > p {
  font-size: 16px;
  color: #333;
  padding: 5px 0 15px;
  line-height: 1.8;
}

.sw0130-ulbx1 {
  display: flex;
  align-items: center;
}
.sw0130-ulbx1 li {
  position: relative;
  padding: 0 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.sw0130-ulbx1 li h2 {
  font-size: 14px;
  color: #999;
}
.sw0130-ulbx1 li p {
  font-size: 18px;
  color: #333;
  font-family: "DIN-Medium";
  padding-top: 3px;
}
.sw0130-ulbx1 li + li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 5px;
  bottom: 5px;
  width: 1px;
  background: #ddd;
}

.sw0130-ulbx2 {
  display: flex;
  align-items: center;
}
.sw0130-ulbx2 li {
  cursor: pointer;
  border-radius: 4px;
  height: 36px;
  background: #ebf2ff;
  padding: 0 15px;
}
.sw0130-ulbx2 li.nbg {
  background: none;
}
.sw0130-ulbx2 li.nbg i {
  color: #666;
}
.sw0130-ulbx2 li i {
  font-size: 16px;
  color: #467ddc;
  line-height: 36px;
  padding-left: 20px;
}
.sw0130-ulbx2 li i.i1 {
  background: url(../images/sw0130_ic1.png) no-repeat left center;
}
.sw0130-ulbx2 li i.i2 {
  background: url(../images/sw0130_ic2.png) no-repeat left center;
}
.sw0130-ulbx2 li i.i3 {
  background: url(../images/sw0130_ic3.png) no-repeat left center;
}
.sw0130-ulbx2 li + li {
  margin-left: 10px;
}
.sw0130-ulbx2 li.on {
  background: #467ddc;
}
.sw0130-ulbx2 li.on i {
  color: #fff;
}
.sw0130-ulbx2 li.on i.i1 {
  background: url(../images/sw0130_ic1_on.png) no-repeat left center;
}
.sw0130-ulbx2 li.on i.i2 {
  background: url(../images/sw0130_ic2_on.png) no-repeat left center;
}

.sw0130-dlbx1 {
  padding: 15px 20px 20px;
  background: #fff;
}
.sw0130-dlbx1 dt {
  font-size: 20px;
  color: #333;
  font-weight: bold;
}
.sw0130-dlbx1 dd {
  padding-top: 15px;
}
.sw0130-dlbx1 dd span {
  font-size: 16px;
  color: #333;
}
.sw0130-dlbx1 dd span em {
  color: #666;
}
.sw0130-dlbx1 dd span + span {
  margin-left: 150px;
}

.sw0130-btn1 {
  width: 93px;
  height: 36px;
  background: #467ddc;
  font-size: 16px;
  color: #fff;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

.sw0130-ictxt1 {
  font-size: 16px;
  color: #999;
  background: url(../images/sw0130_ic3.png) no-repeat left center;
  padding-left: 20px;
}

.sw0130-ullst2 {
  background: #fff;
  padding: 0 20px;
}
.sw0130-ullst2 > li {
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}
.sw0130-ullst2 > li > p {
  font-size: 16px;
  color: #333;
  line-height: 1.8;
  padding: 15px 0;
}
.sw0130-ullst2 > li dl {
  display: flex;
  align-items: center;
}
.sw0130-ullst2 > li dl dt {
  width: 74px;
  height: 74px;
}
.sw0130-ullst2 > li dl dt img {
  display: block;
  width: 100%;
  height: 100%;
}
.sw0130-ullst2 > li dl dd {
  flex: 1;
  padding-left: 18px;
}
.sw0130-ullst2 > li dl dd h1 {
  font-size: 20px;
  color: #333;
  font-weight: bold;
}
.sw0130-ullst2 > li dl dd p {
  padding-top: 10px;
  font-size: 16px;
  color: #999;
}

.sw0228-ullst1 li {
  background: #fff;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 20px;
}
.sw0228-ullst1 li .type {
  margin-right: 18px;
  width: 100px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  font-size: 16px;
  border-radius: 5px;
}
.sw0228-ullst1 li .type.t1 {
  background: #e6f7ef;
  color: #09b361;
}
.sw0228-ullst1 li .type.t2 {
  background: #ebf2ff;
  color: #3784ff;
}
.sw0228-ullst1 li .type.t3 {
  background: #fdf4e9;
  color: #ea8f26;
}
.sw0228-ullst1 li span {
  flex: auto;
  font-size: 16px;
  color: #333;
}
.sw0228-ullst1 li span em {
  color: #999;
}
.sw0228-ullst1 li .btn {
  border: 1px solid #3784ff;
  height: 36px;
  box-sizing: border-box;
  padding: 0 20px;
  font-size: 18px;
  font-weight: bold;
  color: #3784ff;
  border-radius: 300px;
  background: transparent;
}
.sw0228-ullst1 li .btn img {
  vertical-align: 3px;
}
.sw0228-ullst1 li + li {
  margin-top: 10px;
}