/** @format */

class PointUtil {
    constructor(map) {
        this.map = map;

        let arr = [
            {
                name: 'bhd',
                url: './gis/3D/images/common/L3.png'
            }
        ];

        for (let item of arr) {
            if (!map.hasImage(item.name)) {
                map.loadImage(item.url, (error, image) => {
                    if (error) throw error;
                    if (!map.hasImage(item.name)) {
                        map.addImage(item.name, image);
                    }
                });
            }
        }
    }

    //添加点位
    addPoint(layerName, datas) {
        PowerGL.removeLayerFromName(this.map, layerName);

        let data = datas.map((item, index) => {
            let jd = parseFloat(item.JD);
            let wd = parseFloat(item.WD);

            let res = {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [jd, wd]
                },
                properties: item
            };

            return res;
        });

        let layout = { 'icon-image': 'bhd', 'icon-size': 0.8 };

        layout['icon-allow-overlap'] = true; //显示全部点位，默认为按比例显示点位

        this.map.addLayer({
            id: layerName,
            type: 'symbol',
            source: {
                type: 'geojson',
                data: {
                    type: 'FeatureCollection',
                    features: data
                }
            },
            layout: layout
        });
    }
}

export default PointUtil;
