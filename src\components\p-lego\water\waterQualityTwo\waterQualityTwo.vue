<!-- @format -->

<!-- 水质类别 (双y轴) -->

<template>
    <div class="">
        <p-bar
            :data="data"
            :option="optionData"
            :show-option="mOption.showOption"
            :config="{
                color: [
                    '#44c5fd',
                    '#51a5fd',
                    '#73bb31',
                    '#eebd15',
                    '#f88e17',
                    '#ee3b5b',
                    '#BD28B6'
                ]
            }"
            :style="{
                width: mOption.width,
                height: mOption.height,
                background: mOption.bgColor
            }"
        ></p-bar>
    </div>
</template>

<script>
export default {
    name: 'water-quality',
    props: {
        data: {
            //堆叠图数据
            type: Object,
            default: function () {
                return {};
            }
        },
        option: {
            type: Object,
            default: function () {
                return {
                    showOption: false, //是否显示工具栏
                    width: '100%', //宽
                    height: '100%', //高
                    bgColor: '#fff', //背景色
                    xRotate: '45', //x轴字体旋转角度
                    stack: false //优良率是否跟水质类别堆叠
                };
            }
        }
    },
    computed: {
        mOption() {
            return Object.assign(
                {
                    showOption: false, //是否显示工具栏
                    width: '100%', //宽
                    height: '100%', //高
                    bgColor: '#fff', //背景色
                    xRotate: '45', //x轴字体旋转角度
                    stack: false //优良率是否跟水质类别堆叠
                },
                this.option
            );
        }
    },
    data() {
        return {
            dataSet: {},
            optionData: {}
        };
    },

    created() {
        this.dataSet = JSON.parse(JSON.stringify(this.data));
        this.init();
        this.setOption();
    },
    methods: {
        init() {
            this.optionData = {
                legend: {
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: (params) => {
                        // console.log('params', params);
                        let str = '';
                        let unit = '个';

                        str += '<div>' + params[0].name + '</div>'; //显示日期的函数
                        for (let i = 0; i < params.length; i++) {
                            if (params[i].seriesName === '优良率') {
                                unit = '%';
                            } else {
                                unit = '个';
                            }

                            str +=
                                '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
                                params[i].color +
                                ';"></span>' +
                                params[i].seriesName +
                                '</span> : <span>' +
                                (params[i].data &&
                                params[i].data !== '0' &&
                                params[i].data !== '-'
                                    ? params[i].data + unit
                                    : params[i].data) +
                                '</br>';
                        }
                        return str;
                    }
                },
                xAxis: {
                    type: 'category',
                    axisLabel: {
                        rotate: ''
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '',
                        position: 'left',
                        alignTicks: false,
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLabel: {
                            formatter: '{value}个'
                        }
                    },
                    {
                        type: 'value',
                        name: '',
                        position: 'right',
                        alignTicks: false,
                        axisLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        splitLine: {
                            show: false
                        },
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    }
                ],
                series: []
            };
        },
        setOption() {
            // x轴字体旋转角度
            this.optionData.xAxis.axisLabel.rotate = this.mOption.xRotate;

            this.optionData.series = this.dataSet.series.map((item) => {
                if (item.name == '优良率') {
                    item.type = 'line';
                    item.yAxisIndex = 1;
                    if (!this.mOption.stack) {
                        item.stack = 'total';
                    }
                } else {
                    item.type = 'bar';
                    if (!this.mOption.stack) {
                        item.stack = 'one';
                    }
                }

                return item;
            });
        }
    }
};
</script>

<style lang="less" scoped></style>
