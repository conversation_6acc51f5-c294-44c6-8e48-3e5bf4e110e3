<!-- @format -->

<template>
    <div class="arrow-content">
        <!-- 显示的图片 -->
        <img class="arrow-image" v-if="value != 0" :src="image" />
        <span v-if="value == 0" :style="{ fontSize: zeroFontSize }">{{
            zeroText
        }}</span>

        <!-- 显示的数值 -->
        <p
            class="arrow-value"
            :style="{
                fontSize: valueFontSize,
                color: color
            }"
        >
            {{ Math.abs(value) }}<span>{{ units }}</span>
        </p>
    </div>
</template>

<script>
import downRedImageT from './images/t-down-red.png';
import upRedImageT from './images/t-up-red.png';
import downGreenImageT from './images/t-down-green.png';
import upGreenImageT from './images/t-up-green.png';
import downRedImageH from './images/h-down-red.png';
import upRedImageH from './images/h-up-red.png';
import downGreenImageH from './images/h-down-green.png';
import upGreenImageH from './images/h-up-green.png';

export default {
    props: {
        type: {
            // rate 数值大于0 绿色向上箭头 数值小于0红色向下箭头
            //pollutant 数值大于0 红色向上箭头 数值小于0绿色向下箭头
            default: 'rate',
            type: String
        },
        imageType: {
            /*
                同比 用同比图片
                环比 用环比图片
             */
            type: String,
            default: '同比'
        },
        value: {
            default: '1',
            type: String
        },
        units: {
            // 单位
            default: '%',
            type: String
        },
        zeroText: {
            //当值为0时显示的内容
            default: '持平',
            type: String
        },
        zeroFontSize: {
            //当值为0时显示的字体大小
            default: '12px',
            type: String
        },
        valueFontSize: {
            // 数值的字体大小
            default: '16px',
            type: String
        }
    },
    data() {
        return {
            upGreen: upGreenImageT,
            downRed: downRedImageT,
            upRed: upRedImageT,
            downGreen: downGreenImageT,
            image: upGreenImageT,
            color: ''
        };
    },
    watch: {
        type: 'changeImage',
        value: 'changeImage'
    },
    mounted() {
        this.changeImage();
    },
    methods: {
        changeImage() {
            if (this.imageType == '环比') {
                //判断环比 还是同比

                this.upGreen = upGreenImageH;
                this.downRed = downRedImageH;
                this.upRed = upRedImageH;
                this.downGreen = downGreenImageH;
            } else {
                this.upGreen = upGreenImageT;
                this.downRed = downRedImageT;
                this.upRed = upRedImageT;
                this.downGreen = downGreenImageT;
            }

            if (this.type == 'rate') {
                // 箭头对应的颜色
                if (this.value > 0) {
                    this.image = this.upGreen;
                } else {
                    this.image = this.downRed;
                }

                //判断文字颜色
                if (this.value == 0) {
                    this.color = 'unset';
                } else if (this.value > 0) {
                    this.color = '#26d267';
                } else {
                    this.color = '#fc4a4a';
                }
            } else {
                if (this.value > 0) {
                    this.image = this.upRed;
                } else {
                    this.image = this.downGreen;
                }

                //判断文字颜色
                if (this.value == 0) {
                    this.color = 'unset';
                } else if (this.value > 0) {
                    this.color = '#fc4a4a';
                } else {
                    this.color = '#26d267';
                }
            }
        }
    }
};
</script>

<style lang="less" scoped>
.arrow-content {
    display: inline-block;

    .arrow-value {
        height: 100%;
        display: inline-block;
    }
    .arrow-image {
        vertical-align: middle;
    }
}
</style>
