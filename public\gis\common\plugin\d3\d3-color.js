// https://d3js.org/d3-color/ v1.2.3 Copyright 2018 Mike <PERSON>
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.d3=t.d3||{})}(this,function(t){"use strict";function e(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function n(t,e){var n=Object.create(t.prototype);for(var i in e)n[i]=e[i];return n}function i(){}var r="\\s*([+-]?\\d+)\\s*",a="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)\\s*",h="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)%\\s*",s=/^#([0-9a-f]{3})$/,o=/^#([0-9a-f]{6})$/,l=new RegExp("^rgb\\("+[r,r,r]+"\\)$"),u=new RegExp("^rgb\\("+[h,h,h]+"\\)$"),c=new RegExp("^rgba\\("+[r,r,r,a]+"\\)$"),g=new RegExp("^rgba\\("+[h,h,h,a]+"\\)$"),d=new RegExp("^hsl\\("+[a,h,h]+"\\)$"),p=new RegExp("^hsla\\("+[a,h,h,a]+"\\)$"),f={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function b(t){var e;return t=(t+"").trim().toLowerCase(),(e=s.exec(t))?new N((e=parseInt(e[1],16))>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):(e=o.exec(t))?y(parseInt(e[1],16)):(e=l.exec(t))?new N(e[1],e[2],e[3],1):(e=u.exec(t))?new N(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=c.exec(t))?w(e[1],e[2],e[3],e[4]):(e=g.exec(t))?w(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=d.exec(t))?v(e[1],e[2]/100,e[3]/100,1):(e=p.exec(t))?v(e[1],e[2]/100,e[3]/100,e[4]):f.hasOwnProperty(t)?y(f[t]):"transparent"===t?new N(NaN,NaN,NaN,0):null}function y(t){return new N(t>>16&255,t>>8&255,255&t,1)}function w(t,e,n,i){return i<=0&&(t=e=n=NaN),new N(t,e,n,i)}function m(t){return t instanceof i||(t=b(t)),t?new N((t=t.rgb()).r,t.g,t.b,t.opacity):new N}function k(t,e,n,i){return 1===arguments.length?m(t):new N(t,e,n,null==i?1:i)}function N(t,e,n,i){this.r=+t,this.g=+e,this.b=+n,this.opacity=+i}function M(t){return((t=Math.max(0,Math.min(255,Math.round(t)||0)))<16?"0":"")+t.toString(16)}function v(t,e,n,i){return i<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new q(t,e,n,i)}function x(t,e,n,r){return 1===arguments.length?function(t){if(t instanceof q)return new q(t.h,t.s,t.l,t.opacity);if(t instanceof i||(t=b(t)),!t)return new q;if(t instanceof q)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,a=Math.min(e,n,r),h=Math.max(e,n,r),s=NaN,o=h-a,l=(h+a)/2;return o?(s=e===h?(n-r)/o+6*(n<r):n===h?(r-e)/o+2:(e-n)/o+4,o/=l<.5?h+a:2-h-a,s*=60):o=l>0&&l<1?0:s,new q(s,o,l,t.opacity)}(t):new q(t,e,n,null==r?1:r)}function q(t,e,n,i){this.h=+t,this.s=+e,this.l=+n,this.opacity=+i}function E(t,e,n){return 255*(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)}e(i,b,{displayable:function(){return this.rgb().displayable()},hex:function(){return this.rgb().hex()},toString:function(){return this.rgb()+""}}),e(N,k,n(i,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new N(this.r*t,this.g*t,this.b*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new N(this.r*t,this.g*t,this.b*t,this.opacity)},rgb:function(){return this},displayable:function(){return 0<=this.r&&this.r<=255&&0<=this.g&&this.g<=255&&0<=this.b&&this.b<=255&&0<=this.opacity&&this.opacity<=1},hex:function(){return"#"+M(this.r)+M(this.g)+M(this.b)},toString:function(){var t=this.opacity;return(1===(t=isNaN(t)?1:Math.max(0,Math.min(1,t)))?"rgb(":"rgba(")+Math.max(0,Math.min(255,Math.round(this.r)||0))+", "+Math.max(0,Math.min(255,Math.round(this.g)||0))+", "+Math.max(0,Math.min(255,Math.round(this.b)||0))+(1===t?")":", "+t+")")}})),e(q,x,n(i,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new q(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new q(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,i=n+(n<.5?n:1-n)*e,r=2*n-i;return new N(E(t>=240?t-240:t+120,r,i),E(t,r,i),E(t<120?t+240:t-120,r,i),this.opacity)},displayable:function(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1}}));var $=Math.PI/180,R=180/Math.PI,j=.96422,I=1,P=.82521,O=4/29,S=6/29,_=3*S*S,z=S*S*S;function C(t){if(t instanceof A)return new A(t.l,t.a,t.b,t.opacity);if(t instanceof K){if(isNaN(t.h))return new A(t.l,0,0,t.opacity);var e=t.h*$;return new A(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}t instanceof N||(t=m(t));var n,i,r=G(t.r),a=G(t.g),h=G(t.b),s=B((.2225045*r+.7168786*a+.0606169*h)/I);return r===a&&a===h?n=i=s:(n=B((.4360747*r+.3850649*a+.1430804*h)/j),i=B((.0139322*r+.0971045*a+.7141733*h)/P)),new A(116*s-16,500*(n-s),200*(s-i),t.opacity)}function L(t,e,n,i){return 1===arguments.length?C(t):new A(t,e,n,null==i?1:i)}function A(t,e,n,i){this.l=+t,this.a=+e,this.b=+n,this.opacity=+i}function B(t){return t>z?Math.pow(t,1/3):t/_+O}function D(t){return t>S?t*t*t:_*(t-O)}function F(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function G(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function H(t){if(t instanceof K)return new K(t.h,t.c,t.l,t.opacity);if(t instanceof A||(t=C(t)),0===t.a&&0===t.b)return new K(NaN,0,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*R;return new K(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function J(t,e,n,i){return 1===arguments.length?H(t):new K(t,e,n,null==i?1:i)}function K(t,e,n,i){this.h=+t,this.c=+e,this.l=+n,this.opacity=+i}e(A,L,n(i,{brighter:function(t){return new A(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker:function(t){return new A(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb:function(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,n=isNaN(this.b)?t:t-this.b/200;return new N(F(3.1338561*(e=j*D(e))-1.6168667*(t=I*D(t))-.4906146*(n=P*D(n))),F(-.9787684*e+1.9161415*t+.033454*n),F(.0719453*e-.2289914*t+1.4052427*n),this.opacity)}})),e(K,J,n(i,{brighter:function(t){return new K(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker:function(t){return new K(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb:function(){return C(this).rgb()}}));var Q=-.14861,T=1.78277,U=-.29227,V=-.90649,W=1.97294,X=W*V,Y=W*T,Z=T*U-V*Q;function tt(t,e,n,i){return 1===arguments.length?function(t){if(t instanceof et)return new et(t.h,t.s,t.l,t.opacity);t instanceof N||(t=m(t));var e=t.r/255,n=t.g/255,i=t.b/255,r=(Z*i+X*e-Y*n)/(Z+X-Y),a=i-r,h=(W*(n-r)-U*a)/V,s=Math.sqrt(h*h+a*a)/(W*r*(1-r)),o=s?Math.atan2(h,a)*R-120:NaN;return new et(o<0?o+360:o,s,r,t.opacity)}(t):new et(t,e,n,null==i?1:i)}function et(t,e,n,i){this.h=+t,this.s=+e,this.l=+n,this.opacity=+i}e(et,tt,n(i,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new et(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new et(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=isNaN(this.h)?0:(this.h+120)*$,e=+this.l,n=isNaN(this.s)?0:this.s*e*(1-e),i=Math.cos(t),r=Math.sin(t);return new N(255*(e+n*(Q*i+T*r)),255*(e+n*(U*i+V*r)),255*(e+n*(W*i)),this.opacity)}})),t.color=b,t.rgb=k,t.hsl=x,t.lab=L,t.hcl=J,t.lch=function(t,e,n,i){return 1===arguments.length?H(t):new K(n,e,t,null==i?1:i)},t.gray=function(t,e){return new A(t,0,0,null==e?1:e)},t.cubehelix=tt,Object.defineProperty(t,"__esModule",{value:!0})});
