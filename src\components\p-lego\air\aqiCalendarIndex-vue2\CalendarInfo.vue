<!-- @format -->

<template>
    <div class="info">
        <div class="info_left">
            <div>
                <span :style="{ color: levelColor[0] }">{{ fine }}</span>
                <span>天{{ level[0] }}</span>
            </div>
            <div>
                <span :style="{ color: levelColor[3] }">{{ middle }}</span>
                <span>天{{ level[3] }}污染</span>
            </div>
        </div>
        <div class="info_main">
            <div>
                <span :style="{ color: levelColor[1] }">{{ good }}</span>
                <span>天{{ level[1] }}</span>
            </div>
            <div>
                <span :style="{ color: levelColor[4] }">{{ serious }}</span>
                <span>天{{ level[4] }}污染</span>
            </div>
        </div>
        <div class="info_right">
            <div>
                <span :style="{ color: levelColor[2] }">{{ mild }}</span>
                <span>天{{ level[2] }}污染</span>
            </div>
            <div>
                <span :style="{ color: levelColor[5] }">{{ severe }}</span>
                <span>天{{ level[5] }}污染</span>
            </div>
        </div>
    </div>
</template>

<script>
import { LEVEL_TEXTS, LEVEL_COLORS } from './utils/constants.js';
export default {
    name: 'CalendarInfo',
    props: {
        data: {
            type: Array
        }
    },
    data() {
        return {
            good: 0, //良
            fine: 0, //优
            mild: 0, //轻度污染
            middle: 0, //中度污染
            serious: 0, //重度污染
            severe: 0, //严重污染,
            level: [],
            levelColor: []
        };
    },

    watch: {
        data() {
            this.getList();
        }
    },

    mounted() {
        this.getList();
    },

    methods: {
        getList() {
            (this.good = 0),
                (this.fine = 0),
                (this.mild = 0),
                (this.middle = 0),
                (this.serious = 0),
                (this.severe = 0),
                (this.level = LEVEL_TEXTS);
            this.levelColor = LEVEL_COLORS;
            for (let i in this.data) {
                if (this.data[i].data) {
                    if (!this.data[i].notThisMonth) {
                        if (this.data[i].data.quality) {
                            if (this.data[i].data.quality === LEVEL_TEXTS[0]) {
                                this.fine++;
                            } else if (
                                this.data[i].data.quality === LEVEL_TEXTS[1]
                            ) {
                                this.good++;
                            } else if (
                                this.data[i].data.quality === LEVEL_TEXTS[2]
                            ) {
                                this.mild++;
                            } else if (
                                this.data[i].data.quality === LEVEL_TEXTS[3]
                            ) {
                                this.middle++;
                            } else if (
                                this.data[i].data.quality === LEVEL_TEXTS[4]
                            ) {
                                this.serious++;
                            } else if (
                                this.data[i].data.quality === LEVEL_TEXTS[5]
                            ) {
                                this.severe++;
                            }
                        }
                    }
                }
            }
        }
    }
};
</script>

<style scoped>
.info {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: auto;
    width: 100%;
    position: relative;
    top: 10px;
}

.info_left,
.info_main,
.info_right {
    font-size: 14px;
    font-weight: 300;
}

.info_left div,
.info_main div,
.info_right div {
    padding-bottom: 6px;
}
</style>
