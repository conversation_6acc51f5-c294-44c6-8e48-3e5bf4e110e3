<!-- @format -->
<!--全屏 -->
<template>
    <ul class="zy-tools-gis">
        <li>
            <i
                class="tool-mapMode"
                :class="{ threeMode: isThreeMode }"
                @click="changeMode"
                title="二、三维切换"
            ></i>
        </li>
    </ul>
</template>

<script>
export default {
    name: 'ModeType',
    props: ['map'],
    data() {
        return {
            isThreeMode: false
        };
    },
    components: {},
    computed: {},
    mounted() {
        this.initPage();
    },
    methods: {
        initPage() {
            if (!this.map) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.setMode();

            this.map.on('pitchend', () => {
                this.setMode();
            });
        },

        setMode() {
            let pitch = this.map.getPitch();
            if (pitch > 0) {
                this.isThreeMode = true;
            } else {
                this.isThreeMode = false;
            }
        },
        changeMode() {
            this.isThreeMode = !this.isThreeMode;

            if (this.isThreeMode) {
                this.map.setPitch(50);
            } else {
                this.map.setPitch(0);
            }
        }
    },
    watch: {}
};
</script>

<style scoped></style>
