<!-- @format -->

<template>
    <div>
        <div class="pd-row1">
            <span class="s1">排口：</span>
            <div>
                <el-select
                    v-model="outletData"
                    @change="changeOutlet"
                    placeholder="请选择"
                >
                    <el-option
                        v-for="item in outlet"
                        :key="item.XH"
                        :label="item.PKMC"
                        :value="item.XH"
                    >
                    </el-option>
                </el-select>
            </div>
            <span class="s1">监测因子：</span>
            <div style="width: 308px">
                <el-select
                    @change="changeWRW1"
                    v-model="WRWObj"
                    multiple
                    placeholder="请选择"
                >
                    <el-option
                        v-for="item in WRW1List"
                        :key="item.value"
                        :label="item.label"
                        :value="{ value: item.value, label: item.label }"
                    >
                    </el-option>
                </el-select>
            </div>

            <div class="pd-rdobx1">
                <label
                    class="pd-label cur"
                    v-for="(item, index) in DayList"
                    @click="changeDay1(item.value)"
                    :key="index"
                    ><input
                        type="radio"
                        :name="item.value + '1'"
                        :checked="item.value == Day1"
                    /><i></i><span>{{ item.label }}</span></label
                >
            </div>
            <div class="pd-datesrh1">
                <input
                    type="text"
                    :value="getDateByStyle(value1['DayList' + Day1][0])"
                />
                <i></i>
                <input
                    type="text"
                    :value="getDateByStyle(value1['DayList' + Day1][1])"
                />
                <div class="datePicker">
                    <el-date-picker
                        v-model="value1['DayList' + Day1]"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    >
                    </el-date-picker>
                </div>
            </div>
        </div>
        <div class="gap"></div>
        <div class="gap"></div>
        <LinChars
            :arr="pChartsData"
            v-if="pChartsData.length"
            :day="Day1"
            :key="Nums1"
            :WRWObj="WRWObj"
        ></LinChars>
        <div class="nomsg" v-if="!pChartsData.length">暂无数据</div>
    </div>
</template>

<script>
import LinChars from './LineP.vue';
export default {
    components: {
        LinChars
    },
    props: {
        data: {
            type: Array
        },
        option: {
            type: Object
        },
        factorsData: Array,
        outlet: Array
    },
    data() {
        return {
            pChartsData: [],
            outletData: '',
            // 因子
            WRW1List: [],
            // 因子对象数组
            WRWObj: [],
            Day1: 'R',
            DayList: [
                { value: 'R', label: '日数据' },
                { value: 'S', label: '小时数据' }
            ],
            value1: {
                DayListR: [
                    this.$dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
                    this.$dayjs().format('YYYY-MM-DD')
                ],
                DayListS: [
                    this.$dayjs().subtract(1, 'day').format('YYYY-MM-DD HH'),
                    this.$dayjs().format('YYYY-MM-DD HH')
                ]
            }
        };
    },
    computed: {
        getDateByStyle() {
            let that = this;
            return function (value) {
                if (that.Day1 == 'R') {
                    console.log('时间：', value);
                    return that.$dayjs(value).format('YYYY-MM-DD');
                } else {
                    return that.$dayjs(value).format('YYYY-MM-DD HH');
                }
            };
        }
    },
    mounted() {
        // 监测数据
        this.initChar();
    },
    methods: {
        initChar() {
            this.pChartsData = this.data;
            this.WRW1List = this.factorsData;
            this.WRWObj[0] = this.factorsData[0];
            this.outletData = this.outlet[0].XH;
        },
        // 监测数据 污染因子
        changeWRW1(e) {
            this.WRWObj = e;
        },
        // 污染源在线监控 排口
        changeOutlet(e) {
            // 排口改变，重新请求因子与排口数据
        },
        changeDay1(e) {
            this.Day1 = e;
            this[e]++;
        }
    }
};
</script>

<style scoped>
.pd-treesider {
    width: 275px;
    border: 1px solid #0b5f72;
    float: left;
    height: 777px;
}
.pd-treesider h1 {
    padding: 13px 18px;
}
.pd-treesider h1 i {
    font-size: 16px;
    color: #fff;
    position: relative;
    padding-left: 15px;
}
.pd-treesider h1 i:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-color: transparent transparent transparent #fff;
    margin-top: -5px;
}
.pd-treesider ul {
    padding: 0 18px;
    display: none;
}
.pd-treesider ul li + li {
    padding-top: 15px;
}
.pd-treesider ul li.on {
    color: #20beee;
}
.pd-treesider ul li {
    padding-left: 30px;
    font-size: 16px;
    color: #fff;
    padding-top: 15px;
}
.pd-treesider h1.on i:before {
    content: '';
    border-width: 5px 5px 0 5px;
    border-color: #fff transparent transparent transparent;
    margin-top: 0;
}
.pd-treesider h1.on + ul {
    display: list-item;
}
.pd-rdobx1 .pd-label span {
    font-size: 16px;
    color: #fff;
}
.pd-rdobx1 {
    display: flex;
    align-items: center;
    margin-left: 40px;
}
.pd-rdobx1 .pd-label + .pd-label {
    margin-left: 40px;
}
.pd-rdobx1 .pd-label input[type='radio'] ~ i {
    width: 18px;
    height: 18px;
    margin-right: 10px;
}
.pd-label span {
    display: inline-block;
    vertical-align: middle;
}
.zy-til1 {
    font-size: 18px;
    color: #fff;
    line-height: 40px;
    padding-left: 12px;
    position: relative;
}
.zy-til1::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #0dc8ef;
}
.pd-row1 {
    display: flex;
}
.zy-table1 {
    width: 100%;
    border: 1px solid #0b5f72;
}
.zy-table1 td.td-hd {
    background-color: #064959;
    width: 178px;
    padding-left: 15px;
    color: #21bbe0;
}
.zy-table1 td {
    font-size: 16px;
    color: #fff;
    height: 48px;
    border: 1px solid #0b5f72;
    padding-left: 15px;
}
table {
    table-layout: fixed;
}
.tablelist {
    overflow-y: scroll;
}
.pd-tablelst1 {
    width: 100%;
}
.pd-tablelst1 tr td {
    font-size: 14px;
    color: #fff;
    text-align: center;
    height: 40px;
}
.pd-tablelst1 thead tr td {
    background: rgba(39, 117, 155, 0.35);
}
.pd-tablelst1 tr td .prog {
    height: 16px;
    border-radius: 300px;
    background: #11578b;
    position: relative;
}
.pd-tablelst1 tr td .prog b {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background: #34dbff;
    border-radius: 300px 0 0 300px;
}
.pd-tablelst1 tbody tr:first-child td {
    padding-top: 13px;
}
.pd-tablelst1.otr tbody tr:first-child td {
    padding-top: 0;
}
.pd-tablelst1.thd thead tr td {
    font-size: 16px;
}
.pd-tablelst1 tr td.tal {
    text-align: left;
}
.pd-tablelst1.tbd tbody tr td {
    height: 60px;
}
.pd-tablelst1.tbd tbody tr td:last-child {
    font-size: 14px;
}
.pd-tablelst1.otr.htn tbody tr td {
    height: auto;
    padding: 5px 0;
    border-bottom-color: #35626d;
    line-height: 2;
}
.pd-tablelst1.otr.htn tr td:first-child {
    text-align: right;
}
.pd-tablelst1.otr tr td.tal {
    text-align: left;
}

.datePicker {
    position: absolute;
    width: 314px;
    left: 0;
    top: 1px;
    opacity: 0;
}
.pd-rtcon1 {
    overflow: hidden;
    padding-left: 40px;
    height: 777px;
}
.cur {
    cursor: pointer;
}
.pd-treesider ul li.on {
    color: #20beee;
}
.pd-datesrh1 {
    display: flex;
    align-items: center;
    margin-left: 40px;
    position: relative;
}

.pd-datesrh1 input {
    border: 1px solid #0b5f72;
    width: 128px;
    line-height: 32px;
    font-size: 14px;
    color: #fff;
    background: url(./images/dateic1.png) no-repeat 95% center;
    text-indent: 10px;
    outline: none;
}
.pd-datesrh1 button {
    width: 80px;
    height: 34px;
    box-sizing: border-box;
    font-size: 16px;
    color: #023f4e;
    background: #20beee;
    border: none;
    outline: none;
    margin-left: 16px;
}
.pd-datesrh1 i {
    width: 10px;
    height: 1px;
    background: #fff;
    margin: 0 10px;
}
.nomsg {
    font-size: 16px;
    color: #aaa;
    text-align: center;
    line-height: 40px;
}
.pd-slec1a {
    border: 1px solid #27c8e9;
    height: 32px;
    background: url(./images/arwbt1.png) no-repeat 95% center;
    position: relative;
}
.pd-slec1a p {
    white-space: nowrap;
    font-size: 16px;
    color: #fff;
    line-height: 32px;
    text-indent: 10px;
}
.pd-slec1a ul {
    position: absolute;
    left: -1px;
    right: -1px;
    top: 100%;
    border: 1px solid #0b5f72;
    background: #064959;
    margin-top: 4px;
    z-index: 1;
    padding: 5px 0;
    display: none;
}
.pd-slec1a ul li {
    padding: 6px 20px;
}
.pd-slec1a ul li .pd-label input[type='checkbox'] ~ i {
    width: 16px;
    height: 16px;
    margin-right: 10px;
}
.pd-slec1a ul li .pd-label span {
    font-size: 16px;
    color: #fff;
}
.pd-slec1a.on ul {
    display: list-item;
}

/* 单选和复选 */
.pd-label {
    display: inline-block;
    vertical-align: middle;
}
.pd-label input {
    display: none;
}
.pd-label i {
    display: inline-block;
    vertical-align: middle;
    background-repeat: no-repeat;
    background-position: center;
}
.pd-label input[type='checkbox'] ~ i {
    background-image: url(./images/checkic.png);
}
.pd-label input[type='checkbox']:checked ~ i {
    background-image: url(./images/checkicon.png);
}
.pd-label input[type='radio'] ~ i {
    background-image: url(./images/radioic.png);
}
.pd-label input[type='radio']:checked ~ i {
    background-image: url(./images/radioicon.png);
}
.pd-label span {
    display: inline-block;
    vertical-align: middle;
}
.pd-slec1a p span {
    padding-right: 10px;
}

.s1 {
    line-height: 37px;
}
</style>
