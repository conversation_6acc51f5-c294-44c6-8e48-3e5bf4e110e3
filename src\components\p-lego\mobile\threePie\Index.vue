<!-- @format -->
<template>
    <div id="containerPie" style="width: 100%; height: 100%"></div>
</template>

<script>
import Highcharts from 'highcharts';
import Highcharts3D from 'highcharts/highcharts-3d';
Highcharts3D(Highcharts);

export default {
    props: {
        data: {
            type: Object,
            default: () => {
                return {
                    total: 6,
                    color: ['#FFB500', '#FF0000', '#35EB79'],
                    series: [
                        {
                            name: '数量',
                            data: [
                                ['登记管理', 1],
                                ['重点管理', 3],
                                ['简化管理', 2]
                            ],
                            size: '100%', // 外圈
                            innerSize: '60%' // 内圈
                        }
                    ]
                };
            }
        }
    },

    data() {
        return {};
    },

    created() {},
    mounted() {
        this.getChart();
    },
    methods: {
        getChart() {
            let chart = Highcharts.chart('containerPie', {
                chart: {
                    type: 'pie',
                    backgroundColor: 'none',
                    options3d: {
                        enabled: true,
                        alpha: 8
                    }
                },
                title: {
                    // 主标题
                    text: this.data.total,
                    align: 'center',
                    verticalAlign: 'middle',
                    y: 15,
                    style: {
                        fontSize: '16',
                        color: '#fff',
                        fontWeight: 'bold'
                    }
                },
                subtitle: {
                    // 副标题
                    text: '研判总数',
                    align: 'center',
                    verticalAlign: 'middle',
                    y: 25,
                    style: {
                        color: '#fff',
                        fontSize: '12'
                    }
                },
                credits: {
                    enabled: false
                },

                plotOptions: {
                    pie: {
                        innerSize: 60,
                        depth: 100,
                        dataLabels: {
                            softConnector: false,
                            distance: 10, // 数据标签与扇区距离
                            style: {
                                color: '#fff',
                                textOutline: 'none'
                            },
                            formatter: function () {
                                // console.log(this);
                                //this 为当前的点（扇区）对象，可以通过  console.log(this) 来查看详细信息
                                return (
                                    `<span style="margin-right:10px;">${this.key}</span>&nbsp;&nbsp` +
                                    `<span style="color:${this.point.color};font-weight:bold;font-size:18px">${this.y}</span>`
                                );
                            }
                        }
                    }
                },
                colors: this.data.color,
                series: this.data.series
            });
        }
    }
};
</script>

<style scoped></style>
