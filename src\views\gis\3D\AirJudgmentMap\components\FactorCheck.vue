<!-- @format -->

<!-- 空气站-因子切换  -->
<template>
    <dl class="sw0725-dlbx1">
        <dd
            @click="yzChange('AQI')"
            :class="{ on: selectYZ == 'AQI' }"
            style="padding: 10px"
        >
            <p style="text-align: center; font-size: 18px; font-weight: bold">
                AQI
            </p>
        </dd>
        <dd class="sw0726-aqilst" style="padding: 0">
            <p @click="yzChange('PM25')" :class="{ on: selectYZ == 'PM25' }">
                <img src="../css/images/sw0726_ic1.png" alt="" />PM₂.₅
            </p>
            <p @click="yzChange('PM10')" :class="{ on: selectYZ == 'PM10' }">
                <img src="../css/images/sw0726_ic2.png" alt="" />PM₁₀
            </p>
            <p @click="yzChange('SO2')" :class="{ on: selectYZ == 'SO2' }">
                <img src="../css/images/sw0726_ic3.png" alt="" />SO₂
            </p>
            <p @click="yzChange('NO2')" :class="{ on: selectYZ == 'NO2' }">
                <img src="../css/images/sw0726_ic4.png" alt="" />NO₂
            </p>
            <p @click="yzChange('O3')" :class="{ on: selectYZ == 'O3' }">
                <img src="../css/images/sw0726_ic5.png" alt="" />O₃
            </p>
            <p @click="yzChange('CO')" :class="{ on: selectYZ == 'CO' }">
                <img src="../css/images/sw0726_ic6.png" alt="" />CO
            </p>
        </dd>
    </dl>
</template>

<script>
export default {
    data() {
        return {
            selectYZ: 'AQI'
        };
    },
    props: [],

    mounted() {},
    methods: {
        yzChange(yz) {
            this.selectYZ = yz;
            this.$emit('factorChange', this.selectYZ);
        }
    },

    computed: {},

    watch: {}
};
</script>

<style scoped>
.sw0725-dlbx1 dd {
    cursor: pointer;
}
.sw0725-dlbx1 dd.on,
.sw0725-dlbx1 p.on {
    background: #008795;
}

.sw0726-aqilst p {
    width: 34%;
    height: 35px;
    line-height: 35px;
    padding: 2px 15px;
}

.sw0726-aqilst p:nth-child(n + 3) {
    margin-top: 0;
}
</style>
