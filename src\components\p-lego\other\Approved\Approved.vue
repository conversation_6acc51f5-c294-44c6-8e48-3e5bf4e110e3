<!-- @format -->

<template>
  <div id="roved" :style="{ width: width, height: height }"></div>
</template>
<script>
export default {
  props: {
    data: {
      type: Array,
    },
    color: {
      type: Array,
    },
    Option: {
      type: Object,
      defult: {
        sliceTitle: 4,
        ringfontSize: "16",
        typeText: 3,
        showLegend: false,
      },
    },
    width: {
      type: String,
    },
    height: {
      type: String,
    },
  },
  data() {
    return {};
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      let _self = this;
      let Option = this.Option;
      console.log(Option);
      let data = this.data;
      let Xdata = [];
      let titleArr = [],
        seriesArr = [];
      let colors = this.color;
      data.forEach(function (item, index) {
        item.paramName =
          item.name.length > Option.sliceTitle
            ? item.name.substring(0, Option.sliceTitle) + "..."
            : item.name;
        Xdata.push({ name: item.name });
        seriesArr.push({
          name: item.name,
          type: "pie",
          clockWise: false,
          radius: [60, 70],
          itemStyle: {
            normal: {
              color: colors[index][0],
              shadowColor: colors[index][0],
              shadowBlur: 0,
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
          hoverAnimation: false,
          center: [index * 20 + 10 + "%", "50%"],
          data: [
            {
              value: (100 - ((item.zs - item.value) / item.zs) * 100).toFixed(
                2
              ),
              label: {
                normal: {
                  formatter: function (params) {
                    if (Option.typeText == 3) {
                      return [
                        item.paramName,
                        item.state,
                        params.value + "%",
                      ].join("\n\n");
                    } else if (Option.typeText == 2) {
                      return [item.state, params.value + "%"].join("\n\n");
                    } else if (Option.typeText == 1) {
                      return [item.state].join("\n\n");
                    }
                  },
                  position: "center",
                  show: true,
                  textStyle: {
                    fontSize: Option.ringfontSize,
                    fontWeight: "bold",
                    color: colors[index][0],
                  },
                },
              },
            },
            {
              value: (100 - (item.value / item.zs) * 100).toFixed(2),
              name: "invisible",
              itemStyle: {
                normal: {
                  color: colors[index][1],
                },
                emphasis: {
                  color: colors[index][1],
                },
              },
            },
          ],
        });
      });
      let option = {
        title: {
          //   text: "通过率/数据质量评价",
          textStyle: {
            align: "center",
            color: "#333333",
            fontSize: 18,
          },
          top: "2%",
          left: "2%",
        },
        legend: {
          show: Option.showLegend,
          data: Xdata,

          type: "scroll",
          bottom: "40",
          itemGap: 30,
          itemWidth: 20,
          itemHeight: 10,
          textStyle: {
            fontSize: "13",
            color: "#fff",
          },
        },
        tooltip: {
          show: true,
          formatter: function (params, index) {
            if (params.name == "invisible") {
              return (
                params.marker +
                params.seriesName +
                Option.Noqualified +
                Option.untils +
                "：" +
                (_self.data[params.seriesIndex].zs -
                  _self.data[params.seriesIndex].value) +
                "(" +
                params.value +
                "%" +
                ")"
              );
            }
            return (
              params.marker +
              params.seriesName +
              Option.qualified +
              Option.untils +
              "：" +
              _self.data[params.seriesIndex].value +
              "(" +
              params.value +
              "%" +
              ")"
            );
          },
        },
        series: seriesArr,
      };
      let chart = this.$echarts.init(document.getElementById("roved"));

      chart.clear();
      chart.setOption(option);
    },
  },
};
</script>
