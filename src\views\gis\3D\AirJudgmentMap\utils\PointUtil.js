/** @format */

/** @format */
import { createVNode, render } from 'vue';
import BasePointUtil from '@/components/gis/3D/utils/BasePointUtil';
import PopZD from '@/views/gis/3D/AirMap/dialogs/PopZD';
class PointUtil extends BasePointUtil {
    constructor(map, callback) {
        super(map, callback);
        let arr = [
            {
                name: 'gyy-1',
                url: './gis/3D/images/WRYZX/FQ-ZC.png' //正常
            },
            {
                name: 'gyy-2',
                url: './gis/3D/images/WRYZX/FQ-CB.png' //超标
            },
            {
                name: 'gyy-3',
                url: './gis/3D/images/WRYZX/FQ-WSJ.png' // 无数据
            },
            {
                name: 'SQZDY',
                url: './gis/3D/images/QT/SQZDY.png' //重点源
            }
        ];

        this.loadImages(arr);
    }

    // mapboxgl.Popup参数 可选择是否覆盖的方法
    getPopupOption(id, properties) {
        let option = {};

        switch (id) {
            case '在线监测企业':
            case '涉气重点源':
                option = {
                    offset: [0, -20]
                };
                break;

            case '大气站点':
                option = {
                    offset: [0, -100],
                    className: 'mapbox-tooltip-tip-air'
                };
                break;
        }

        return option;
    }
    // mapboxgl.Popup.HTML 需要覆盖的方法
    getPopupStr(id, properties) {
        let html = '';
        let vNodePopup = null;

        switch (id) {
            case '在线监测企业':
            case '涉气重点源':
                html = `<div class="protecttip" > ${properties.QYMC} </div>`;
                break;

            case '大气站点':
                html = document.createElement('div');

                //comMarkerPopup为组件名称
                vNodePopup = createVNode(PopZD, {
                    selectItem: properties,
                    layerID: properties.layerId
                });
                render(vNodePopup, html);
                break;
        }

        return html;
    }

    // marker.option 可选择是否覆盖方法
    getMarkerOption(id, properties) {
        let result = { className: 'coustom-marker' };
        return result;
    }

    //生成marker.element 需要覆盖的方法
    getMarkerHtml(properties, params) {
        let el = document.createElement('div');
        el.className = 'air-marker';
        let template = ``;

        switch (params.id) {
            case '大气站点':
                template = this.getAirMarkerStr(properties, params, '1');
                break;
        }
        el.innerHTML = template;
        return el;
    }

    /**
     * 大气marker的样式
     * type: 1: 默认样式 2: 真气网-小圆点 3、真气网-背景+文字 4：图片+底部闪烁 5:图片+文字+地图闪烁
     */
    getAirMarkerStr(properties, params, type = '1') {
        let level, dj, data, width, color, fontColor;
        level = PowerGL.getLevelByWrw(params.selectYZ, properties, 1);
        dj = PowerGL.getAirDJByLevel(level);
        data = properties[params.selectYZ] ? properties[params.selectYZ] : '--';

        let template = '';

        switch (type.toString()) {
            case '1': //常见效果
                template = `<dl style="transform: translate(0%, -50%);" class="air-effect1 dj${dj}"><dt>${properties.CDMC}&emsp;${data}</dt><dd></dd></dl>`;

                if (!params.showAno) {
                    template = `<dl class="air-effect1 dj${dj}" style="transform: translate(0%, -50%);"><dt>${data}</dt><dd></dd></dl>`;
                }
                break;

            case '2': //真气网-小圆点
                template = `<div class="air-effect2 circlelevel${dj}"></div>`;
                break;

            case '3': //真气网-背景+文字
                width = 18 * properties.CDMC.length;
                color = PowerGL.getAirColorByLevel(level);

                if (dj > 3) {
                    fontColor = '#fff';
                } else {
                    fontColor = '#000';
                }

                template = `
                <div class="air-effect3">
                <div class="marker_marker_1AA" style="background:${color};color:${fontColor}">
                <div class="marker_arrow_18U" style="border-top-color: ${color}"></div>${data}</div>
                <div class="marker_name" style="width: ${width}px; margin-left: -${(width - 35) / 2
                    }px;">${properties.CDMC}</div>
                </div>
                `;

                if (!params.showAno) {
                    template = `
                    <div class="air-effect3">
                    <div class="marker_marker_1AA" style="background:${color};color:${fontColor}">
                    <div class="marker_arrow_18U" style="border-top-color: ${color}"></div>${data}</div>
                    `;
                }
                break;
            case '4': // 图片+底部闪烁
                color = PowerGL.getAirColorByLevel(level);
                template = `<div class="air-effect4">
                <img><img style="width: 26px;margin-left: 2px;" src="
                     ./gis/3D/images/Air/KQZDW${dj}.png" alt="">
                <div class="air-effect4-dipan">
                    <svg width="34" height="16">
                        <ellipse class="e1" cx="17" cy="8" rx="17" ry="8" style="fill: ${color}"></ellipse>
                        <ellipse class="e2" cx="17" cy="8" rx="8" ry="4" style="fill:${color}></ellipse>
                    </svg>
                </div>
            </div>`;
                break;

            case '5': //宜昌大屏点位效果： 图片+文字+地图闪烁
                color = PowerGL.getAirColorByLevel(level);
                template = `<div class="air-effect5 type${dj}" >
				<h1><p>${properties.CDMC}</p><i>${data}</i></h1>
				<div class="air-effect5-dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                if (!params.showAno) {
                    template = `<div class="air-effect5  type${dj}" >
				<h1><i>${data}</i></h1>
				<div class="air-effect5-dipan">
                    <svg width="68" height="24">
                        <ellipse class="e1" cx="34" cy="12" rx="34" ry="12" stroke-width="2" fill="none" style="stroke:${color};"></ellipse>
                        <ellipse class="e2" cx="34" cy="12" rx="34" ry="12" stroke-dasharray="1,1" stroke-width="2" fill="none" style="stroke:${color}"></ellipse>
                        <ellipse class="e3" cx="34" cy="12" rx="34" ry="12" style="fill:${color};"></ellipse>
                    </svg>
                </div>
			</div>`;
                }
                break;

            case '6':
                color = PowerGL.getAirColorByLevel(level);

                debugger;
                template = `<div class="air-effect6  kqzl-${dj}">
                <h1>
                ${properties.CDMC}
                    <i style="color:${color};">省控</i>
                    <i style="color:${color};">
                    <b>${data}</b></i>
                </h1>
                <div class="air-effect6-dipan">
                    <svg width="34" height="16" style=" display: block;margin: 0 auto;">
                        <ellipse class="e1" cx="17" cy="8" rx="17" ry="8" style="fill: ${color}"></ellipse>
                        <ellipse class="e2" cx="17" cy="8" rx="8" ry="4" style="fill: ${color}"></ellipse>
                    </svg>
                </div>
            </div>`;
                break;
        }

        return template;
    }

    // 获取symbol layout属性  需要覆盖的方法
    getSymbolLayout(layerId) {
        let layout = {};

        switch (layerId) {
            case '在线监测企业':
                layout = {
                    'icon-image': [
                        'image',
                        ['concat', 'gyy-', ['get', 'SFCB']]
                    ],
                    'icon-size': [
                        'step',
                        ['zoom'],
                        0.3,
                        10,
                        0.5,
                        13,
                        0.8,
                        18,
                        1
                    ]
                };
                break;

            case '涉气重点源':
                layout = {
                    'icon-image': 'SQZDY',
                    'icon-size': [
                        'step',
                        ['zoom'],
                        0.3,
                        10,
                        0.5,
                        13,
                        0.8,
                        18,
                        1
                    ]
                };
                break;
        }

        layout['icon-allow-overlap'] = true;

        return layout;
    }

    //fill-paint 需要覆盖方法
    getFillPaint(params) {
        let option = {};
        switch (params.id) {
            case '周边范围':
                option = {
                    'fill-opacity': 0.4, // 填充的不透明度（可选，取值范围为 0 ~ 1，默认值为 1）
                    'fill-color': '#0f8cce', // 填充的颜色（可选，默认值为 #000000。如果设置了 fill-pattern，则 fill-color 将无效）
                    'fill-outline-color': '#70cdff'
                };

                break;
            default:
                break;
        }
        return option;
    }
}
export default PointUtil;
