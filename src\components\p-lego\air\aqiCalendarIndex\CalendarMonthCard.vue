<!-- @format -->

<!-- 空气质量日历网格视图 -->

<template>
    <div class="calendar-card">
        <div class="title">{{ monthTitleList[month - 1] }}</div>
        <div class="calandar-main">
            <div class="calendar-view">
                <div
                    v-for="(item, index) in weekList"
                    :key="index.id"
                    class="week-item"
                >
                    {{ item }}
                </div>
                <div v-for="(date, index) in monthDays" :key="index.id">
                    <CalendarTile
                        :day="tileDay(date)"
                        :year="tileYear(date)"
                        :month="tileMonth(date)"
                        :data="data"
                        :tileWidth="tileWidth"
                        :tileHeight="tileHeight"
                        :currentMonth="month"
                        :pollutant="pollutant"
                        :dayFontSize="dayFontSize"
                        :noMonthShow="noMonthShow"
                    />
                </div>
            </div>
            <CalendarInfo :data="monthData" />
        </div>
    </div>
</template>

<script>
import CalendarTile from './CalendarTile.vue';
import { getMonthDays } from './calendar.js';
import CalendarInfo from './CalendarInfo.vue';

export default {
    name: 'CalendarView',
    components: {
        CalendarTile,
        CalendarInfo
    },
    data() {
        return {
            aqi: '',
            day: '',
            monthData: [], //每个月的污染物数据
            weekList: ['日', '一', '二', '三', '四', '五', '六'],
            monthTitleList: [
                '一月',
                '二月',
                '三月',
                '四月',
                '五月',
                '六月',
                '七月',
                '八月',
                '九月',
                '十月',
                '十一月',
                '十二月'
            ]
        };
    },

    props: {
        data: Object,
        alldata: {
            type: Array,
            default: () => []
        },
        year: {
            type: String,
            default: '0'
        },
        month: Number,
        dayFontSize: {
            type: Number,
            default: 14
        },
        pollutant: {
            type: String,
            default: 'AQI'
        },
        tileWidth: {
            type: Number,
            default: 42
        },
        tileHeight: {
            type: Number,
            default: 42
        },
        noMonthShow: {
            type: Boolean,
            default: true
        }
    },

    computed: {
        //获取每个月的日期天数
        monthDays: function () {
            let result = getMonthDays(this.year, this.month);
            return result;
        }
    },

    watch: {
        data() {
            this.getMonthList();
            //       return this.monthData;
        }
    },

    mounted() {
        this.getMonthList();
    },

    methods: {
        //获取污染天数，遍历后传入info组件
        getMonthList() {
            let monthdata = [];
            for (let i in this.data) {
                if (
                    parseInt(this.data[i].date.substring(5, 7)) === this.month
                ) {
                    monthdata.push(this.data[i]);
                    this.monthData = monthdata;
                }
            }
        },
        tileYear(date) {
            return parseInt(date.substring(0, 4));
        },

        tileMonth(date) {
            return parseInt(date.substring(5, 7));
        },

        tileDay(date) {
            return parseInt(date.substring(8, 10));
        }
    }
};
</script>

<style lang="scss" scoped>
.calendar-view {
    position: relative;
    box-sizing: border-box;
    display: inline-grid;
    display: -ms-inline-grid;
    grid-template-columns: repeat(7, 1fr);
    -ms-grid-columns: repeat(7, 1fr);
    grid-column-gap: 6px;
    -ms-grid-column-gap: 6px;
}

.calandar-main {
    display: flex;
    flex-flow: column;
    flex-grow: 1;
}

.calendar-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.title {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    padding: 16px;
}

.week-item {
    text-align: center;
    font-size: 14px;
    color: #999;
    border-bottom: 1px solid #eee;
}
</style>
