<!-- @format -->

<template>
    <div>
        <!-- 地图全屏 -->
        <FullScreen :map="map" :mapCls="mapCls"></FullScreen>
    </div>
</template>

<script>
import Navigation from '../Navigation';
import CompassControl from '../CompassControl';
import FullScreen from '../FullScreen';
import MapMode from '../MapMode';
import LayerManagement from '../LayerManagement';
import MapXZQ from '../MapXZQ';
import MapMeasure from '../MapMeasure';
import ExportMap from '../ExportMap.vue';

export default {
    name: 'TopRightTool',
    props: ['map', 'mapCls', 'dock'],
    data() {
        return {};
    },
    components: {
        Navigation,
        CompassControl,
        FullScreen,
        MapMode,
        LayerManagement,
        MapXZQ,
        MapMeasure,
        ExportMap
    },
    computed: {},
    mounted() {},
    methods: {},
    watch: {}
};
</script>

<style scoped></style>
