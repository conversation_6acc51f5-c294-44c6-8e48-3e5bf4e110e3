<!-- @format -->

<template>
    <div class="zy-cell" style="height: 494px; width: 580px">
        <div class="zy-cell-hd">
            <p class="til til1">行政处罚</p>
            <span class="more"></span>
        </div>
        <div>
            <ul class="zy-data6">
                <li>
                    <span class="s1">一般处罚</span>
                    <span class="s2">{{ pageData.YBCF_TOTAL || '0' }}</span>
                </li>
                <li>
                    <span class="s1">按日计罚</span>
                    <span class="s2">{{ pageData.ARCF_TOTAL || '0' }}</span>
                </li>
                <li>
                    <span class="s1">查封扣押</span>
                    <span class="s2">{{ pageData.CFKY_TOTAL || '0' }}</span>
                </li>
                <li>
                    <span class="s1">限产停产 </span>
                    <span class="s2">{{ pageData.XCTC_TOTAL || '0' }}</span>
                </li>
                <li>
                    <span class="s1">行政拘留</span>
                    <span class="s2">{{ pageData.XZJL_TOTAL || '0' }}</span>
                </li>
                <li>
                    <span class="s1">涉嫌犯罪</span>
                    <span class="s2">{{ pageData.SXFZ_TOTAL || '0' }}</span>
                </li>
            </ul>

            <p class="zy-til1">违法案件类型</p>
            <div class="zy-tu">
                <p-pie
                    :data="wfData"
                    :option="pieOpt"
                    :config="{
                        showLabel: false,
                        color: [
                            '#61A5E8',
                            '#7ECF51',
                            '#E3935D',
                            '#EECB5F',
                            '#CD6757',
                            '#00FFFF',
                            '#FF00FF',
                            '#B3E8FF'
                        ]
                    }"
                    style="width: 100%; height: 200px"
                ></p-pie>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'administrativeSanction',
    props: {
        data: {
            type: Object,
            default: function () {
                return {
                    YBCF_TOTAL: 0,
                    XCTC_TOTAL: 0,
                    CFKY_TOTAL: 0,
                    SXFZ_TOTAL: 0,
                    XZJL_TOTAL: 0,
                    ARCF_TOTAL: 0,
                    wfajlx: [
                        { total: 0, name: '水' },
                        { total: 0, name: '土壤' },
                        { total: 0, name: '生态' },
                        { total: 0, name: '其他' },
                        { total: 0, name: '固废' },
                        { total: 0, name: '建设项目' },
                        { total: 0, name: '无' },
                        { total: 0, name: '气' }
                    ]
                };
            }
        }
    },
    data() {
        return {
            pageData: {},
            wfData: [],
            pieOpt: {}
        };
    },
    watch: {},
    created() {
        this.$pChart.setChartConfig({
            SHOW_TOOLBOX: false
        });
    },
    mounted() {
        this.getAdministrativeSantData();
    },
    methods: {
        getAdministrativeSantData() {
            this.wfData = [];
            this.pageData = this.data;
            let count = 0;
            let array = [];
            for (let i in this.data.wfajlx) {
                count += parseInt(this.data.wfajlx[i].total);
            }
            for (let i in this.data.wfajlx) {
                let zb = 0;
                zb =
                    count <= 0
                        ? 0
                        : Math.round(
                              (parseInt(this.data.wfajlx[i].total) / count) *
                                  10000
                          ) / 100.0;
                let temp = {
                    name: this.data.wfajlx[i].name,
                    value: this.data.wfajlx[i].total + '',
                    zb: zb
                };
                this.wfData.push(temp);
            }
            array = this.wfData;

            this.pieOpt = {
                color: [
                    '#3d8fff',
                    '#ee8d3a',
                    '#24bd5d',
                    '#eac722',
                    '#d83889',
                    '#29cde6',
                    '#8856fa'
                ],
                series: [
                    {
                        radius: ['50%', '80%'],
                        center: ['20%', '50%'],
                        tooltip: {
                            formatter: '{b}: {c}个 &nbsp {d}%'
                        }
                    }
                ],
                legend: {
                    show: true,
                    top: 'center',
                    left: '41%',
                    icon: 'rect',
                    // itemGap: 20,
                    itemHeight: 10,
                    itemWidth: 10,
                    textStyle: {
                        color: '#ffffff',
                        rich: {
                            label: {
                                width: 70,
                                lineHeight: 24,
                                fontSize: 16
                            },
                            val: {
                                width: 60,
                                lineHeight: 24,
                                fontSize: 16
                            }
                        }
                    },
                    formatter: function (r) {
                        for (let i in array) {
                            if (array[i].name === r) {
                                // return r + '  ' + array[i].zb + '%';
                                return `{label|${r}} {val|${array[i].zb}%}`;
                            }
                        }
                    }
                }
            };
        }
    }
};
</script>

<style lang="scss" scoped>
/* -- Reset -- */
body,
ul,
ol,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
p,
form,
input,
textarea,
select,
button {
    margin: 0;
    padding: 0;
    font: 12px 'Microsoft YaHei', SimSun, Arial, Helvetica, sans-serif;
}

@font-face {
    font-family: 'TTTGB';
    src: url('./fonts/TTTGB-Medium.woff2') format('woff2'),
        url('./fonts/TTTGB-Medium.woff') format('woff'),
        url('./fonts/TTTGB-Medium.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'DIN-Bold';
    src: url('./fonts/DIN-Bold.woff2') format('woff2'),
        url('./fonts/DIN-Bold.woff') format('woff'),
        url('./fonts/DIN-Bold.ttf') format('truetype'),
        url('./fonts/DIN-Bold.eot') format('embedded-opentype'),
        url('./fonts/DIN-Bold.svg') format('svg'),
        url('./fonts/DIN-Bold.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}
.zy-cell {
    background-color: rgba(16, 37, 58, 1);
    position: relative;
    margin-bottom: 10px;
    padding-bottom: 1px;
}
.zy-cell::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 10px;
    height: 10px;
    background-image: url(./images/cell-bg.png);
}
.zy-cell-hd {
    display: flex;
    height: 46px;
    padding: 0 10px;
    padding-bottom: 6px;
    background: url(./images/hd-bg.png) center bottom no-repeat;
    justify-content: space-between;
    align-items: center;
}

.zy-cell-hd .til {
    padding-left: 38px;
    font-size: 22px;
    color: #fff;
    line-height: 46px;
    background-position: 0 10px;
    background-repeat: no-repeat;
    font-family: 'TTTGB';
    text-shadow: 1px 1px 5px rgba(255, 255, 255, 0.8);
}

.zy-cell-hd .til1 {
    background-image: url(./images/til1.png);
}

.zy-cell-hd .more {
    width: 20px;
    height: 20px;
    background: url(./images/ic-more.png) no-repeat;
    cursor: pointer;
    margin-right: 20px;
}
.zy-data6 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 15px;
    margin-top: 25px;
}

.zy-data6 li {
    display: flex;
    width: 172px;
    height: 58px;
    background: url(./images/data6-li.png) no-repeat;
    margin-bottom: 15px;
}

.zy-data6 li .s1 {
    font-size: 16px;
    color: #fff;
    width: 84px;
    text-align: center;
    line-height: 58px;
}

.zy-data6 li .s2 {
    font-size: 32px;
    color: #00b4ff;
    line-height: 58px;
    font-family: 'DIN-Bold';
}
.zy-til1 {
    font-size: 18px;
    color: #fff;
    padding-left: 30px;
    background: url(./images/til1-bg.png) 0 center no-repeat;
    line-height: 50px;
    margin-left: 20px;
    text-align: left;
}
</style>
