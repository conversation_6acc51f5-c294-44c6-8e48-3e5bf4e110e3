<!-- @format -->

<template>
    <div ref="statistics1" class="chart"></div>
</template>
<script>
import * as echarts from 'echarts';
export default {
    props: {
        data: {
            type: Object,
            default: () => {
                return {
                    yAxisData: ['VOCS', 'SO2', 'NOX', 'COD', 'NH3'],
                    seriesData: [35, 17, 17, 13, 13],
                    radius: [0, 30, 30, 0]
                };
            }
        }
    },
    data() {
        return {};
    },

    computed: {},

    mounted() {
        this.setCharts();
    },
    methods: {
        setCharts() {
            let myChart = echarts.init(this.$refs.statistics1);
            let that = this;
            let option = {
                grid: {
                    top: 20,
                    left: 30,
                    right: 80,
                    bottom: 20,
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    fontSize: 14,
                    show: true
                },
                xAxis: {
                    name: `企业数量\n(家)`,
                    nameLocation: 'end',
                    nameTextStyle: {
                        fontSize: 15,
                        color: '#fff',
                        align: 'center',
                        verticalAlign: 'middle',
                        lineHeight: 25,
                        padding: [0, 0, 0, 50]
                    },
                    type: 'value',
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(255, 255, 255, 0.2)'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        fontSize: 14,
                        color: '#fff'
                    },

                    splitLine: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'category',
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(255, 255, 255, 0.2)'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        fontSize: 14,
                        color: '#fff'
                    },
                    data: that.data.yAxisData
                },
                series: [
                    {
                        type: 'bar',
                        barWidth: 15,
                        itemStyle: {
                            normal: {
                                show: true,
                                color: new echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    1,
                                    0,
                                    [
                                        {
                                            offset: 0,
                                            color: '#94D6E8'
                                        },
                                        {
                                            offset: 1,
                                            color: '#00D4E2'
                                        }
                                    ]
                                ), // 设置柱子的渐变色
                                barBorderRadius: that.data.radius
                            }
                        },

                        labelLine: {
                            show: true
                        },

                        data: that.data.seriesData
                    }
                ]
            };

            myChart.setOption(option);
        }
    }
};
</script>
<style scoped>
.chart {
    width: 100%;
    height: 100%;
}
</style>
