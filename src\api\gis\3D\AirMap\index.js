/** @format */

import axios from '_u/ajaxRequest';
import axios2 from 'axios';

// eslint-disable-next-line no-undef
const BASE_URL = '';
// ServerGlobalConstant.BASE_URL;

//空气站最新时间
export const getPointHourMaxDate = (data) => {
    return axios.request({
        url: BASE_URL + '/air/gis/getPointHourMaxDate',
        method: 'get',
        params: data
    });
};

//空气站点
export const getAirStationBaseInfo = (data) => {
    return axios.request({
        url: BASE_URL + '/air/gis/getAirStationBaseInfo',
        method: 'get',
        params: data
    });
};

//空气站点--数量
export const getAirTotal = (data) => {
    return axios.request({
        url: BASE_URL + '/air/gis/getAirTotal',
        method: 'get',
        params: data
    });
};

//工业源
export const getEnterpriseBaseInfo = (data) => {
    return axios.request({
        url: BASE_URL + '/air/gis/getEnterpriseBaseInfo',
        method: 'get',
        params: data
    });
};

//扬尘源
export const getDustingBaseInfo = (data) => {
    return axios.request({
        url: BASE_URL + '/air/gis/getDustingBaseInfo',
        method: 'get',
        params: data
    });
};

//涉气源数量
export const getAirSourceTotal = (data) => {
    return axios.request({
        url: BASE_URL + '/air/gis/getAirSourceTotal',
        method: 'get',
        params: data
    });
};

//周边分析-周边站点
export const getZbzdinfoList = (data) => {
    return axios.request({
        url: BASE_URL + '/air/gis/getZbzdinfoList',
        method: 'get',
        params: data
    });
};

//周边分析-周边点源
export const getSqwryxxList = (data) => {
    return axios.request({
        url: BASE_URL + '/air/gis/getSqwryxxList',
        method: 'get',
        params: data
    });
};

//获取服务器json数据
export const getJosnData = (url) => {
    return axios2.get(url, {
        params: {}
    });
};
