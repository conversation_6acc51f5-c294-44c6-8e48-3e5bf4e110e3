<!-- @format -->
<!-- 小圆点 -->
<template>
    <div></div>
</template>

<script>
import PointUtil from '../utils/PointUtil';
export default {
    props: [],
    data() {
        return {
            map: null,
            pointUtil: null,
            layerID: '小圆点'
        };
    },

    unmounted() {
        this.clear();
    },

    mounted() {
        this.initPage();
    },

    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);

            this.addLayer();
        },

        addLayer() {
            let data = [];

            let colors = [
                '#2DB62D',
                '#CBCC33',
                '#FF7E00',
                '#FF0000',
                '#99004C',
                '#7E0023',
                '#CCCCCC'
            ];

            for (let i = 0; i < 200; i++) {
                let ii = i % 7;
                let obj = {
                    JD: 120.59712716 + (Math.random() - 0.5) * 0.2,
                    WD: 30.96939348 + (Math.random() - 0.5) * 0.3,
                    MC: '点位' + i,
                    color: colors[ii]
                };

                data.push(obj);
            }

            let params = {
                id: this.layerID
            };
            this.pointUtil.addCirclePoint(data, params);
        },

        clear() {
            this.pointUtil.removeLayerByName(this.layerID);
        },

        pointClickHandle() {}
    }
};
</script>

<style scoped></style>
