<!-- @format -->

<template>
    <div>
        <div class="mask" style="display: block"></div>
        <div
            class="p-lego-alert"
            :style="{
                width: config.width || '1200px',
                height: config.height || '800px'
            }"
        >
            <div
                class="p-lego-hd"
                :style="{
                    backgroundColor: config.headBgcolor || '#0582D6'
                }"
            >
                <p class="til">{{ title || '-' }}</p>
                <i class="close" @click="handleClose"> </i>
            </div>
            <div class="p-lego-bd">
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    props: {
        title: {
            required: true,
            type: String
        },
        config: {
            type: Object,
            default: () => {
                return {
                    width: '1200px',
                    height: '800px',
                    headBgcolor: ''
                };
            }
        }
    },
    data() {
        return {};
    },
    mounted() {},
    methods: {
        handleClose() {
            this.$emit('close');
        }
    }
};
</script>

<style lang="scss" scoped>
/* 遮罩层 */
.mask {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: black;
    opacity: 0.5;
    z-index: 999;
    display: none;
}

.p-lego-alert {
    position: fixed;
    top: 100px;
    left: 50%;
    bottom: 0;
    right: 0;
    transform: translateX(-50%);
    background-color: var(--el-color-white);
    z-index: 1111;
    border-radius: 5px;
    overflow: hidden;
    .p-lego-hd {
        display: flex;
        height: 50px;
        // background-color: #008aff;
        justify-content: space-between;
        padding: 0 15px;
        align-items: center;
        .til {
            font-size: 18px;
            color: #fff;
            line-height: 50px;
        }
        .close {
            width: 17px;
            height: 17px;
            background: url(./images/aa-close.png) 0 0 no-repeat;
            background-size: 100%;
            cursor: pointer;
        }
    }
    .p-lego-bd {
        position: absolute;
        top: 50px;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 15px;
    }
}
</style>
