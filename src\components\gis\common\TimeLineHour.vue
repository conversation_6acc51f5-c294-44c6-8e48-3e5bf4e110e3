<!-- @format -->
<!--小时播放条，支持某一天24小时，一般用于大气 -->
<template>
    <div class="gis-timeaxis">
        <img
            @click="playClick"
            v-show="!timeId"
            src="../../../assets/gis/commom/images/dark/gis_plybtn.png"
            class="pd-play"
            v-if="showPlay"
            alt="渲染失败"
        />

        <img
            v-show="timeId"
            @click="pauseClick"
            src="../../../assets/gis/commom/images/dark/gis_pausebtn.png"
            class="pd-play"
            v-if="showPlay"
            alt="渲染失败"
        />
        <el-date-picker
            v-model="selectDate"
            type="date"
            placeholder="选择日期"
            class="dateCls2"
            :clearable="false"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-if="showDatePick"
            @change="selectDateChanged"
            style="width: 160px; margin-top: 10px"
        >
        </el-date-picker>
        <ul class="pd-ulnumlst">
            <li
                v-for="(item, index) of arrDate"
                :key="index"
                :class="[
                    {
                        on: selectHour == item.hour
                    },
                    item.clsName
                ]"
                @click="itemClick(item)"
            >
                {{ item.hour }}<sup>{{ item.tipStr }}</sup>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    data() {
        return {
            selectDate: '2021-03-22', //当前选中的日历
            arrDate: [], //
            selectHour: '', //当前选中的小时、
            lastDate: '', //最新时间
            timeId: '',

            showDatePick: true,
            showPlay: true
        };
    },
    components: {},
    unmounted() {
        this.clearTime();
    },
    methods: {
        selectDateChanged() {
            if (this.selectDate == this.lastDate.substring(0, 10)) {
                this.selectHour = this.lastDate.substring(11, 13);
            } else {
                this.selectHour = '23';
            }

            this.$emit('datePickChange', this.selectDate);
            this.intData();
        },

        //初始化timeline
        initTimeLine(option) {
            this.lastDate = option.lastDate;
            this.selectDate = option.selectDate.substring(0, 10);
            this.selectHour = parseInt(option.selectDate.substring(11, 13));

            if (option.showDatePick != undefined) {
                this.showDatePick = option.showDatePick;
            }

            if (option.showPlay != undefined) {
                this.showPlay = option.showPlay;
            }

            this.intData();
        },

        intData() {
            this.arrDate = [];

            for (let i = 0; i <= 23; i++) {
                let sj = this.selectDate + (i >= 10 ? ' ' + i : ' 0' + i);

                //在最新时间（initDate）之后
                let flag = this.$dayjs(sj + ':00:00').isAfter(
                    this.$dayjs(this.lastDate + ':00:00')
                );

                this.arrDate.push({
                    hour: i,
                    sj: sj,
                    tipStr: this.$dayjs(sj + ':00:00').format('MM/DD HH时'),
                    clsName: flag ? '' : 'init'
                });
            }

            this.$emit(
                'dateChange',
                this.selectDate + ' ' + this.selectHour,
                false
            );
        },

        itemClick(item) {
            this.clearTime();
            this.selectHour = item.hour;

            this.$emit('dateChange', item.sj, false);
        },

        //暂停
        pauseClick() {
            this.clearTime();
        },

        //播放按钮点击
        playClick() {
            //如果正在播放，则停止
            if (this.timeId) {
                this.clearTime();
                return;
            }

            let sj = this.selectDate + ' ' + this.selectHour;
            let flag1 = this.$dayjs(sj + ':00:00').isAfter(
                this.$dayjs(this.lastDate + ':00:00')
            );

            let flag2 = this.$dayjs(sj + ':00:00').isSame(
                this.$dayjs(this.lastDate + ':00:00')
            );

            //如果当前时间在最新时间的后面，则从第一个开始播放
            if (flag1 || flag2 || this.selectHour == '23') {
                this.selectHour = '00';
            }

            this.timeId = setInterval(() => {
                let i = parseInt(this.selectHour);
                i++;
                this.selectHour = i >= 10 ? i : '0' + i;

                let sj = this.selectDate + ' ' + this.selectHour;

                this.$emit('dateChange', sj, true);

                let flag = this.$dayjs(sj + ':00:00').isSame(
                    this.$dayjs(this.lastDate + ':00:00')
                );

                if (i == 23 || flag) {
                    this.clearTime();
                }
            }, 1000);
        },

        //设置进度条的状态
        setCls(arr) {
            // arr = [{ date: '2021-01', clsName: 'cb' }];
            for (let item of this.arrDate) {
                let result = arr.filter((oo) => {
                    return oo.date == item.sj;
                });

                if (result && result[0]) {
                    item.clsName = result[0].clsName;
                } else {
                    item.clsName = 'gray';
                }
            }
        },

        //清除定时器
        clearTime() {
            if (this.timeId) {
                clearInterval(this.timeId);
                this.timeId = null;
            }
        }
    }
};
</script>

<style scoped></style>
