<!-- @format -->

<template>
    <div
        id="exam_page"
        class="sw1212-wrap"
        style="background: #f1f3f7; width: 100%; bottom: 0; height: auto"
    >
        <!-- <div class="sw1212-dlghd">
            <strong>考试</strong>
            <img src="@/assets/exam/images/sw1212_cls2.png" class="icon" />
        </div> -->
        <div class="sw1212-dlgbd" style="padding: 16px 16px 0; top: 0">
            <div class="sw1212-mod">
                <div class="gap"></div>
                <div class="gap"></div>
                <dl class="sw1212-dlbx4">
                    <dt>{{ title || '-' }}</dt>
                    <dd>
                        <p class="bar" style="flex: 1; width: 0">
                            <b
                                :style="{
                                    width:
                                        (questions.filter(
                                            (item) => !!item.answer
                                        ).length *
                                            100) /
                                            questions.length +
                                        '%'
                                }"
                            ></b>
                        </p>
                        <span
                            ><i
                                >已完成
                                {{
                                    questions.filter((item) => !!item.answer)
                                        .length
                                }}题</i
                            >/共<i>{{ questions.length }}</i
                            >题</span
                        >
                    </dd>
                </dl>
                <div class="gap"></div>
                <div class="gap"></div>
            </div>
            <div class="gap15"></div>
            <div class="flx1" style="height: calc(100% - 152px)">
                <div style="flex: 1; width: 0">
                    <div
                        class="sw1212-mod"
                        style="
                            height: calc(100% - 100px);
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                        "
                        v-if="questions[currentIndex]"
                    >
                        <dl class="sw1212-dlbx5" style="margin: auto">
                            <dt>
                                第{{ currentIndex + 1 }}题
                                <i
                                    >（{{
                                        getTypeName(questions[currentIndex])
                                    }}）：</i
                                >
                            </dt>
                            <dd>
                                <div class="gap15"></div>
                                <div class="gap"></div>
                                <h1>
                                    {{ currentIndex + 1 }}、{{
                                        currentQuestion.name
                                    }}（ ）。
                                </h1>
                                <div class="gap15"></div>

                                <el-image
                                    fit="object-fit"
                                    style="width: 400px; height: 200px"
                                    v-for="(
                                        picTtem, picIndex
                                    ) in currentQuestion.tmFile"
                                    :key="picTtem"
                                    preview-teleported
                                    :initial-index="picIndex"
                                    :src="'/stfs_gd' + picTtem.path"
                                    :preview-src-list="
                                        returnImgUrl(currentQuestion.tmFile)
                                    "
                                >
                                </el-image>

                                <div class="gap15"></div>
                                <div
                                    class="sw1212-radiolst1"
                                    v-if="
                                        currentQuestion.type === 'DXT' ||
                                        currentQuestion.type === 'PDT'
                                    "
                                >
                                    <div>
                                        <el-radio-group
                                            style="
                                                display: flex;
                                                flex-direction: column;
                                                align-items: flex-start;
                                            "
                                            v-model="currentAnswer"
                                            disabled
                                        >
                                            <div
                                                v-for="(
                                                    item, index
                                                ) in currentQuestion.choice"
                                                :key="item.xh"
                                            >
                                                <el-radio
                                                    :label="item.xh"
                                                    size="large"
                                                    ><p
                                                        style="font-size: 22px"
                                                        :style="{
                                                            color: getChoiceColor(
                                                                item
                                                            )
                                                        }"
                                                    >
                                                        {{ item.text }}
                                                    </p>
                                                </el-radio>
                                                <div>
                                                    <el-image
                                                        fit="object-fit"
                                                        style="
                                                            width: 300px;
                                                            height: 200px;
                                                            margin: 10px;
                                                        "
                                                        v-for="(
                                                            picTtem, picIndex
                                                        ) in item.xxFile"
                                                        :key="picTtem"
                                                        preview-teleported
                                                        :initial-index="
                                                            picIndex
                                                        "
                                                        :src="
                                                            '/stfs_gd' +
                                                            picTtem.path
                                                        "
                                                        :preview-src-list="
                                                            returnImgUrl(
                                                                item.xxFile
                                                            )
                                                        "
                                                    >
                                                    </el-image>
                                                </div>
                                            </div>
                                        </el-radio-group>
                                    </div>
                                </div>
                                <div
                                    class="sw1212-radiolst1"
                                    v-if="currentQuestion.type === 'FXT'"
                                >
                                    <!-- <div
                                        v-for="(
                                            item, index
                                        ) in currentQuestion.choice"
                                        :key="item.xh"
                                    >
                                        <label
                                            class="sw1212-radio1"
                                            style="cursor: pointer"
                                        >
                                            <el-checkbox
                                                v-model="item.checked"
                                                @change="selectAnswer(item)"
                                            />
                                            <span>{{ item.text }}</span>
                                        </label>
                                        <div
                                            class="gap"
                                            v-if="
                                                index !==
                                                currentQuestion.choice.length -
                                                    1
                                            "
                                        ></div>
                                        <div
                                            class="gap"
                                            v-if="
                                                index !==
                                                currentQuestion.choice.length -
                                                    1
                                            "
                                        ></div>
                                    </div> -->
                                    <div>
                                        <el-checkbox-group
                                            style="
                                                display: flex;
                                                flex-direction: column;
                                                align-items: flex-start;
                                            "
                                            v-model="currentAnswer"
                                            disabled
                                        >
                                            <div
                                                v-for="(
                                                    item, index
                                                ) in currentQuestion.choice"
                                                :key="item.xh"
                                            >
                                                <el-checkbox
                                                    :label="item.xh"
                                                    size="large"
                                                    ><p
                                                        style="font-size: 22px"
                                                        :style="{
                                                            color: getChoiceColor(
                                                                item
                                                            )
                                                        }"
                                                    >
                                                        {{ item.text }}
                                                    </p>
                                                </el-checkbox>
                                                <div>
                                                    <el-image
                                                        fit="object-fit"
                                                        style="
                                                            width: 300px;
                                                            height: 200px;
                                                            margin: 10px;
                                                        "
                                                        v-for="(
                                                            picTtem, picIndex
                                                        ) in item.xxFile"
                                                        :key="picTtem"
                                                        preview-teleported
                                                        :initial-index="
                                                            picIndex
                                                        "
                                                        :src="
                                                            '/stfs_gd' +
                                                            picTtem.path
                                                        "
                                                        :preview-src-list="
                                                            returnImgUrl(
                                                                item.xxFile
                                                            )
                                                        "
                                                    >
                                                    </el-image>
                                                </div>
                                            </div>
                                        </el-checkbox-group>
                                    </div>
                                </div>
                            </dd>
                            <!-- <div class="gap" style="height: 300px"></div> -->
                            <!-- <dt>正确答案：C&emsp;&emsp;&emsp;我的答案：C</dt> -->
                        </dl>
                        <div>
                            <div
                                class="sw1212-askcon"
                                style="margin-bottom: 30px"
                                v-if="currentQuestion.ztjx"
                            >
                                <h1>正确答案：{{ rightOption }}</h1>
                                <h1>解析：</h1>
                                <p v-html="currentQuestion.ztjx"></p>
                            </div>
                        </div>
                    </div>
                    <div class="gap15"></div>
                    <ul class="sw1212-ulpage1">
                        <li
                            @click="prev()"
                            :style="{
                                color: currentIndex === 0 ? '#eee' : '#467DDC'
                            }"
                        >
                            上一题
                        </li>
                        <li
                            @click="next()"
                            :style="{
                                color:
                                    currentIndex === questions.length - 1
                                        ? '#eee'
                                        : '#467DDC'
                            }"
                        >
                            下一题
                        </li>
                    </ul>
                    <div class="gap15"></div>
                </div>
                <div style="width: 400px; margin-left: 14px">
                    <div
                        class="sw1212-mod"
                        style="height: 100%; overflow: auto"
                    >
                        <div class="gap15"></div>
                        <div class="flx1 ac">
                            <strong class="sw1212-txt1"
                                >答题卡
                                <!-- <i style="color: #467ddc">0</i>/6 -->
                            </strong>
                        </div>
                        <div class="gap"></div>
                        <div
                            class="gap"
                            style="border-top: 1px solid #eeeeee"
                        ></div>
                        <p
                            class="sw1212-txt3"
                            v-if="
                                questions.filter((item) => item.type === 'DXT')
                                    .length
                            "
                        >
                            单选题 （共{{
                                questions.filter((item) => item.type === 'DXT')
                                    .length
                            }}题）
                        </p>
                        <div class="gap15"></div>
                        <ul class="sw1212-ulbx6">
                            <li
                                :class="[
                                    { cur: questionIndex === currentIndex },
                                    { done: question.answer },
                                    { right: question.isRight === '1' },
                                    {
                                        wrong:
                                            question.answer &&
                                            question.isRight === '0'
                                    }
                                ]"
                                v-for="(
                                    question, questionIndex
                                ) in questions.filter(
                                    (item) => item.type === 'DXT'
                                )"
                                :key="questionIndex"
                                @click="currentIndex = questionIndex"
                            >
                                {{ question.num }}
                            </li>
                            <!-- <li class="on"></li> -->
                        </ul>
                        <div class="gap15"></div>
                        <div class="gap15"></div>
                        <p
                            class="sw1212-txt3"
                            v-if="
                                questions.filter((item) => item.type === 'FXT')
                                    .length
                            "
                        >
                            多选题 （共{{
                                questions.filter((item) => item.type === 'FXT')
                                    .length
                            }}题）
                        </p>
                        <div class="gap15"></div>
                        <ul class="sw1212-ulbx6">
                            <li
                                :class="[
                                    {
                                        cur:
                                            questionIndex ===
                                            currentIndex -
                                                questions.filter(
                                                    (item) =>
                                                        item.type === 'DXT'
                                                ).length
                                    },
                                    { done: question.answer },
                                    { right: question.isRight === '1' },
                                    {
                                        wrong:
                                            question.answer &&
                                            question.isRight === '0'
                                    }
                                ]"
                                v-for="(
                                    question, questionIndex
                                ) in questions.filter(
                                    (item) => item.type === 'FXT'
                                )"
                                :key="questionIndex"
                                @click="
                                    currentIndex =
                                        questionIndex +
                                        questions.filter(
                                            (item) => item.type === 'DXT'
                                        ).length
                                "
                            >
                                {{ question.num }}
                            </li>
                        </ul>
                        <div class="gap15"></div>
                        <div class="gap15"></div>
                        <p
                            class="sw1212-txt3"
                            v-if="
                                questions.filter((item) => item.type === 'PDT')
                                    .length
                            "
                        >
                            判断题 （共{{
                                questions.filter((item) => item.type === 'PDT')
                                    .length
                            }}题）
                        </p>
                        <div class="gap15"></div>
                        <ul class="sw1212-ulbx6">
                            <li
                                :class="[
                                    {
                                        cur:
                                            questionIndex ===
                                            currentIndex -
                                                questions.filter(
                                                    (item) =>
                                                        item.type === 'DXT'
                                                ).length -
                                                questions.filter(
                                                    (item) =>
                                                        item.type === 'FXT'
                                                ).length
                                    },
                                    { done: question.answer },
                                    { right: question.isRight === '1' },
                                    {
                                        wrong:
                                            question.answer &&
                                            question.isRight === '0'
                                    }
                                ]"
                                v-for="(
                                    question, questionIndex
                                ) in questions.filter(
                                    (item) => item.type === 'PDT'
                                )"
                                :key="questionIndex"
                                @click="
                                    currentIndex =
                                        questionIndex +
                                        questions.filter(
                                            (item) => item.type === 'DXT'
                                        ).length +
                                        questions.filter(
                                            (item) => item.type === 'FXT'
                                        ).length
                                "
                            >
                                {{ question.num }}
                            </li>
                        </ul>
                        <div class="gap" style="height: 20px"></div>
                        <ul class="sw1212-ulbx7">
                            <li>
                                <img
                                    src="@/assets/exam/images/sw1212_ic3d.png"
                                    alt=""
                                />已做
                            </li>
                            <li>
                                <img
                                    src="@/assets/exam/images/sw1212_ic4d.png"
                                    alt=""
                                />未做
                            </li>
                            <li style="visibility: hidden">
                                <img
                                    src="@/assets/exam/images/sw1212_ic2d.png"
                                    alt=""
                                />标记
                            </li>
                        </ul>
                        <div class="gap15"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { queryKsstList } from '@/api/exam.js';
import { get } from 'lodash';

export default {
    name: 'Exercise',
    data() {
        return {
            title: '',
            questions: [],
            currentAnswer: null,
            currentIndex: 0
        };
    },
    computed: {
        currentQuestion() {
            return this.questions[this.currentIndex];
        },
        rightOption() {
            if (this.currentQuestion.type === 'PDT') {
                return this.currentQuestion.choice.find(
                    (choice) => choice.isRight != '0'
                ).text;
                // .replace(/A.|B.|C.|D.|/, '');
            } else {
                let rightOptions = [];
                this.currentQuestion.choice.forEach((choice, index) => {
                    if (choice.isRight != '0') {
                        switch (index) {
                            case 0:
                                rightOptions.push('A');
                                break;
                            case 1:
                                rightOptions.push('B');
                                break;
                            case 2:
                                rightOptions.push('C');
                                break;
                            case 3:
                                rightOptions.push('D');
                                break;
                            case 4:
                                rightOptions.push('E');
                                break;
                            case 5:
                                rightOptions.push('F');
                                break;
                        }
                        return index;
                    }
                });

                return rightOptions.join(',');
            }
        },
        rightOptionXh() {
            if (this.currentQuestion.type === 'PDT') {
                return this.currentQuestion.choice.find(
                    (choice) => choice.isRight != '0'
                ).xh;
            } else {
                let rightOptions = [];
                this.currentQuestion.choice.forEach((choice, index) => {
                    if (choice.isRight != '0') {
                        rightOptions.push(choice.xh);
                    }
                });

                return rightOptions;
            }
        }
    },

    created() {
        this.getQuestions();

        // this.questions = [
        //     ...(test[0].DXT || []),
        //     ...(test[0].FXT || []),
        //     ...(test[0].PDT || [])
        // ];
        // // 遍历this.questions，给每个加上num字段
        // this.questions.forEach((item, index) => {
        //     item.num = index + 1;
        // });
        // //初始化答案
        // this.currentAnswer = this.currentQuestion?.answer;
    },
    unmounted() {},
    watch: {
        currentIndex: {
            handler() {
                if (this.currentQuestion.type === 'FXT') {
                    this.currentAnswer = this.currentQuestion.answer
                        ? this.currentQuestion.answer.split(',')
                        : [];
                } else {
                    this.currentAnswer = this.currentQuestion.answer
                        ? this.currentQuestion.answer
                        : '';
                }
            }
        }
    },
    mounted() {},
    methods: {
        getQuestions() {
            console.log(this.$route);
            this.title = this.$route.query.BT;
            let params = {
                SJXH: this.$route.params.id,
                errorTm: this.$route.query.errorTm || '',
                SFJS: this.$route.query.SFJS || ''
            };
            if (!this.$route.query.errorTm) delete params.errorTm;
            if (!this.$route.query.SFJS) delete params.SFJS;
            queryKsstList(params).then((res) => {
                this.questions = [
                    ...(res.data_json[0].DXT || []),
                    ...(res.data_json[0].FXT || []),
                    ...(res.data_json[0].PDT || [])
                ];
                // 遍历this.questions，给每个加上num字段
                this.questions.forEach((item, index) => {
                    item.num = index + 1;
                });
                //初始化答案
                this.currentAnswer = this.currentQuestion?.answer;
            });
        },
        prev() {
            if (this.currentIndex !== 0) {
                this.currentIndex -= 1;
            }
        },
        next() {
            if (this.currentIndex !== this.questions.length - 1) {
                this.currentIndex += 1;
            }
        },
        getTypeName(item) {
            switch (item.type) {
                case 'DXT':
                    return '单选题';
                case 'FXT':
                    return '多选题';
                case 'PDT':
                    return '判断题';
            }
        },
        returnImgUrl(list) {
            let arr = [];
            list.map((e) => {
                arr.push('/stfs_gd' + e.path);
            });
            return arr;
        },
        getChoiceColor(item) {
            if (this.currentQuestion.type === 'FXT') {
                if (this.currentAnswer.includes(item.xh)) {
                    //多选题全选对了才显示绿色
                    if (this.currentQuestion.isRight === '1') {
                        return '#67C23A';
                    } else {
                        return '#F56C6C';
                    }
                } else {
                    return '#333';
                }
            } else {
                if (this.currentAnswer === item.xh) {
                    if (this.currentQuestion.isRight === '1') {
                        return '#67C23A';
                    } else {
                        return '#F56C6C';
                    }
                } else {
                    return '#333';
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep.el-radio,
::v-deep.el-checkbox {
    height: auto;
    white-space: unset;
    margin-bottom: 10px;
}

.exam_result {
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    h1 {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    div {
        font-size: 20px;
        font-weight: bold;
        line-height: 30px;
        width: 200px;
        display: flex;
        justify-content: space-between;
    }
    div:nth-of-type(3) {
        margin-bottom: 20px;
    }
}
.sw1212-ulbx6 li.cur {
    border-color: #467ddc !important;
    background-color: #467ddc !important;
    color: #fff;
}

.sw1212-ulbx6 li.right {
    background: #67c23a;
    border-color: #67c23a;
    color: #fff;
}
.sw1212-ulbx6 li.wrong {
    background: #f56c6c;
    border-color: #f56c6c;
    color: #fff;
}
</style>
<style>
.dialog_custom .el-dialog__body {
    height: calc(100% - 44px);
    width: 100%;
    padding: 0;
}
.el-radio__input.is-disabled + span.el-radio__label {
    color: #333;
}
.el-radio__input.is-disabled.is-checked .el-radio__inner {
    background-color: #409eff;
    border-color: #2083f3;
}
.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
    background-color: #f5f7fa;
}

.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #409eff;
    border-color: #2083f3;
}
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #f5f7fa;
}
</style>
