<!-- @format -->
<!--
  @Author: maliang
  @Time: 2022/8/24
  @Des: el-tabel二次封装；包含单选、多选、自动滚动等功能
 -->
<template>
    <div
        :class="[
            'bowo-table_wrap',
            { 'is-custom-style': tableConfig.useCustomStyle }
        ]"
    >
        <el-table
            ref="bowoTableView"
            v-if="!reloadFlag"
            flexible
            :data="tableList"
            empty-text="暂无数据"
            @select="handleSelect"
            @select-all="handleSelectAll"
            @cell-mouse-enter="handleMouseEnter"
            @cell-mouse-leave="handleMouseLeave"
            v-bind="tableConfig"
            style="width: 100%"
        >
            <!-- 勾选框 -->
            <el-table-column
                type="selection"
                v-if="tableConfig.showCheckbox"
                fixed="left"
                width="55"
            >
            </el-table-column>
            <!-- 序号 -->
            <el-table-column
                v-if="tableConfig.showOrder"
                type="index"
                :align="'center'"
                fixed="left"
                width="60"
            >
                <template #header><span>序号</span></template>
                <template #default="scope"
                    ><span>{{ getOrder(scope.$index + 1) }}</span></template
                >
            </el-table-column>
            <!-- 主体 -->
            <el-table-column
                v-for="(column, index) in columns"
                :key="`${column.field}-${index}`"
                :prop="column.field"
                :label="column.title"
                show-overflow-tooltip
                :align="column.align || tableConfig.align || 'left'"
                :fixed="
                    column.fixed || (column.type === 'operation' && 'right')
                "
                :width="column.width"
                :min-width="column.minWidth"
            >
                <template #header>
                    <cell
                        v-if="column.headerRender"
                        :column="column"
                        :render="column.headerRender"
                    ></cell>
                    <template v-else>
                        <span v-html="column.title"></span>
                    </template>
                </template>
                <template v-slot="scope">
                    <cell
                        v-if="column.render"
                        :column="column"
                        :row="scope.row"
                        :index="scope.$index"
                        :render="column.render"
                    ></cell>
                    <span
                        v-else-if="
                            column.formatter && isFunction(column.formatter)
                        "
                        >{{
                            column.formatter(scope.row[column.field]) || '-'
                        }}</span
                    >
                    <span v-else>
                        {{
                            scope.row[column.field] ||
                            (scope.row[column.field] === 0 ? '0' : '-')
                        }}
                    </span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import Cell from './cell';
export default {
    props: {
        // 样式配置
        styleConfig: {
            type: Object,
            default() {
                return {};
            }
        },
        /**
         * field: 映射字段
         * title: 表头标题
         * align: 对齐方式 同el-table align
         * fixed: 固定位置 同el-table fixed
         * type为operation(操作) fixed:right
         * width: 列宽  同el-table width
         * minWidth: 最小列宽   同el-table minWidth
         * */
        // 表头
        columns: {
            type: Array,
            default() {
                return [];
            }
        },
        // 表数据
        tableData: {
            type: Array,
            default() {
                return [];
            }
        },
        // 勾选的主键集合
        checkedIds: {
            type: Array,
            default() {
                return [];
            }
        },
        // 滚动配置
        scrollConfig: {
            type: Object,
            default() {
                return {
                    autoPlay: false,
                    step: 0.5,
                    limitMoveNum: 6 // 开始无缝滚动的数据量
                };
            }
        },
        // 配置项
        config: {
            type: Object,
            default() {
                return {};
            }
        },
        // 自定义序号函数
        setOrder: Function
    },
    components: {
        Cell
    },
    computed: {
        finalScrollConfig() {
            const defaultScrollConfig = {
                autoPlay: false,
                step: 0.5,
                limitMoveNum: 6 // 开始无缝滚动的数据量
            };
            return Object.assign({}, defaultScrollConfig, this.scrollConfig);
        },
        // 完成一次动画时间
        finishAnimationTime() {
            const { step } = this.finalScrollConfig;
            const { tableData } = this;
            const len = tableData.length;
            return step * len * 2 + 's';
        },
        tableConfig() {
            const defaultConfig = {
                primaryKey: 'id', // row 主键
                maxHeight: '300px',
                border: false,
                showOrder: true, // 是否显示序号
                showCheckbox: true, // 是否显示勾选框
                multiple: true, // 是否多选
                align: 'center',
                useCustomStyle: false // 是否开启自定义样式功能 默认否
            };
            return Object.assign({}, defaultConfig, this.config);
        },
        tableStyle() {
            const { styleConfig } = this;
            const defaultStyle = {
                headerBgColor: 'rgba(2, 115, 194, 0.2)', // 表头背景色
                headerFontColor: '#fff', // 表头字体颜色
                cellHeight: '60px', // 单元格高度

                bodyBgColor: 'transparent', // tbody背景色
                bodyFontColor: '#fff', // tbody字体颜色
                borderColor: 'rgba(52,151,228,0.3)', // 边框颜色
                hoverBgColor: 'transparent', // hover背景色

                fixedBgColor: 'transparent' // 固定列背景色
            };
            return Object.assign({}, defaultStyle, styleConfig);
        }
    },
    watch: {
        columns(nv) {
            this.reloadFlag = true;
            this.$nextTick(() => {
                // this.$refs.bowoTableView && this.$refs.bowoTableView.doLayout(); // 使用重新布局不管用
                this.reloadFlag = false;
                this.$nextTick(() => {
                    this.handleCheckedIds();
                });
            });
        },
        tableData: {
            handler(nv) {
                const { autoPlay = false, limitMoveNum = 6 } =
                    this.finalScrollConfig;
                if (nv.length < limitMoveNum) {
                    this.canScroll = false;
                }
                if (autoPlay) {
                    this.tableList = [...nv.slice(), ...nv.slice()];
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.startMove();
                        }, 2000);
                    });
                } else {
                    this.tableList = nv.slice();
                }
            },
            immediate: true
        },
        checkedIds: {
            handler(ids) {
                if (!this.reloadFlag) {
                    this.handleCheckedIds();
                }
            },
            immediate: true
        },
        selectedList: {
            handler(v) {
                this.$emit('on-selectList', v);
            }
        }
    },
    data() {
        return {
            selectedList: [], //已勾选行

            reloadFlag: false,

            tableList: [],
            canScroll: true,

            moveY: 'translate3d(0, -240px, 0)'
        };
    },
    methods: {
        isFunction(fn) {
            return Object.prototype.toString.call(fn) === '[object Function]';
        },
        handleSelectAll(selection) {
            const { primaryKey = 'id', multiple } = this.tableConfig;
            if (multiple === false) {
                this.$refs.bowoTableView.clearSelection();
                this.selectedList = [];
                this.$emit('update:checkedIds', []);
            } else {
                this.selectedList = selection;
                this.$emit(
                    'update:checkedIds',
                    selection.map((item) => item[primaryKey])
                );
            }
        },
        //处理选择
        handleSelect(selection, row) {
            const { primaryKey = 'id', multiple } = this.tableConfig;
            if (!multiple) {
                // 单选
                const targetIndex = selection.findIndex(
                    (item) => item[primaryKey] === row[primaryKey]
                );
                selection.forEach((item, index) => {
                    if (targetIndex === index) {
                        this.$refs.bowoTableView.toggleRowSelection(item, true);
                    } else {
                        this.$refs.bowoTableView.toggleRowSelection(
                            item,
                            false
                        );
                    }
                });
                this.selectedList = selection.length === 0 ? [] : [row];
                this.$emit(
                    'update:checkedIds',
                    selection.length === 0 ? [] : [row[primaryKey]]
                );
            } else {
                // 多选
                this.selectedList = selection;
                this.$emit(
                    'update:checkedIds',
                    selection.map((item) => item[primaryKey])
                );
            }
        },
        handleCheckedIds() {
            const { primaryKey = 'id', multiple } = this.tableConfig;
            let ids = [];
            //过滤相同的id
            let checkedIds = [...new Set(this.checkedIds)];
            if (multiple) {
                ids = checkedIds;
            } else {
                ids = checkedIds.length > 0 ? [checkedIds[0]] : [];
            }
            if (this.tableData.length === 0) {
                return;
            }
            let arr = [];
            this.$nextTick(() => {
                this.tableData.forEach((item) => {
                    if (ids.includes(item[primaryKey])) {
                        arr.push(item);
                        this.$refs.bowoTableView.toggleRowSelection(item, true);
                    } else {
                        this.$refs.bowoTableView.toggleRowSelection(
                            item,
                            false
                        );
                    }
                });
                this.selectedList = arr;
            });
        },
        getOrder(val) {
            const { setOrder, isFunction } = this;
            if (setOrder && isFunction(setOrder)) {
                return setOrder(val);
            }
            let num = val;
            return num;
        },
        // 开始滚动
        startMove() {
            const { canScroll } = this;
            const tableWrapEl = this.getTableWrapEl();
            const table = this.$refs.bowoTableView;
            const sct =
                table.$el.querySelector('.el-scrollbar__wrap') ||
                table.$el.querySelector('.el-table__body-wrapper');
            const { autoPlay } = this.finalScrollConfig;
            if (tableWrapEl && canScroll && autoPlay) {
                // 拿到表格中承载数据的div元素
                const Y = Math.floor(tableWrapEl.offsetHeight / 2);
                sct.style.overflowY = 'hidden';
                setTimeout(() => {
                    tableWrapEl.style.width = '100%';
                }, 20);

                this.moveY = `translate3d(0, -${Y}px, 0)`;
                if (!tableWrapEl.classList.contains('rowup')) {
                    this.setKeyframes(
                        this.moveY,
                        tableWrapEl,
                        this.finishAnimationTime
                    );
                }
            }
        },

        getTableWrapEl() {
            const table = this.$refs.bowoTableView;
            const tableWrapEl =
                table.$el.getElementsByClassName('el-scrollbar__view')[0] ||
                table.$el.getElementsByClassName('el-scrollbar__wrap')[0] ||
                table.$el.getElementsByClassName('el-table__body')[0];
            return tableWrapEl;
        },
        // 暂停滚动
        handleMouseEnter() {
            const { autoPlay = false } = this.finalScrollConfig;
            if (!autoPlay) {
                return;
            }
            this.canScroll = false;
            const tableWrapEl = this.getTableWrapEl();
            tableWrapEl && (tableWrapEl.style.animationPlayState = 'paused');
        },
        // 若可以自动滚动 启用滚动
        handleMouseLeave() {
            const { autoPlay = false } = this.finalScrollConfig;
            if (!autoPlay) {
                return;
            }
            this.canScroll = true;
            const tableWrapEl = this.getTableWrapEl();
            tableWrapEl && (tableWrapEl.style.animationPlayState = 'running');
        },
        setKeyframes(moveY, el, finishAnimationTime) {
            const cssStyle = `@keyframes rowup {
                0% {-webkit-transform: translate3d(0, 0, 0);transform: translate3d(0, 0, 0);}
                100% {-webkit-transform: ${moveY};transform: ${moveY};}
            }`;
            this.insertCSSRule(el, cssStyle);
            el.setAttribute(
                'style',
                `animation: ${finishAnimationTime} rowup linear infinite normal;`
            );
        },
        insertCSSRule(element, cssStyle) {
            const style = document.createElement('style');
            style.appendChild(document.createTextNode(cssStyle));
            element.appendChild(style);
        }
    }
};
</script>

<style lang="scss" scoped>
.bowo-table_wrap {
    :deep(.rowup) {
        position: relative;
    }
    &.is-custom-style {
        :deep(.el-table) {
            --el-table-border-color: v-bind('tableStyle.borderColor');
            background-color: v-bind('tableStyle.bodyBgColor');
        }
        :deep(.el-table thead th) {
            color: v-bind('tableStyle.headerFontColor');
            background-color: v-bind('tableStyle.headerBgColor');
        }
        :deep(.el-table th.is-leaf) {
            border-color: v-bind('tableStyle.borderColor');
        }
        :deep(.el-table td) {
            border-color: v-bind('tableStyle.borderColor');
        }
        :deep(.el-table::before) {
            background-color: v-bind('tableStyle.borderColor');
        }
        :deep(.el-table th.el-table__cell) {
            background-color: v-bind('tableStyle.headerBgColor');
        }
        :deep(.el-table tr) {
            background-color: v-bind('tableStyle.bodyBgColor');
            --el-table-row-hover-bg-color: v-bind('tableStyle.hoverBgColor');
            color: v-bind('tableStyle.bodyFontColor');
        }

        :deep(.el-table tbody tr.el-table__row:hover) {
            background-color: v-bind('tableStyle.hoverBgColor');
        }
        :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
            background-color: v-bind('tableStyle.hoverBgColor');
        }
        :deep(.el-table .el-table__cell) {
            padding: 0px;
            height: v-bind('tableStyle.cellHeight');
            line-height: v-bind('tableStyle.cellHeight');
        }
        :deep(
                .el-table__body-wrapper tr td.el-table-fixed-column--left,
                .el-table__body-wrapper tr td.el-table-fixed-column--right,
                .el-table__body-wrapper tr th.el-table-fixed-column--left,
                .el-table__body-wrapper tr th.el-table-fixed-column--right,
                .el-table__footer-wrapper tr td.el-table-fixed-column--left,
                .el-table__footer-wrapper tr td.el-table-fixed-column--right,
                .el-table__footer-wrapper tr th.el-table-fixed-column--left,
                .el-table__footer-wrapper tr th.el-table-fixed-column--right
            ) {
            background: v-bind('tableStyle.fixedBgColor');
        }
    }
}
</style>
