<!-- @format -->

<template>
    <div class="styleTool">
        <ul>
            <li
                v-for="(item, index) of arrType"
                :key="index"
                @click="itemClick(item)"
                :class="{ on: item.value == style }"
            >
                {{ item.name }}
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    data() {
        return {
            arrType: [
                {
                    name: 'echart转移图',
                    value: '0'
                },
                {
                    name: '热力图',
                    value: '1'
                },
                {
                    name: '小圆点',
                    value: '2'
                },
                {
                    name: '面填充与文本注记',
                    value: '3'
                },
                {
                    name: '线绘制',
                    value: '4'
                },
                {
                    name: '拉升面',
                    value: '5'
                },
                {
                    name: '立体图',
                    value: '6'
                },
                {
                    name: 'geo<PERSON>son展示',
                    value: '7'
                },
                {
                    name: '点，线、面缓冲',
                    value: '8'
                }
            ],
            style: '0'
        };
    },

    methods: {
        itemClick(item) {
            this.style = item.value;

            this.$emit('funcChange', this.style);
        }
    }
};
</script>

<style scoped>
.styleTool {
    position: fixed;
    top: 80px;
    right: 10px;
    left: 0;
}

.styleTool ul {
    display: flex;
}

.styleTool ul li {
    flex: 1;
    border: 1px solid #0097a7;
    border-radius: 4px;
    font-size: 16px;
    color: #ddd;
    cursor: pointer;
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    text-align: center;
    padding: 0 10px;
    margin-left: 10px;
}

.styleTool ul li.on {
    background: #01cbe1;
    color: #0a343a;
}
</style>
