/** @format */

import axios from '_u/ajaxRequest';

const BASE_URL = ServerGlobalConstant.dataUrl;

// 动态列表查询
export const analysiscontroller = (data) => {
    return axios.request({
        method: 'post',
        url:
            BASE_URL +
            '/platform/component/queryservice/analysis/analysiscontroller/query/' +
            data.xh,
        data: data
    });
};

// 互动交流列表
export const getLynrList = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/hdjl/hdjlController/getLynrList',
        data: data
    });
};

// 评论点赞
export const agreeMessage = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/hdjl/hdjlController/agreeMessage',
        data: data
    });
};

// 发送评论
export const saveMessage = (data) => {
    return axios.request({
        method: 'post',
        url: BASE_URL + '/xxks/hdjl/hdjlController/saveMessage',
        data: data
    });
};
