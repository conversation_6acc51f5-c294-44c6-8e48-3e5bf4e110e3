<!-- @format -->
<!-- 业务主要入口 -->
<template>
    <div style="position: fixed; top: 10px; left: 10px" v-show="mod != 'look'">
        <div class="condition">
            <label>经度：</label>
            <input type="text" v-model="selectItem.JD" />

            <label style="margin-left: 20px">纬度：</label>
            <input type="text" v-model="selectItem.WD" />

            <el-button
                type="primary"
                @click="btnPlotClick('POINT')"
                style="margin-left: 10px"
                >选点</el-button
            >

            <el-button type="primary" @click="btnPointClick()">定位</el-button>

            <el-button type="warning" @click="btnClearAllClick">清除</el-button>
            <el-button type="success" @click="btnConfirmClick">保存</el-button>

            <!-- <input type="text" v-model="keyStr" placeholder="关键字搜索" /> -->
        </div>

        <Map2DPlot ref="childPlot" :map="map"></Map2DPlot>
    </div>
</template>
<script>
import Map2DPlot from '@/components/gis/2D/Map2DPlot';
export default {
    name: 'GetPointMain',
    created() {},
    data() {
        return {
            arrLegend: [],
            showPanel: false, //右侧面板是否显示

            selectItem: {},
            mod: '',
            drawTool: null,
            keyStr: '',

            showMap: true
        };
    },
    props: ['map'],
    unmounted() {},
    components: { Map2DPlot },
    mounted() {
        this.queryObj = PowerGis.getQueryObject();

        if (this.queryObj.mod) {
            this.mod = this.queryObj.mod;
        }

        this.initPage();
    },
    methods: {
        //页面初始化
        initPage() {
            if (!this.map) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.addMask();
            this.initEvent();

            //look 模式下，绘制点位
            if (this.queryObj.mod) {
                this.drawGraphic(this.queryObj);
            }
        },

        //定义地图事件,与后端页面交互
        initEvent() {
            this.map.on('zoom-end', (evt) => {
                //遮罩会影响点位的展示、所以大于17级隐藏遮罩
                if (evt.level > 17) {
                    PowerGis.setLayerVisible(
                        this.map,
                        '添加行政区划遮罩',
                        false
                    );
                } else {
                    PowerGis.setLayerVisible(
                        this.map,
                        '添加行政区划遮罩',
                        true
                    );
                }
            });

            this.map.on('extent-change', (evt) => {
                if (this.queryObj && this.queryObj.mod == 'look') {
                    PowerGis.removeMapTip();
                    this.drawEnd();
                }
            });

            //地图加载完成交互
            window.parent.postMessage(
                {
                    type: 'mapLoadComplete',
                    item: {}
                },
                '*'
            );

            //接受父页面的初始值
            window.addEventListener(
                'message',
                (e) => {
                    if (e.data.type == 'initData') {
                        console.log('接收到父页面消息');
                        console.log(e.data.item);
                        this.selectItem = e.data.item;
                        this.drawInitGra();
                    }
                },
                false
            );
        },

        //绘制遮罩
        addMask() {
            let url = './gis/2D/data/331123_遂昌县.json';
            $.getJSON(url, (data) => {
                let item = data.features[0];
                let obj = {};
                obj = item.attributes;
                obj.rings = item.geometry.rings;
                obj.option = {
                    color: [255, 255, 255, 0.7], //填充颜色
                    tcstyle: 'STYLE_SOLID', //填充样式
                    lineColor: [128, 0, 255, 0.7], //设置符号线的颜色
                    style: 'STYLE_SOLID',
                    lineWidth: 1 //线的宽度
                };

                PowerGis.addShadePolygon(
                    this.map,
                    '添加行政区划遮罩',
                    obj,
                    true,
                    (layer) => {
                        //定位到图层

                        let jsonStr = item.geometry;

                        let geo = PowerGis.getGeomtry(jsonStr, 'polygon');
                        this.map.setExtent(geo.getExtent().expand(1));
                        // PowerGis.pointTolayer(this.map, layer);
                    }
                );
            });
        },

        //绘制点位，如果URL上参数为look 模式,显示点位tip
        drawInitGra() {
            if (this.selectItem.JD && this.selectItem.WD) {
                //绘制点位
                let pt = {
                    x: this.selectItem.JD,
                    y: this.selectItem.WD
                };

                this.$refs.childPlot.drawPoint(pt, '定位点', this.drawEnd);
            }
        },

        //绘制完成
        drawGraphic(queryObj) {
            if (queryObj.JD && queryObj.WD) {
                let pt = {
                    x: queryObj.JD,
                    y: queryObj.WD
                };

                this.$refs.childPlot.drawPoint(pt, '定位点', this.drawEnd);

                PowerGis.pointTo(this.map, queryObj, false, 15);
            }
        },

        //默认显示tip
        drawEnd() {
            let contentStr = `<h1 style="font-size: 14px;font-weight: bold;height: 30px; line-height: 30px;">${this.queryObj.QYMC}</h1>
            <p><span style="font-weight: bold;">企业地址：</span>${this.queryObj.QYDZ}</p>`;

            let cssObj = {
                padding: '10px',
                'border-radius': '3px',
                color: '#333',
                transform: 'translate(-50%,-100%)',
                background: 'rgba(255,255,255,0.8)',
                'margin-top': '-30px'
            };

            let mapPoint = new PowerGis.Point(
                this.queryObj.JD,
                this.queryObj.WD
            );
            PowerGis.showMapTip(mapPoint, this.map, cssObj, contentStr);
        },

        //标绘
        btnPlotClick(type) {
            this.$refs.childPlot.clear();
            PowerGis.clearLayer(this.map, '定位点');
            this.$refs.childPlot.drawData(type, false, this.drawEndHandle);
        },

        //绘制完成，将数据保存到selectItem，并在页面显示坐标
        drawEndHandle() {
            let layer = this.map.getLayer('标绘图层');
            if (layer) {
                let gs = layer.graphics;

                let arr = gs.map((g) => {
                    return g.geometry;
                });

                let item = {
                    JD: '',
                    WD: ''
                };

                if (arr.length > 0) {
                    item = {
                        JD: arr[0].x.toFixed(6),
                        WD: arr[0].y.toFixed(6)
                    };
                }

                Object.assign(this.selectItem, item);

                // console.log('保存成功');
            }
        },

        //清除全部
        btnClearAllClick() {
            this.selectItem = {
                JD: '',
                WD: ''
            };
            this.$refs.childPlot.clear();
        },

        //获取点位数据，传递给后端
        btnConfirmClick() {
            let param = JSON.parse(
                JSON.stringify({
                    type: 'PlotSucess', //标绘成功
                    item: this.selectItem
                })
            );

            console.log('gis传递给后端页面结果');
            console.log(param);

            window.parent.postMessage(param, '*');
        },

        //定位
        btnPointClick() {
            if (this.selectItem && this.selectItem.JD && this.selectItem.WD) {
                this.$refs.childPlot.clear();
                PowerGis.clearLayer(this.map, '定位点');

                this.drawInitGra();
                PowerGis.pointTo(this.map, this.selectItem);
            }
        }
    },
    watch: {},
    computed: {}
};
</script>

<style>
.gis-search {
    position: absolute;
    top: 77px;
    left: 172px;
}

.gis-legend {
    position: absolute;
    left: 170px;
    bottom: 80px;
}

.condition {
    padding: 10px;
    background: rgba(255, 255, 255, 0.9);
}

.condition label {
    font-size: 14px;
    color: #333;
}

.condition input[type='text'] {
    height: 30px;
    font-size: 14px;
    padding-left: 10px;
}

.cesiumMapCls {
    position: fixed;
    left: 20px;
    bottom: 10px;
    width: 100px;
    height: 100px;
}

.logMask {
    width: 100%;
    height: 25px;
    position: absolute;
    left: 0px;
    bottom: 0px;
    background: rgba(0, 0, 0, 0.9);
    color: #fff;
    text-align: center;
    line-height: 25px;
}
</style>
