<!-- @format -->

<!-- 空气点位控制  -->
<!-- @format -->
<template>
    <dl class="gis-dloption1">
        <dt>
            <label
                class="pd-label arw on"
                @click="allLayerClick()"
                style="float: left; padding-left: 10px"
                ><input type="checkbox" :checked="checkAll" disabled /><i></i
                ><span>污染源</span></label
            >
        </dt>
        <dd class="gis-optwrap1">
            <div class="gis-sublevel" style="display: block">
                <label
                    class="pd-label"
                    v-for="(item, index) of zts"
                    :key="index"
                    @click="ztLayerClick(item)"
                    ><input
                        type="checkbox"
                        :checked="item.selected"
                        disabled
                    /><i></i><span>{{ item.name }}</span
                    >（<span>{{ item.total }}</span
                    >）</label
                >
            </div>
        </dd>
    </dl>
</template>

<script>
import {
    getEnterpriseBaseInfo, //污染源点位
    getDustingBaseInfo, //扬尘源点位
    getAirSourceTotal
} from '@/api/gis/3D/AirMap/index';
import PointUtil from '../utils/PointUtil';
export default {
    data() {
        return {
            map: null,
            result: {}, //专题数据集合
            checkAll: false,
            pointUtil: null,
            zts: [
                {
                    name: '工业源',
                    selected: false,
                    lx: 'GYY',
                    total: 0,
                    iconCls: ''
                },
                {
                    name: '扬尘源',
                    selected: false,
                    lx: 'YCY',
                    total: 0,
                    iconCls: 'icon-gdyc'
                }
            ]
        };
    },
    props: ['curentTime'],
    inject: ['pointClickHandle', 'refreshSearchData'],
    unmounted() {
        this.clearAll();
    },
    mounted() {
        this.initPage();
    },
    methods: {
        initPage() {
            if (!window.glMap) {
                setTimeout(() => {
                    this.initPage();
                }, 200);
                return;
            }

            this.getAirSourceTotal();

            this.map = window.glMap;
            this.pointUtil = new PointUtil(this.map, this.pointClickHandle);
        },

        //专题点击
        allLayerClick() {
            this.checkAll = !this.checkAll;

            for (let o of this.zts) {
                o.selected = this.checkAll;
            }

            if (this.checkAll) {
                this.getAllData();
            } else {
                this.clear();
            }
        },

        //子专题点击
        ztLayerClick(obj) {
            obj.selected = !obj.selected;

            //根据子级的选中状态，设置父级的选中状态
            this.checkAll = this.zts.some((item) => {
                return item.selected;
            });

            if (obj.selected) {
                this.getData(obj);
            } else {
                this.clear(obj.name);
            }
        },

        //根据专题加载数据
        getData(obj) {
            switch (obj.name) {
                case '工业源':
                    this.getWryData(obj);
                    break;
                case '扬尘源':
                    this.getYCYData(obj);
                    break;
                default:
                    break;
            }
        },

        //获取工业源
        getWryData(obj) {
            if (this.result[obj.name]) {
                this.addPointToMap(obj.name);
                return;
            }
            let param = {
                jcsj: this.curentTime
            };

            getEnterpriseBaseInfo(param).then((res) => {
                this.result[obj.name] = res.data.map((item) => {
                    item.SJZT = '';
                    if (!item.JCSJ) {
                        item.SJZT = '03';
                    } else {
                        if (item.SFCB == '1') {
                            item.SJZT = '02';
                        } else {
                            item.SJZT = '01';
                        }
                    }

                    item.DQSJ = this.curentTime + ':00:00';
                    return item;
                });
                this.addPointToMap(obj.name);
            });
        },

        //获取扬尘源数据
        getYCYData(obj) {
            if (this.result[obj.name]) {
                this.addPointToMap(obj.name);
                return;
            }
            let param = {
                jcsj: this.curentTime
            };

            getDustingBaseInfo(param).then((res) => {
                this.result[obj.name] = res.data.map((item) => {
                    item.DQSJ = this.curentTime + ':00:00';
                    return item;
                });

                this.addPointToMap(obj.name);
            });
        },

        //地图上绘制点位
        addPointToMap(type) {
            this.pointUtil.addImgPoint(this.result[type], {
                id: type
            });

            this.setSearchData(type, this.result[type]);

            // if (type == '工业源') {
            //     this.addCBdata(vis);
            // }
        },

        addCBdata(vis) {
            if (vis) {
                this.pointUtil.addCbData(this.wryArr);
            } else {
                this.pointUtil.clear3();
            }
        },

        //请求所有选中专题的数据
        getAllData() {
            for (let obj of this.zts) {
                if (obj.selected) {
                    this.getData(obj);
                }
            }
        },

        //获取涉气源数据
        getAirSourceTotal() {
            getAirSourceTotal({}).then((res) => {
                if (res && res.data) {
                    for (let obj of res.data) {
                        for (let item of this.zts) {
                            if (obj.name == item.name) {
                                item.total = obj.total;
                            }
                        }
                    }
                }
            });
        },

        //清理图层
        clearAll() {
            for (let item of this.zts) {
                this.clear(item.name);
            }
        },

        clear(layerId) {
            this.pointUtil.removeLayerByName(layerId);
            this.setSearchData(layerId, []);
        },

        //设置搜索框数据
        setSearchData(layerId, arr) {
            let data = {};
            data[layerId] = arr;
            this.refreshSearchData(data);
        }
    },

    watch: {
        curentTime(newVal, oldVal) {
            this.result = null;
            this.getAllData();
        }
    }
};
</script>

<style></style>
