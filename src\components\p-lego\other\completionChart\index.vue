<!-- @format -->

<template>
    <div>
        <div class="rel" style="width: 94%">
            <p-stack-bar
                :data="chartData"
                style="height: 117px"
                :option="barOpt"
                :config="{
                    barWidth: 16
                }"
            ></p-stack-bar>
            <ul class="label-total">
                <li>总数</li>
                <li v-for="(item, idx) in totalList" :key="idx">
                    {{ item }}
                </li>
            </ul>
        </div>
        <!--  <img
            src="../../../../assets/screen/images/tz_char1a.png"
            class="db auto"
        /> -->
    </div>
</template>

<script>
export default {
    props: {
        data: {
            type: Array,
            default: () => {
                return [];
            }
        }
    },
    data() {
        return {
            chartData: [],
            totalList: [],
            barOpt: {
                toolbox: {
                    show: false
                },
                legend: {
                    textStyle: {
                        color: '#fff'
                    },
                    left: 'center'
                },
                tooltip: { show: false },
                grid: {
                    top: 30,
                    bottom: -30,
                    left: 30,
                    right: 30
                },
                color: ['#3adbf2', '#0087a6'],
                xAxis: {
                    show: false,
                    max: 100
                },
                yAxis: {
                    axisLabel: {
                        interval: 0,
                        align: 'left',
                        padding: [0, 0, 0, -100],
                        fontSize: 16,
                        color: '#fff'
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    }
                },
                series: [
                    {
                        barMinHeight: 15,
                        label: {
                            show: true,
                            position: 'insideRight',
                            textStyle: {
                                padding: [2, 0, 0, 0],
                                color: '#000'
                            }
                        }
                    },
                    {
                        label: {
                            show: true,
                            position: 'insideRight',
                            textStyle: {
                                padding: [2, 0, 0, 0],
                                color: '#fff'
                            }
                        }
                    }
                ]
            }
        };
    },
    watch: {
        data: 'init'
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            let y = [];
            let d1 = [];
            let d2 = [];
            let totalList = [];
            this.data.forEach((v) => {
                totalList.unshift(v.total);
                y.push(v.name);
                let doneRate = (100 * v.done) / v.total;
                doneRate = isNaN(doneRate) ? 0 : doneRate;
                let notRate = (100 - doneRate).toFixed(2);
                v.doneRate = Number(doneRate).toFixed(2);
                d1.push({
                    value: doneRate,
                    label: {
                        show: true,
                        formatter: Number(v.done).toFixed(0)
                    }
                });
                d2.push({
                    value: notRate,
                    label: {
                        show: v.total - v.done > 0,
                        formatter: (v.total - v.done).toFixed(0)
                    }
                });
            });
            this.chartData = {
                yAxis: y,
                series: [
                    { name: '已完成', data: d1 },
                    { name: '未完成', data: d2 }
                ]
            };
            this.totalList = totalList;
        }
    }
};
</script>

<style lang="scss" scoped>
.rel {
    position: relative;
}
.label-total {
    position: absolute;
    right: -10px;
    top: 0;
    margin: 0;
}
.label-total li {
    color: #fff;
    font-size: 14px;
    line-height: 31px;
    text-align: left;
}
</style>
