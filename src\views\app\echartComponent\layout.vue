<!-- @format -->

<template>
    <div class="chart-main" :class="{ dark: isDark }" v-if="isFresh">
        <div style="height: 30px"></div>
        <h1>
            <a
                style="
                    font-size: 24px;
                    padding-top: 30px;
                    padding-left: 20px;
                    color: #2083f3;
                "
                href="http://202.104.140.36:3000/chartDocs/#/"
                target="_blank"
                >p-charts文档传送门</a
            >
        </h1>
        <br />
        <!--<PLine
            ref="lineChart"
            :data="data"
            :option="option"
            style="width: 400px;height: 400px;"
            @click="testClick"
        ></PLine>-->
        <!-- <PLine
            style="width:100%; height: 400px; margin: 0 auto"
            :data="pltData"
        ></PLine> -->

        <el-row>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>通用</span>
                    </div>
                    <!-- <p-wordcloud
                        :config="{
                            showShape: false
                        }"
                        :data="wordcloud"
                        style="width: 600px; height: 400px"
                    ></p-wordcloud> -->

                    <PChart
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :option="{
                            series: [
                                {
                                    type: 'bar',
                                    data: [11, 12, 15, 16, 13, 12, 14]
                                }
                            ],
                            xAxis: {
                                data: ['a', 'b', 'c', 'd', 'e', 'f', 'g']
                            },
                            yAxis: {},
                            tooltip: {}
                        }"
                    ></PChart> </el-card
            ></el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>无数据</span>
                    </div>
                    <PStackBar
                        :data="[]"
                        style="width: 100%; height: 400px; margin: 0 auto"
                    ></PStackBar>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>水球图</span>
                    </div>
                    <p-water-polo
                        style="width: 100%; height: 400px"
                        :data="{ value: 60.5 }"
                    ></p-water-polo>
                </el-card>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>饼图</span>
                    </div>
                    <PPie
                        :data="pieData"
                        :config="{
                            color: 'waterGradesColor',
                            title: '总个数\n300',
                            type: 'ring',
                            showLegend: true
                        }"
                        style="width: 100%; height: 400px; margin: 0 auto"
                    ></PPie></el-card
            ></el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>饼图</span>
                    </div>
                    <PPie
                        :data="pieData"
                        :config="{
                            color: 'waterGradesColor',
                            type: 'rose'
                        }"
                        :option="{
                            legend: {
                                bottom: 0
                            }
                        }"
                        style="width: 100%; height: 400px; margin: 0 auto"
                    ></PPie>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>饼图</span>
                    </div>
                    <PPie
                        :data="pieData2"
                        style="width: 100%; height: 400px; margin: 0 auto"
                        @click="testClick"
                    ></PPie>
                </el-card>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>折线图</span>
                    </div>
                    <PLine
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="{
                            xAxis: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            series: [
                                {
                                    name: '2018年',
                                    data: [22, 33, 28, 36, 28, 35]
                                },
                                {
                                    name: '2019年',
                                    data: [28, 36, 28, 35, 22, 33]
                                }
                            ]
                        }"
                    ></PLine></el-card
            ></el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>折线图</span>
                    </div>
                    <PLine
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="{
                            xAxis: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            series: [
                                {
                                    name: '2018年',
                                    data: [22, 33, 28, 36, 28, 35]
                                },
                                {
                                    name: '2019年',
                                    data: [28, 36, 28, 35, 22, 33]
                                }
                            ]
                        }"
                        :config="{
                            showFillArea: true,
                            unit: 'mg/l',
                            showLabel: true
                        }"
                    ></PLine>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>折线图</span>
                    </div>
                    <PLine
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="{
                            xAxis: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            series: [
                                {
                                    name: '2018年',
                                    data: [22, 33, 28, 36, 28, 35]
                                },
                                {
                                    name: '2019年',
                                    data: [28, 36, 28, 35, 22, 33]
                                }
                            ]
                        }"
                        :config="{
                            markLine: [
                                { name: '目标值', value: 30, color: '#f00' }
                            ]
                        }"
                    ></PLine>
                </el-card>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>柱状图</span>
                    </div>
                    <PBar
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="{
                            xAxis: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            series: [
                                {
                                    name: '2018年',
                                    data: [22, 33, 28, 36, 28, 35]
                                },
                                {
                                    name: '2019年',
                                    data: [28, 36, 28, 35, 22, 33]
                                }
                            ]
                        }"
                        :config="{
                            showLabel: true
                        }"
                    ></PBar></el-card
            ></el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>线柱混合</span>
                    </div>
                    <PBar
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="{
                            xAxis: ['1月', '2月', '3月', '4月', '5月', '6月'],
                            series: [
                                {
                                    name: '2018年',
                                    type: 'line',
                                    data: [22, 33, 28, 36, 28, 35]
                                },
                                {
                                    name: '2019年',
                                    type: 'bar',
                                    data: [28, 36, 28, 35, 22, 33]
                                }
                            ]
                        }"
                    ></PBar>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>堆叠图</span>
                    </div>
                    <PStackBar
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="{
                            xAxis: ['2015年', '2016年', '2017年'],
                            series: [
                                { name: '优', data: [20, 25, 30] },
                                { name: '良', data: [20, 25, 30] },
                                { name: '轻度污染', data: [60, 50, 40] }
                            ]
                        }"
                    ></PStackBar>
                </el-card>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>柱状图</span>
                    </div>
                    <p-bar
                        :data="{
                            xAxis: [
                                '2014年',
                                '2015年',
                                '2016年',
                                '2017年',
                                '2018年',
                                '2019年'
                            ],
                            series: [
                                {
                                    name: '2018年',
                                    data: [22, 33, 28, 36, 28, 35]
                                },
                                {
                                    name: '2019年',
                                    data: [28, 36, 28, 35, 22, 33]
                                }
                            ]
                        }"
                        :config="{
                            pictorial: 'circle'
                        }"
                        style="width: 100%; height: 400px; margin: 0 auto"
                    ></p-bar>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>圆状图</span>
                    </div>
                    <p-cylinder-bar
                        :data="barData"
                        :config="{ color: barColor, barWidth: 40 }"
                        style="width: 100%; height: 400px"
                    ></p-cylinder-bar></el-card
            ></el-col>

            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>条纹图</span>
                    </div>
                    <PStripeBar
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="[
                            { name: '广州', rank: 1, value: 2.5 },
                            { name: '广州', rank: 2, value: 2.8 },
                            { name: '广州', rank: 3, value: 2.9 },
                            { name: '广州', rank: 4, value: 3.1 },
                            { name: '广州', rank: 5, value: 3.5 },
                            { name: '广州', rank: 6, value: 3.8 },
                            { name: '广州', rank: 7, value: 4.5 },
                            { name: '广州', rank: 8, value: 4.8 },
                            { name: '广州', rank: 9, value: 5.6 },
                            { name: '广州', rank: 10, value: 5.8 }
                        ]"
                        :config="{
                            color: isDark
                                ? ['#2ad9ff', 'rgba(255,255,255,0.2)']
                                : ['#6ebffb', '#e4e4e4'],
                            unit: '%'
                        }"
                    ></PStripeBar>
                </el-card>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>仪表盘</span>
                    </div>
                    <p-dash-board
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="{ value: 20 }"
                        :config="{
                            type: '2',
                            barWidth: 10,
                            isShowTick: true,
                            isShowPointer: true,
                            max: 60,
                            unit: 'mg/L',
                            title: '污染物'
                        }"
                    ></p-dash-board>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>资产建设图</span>
                    </div>
                    <PAssetsBar
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="[
                            { name: '广州', time1: 2012, time2: 2018 },
                            { name: '广州', time1: 2013, time2: 2016 },
                            { name: '广州', time1: 2014, time2: 2015 },
                            { name: '广州', time1: 2012, time2: 2015 },
                            { name: '广州', time1: 2015, time2: 2014 },
                            { name: '广州', time1: 2012, time2: 2018 },
                            { name: '广州', time1: 2012, time2: 2018 },
                            { name: '广州', time1: 2012, time2: 2018 },
                            { name: '广州', time1: 2012, time2: 2018 },
                            { name: '广州', time1: 2012, time2: 2018 }
                        ]"
                        :config="{
                            color: isDark
                                ? ['#26d267', '#2ad9ff']
                                : ['#00e1c4', '#6ebffb']
                        }"
                    ></PAssetsBar></el-card
            ></el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>变化排名条纹图</span>
                    </div>
                    <PVarietyBar
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="[
                            { name: '广州', rank: 1, value: -2.5 },
                            { name: '广州', rank: 2, value: -2.8 },
                            { name: '广州', rank: 3, value: -2.9 },
                            { name: '广州', rank: 4, value: -3.1 },
                            { name: '广州', rank: 5, value: -3.5 },
                            { name: '广州', rank: 6, value: -3.8 },
                            { name: '广州', rank: 7, value: 4.5 },
                            { name: '广州', rank: 8, value: 4.8 },
                            { name: '广州', rank: 9, value: 5.6 },
                            { name: '广州', rank: 10, value: 5.8 }
                        ]"
                        :config="{
                            color: isDark
                                ? [
                                      '#d8cf3a',
                                      '#20cb44',
                                      'rgba(255,255,255,0.2)'
                                  ]
                                : ['#d8cf3a', '#20cb44', '#e4e4e4'],
                            barWidth: 16,
                            unit: '%'
                        }"
                    ></PVarietyBar>
                </el-card>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>彩虹图</span>
                    </div>
                    <PRainbow
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="rainbowData"
                    ></PRainbow> </el-card
            ></el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>热力图</span>
                    </div>
                    <PHeatmap
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="{
                            xAxis: [
                                '2015年',
                                '2016年',
                                '2017年',
                                '2018年',
                                '2019年'
                            ],
                            series: [
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, null, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [400, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [0, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 5, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 0, 200, 300]
                                }
                            ]
                        }"
                    ></PHeatmap>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card class="box-card">
                    <div slot="header" class="clearfix">
                        <span>热力图</span>
                    </div>
                    <PHeatmap
                        style="width: 100%; height: 400px; margin: 0 auto"
                        :data="{
                            xAxis: [
                                '2015年',
                                '2016年',
                                '2017年',
                                '2018年',
                                '2019年'
                            ],
                            series: [
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, null, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [400, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [0, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 5, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 140, 200, 300]
                                },
                                {
                                    name: '广州市',
                                    data: [100, 120, 0, 200, 300]
                                }
                            ]
                        }"
                        :config="{
                            min: 0,
                            max: 300,
                            color: ['#f00', '#fff'],
                            unit: 'mg/m³'
                        }"
                    ></PHeatmap>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
// import WaterPointCalendar from '_c/WaterPointCalendar';
export default {
    name: 'layout.vue',
    components: {
        // WaterPointCalendar
    },
    created() {
        let theme = ServerGlobalConstant.eleTheme;
        this.isDark = theme === 'dark';
        // elementUI 样式reset，分为深色系和浅色系
        this.$pChart.setChartConfig({
            THEME_COLOR: theme
        });
    },
    data() {
        return {
            barData: {
                xAxis: ['累计值', '目标值', '控制值'],
                data: [26, 22, 8]
            },
            barColor: [
                {
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    type: 'linear',
                    global: false,
                    colorStops: [
                        {
                            offset: 0,
                            color: '#50cfff'
                        },
                        {
                            offset: 1,
                            color: '#0397fe'
                        }
                    ]
                },
                {
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    type: 'linear',
                    global: false,
                    colorStops: [
                        {
                            offset: 0,
                            color: '#2bd31e'
                        },
                        {
                            offset: 1,
                            color: '#0cad00'
                        }
                    ]
                },
                {
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    type: 'linear',
                    global: false,
                    colorStops: [
                        {
                            offset: 0,
                            color: '#f6ae30'
                        },
                        {
                            offset: 1,
                            color: '#ef9801'
                        }
                    ]
                }
            ],
            isDark: false,
            isFresh: true,

            wordcloud: [
                {
                    name: '供应商01',
                    value: 200
                },
                {
                    name: '供应商02',
                    value: 181
                },
                {
                    name: '供应商03',
                    value: 386
                },
                {
                    name: '供应商04',
                    value: 155
                },
                {
                    name: '供应商05',
                    value: 467
                },
                {
                    name: '供应商06',
                    value: 244
                },
                {
                    name: '供应商07',
                    value: 898
                },
                {
                    name: '供应商08',
                    value: 484
                },
                {
                    name: '供应商09',
                    value: 112
                },
                {
                    name: '供应商10',
                    value: 465
                },
                {
                    name: '供应商11',
                    value: 447
                },
                {
                    name: '供应商12',
                    value: 582
                },
                {
                    name: '供应商13',
                    value: 555
                },
                {
                    name: '供应商14',
                    value: 550
                },
                {
                    name: '供应商15',
                    value: 462
                }
            ],
            pieData: [
                { name: 'Ⅰ类', value: 5 },
                { name: 'Ⅱ类', value: 5 },
                { name: 'Ⅲ类', value: 30 },
                { name: 'Ⅳ类', value: 20 },
                { name: 'Ⅴ类', value: 10 },
                { name: '劣Ⅴ类', value: 2 }
            ],
            pieData2: [
                { name: 'a类', value: 5 },
                { name: 'b类', value: 5 },
                { name: 'c类', value: 30 },
                { name: 'd类', value: 20 },
                { name: 'e类', value: 10 },
                { name: 'f类', value: 2 }
            ],
            rainbowData: [
                {
                    name: '蓝天',
                    value: '1000'
                },
                {
                    name: '白云',
                    value: '500'
                },
                {
                    name: '土',
                    value: '800'
                }
            ],
            calendarData: [
                {
                    name: '断面名称xx1',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅱ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '化学需氧量(1.1)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: 'yyy()',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: 'xxx(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称xx',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称xx',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称xx',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称xx',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称xx',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称xx',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称xx',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称xx',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称xx',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称xx',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '',
                            status: '0',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '2月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅱ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                },
                {
                    name: '断面名称',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '2月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅴ类',
                            status: '0'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅱ类',
                            status: '1'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类',
                            status: '0'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类',
                            status: '1'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类',
                            status: '1'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: '',
                            status: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类',
                            status: '0'
                        }
                    ]
                },
                {
                    name: '断面名称',
                    data: [
                        {
                            time: '1月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '2月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅴ类'
                        },
                        {
                            time: '3月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '4月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅱ类'
                        },
                        {
                            time: '5月',
                            plt: '',
                            level: 'Ⅳ类'
                        },
                        {
                            time: '6月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '7月',
                            plt: '',
                            level: 'Ⅲ类'
                        },
                        {
                            time: '8月',
                            plt: '',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '9月',
                            plt: '总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '10月',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '11月',
                            plt: '氨氮(2.13))',
                            level: 'Ⅰ类'
                        },
                        {
                            time: '12月',
                            plt: '',
                            level: ''
                        },
                        {
                            time: '累计',
                            plt: '氨氮(2.13),总磷(2.13)',
                            level: '劣Ⅴ类'
                        }
                    ]
                }
            ],
            pltData: {}
        };
    },
    mounted() {},
    methods: {
        testClick() {
            // console.log(e);
        },
        setStyle() {
            this.isDark = !this.isDark;
            this.$pChart.setChartConfig({
                THEME_COLOR: this.isDark ? 'dark' : 'light'
            });

            this.isFresh = false;
            this.$nextTick(() => {
                this.isFresh = true;
            });
        }
    }
};
</script>

<style scoped>
h4 {
    font-size: 16px;
    padding: 20px;
    margin-top: 10px;
    border-top: 1px solid #ddd;
}
h5 {
    padding-left: 50px;
}
.chart-main {
    border-radius: 5px;
    overflow-y: auto;
    width: 100%;
    height: 100%;
    background: #f5f5f5;
}
.dark {
    background-color: #010e5d;
}
.dark h4,
.dark h5 {
    color: #fff;
}
.el-card {
    margin: 20px;
}
</style>
