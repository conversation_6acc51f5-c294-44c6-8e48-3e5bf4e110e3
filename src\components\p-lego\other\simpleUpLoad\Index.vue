<!-- @format -->

<template>
    <div class="simplefile">
        <div class="simplefile-til">
            <p class="simplefile-til-txt">上传附件</p>
            <img src="./images/fIcon.png" class="simplefile-til-icon" />

            <span class="simplefile-til-tip" v-if="!finalOption.isReadOnly"
                >（支持{{ allowableSize }}M以内<template v-if="accept"
                    >；{{ accept }}格式</template
                >的文件；最多可上传{{ limit }}个）
            </span>

            <el-upload
                style="margin-left: auto"
                :http-request="uploadSectionFile"
                action=""
                multiple
                :accept="accept.replace(/\*/g, '').replace(/;/g, ',')"
                :before-upload="beforeUpload"
                :limit="limit + 1"
                :show-file-list="false"
                v-if="!finalOption.isReadOnly"
            >
                <div class="simplefile-til-sbtn">
                    <img
                        @click="setAccept"
                        src="./images/addic.png"
                        alt=""
                    />添加
                </div>
            </el-upload>
        </div>

        <div class="simplefile-list">
            <div
                class="simplefile-list-item"
                v-for="(item, index) in fileList"
                :key="index"
            >
                <img
                    src="./images/file.png"
                    alt=""
                    class="simplefile-list-item-icon"
                />

                <span class="simplefile-list-item-name" @click="download(item)">
                    {{ item.WJMC }}
                </span>

                <img
                    v-if="!finalOption.isReadOnly"
                    src="./images/del.png"
                    alt=""
                    class="simplefile-list-item-del"
                    @click="delOne(item)"
                />
            </div>
        </div>
        <el-progress
            :percentage="percentage"
            status="success"
            class="progress"
            v-show="showProgress"
            :color="colors"
            ><el-button type="text"></el-button
        ></el-progress>
    </div>
</template>

<script>
import axios from 'axios';
export default {
    props: {
        code: {
            //关联序号
            type: String
        },
        option: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    data() {
        return {
            defaultOption: {
                isReadOnly: false, //是否只读 默认false
                LXDM: '', //必传 类型代码 由后台提供
                ZLXDM: '', //必传 子类型代码 由后台提供
                ctx: ServerGlobalConstant.ctx, //服务基础地址
                queryfileUrl:
                    '/platform/file/filemanagecontroller/queryfileinfos', //查询已上传文件接口地址
                deletefileUrl:
                    '/platform/file/filemanagecontroller/deletefile/', // 删除单个文件接口地址 （后面接wjid）
                uploadUrl: '/platform/file/filemanagecontroller/upload', //上传接口地址
                previewUrl:
                    '/platform/file/filemanagecontroller/downloadfilebyid/' //下载接口地址 （后面接wjid）
            },
            fileList: [], // 附件列表
            accept: '', //允许的附件类型  '.jpg,.jpeg,.png,.gif,avi,.wmv,.mpg,.mpeg,.mp4,.rmvb'

            showProgress: false, //进度条展示隐藏
            percentage: 0, //进度条进度
            existenceList: 0, //已上传附件列表数量
            limit: 5, //最大允许上传个数
            allowableSize: 20, //允许文件大小（M）
            choiceNum: 0, //选择的文件数量
            colors: [
                //进度条颜色
                { color: '#f56c6c', percentage: 20 },
                { color: '#f56c6c', percentage: 40 },
                { color: '#5cb87a', percentage: 60 },
                { color: '#5cb87a', percentage: 80 },
                { color: '#1989fa', percentage: 100 }
            ],
            first: true
        };
    },
    computed: {
        finalOption() {
            return this.option
                ? {
                      ...this.defaultOption,
                      ...this.option
                  }
                : this.defaultOption;
        }
    },
    watch: {
        code: 'getFileListDiy'
    },

    mounted() {
        this.getFileListDiy();
    },

    methods: {
        // 获取附件列表
        getFileListDiy() {
            if (!this.finalOption.LXDM || !this.finalOption.ZLXDM) {
                console.error('缺少LXDM 或ZLXDM');
                return;
            }
            if (!this.code) {
                return;
            }

            axios
                .request({
                    url: this.finalOption.ctx + this.finalOption.queryfileUrl,
                    method: 'post',
                    data: {
                        LXDMS: this.finalOption.LXDM,
                        ZLXDM: this.finalOption.ZLXDM,
                        YWSJID: this.code
                    }
                })
                .then((r) => {
                    let res = r.data;
                    if (!res[0] || !res[0].zlxList[0]) {
                        return;
                    }
                    let data = res[0].zlxList[0];
                    if (this.first) {
                        this.first = false;
                        this.accept = data.FJLX || this.accept;
                        this.limit = data.WJSL || this.limit;
                        this.allowableSize = data.FJDX || this.allowableSize;
                    }
                    this.fileList = data.fileList;
                });
        },

        // 文件上传之前
        beforeUpload(file) {
            this.choiceNum += 1;

            if (this.choiceNum + this.existenceList > this.limit) {
                this.$message.error('最多可上传' + this.limit + '个');
                return false;
            }

            if (file.size / 1024 / 1024 > this.allowableSize) {
                this.$message.error(
                    '文件超过' + this.allowableSize + 'MB无法上传'
                );
                this.showProgress = false;
                return false;
            }

            let type = file.name.split('.')[file.name.split('.').length - 1];
            if (this.accept && this.accept.indexOf(type) === -1) {
                this.$message.error('当前文件格式不支持，请重新选择');
                return false;
            }
        },

        //上传之前获取相关的信息
        setAccept() {
            // 将进度条进度置为0
            this.percentage = 0;
            //选择的附件数量置为0
            this.choiceNum = 0;

            // 已上传的数量
            this.existenceList = this.fileList.length;
        },

        // 自定义上传
        uploadSectionFile(params) {
            let formdata = new FormData();
            let file = params.file;
            formdata.append('file', file);
            formdata.append('WJMC', file.name);
            formdata.append(
                'WJLX',
                file.name.split('.')[file.name.split('.').length - 1]
            );
            formdata.append('WJDX', (params.file.size / 1024).toFixed(2));
            formdata.append('LXDM', this.finalOption.LXDM);
            formdata.append('ZLXDM', this.finalOption.ZLXDM);
            formdata.append('YWSJID', this.code);
            axios
                .post(
                    this.finalOption.ctx + this.finalOption.uploadUrl,
                    formdata,
                    {
                        onUploadProgress: (progressEvent) => {
                            //进度条
                            if (progressEvent.lengthComputable) {
                                this.showProgress = true;

                                let val = (
                                    (progressEvent.loaded /
                                        progressEvent.total) *
                                    100
                                ).toFixed(0);
                                this.percentage = Number(val);
                            }
                        }
                    }
                )
                .then((response) => {
                    if (response.data.resp_msg === 'success') {
                        this.$message({
                            message: '文件上传成功！',
                            type: 'success'
                        });
                        setTimeout(() => {
                            this.percentage = 0;
                            this.showProgress = false;
                            this.getFileListDiy();
                        }, 1000);
                    } else {
                        this.$message.error('上传失败');
                        this.percentage = 0;
                        this.showProgress = false;
                    }
                })
                .catch((err) => {
                    console.log(err);
                    this.$message.error('上传失败');
                    setTimeout(() => {
                        this.percentage = 0;
                        this.showProgress = false;
                    }, 1000);
                });
        },

        // 删除
        delOne(parame) {
            if (this.finalOption.isReadOnly) {
                this.$message.error(`只读状态无法删除`);
            } else {
                this.$confirm(`确定移除 ${parame.WJMC}?`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(() => {
                        axios
                            .request({
                                url:
                                    this.finalOption.ctx +
                                    this.finalOption.deletefileUrl +
                                    parame.WJID,
                                method: 'post'
                            })
                            .then((r) => {
                                let res = r.data;
                                if (res.count) {
                                    this.$message.success('删除成功');
                                    this.getFileListDiy();
                                } else {
                                    this.$message.error('删除失败');
                                }
                            })
                            .catch((err) => {
                                console.log(err);
                                this.$message.error('删除失败');
                            });
                    })
                    .catch((err) => {
                        console.log(err);
                    });
            }
        },

        // 下载
        download(parame) {
            window.open(
                this.finalOption.ctx + this.finalOption.previewUrl + parame.WJID
            );
        }

        //图标
        /* getFileSuffix(fileName) {
            let suffix = fileName.split('.')[fileName.split('.').length - 1];
            return `#icon-${suffix}`;
        } */
    }
};
</script>

<style lang="less" scoped>
.simplefile {
    padding: 10px 15px;
    &-til {
        display: flex;
        line-height: 36px;
        align-items: center;

        &-txt {
            font-weight: bold;
        }
        &-icon {
            width: 20px;
            height: 20px;
            margin-left: 10px;
        }
        &-sbtn {
            width: 80px;
            height: 36px;
            line-height: 36px;
            text-align: center;
            font-size: 16px;
            color: #fff;
            background: url(./images/sumit1.png) no-repeat center center;
            background-size: 100% 100%;
            cursor: pointer;
            & > img {
                margin-right: 5px;
            }
        }
        &-tip {
            opacity: 0.8;
            padding-right: 20px;
        }
    }

    &-list {
        display: flex;
        flex-wrap: wrap;
        padding-top: 15px;
        &-item {
            width: 200px;
            padding-right: 20px;
            display: flex;
            align-items: center;
            margin-bottom: 15px;

            &-icon {
                height: 26px;
            }

            &-name {
                cursor: pointer;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 0 8px;

                &:hover {
                    text-decoration: underline;
                }
            }

            &-del {
                margin-left: auto;
                cursor: pointer;
            }
        }
    }
}
</style>
