<!-- @format  -->
<!-- 底图切换  -->
<template>
    <ul
        class="map-tabs-gis on"
        @mouseenter="mouseHandel('enter')"
        @mouseleave="mouseHandel('leave')"
    >
        <li
            v-for="(item, index) of arrMap"
            :key="index"
            v-show="showAll || item.label == selectMap"
            :class="{ cur: item.label == selectMap }"
            @click="changeMap(item)"
        >
            <img :src="'./gis/2D/images/common/' + item.minImg" alt="" />
            <p>{{ item.label }}</p>
        </li>
    </ul>
</template>

<script>
export default {
    name: 'MapSwitchTool',
    props: ['map', 'mapOption'],
    data() {
        return {
            arrMap: [],
            selectMap: '',
            showAll: false,
            basemaps: null
        };
    },
    components: {},
    computed: {},
    mounted() {
        this.initPage();
    },
    methods: {
        initPage() {
            if (!this.map) {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
                return;
            }

            this.loadMap();
        },

        loadMap() {
            this.basemaps =
                this.mapOption.basemaps ||
                GisServerGlobalConstant.arcgis.basemaps;
            this.arrMap = this.basemaps.filter((item) => {
                return item.layerControl == true;
            });

            this.arrMap.map((item) => {
                if (item.visible) {
                    this.selectMap = item.label;
                }
            });
        },
        mouseHandel(type) {
            if (type == 'enter') {
                this.showAll = true;
            } else {
                this.showAll = false;
            }
        },

        // 图层切换
        changeMap(item) {
            if (!this.map) {
                setTimeout(() => {
                    this.changeMap(item);
                }, 500);
                return;
            }

            // if (this.selectMap == item.label) {
            //     return;
            // }
            this.selectMap = item.label;

            for (let obj of this.arrMap) {
                let layer = this.map.getLayer(obj.label);
                let layerCz = this.map.getLayer(obj.label + '注记');

                if (obj.label == item.label) {
                    layer.show();

                    if (layerCz) {
                        layerCz.show();
                    }
                } else {
                    layer.hide();
                    if (layerCz) {
                        layerCz.hide();
                    }
                }
            }

            this.basemaps.map((objMap) => {
                if (
                    objMap.label == this.selectMap ||
                    objMap.label == this.selectMap + '注记'
                ) {
                    objMap.visible = true;
                } else {
                    objMap.visible = false;
                }
            });
            this.$emit('baseMapChange');
        }
    },
    watch: {}
};
</script>

<style scoped></style>
