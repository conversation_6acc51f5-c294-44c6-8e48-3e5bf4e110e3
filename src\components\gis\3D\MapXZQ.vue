<!-- @format -->
<!--行政区选择 -->
<template>
    <div style="position: relative">
        <ul class="zy-tools-gis">
            <li :class="{ on: showtoolXZQ }">
                <i
                    title="选择地图范围"
                    class="tool-mapXZQ"
                    @click="showtoolXZQHandle"
                ></i>
            </li>
        </ul>

        <div class="er" v-show="showtoolXZQ" :class="{ left: dock == 'left' }">
            <span
                v-for="(item, index) in xzhArr"
                :class="{ cur: item == selectXZQ }"
                :key="index"
                @click="changeMapExtent(item)"
            >
                {{ item }}
            </span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'MapXZQ',
    props: ['map', 'dock'],
    data() {
        return {
            showtoolXZQ: false,
            xzhArr: ['黄石', '湖北', '全国'],
            selectXZQ: '黄石',
            time1: null
        };
    },
    components: {},
    computed: {},
    mounted() {},
    methods: {
        showtoolXZQHandle() {
            this.showtoolXZQ = !this.showtoolXZQ;
        },
        changeMapExtent(item) {
            this.selectXZQ = item;
            let target = [];

            let zoom = 10;

            switch (this.selectXZQ) {
                case '黄石':
                    target = [115.19864589160005, 29.92057029425267];
                    zoom = 9.4;
                    break;

                case '湖北':
                    target = [113.7587321264158, 31.06212720448086];
                    zoom = 6.57;

                    break;

                case '全国':
                    target = [110.95919306252853, 36.445695254846214];
                    zoom = 4;
                    break;
            }

            this.map.flyTo({
                center: target,
                zoom: zoom,
                bearing: 0,
                pitch: 0,
                speed: 0.5, // make the flying slow
                curve: 1, // change the speed at which it zooms out

                easing: function (t) {
                    return t;
                }
            });
        }
    },
    watch: {}
};
</script>

<style scoped>
.er {
    width: 158px;
    position: absolute;
    left: -160px;
    top: 0;
    height: 35px;
}

.er.left {
    left: 40px;
}
.er span {
    float: left;
    padding: 0 10px;
    font-size: 16px;
    line-height: 35px;
    position: relative;
    cursor: pointer;
}

.er span.cur {
    color: #00cd5b !important;
}

.lightTheme .er {
    background: rgba(255, 255, 255, 0.85);
}

.lightTheme .er span {
    color: #333;
}

.darkTheme .er {
    background: #075393;
}

.darkTheme .er span {
    color: #fff;
}
</style>
