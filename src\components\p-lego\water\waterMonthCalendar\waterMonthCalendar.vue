<!-- @format -->

<template>
    <div class="water-month-calendar">
        <table class="water-month-calendar-table">
            <tr>
                <td v-for="(monthValue, index) in halfData" :key="index">
                    <div>
                        <div class="water-month-calendar-label">
                            {{ index + 1 }}月
                        </div>
                        <div
                            class="water-month-calendar-value"
                            :style="getStyle(monthValue)"
                        >
                            {{ getCategoryText(monthValue) }}
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td v-for="(monthValue, index) in latterData" :key="index">
                    <div>
                        <div class="water-month-calendar-label">
                            {{ index + 7 }}月
                        </div>
                        <div
                            class="water-month-calendar-value"
                            :style="getStyle(monthValue)"
                        >
                            {{ getCategoryText(monthValue) }}
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</template>

<script>
export default {
    props: {
        //月历数据，数据格式 ['1','2','3','4','5','6','6','5','4','3','2','1'] 或者['Ⅰ类', 'Ⅱ类', 'Ⅲ类',Ⅳ类', 'Ⅴ类', '劣Ⅴ类', '-',  '-', '', '',  '', '' ]
        data: {
            type: Array,
            default: function () {
                return [];
            }
        }
    },
    data() {
        return {
            colorObj: {
                none: '#b5b5b5',
                Ⅰ类: '#44c5fd',
                Ⅱ类: '#51a5fd',
                'Ⅰ~Ⅱ类': '#51a5fd',
                Ⅲ类: '#73bb31',
                Ⅳ类: '#eebd15',
                Ⅴ类: '#f88e17',
                劣Ⅴ类: '#ee3b5b'
            }
        };
    },
    computed: {
        halfData: function () {
            return this.data.slice(0, 6);
        },
        latterData: function () {
            return this.data.slice(6);
        }
    },
    watch: {},
    created() {},
    mounted() {},
    methods: {
        getCategoryText(value) {
            switch (value) {
                case 0:
                case '0':
                    return '-';
                case 1:
                case '1':
                case 'Ⅰ':
                    return 'Ⅰ类';
                case 2:
                case '2':
                    return 'Ⅱ类';
                case 3:
                case '3':
                    return 'Ⅲ类';
                case 4:
                case '4':
                    return 'Ⅳ类';
                case 5:
                case '5':
                    return 'Ⅴ类';
                case 6:
                case '6':
                    return '劣Ⅴ类';
                default:
                    return value || '-';
            }
        },
        getStyle(value) {
            let label = this.getCategoryText(value);
            let backgroundColor = this.colorObj[label] || this.colorObj.none;
            return {
                backgroundColor
            };
        },
        showMonthDetail(item) {}
    }
};
</script>

<style scoped lang="less">
.water-month-calendar {
    &-table {
        width: 100%;
        table-layout: fixed;
        td {
            text-align: center;
            border: 1px solid var(--el-border-color);
            padding: 10px;
        }
    }
    &-label {
        font-size: 16px;
        padding-bottom: 8px;
    }
    &-value {
        height: 48px;
        overflow: hidden;
        margin: 0 auto;
        font-size: 16px;
        color: #fff;
        background-color: #bfbfbf;
        text-align: center;
        line-height: 48px;
        border-radius: 4px;
    }
}
</style>
