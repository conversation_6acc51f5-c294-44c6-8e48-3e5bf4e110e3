### 组件说明

基础日历组件
### 插件依赖

dayjs插件
### props选项说明

```jsx
{
    dateTime: String, //包含 YYYY-MM 时间格式 必须字段
    list: Array, //从接口请求的源数据
    timeFieldKey: String, //源数据对应的时间字段 默认：JCSJ
    renderItem: Function, //自定义每日单元格内容
}
```

### 水质日历

```vue
<template>
    <div class="water-calendar">
        <p class="water-calendar__title">水质日历</p>
        <div class="water-calendar__selector">
            <el-date-picker
                style="width: 128px"
                v-model="strDate"
                type="month"
                :clearable="false"
                placeholder="选择日期"
                format="YYYY-MM"
                value-format="YYYY-MM"
                @change="changeMonth"
            >
            </el-date-picker>
        </div>
        <BowoCalendar
            :date-time="strDate"
            :list="list"
            style="width: 800px; background-color: #0b5465"
            :renderItem="renderItem"
        >
        </BowoCalendar>
    </div>
</template>

<script>
import BowoCalendar from '_c/business/other/bowoCalendar/Index.vue';
const levelObj = {
    Ⅰ类: '#68cffe',
    Ⅱ类: '#49a1fe',
    'Ⅰ~Ⅱ类': '#49a1fe',
    Ⅲ类: '#37b70c',
    Ⅳ类: '#fae521',
    Ⅴ类: '#f29c00',
    劣Ⅴ类: '#dd3f36'
};
export default {
    components: {
        BowoCalendar
    },
    data() {
        return {
            strDate: '2023-01-16',
            list: [
                {
                    JCSJ: '2023-01-01 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-02 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-03 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-04 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-05 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-06 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-07 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-08 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-09 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-10 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-11 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-12 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-13 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-14 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-15 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-16 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-17 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-18 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-19 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-20 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-21 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-22 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-23 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-24 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-25 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-26 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-27 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-28 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-29 00:00:00',
                    SZLB: 'Ⅳ类'
                },
                {
                    JCSJ: '2023-01-30 00:00:00',
                    SZLB: 'Ⅲ类'
                },
                {
                    JCSJ: '2023-01-31 00:00:00',
                    SZLB: 'Ⅲ类'
                }
            ]
        };
    },
    methods: {
        renderItem(h, { info, index }) {
            const sty = {
                backgroundColor: levelObj[info.SZLB],
                color: info.SZLB == 'Ⅳ类' ? '#333' : '#fff'
            };
            return (
                <div class="type" style={sty}>
                    <p class="p1">{info.day}</p>
                    <p class="p2">{info.SZLB || '-'}</p>
                </div>
            );
        },
        changeMonth(v) {
            console.log('9999----', v);
            setTimeout(() => {
                this.list = this.list.map((item) => {
                    return {
                        ...item,
                        JCSJ: this.$dayjs(item.JCSJ).format(`${v}-DD`)
                    };
                });
            }, 300);
        }
    }
};
</script>

<style lang="scss">
.water-calendar {
    background: #0b5465;

    &__title {
        font-size: 18px;
        color: #fff;
        line-height: 40px;
        padding-left: 12px;
        position: relative;
        text-align: left;
        margin-bottom: 10px;

        &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #0dc8ef;
        }
    }

    &__selector {
        position: relative;
        text-align: left;
        margin-bottom: 10px;
    }
}
.type {
    border-radius: 3px;
    padding-top: 10px;
    padding-bottom: 5px;
    text-align: center;
    background: url(./type-img.png) right bottom no-repeat;

    .p1 {
        font-size: 18px;
        color: inherit;
        line-height: 24px;
    }
    .p2 {
        font-size: 18px;
        color: inherit;
        line-height: 40px;
    }
}
</style>
```