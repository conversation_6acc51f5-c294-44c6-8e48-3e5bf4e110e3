<!-- @format -->
<!-- 水质类别 -->

<template>
    <!-- <div class="gap"></div> -->
    <!-- <div class="gap"></div> -->
    <p-pie
        :data="pieData"
        :config="{
            color: 'waterGradesColor',
            type: 'ring',
            showLabel: false,
            isShowInnerShadow: false,
            showLegend: true,
            legendOrient: 'vertical'
        }"
        :option="pieOpt"
        style="width: 100%; height: 170px"
    ></p-pie>

    <div class="gap"></div>
    <div class="gap"></div>
    <ul class="sw09-ultxt1">
        <li>
            <strong class="dot1"
                >I类 — III类：{{ goodData.num || '-' }}个（{{
                    goodData.now || '-'
                }}%）</strong
            ><strong class="dot2"
                >劣V类断面：{{ badData.num || '-' }}个（{{
                    badData.now || '-'
                }}%）</strong
            >
        </li>
        <li>
            <!-- I类 — III类 -->
            <!-- 同比 -->
            <i>
                <img
                    v-if="goodData.yearErlier > 0"
                    src="./images/sw09_uptb1.png"
                    alt=""
                />

                <img
                    v-if="goodData.yearErlier < 0"
                    src="./images/sw09_downtb1.png"
                    alt=""
                />
                <img
                    v-if="goodData.yearErlier == 0"
                    src="./images/tb_cp.png"
                    alt=""
                />

                <i v-if="goodData.yearErlier !== '0'">
                    {{ Math.abs(goodData.yearErlier) }}%
                </i>
            </i>

            <!-- 环比 -->
            <i>
                <img
                    v-if="goodData.monthErlier > 0"
                    src="./images/sw09_uphb1.png"
                    alt=""
                />
                <img
                    v-if="goodData.monthErlier < 0"
                    src="./images/sw09_downhb1.png"
                    alt=""
                />
                <img
                    v-if="goodData.monthErlier == 0"
                    src="./images/hb_cp.png"
                    alt=""
                />

                <i v-if="goodData.monthErlier !== '0'">
                    {{ Math.abs(goodData.monthErlier) }}%
                </i>
            </i>

            <!-- 劣V类 -->
            <!-- 同比 -->
            <i>
                <img
                    v-if="badData.yearErlier > 0"
                    src="./images/sw09_uptb.png"
                    alt=""
                />
                <img
                    v-if="badData.yearErlier < 0"
                    src="./images/sw09_downtb.png"
                    alt=""
                />

                <img
                    v-if="badData.yearErlier == 0"
                    src="./images/tb_cp.png"
                    alt=""
                />

                <i v-if="badData.yearErlier !== '0'">
                    {{ Math.abs(badData.yearErlier) }}%
                </i>
            </i>
            <!-- 环比 -->
            <i>
                <img
                    v-if="badData.monthErlier > 0"
                    src="./images/sw09_uphb.png"
                    alt=""
                />
                <img
                    v-if="badData.monthErlier < 0"
                    src="./images/sw09_downhb.png"
                    alt=""
                />

                <img
                    v-if="badData.monthErlier == 0"
                    src="./images/hb_cp.png"
                    alt=""
                />
                <i v-if="badData.monthErlier !== '0'">
                    {{ Math.abs(badData.monthErlier) }}%
                </i>
            </i>
        </li>
    </ul>
</template>

<script>
import dataUtil from './utils/dataUtil';
export default {
    name: '',
    props: ['data'],
    data() {
        return {
            pieData: [],
            pieOpt: {},
            goodData: {
                num: '',
                now: '',
                yearErlier: 0,
                monthErlier: 0
            },
            badData: {
                num: '',
                now: '',
                yearErlier: 0,
                monthErlier: 0
            },
            defineColor: '' //默认字体颜色
        };
    },

    watch: {
        data: {
            handler() {
                let theme = window.localStorage.getItem('themeType') || 'dark';

                this.defineColor = theme === 'light' ? '#999' : '#fff';
                this.setData();
            },
            deep: true
        }
    },
    mounted() {
        // this.setPieOption();
    },
    methods: {
        setData() {
            this.pieData = dataUtil.dealWaterCategoryPercentTwo(
                this.data[0][0]
            );

            // 设置饼图
            this.setPieOption();

            this.goodData = dataUtil.dealOneThreeRate(this.data);
            this.badData = dataUtil.dealBadFiveRate(this.data);
        },
        // 设置饼图
        setPieOption() {
            this.pieOpt = {
                legend: {
                    right: '10%',
                    formatter: (name) => {
                        // 添加
                        let target;
                        for (let i = 0; i < this.pieData.length; i++) {
                            if (this.pieData[i].name === name) {
                                target = this.pieData[i];
                            }
                        }
                        let arr = [
                            '{a|' + name + '}',
                            '{b|' + target.value + '个}',
                            '{c|' + target.rate + '%}'
                        ];
                        return arr.join('');
                    },
                    textStyle: {
                        // 添加
                        padding: [4, 0, 0, 0],

                        rich: {
                            a: {
                                fontSize: 15,
                                width: 100
                            },
                            b: {
                                width: 50,
                                fontSize: 15
                            },

                            c: {
                                fontSize: 15
                            }
                        }
                    }
                },
                tooltip: {
                    formatter: '{b}：{c} {d}%'
                },
                series: [
                    {
                        center: ['25%', '50%'],
                        radius: ['55%', '90%']
                    },
                    {
                        center: ['25%', '50%'],
                        label: {
                            color: '#fff',
                            formatter: dataUtil.dealWaterCategoryStatus(
                                this.data[0][0]
                            ).txt,
                            backgroundColor: dataUtil.dealWaterCategoryStatus(
                                this.data[0][0]
                            ).color,
                            width: 70,
                            height: 70,
                            borderRadius: 100
                        }
                    }
                ]
            };
        }
    }
};
</script>

<style lang="scss" scoped>
.gap {
    height: 10px;
    width: 100%;
}

.sw09-ultxt1 {
    padding: 0 15px;
}
.sw09-ultxt1 li {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.sw09-ultxt1 li strong {
    font-size: 16px;
    color: var(--font-color);
    background-repeat: no-repeat;
    background-position: left center;
    padding-left: 15px;
    position: relative;
}
.sw09-ultxt1 li strong.dot1:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #5cbb14;
    margin-top: -4px;
}
.sw09-ultxt1 li strong.dot2:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff3547;
    margin-top: -4px;
}
.sw09-ultxt1 li i img {
    margin-right: 3px;
}
.sw09-ultxt1 li i {
    font-family: 'DIN-Bold';
    font-size: 16px;
    color: var(--font-color);
    display: flex;
    align-items: center;
}
.sw09-ultxt1 li + li {
    margin-top: 15px;
    padding-left: 15px;
}
</style>
