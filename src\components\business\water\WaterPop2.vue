<!-- @format -->

<template>
    <div>
        <div class="mask" style="display: block"></div>
        <div class="pd-dlg dlg1">
            <div class="pd-dlghd">
                <span>xxx断面详情</span>
                <i class="dlgcls" @click="$emit('close')"></i>
            </div>
            <div class="pd-dlgbd">
                <div class="pd-bdlt">
                    <div class="gap"></div>
                    <div class="pd-path">桔子洲</div>
                    <div class="gap"></div>
                    <div class="pd-stithd">
                        <strong>基本信息</strong>
                    </div>
                    <div class="gap"></div>
                    <table  class="pd-table1a">
                        <colgroup>
                            <col width="40%" />
                        </colgroup>

                        <tr v-for="item in baseDataOpt" :key="item.value">
                            <td class="td-hd">{{ item.label }}：</td>
                            <td>{{ baseData[item.value] || '-' }}</td>
                        </tr>
                    </table>
                    <div class="gap"></div>
                    <div class="gap"></div>
                    <!-- <div class="pd-stithd">
                        <strong>测点标签</strong>
                    </div>
                    <div class="gap"></div>
                    <ul class="pd-ulbtn1">
                        <li>国控</li>
                        <li>长江经济带</li>
                        <li>全省211</li>
                    </ul>
                    <div class="gap"></div>
                    <div class="gap"></div> -->
                </div>
                <div class="pd-bdrt">
                    <div class="gap"></div>
                    <div class="pd-stithd">
                        <strong>水质状况<em>（2020年10月17日 14）</em></strong>
                    </div>
                    <div class="gap"></div>
                    <table  class="pd-tablelst1 otr">
                        <colgroup>
                            <col width="20%" />
                        </colgroup>
                        <thead>
                            <tr>
                                <td></td>
                                <td>当期</td>
                                <td>累计</td>
                                <td>同比</td>
                                <td>环比</td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="td-hd">水质类别</td>
                                <td style="color: #eebd15">Ⅳ类</td>
                                <td style="color: #eebd15">Ⅳ类</td>
                                <td style="color: #eebd15">Ⅳ类</td>
                                <td style="color: #eebd15">Ⅳ类</td>
                            </tr>
                            <tr>
                                <td class="td-hd">平均综合污染指数</td>
                                <td>12.414</td>
                                <td>12.414</td>
                                <td>12.414</td>
                                <td>12.414</td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="gap"></div>
                    <table  class="pd-table2a otr">
                        <colgroup>
                            <col width="" />
                            <col width="" />
                            <col width="" />
                        </colgroup>
                        <tr>
                            <td class="td-hd">功能目标：</td>
                            <td>Ⅱ类</td>
                            <td class="td-hd">超标污染物及浓度：</td>
                            <td>无</td>
                        </tr>
                        <tr>
                            <td class="td-hd">国考目标：</td>
                            <td>Ⅱ类</td>
                            <td class="td-hd">超标污染物及浓度：</td>
                            <td>氨氮（2.0）</td>
                        </tr>
                    </table>
                    <div class="gap"></div>
                    <table  class="pd-table2a">
                        <colgroup>
                            <col width="35%" />
                            <col width="" />
                            <col width="" />
                        </colgroup>
                        <tr>
                            <td>氨氮：0.23<em>Ⅲ类</em></td>
                            <td>高猛酸钾指数：1.3<em>Ⅱ类</em></td>
                            <td>总磷：0.23<em>Ⅱ类</em></td>
                        </tr>
                        <tr>
                            <td>五日生化需氧量：1.3<em>Ⅲ类</em></td>
                            <td>化学需氧量：4<em>Ⅱ类</em></td>
                            <td>阴离子表面活性剂：0.1<em>Ⅱ类</em></td>
                        </tr>
                    </table>
                    <div class="gap"></div>
                    <div class="pd-stithd">
                        <strong>主要污染物浓度变化</strong>
                    </div>
                    <div class="gap"></div>
                    <ul class="pd-ultbs3">
                        <li
                            :class="{ on: item.value === plt }"
                            v-for="(item, index) in pltList"
                            :key="index"
                            @click="pltChange(item.value)"
                        >
                            {{ item.name }}
                        </li>
                    </ul>
                    <div class="gap"></div>
                    <div class="layer-rili">
                        <table>
                            <thead>
                                <tr>
                                    <td>年度</td>
                                    <td>1月</td>
                                    <td>2月</td>
                                    <td>3月</td>
                                    <td>4月</td>
                                    <td>5月</td>
                                    <td>6月</td>
                                    <td>7月</td>
                                    <td>8月</td>
                                    <td>9月</td>
                                    <td>10月</td>
                                    <td>11月</td>
                                    <td>12月</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr
                                    v-for="(item, index) in szrlData"
                                    :key="index"
                                >
                                    <td>{{ item.year }}</td>
                                    <td
                                        v-for="(v, i) in item.data"
                                        :key="i"
                                        :style="{
                                            'background-color':
                                                getCategory(v).color
                                        }"
                                    >
                                        {{ getCategory(v).txt }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {},
    components: {},
    data() {
        return {
            baseData: {
                SSLY: '黄河流域',
                SSHL: 'xxx'
            }, //站点数据
            baseDataOpt: [
                { label: '所属流域', value: 'SSLY' },
                { label: '所属河流', value: 'SSHL' },
                { label: '所属行政区', value: 'XZQH' },
                { label: '考核级别', value: 'KHJB' },
                { label: '控制级别', value: 'KZJB' }
            ], //站点展示配置列表
            pltList: [
                { name: '氨氮', value: 'NH' },
                { name: '总磷', value: 'TP' },
                { name: '高锰酸盐指数', value: 'COMN' },
                { name: '化学需氧量', value: 'COD' },
                { name: '生化需氧量', value: 'BOD' }
            ],
            plt: 'NH',
            szrlData: [
                {
                    year: '2016',
                    data: [
                        '1',
                        '2',
                        '3',
                        '4',
                        '5',
                        '6',
                        '6',
                        '0',
                        '4',
                        '3',
                        '2',
                        '1'
                    ]
                },
                {
                    year: '2017',
                    data: [
                        '1',
                        '2',
                        '3',
                        '4',
                        '5',
                        '6',
                        '6',
                        '5',
                        '4',
                        '3',
                        '2',
                        '1'
                    ]
                }
            ]
        };
    },
    computed: {},
    watch: {},
    mounted() {},
    unmounted() {},
    methods: {
        pltChange(value) {
            this.plt = value;
        },
        getCategory(value) {
            let colors = [
                '#cccccc',
                '#44c5fd',
                '#51a5fd',
                '#73bb31',
                '#eebd15',
                '#f88e17',
                '#ee3b5b'
            ];
            switch (value) {
                case 1:
                case '1':
                case 'I':
                    return { txt: 'Ⅰ类', color: colors[1] };
                case 2:
                case '2':
                case 'II':
                    return { txt: 'Ⅱ类', color: colors[2] };
                case 3:
                case '3':
                case 'III':
                    return { txt: 'Ⅲ类', color: colors[3] };
                case 4:
                case '4':
                case 'IV':
                    return { txt: 'Ⅳ类', color: colors[4] };
                case 5:
                case '5':
                case 'V':
                    return { txt: 'Ⅴ类', color: colors[5] };
                case 6:
                case '6':
                    return { txt: '劣Ⅴ类', color: colors[6] };
                default: {
                    return { txt: '-', color: colors[0] };
                }
            }
        }
    }
};
</script>
<style lang="less">
.darkTheme {
    --txt-color: #fff;
    --bg-color-right: #062a3d;
    --bg-color-left: #062a3d;
    --bg-hd-color: #075393;
    --table-hd-color: #0a3c6e;
    --table-border-color: #004d8c;
}
.lightTheme {
    --txt-color: #333;
    --bg-color-right: #fff;
    --bg-color-left: #eff7ff;
    --bg-hd-color: #3f88d4;
    --table-hd-color: #f5f6fa;
    --table-border-color: #eeeeee;
}
</style>
<style scoped>
table {
    border-collapse: collapse;
}
.pd-dlg.dlg1 {
    width: 920px;
    height: 606px;
    margin: -303px 0 0 -460px;
}
.pd-dlg {
    position: absolute;
    left: 50%;
    top: 50%;
    background: #062a3d;
    border-radius: 5px;
    overflow: hidden;
    z-index: 1000;
}
.pd-dlghd {
    height: 40px;
    background: var(--bg-hd-color);
}
.pd-dlghd span {
    float: left;
    font-size: 16px;
    color: #fff;
    line-height: 40px;
    padding-left: 16px;
}
.pd-dlghd .dlgcls {
    float: right;
    width: 40px;
    height: 40px;
    background: url(~_as/images/close.png) no-repeat center;
    cursor: pointer;
}

.pd-dlgbd {
    position: absolute;
    left: 0;
    top: 40px;
    right: 0;
    bottom: 0;
}
.pd-bdlt {
    float: left;
    width: 270px;
    background: var(--bg-color-left);
    height: 100%;
    padding: 0 13px;
    box-sizing: border-box;
    border-right: 1px solid var(--bg-color-right);
}
.pd-stithd {
    overflow: hidden;
    border-bottom: 1px solid var(--table-border-color);
    padding-bottom: 5px;
}
.pd-stithd strong {
    float: left;
    font-size: 14px;
    /* color: #fff; */
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAARCAYAAAAougcOAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDE1QjRCMkM2MTZGMTFFQzlEQjQ5REQ2Q0YwNzk3RTEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDE1QjRCMkQ2MTZGMTFFQzlEQjQ5REQ2Q0YwNzk3RTEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpEMTVCNEIyQTYxNkYxMUVDOURCNDlERDZDRjA3OTdFMSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpEMTVCNEIyQjYxNkYxMUVDOURCNDlERDZDRjA3OTdFMSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PpSRqf4AAAIPSURBVHjalJVPaxNBHIbfnf2TZLONjdgYSJs0QU/1IoKCFEzBi731lpvST9Ae2w/QHttjL4XWU0HwG0gqqEE96EE8ysYg+OegIO0GscZ3ppuk3cwm6Q+enczM7jw7M7/ZGNjsQBcvasD8AUr8uU+WWf+kue0qWSFb5DtiQsDkVcP8EwpM1Mk9WbJe0Qg2yQ2yQXIXlXQF5bBeVKKnPVE+FExFhDm9xOb1PCVSJ+VIe1G273zEnfDNpyJj5eJEAhavfUrkkJQj7YoZD8W5LJ4Ff9Ve6UIrMiZ3exsvHzwks9qnHeDhNWCCs7INBNcvoZE0cRwjk0mw1k0Go/RYSeTA9TjBZQpq7PHsfpstEFQmxhMZcwedoYIsB14qAmlrsM/ijMoeGokRIstzsBcnkPGgAGQcfR/XIPUlwM2Kh5dD9mhVeAk8Ik0CHW9/AX/+MYuNQRyB9rSLd4gPOZNt4TrwSZU0CaKcCJ5+KeJrm6KPY6I94+IVRaP25JuVPl0Kn1Tj9kamxpvfwN1JICnULIK8g4Ytxssuy+2vtxQtDEuC90fA7QyOCgm8ZhqPJVCHMcXsOYNPFsISUTomWj9PcJ+Cz+MK1EyS9sCN3aWLHsyWbL+VVl9jPzzZV0YJ1EwSzH8NTVIlflhvyfpSpve5/xoO+GOUQEmYJdCx6KLJskqey5L16P+JFK2TD8MEMv4LMABFpYel0nULCQAAAABJRU5ErkJggg==)
        no-repeat left center;
    padding-left: 30px;
}
.pd-stithd i {
    float: right;
    font-size: 14px;
    color: #999;
}
.pd-table1a {
    width: 100%;
}
.pd-table1a tr td {
    font-size: 14px;
    /* color: #fff; */
    padding: 0 4px;
}
.pd-table1a tr td.td-hd {
    /* color: #fff; */
    text-align: right;
}
.pd-table1a tr + tr td {
    padding-top: 8px;
}
.pd-ulbtn1 {
    overflow: hidden;
}
.pd-ulbtn1 li {
    float: left;
    height: 28px;
    line-height: 28px;
    padding: 0 12px;
    font-size: 14px;
    color: #3f88d4;
    background: #c5e2ff;
    border-radius: 300px;
}
.pd-ulbtn1 li + li {
    margin-left: 10px;
}
.pd-path {
    /* background: url(../images/pathic1.png) no-repeat left center; */
    /* padding-left: 25px; */
    font-size: 16px;
    color: #3f88d4;
}

.pd-bdrt {
    overflow: hidden;
    padding: 0 10px;
    height: 100%;
    background-color: var(--bg-color-right);
}
.pd-bdrt .pd-stithd {
    border-bottom: none;
}
.pd-bdrt .pd-stithd strong em {
    font-weight: normal;
}

.pd-table2a {
    width: 100%;
    border: 1px solid var(--table-border-color);
}
.pd-table2a tr td i {
    color: #73bb31;
}
.pd-table2a tr td em {
    color: #44c5fd;
    margin-left: 10px;
}
.pd-table2a tr td {
    font-size: 14px;
    /* color: #fff; */
    padding: 10px 15px;
}

.pd-table2a.otr tr td img {
    vertical-align: middle;
}
.pd-table2a.otr tr td {
    padding: 5px 0;
}
.pd-table2a.otr tr td.td-hd {
    text-align: right;
}
.pd-ultbs3 {
    text-align: center;
    font-size: 0;
}
.pd-ultbs3 li {
    display: inline-block;
    height: 26px;
    line-height: 26px;
    border-radius: 300px;
    border: 1px solid #3f88d4;
    font-size: 14px;
    color: var(--txt-color);
    padding: 0 15px;
    margin: 0 5px;
}
.pd-ultbs3 li.on {
    background: #3f88d4;
    color: #fff;
}
.pd-tablelst1 {
    width: 100%;
    border: 1px solid var(--table-border-color);
}
.pd-tablelst1 tr td {
    font-size: 14px;
    /* color: #fff; */
    text-align: center;
    height: 40px;
    border-bottom: 1px solid var(--table-border-color);
}
.pd-tablelst1 tr td.td-hd {
    text-align: right;
}
.pd-tablelst1 thead tr td {
    background: var(--table-hd-color);
    /* color: #fff; */
    border-right: 1px solid var(--table-border-color);
}
.pd-tablelst1 tbody tr.on td {
    background: #07427b;
}
.pd-tablelst1.otr tr td {
    border-right: none;
    padding: 0;
}

/* .layer-rili {
    color: #fff;
} */
.layer-rili td:nth-child(1) {
    padding: 12px 5px;
}
.layer-rili td {
    text-align: center;
    width: 55px;
    padding: 12px 0;
    border: 1px solid var(--table-border-color);
}
</style>
