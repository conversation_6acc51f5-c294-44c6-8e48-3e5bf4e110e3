function MathUtil(params) {
    Object.assign(this, params);
}
// 高斯模糊
MathUtil.getGussNumArray = function (length) {
    const glb_gauss_num = function (x) {
        let pi = 3.1415927;
        let e = 2.71828;
        let theta = 0.1;
        let theta2 = theta * theta;
        let temp1 = 1.0 / (theta * Math.sqrt(2 * pi));
        let temp2 = Math.pow(e, (-(x * x) / 2) * theta2);
        return temp1 * temp2;
    };
    let g_GaussNum = [];

    g_GaussNum[0] = 1.0;
    for (var i = 1; i < length; i++) {
        g_GaussNum[i] = glb_gauss_num(i);
    }

    let total = 0.0;
    for (var i = 0; i < length; i++) {
        total += g_GaussNum[i];
    }

    for (var i = 0; i < length; i++) {
        g_GaussNum[i] = g_GaussNum[i] / total;
    }
    return g_GaussNum;
};
export default MathUtil;
