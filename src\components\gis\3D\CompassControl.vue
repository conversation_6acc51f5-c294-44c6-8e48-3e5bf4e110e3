<!-- @format -->
<!--全屏 -->
<template>
    <ul class="zy-tools-gis">
        <li>
            <i
                class="tool-Compass"
                ref="toolCompass"
                @click="goBack"
                title="指北针"
            ></i>
        </li>
    </ul>
</template>

<script>
export default {
    name: 'CompassControl', //指北针
    props: ['map'],
    data() {
        return {};
    },
    components: {},
    computed: {},
    mounted() {
        this.initPage();
    },
    methods: {
        initPage() {
            if (this.map) {
                this.syncRotate();
                this.map.on('rotate', this.syncRotate);
            } else {
                setTimeout(() => {
                    this.initPage();
                }, 1000);
            }
        },

        syncRotate() {
            const angle = this.map.getBearing() * -1;
            this.$refs.toolCompass.style.transform = `rotate(${angle}deg)`;
        },

        //返回正北
        goBack() {
            this.map.setBearing(0);
        }
    },
    watch: {}
};
</script>

<style scoped></style>
