<!-- @format -->
<!-- 小时进度条，支持多天、间隔小时等，一般用于水环境 -->
<template>
    <div>
        <div class="gis-timeline">
            <img
                @click="playClick"
                v-show="!timeId && showPlay"
                :src="iconPlay"
                class="pd-play"
                alt="渲染失败"
            />

            <img
                v-show="timeId && showPlay"
                @click="pauseClick"
                :src="iconPause"
                class="pd-play"
                alt="渲染失败"
            />
            <el-date-picker
                v-if="showDatePick"
                v-model="selectDate"
                type="date"
                placeholder="选择日期"
                :clearable="false"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                style="width: 150px; margin-top: 14px"
                @change="selectDateChanged"
            >
            </el-date-picker>
            <div class="slide">
                <div
                    class="item"
                    style="width: 100%"
                    v-for="(item, index) of arrDate"
                    :key="index"
                >
                    <ol class="colorBar">
                        <li
                            v-for="(hour, ii) of item.hours"
                            :key="index + ii"
                            :sj="item.day + ' ' + hour.data"
                            :class="[
                                {
                                    on: dateStr == item.day + ' ' + hour.data
                                },
                                hour.clsName
                            ]"
                            @click="itemClick(item, hour.data, index, ii)"
                        >
                            <sup>{{ item.day + ' ' + hour.data }}</sup>
                        </li>
                    </ol>
                    <ol class="calendar">
                        <li
                            v-for="(hour, ii) of item.hours"
                            :key="index + ii"
                            v-show="labelShow"
                        >
                            {{ parseInt(hour.data) }}
                        </li>
                    </ol>
                    <p>{{ item.day }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'TimeLine',
    props: [],
    data() {
        return {
            timeId: null,
            selectDate: '', //时期组件选择时间
            arrDate: [], // 进度条上的时间
            dateStr: '', //当前选中时间
            initDate: '', //初始时间
            initSelectDate: '', //初始选中时间
            days: 5, //天数
            gap: 1, // 小时间隔
            labelShow: true, // 是否显示小时
            showDatePick: false, // 是否显示日期控件
            showPlay: false, // 是否显示播放控件
            selectIndex: 0, //当前选中的序号

            iconPlay: require('@/assets/gis/commom/images/dark/gis_plybtn.png'),
            iconPause: require('@/assets/gis/commom/images/dark/gis_pausebtn.png')
        };
    },
    components: {},
    computed: {},
    unmounted() {
        this.clearTime();
    },
    mounted() {
        let theme = window.localStorage.getItem('themeType');
        if (theme == 'light') {
            this.iconPlay = require('@/assets/gis/commom/images/light/gis_light_plybtn.png');
            this.iconPause = require('@/assets/gis/commom/images/light/gis_light_pausebtn.png');
        }
    },
    methods: {
        selectDateChanged() {
            this.$emit('datePickChange', this.dateStr);

            //初始化进度条时间
            this.initData();
        },

        /**
         *时间轴的初始化
         */
        initTimeLine(option) {
            //初始时间， 在表示过去的时间轴时，也代表最大的时间 ，
            //比如 2022-05-10 12  表示最大时间为12点， 初始日期为 2022-05-10, 最大时间之后为灰色
            this.initDate = option.initDate;

            //初始选中时间
            this.initSelectDate = option.selectDate;

            this.selectDate = this.initDate.substring(0, 10);

            this.days = option.days; //为正，表示未来的时间(一般用于预测)， 为负表示过去的时间
            this.gap = option.gap;

            if (option.labelShow != undefined) {
                this.labelShow = option.labelShow;
            }

            if (option.showDatePick != undefined) {
                this.showDatePick = option.showDatePick;
            }

            if (option.showPlay != undefined) {
                this.showPlay = option.showPlay;
            }

            this.initData();
        },

        initData() {
            let result = [];

            let dd = Math.abs(this.days);

            let index = -1;

            //为正，表示未来的时间， 为负表示过去的时间
            if (this.days > 0) {
                for (let i = 0; i < dd; i++) {
                    let day = this.$dayjs(new Date(this.selectDate))
                        .add(i, 'day')
                        .format('YYYY-MM-DD');

                    let hours = [];
                    for (let j = 0; j <= 23; j++) {
                        if (j % this.gap == 0) {
                            let hour = j >= 10 ? j : '0' + j;

                            let sj = day + ' ' + hour;

                            hours.push({
                                data: hour,
                                clsName: 'init'
                            });

                            if (sj == this.initSelectDate) {
                                index = (i * 24) / this.gap + hours.length - 1;
                            }
                        }
                    }

                    result.push({
                        day: day,
                        hours: hours
                    });
                }

                if (index > 0) {
                    this.dateStr = this.initSelectDate;
                    this.selectIndex = index;
                } else {
                    this.dateStr =
                        result[0].day + ' ' + result[0].hours[0].data;
                    this.selectIndex = 0;
                }
            } else {
                for (let i = dd - 1; i >= 0; i--) {
                    let day = this.$dayjs(new Date(this.selectDate))
                        .add(-i, 'day')
                        .format('YYYY-MM-DD');

                    let hours = [];
                    for (let j = 0; j <= 23; j++) {
                        if (j % this.gap == 0) {
                            let hour = j >= 10 ? j : '0' + j;

                            let sj = day + ' ' + hour;

                            //在最新时间（initDate）之后
                            let flag = this.$dayjs(sj + ':00:00').isAfter(
                                this.$dayjs(this.initDate + ':00:00')
                            );

                            hours.push({
                                data: hour,
                                clsName: flag ? 'gray' : 'init'
                            });
                            if (sj == this.initSelectDate) {
                                index =
                                    ((dd - 1 - i) * 24) / this.gap +
                                    hours.length -
                                    1;
                            }
                        }
                    }

                    result.push({
                        day: day,
                        hours: hours
                    });
                }

                if (index >= 0) {
                    this.dateStr = this.initSelectDate;
                    this.selectIndex = index;
                } else {
                    let len = result.length - 1;
                    let len2 = result[len].hours.length - 1;
                    this.dateStr =
                        result[len].day + ' ' + result[len].hours[len2].data;
                    this.selectIndex = len * 24 + len2;
                }
            }

            this.arrDate = result;
            console.log(this.dateStr);

            this.$emit('dateChange', this.dateStr, false);
        },
        //设置进度条的状态
        setCls(arr) {
            // let arr = [{ date: '2022-01-07 12', clsName: 'cb' }];

            for (let item of this.arrDate) {
                for (let obj of item.hours) {
                    let sj = item.day + ' ' + obj.data;

                    let result = arr.filter((oo) => {
                        return oo.date == sj;
                    });

                    if (result && result[0]) {
                        obj.clsName = result[0].clsName;
                    } else {
                        obj.clsName = 'gray';
                    }
                }
            }
        },

        itemClick(item, hour, index, ii) {
            this.selectIndex = (index * 24) / this.gap + ii;

            //手动点击后，播放暂停
            this.clearTime();

            this.dateStr = item.day + ' ' + hour;
            this.$emit('dateChange', this.dateStr, false);
        },

        //暂停
        pauseClick() {
            this.clearTime();
        },

        //播放按钮点击
        playClick() {
            //如果正在播放，则停止
            if (this.timeId) {
                this.clearTime();
                return;
            }

            let max = (Math.abs(this.days) * 24) / this.gap;

            let flag1 = this.$dayjs(this.dateStr + ':00:00').isAfter(
                this.$dayjs(this.initDate + ':00:00')
            );

            let flag2 = this.$dayjs(this.dateStr + ':00:00').isSame(
                this.$dayjs(this.initDate + ':00:00')
            );

            //如果当前时间在最新时间的后面，则从第一个开始播放
            if (flag1 || flag2 || this.selectIndex == max - 1) {
                this.selectIndex = 0;
            }

            this.timeId = setInterval(() => {
                this.selectIndex++;

                this.getDateStr();

                this.$emit('dateChange', this.dateStr, true);

                let flag = this.$dayjs(this.dateStr + ':00:00').isSame(
                    this.$dayjs(this.initDate + ':00:00')
                );

                if (this.selectIndex == max - 1 || flag) {
                    this.clearTime();
                }
            }, 1000);
        },

        getDateStr() {
            let index = Math.ceil(this.selectIndex / (24 / this.gap));
            let ii = this.selectIndex % (24 / this.gap);

            if (ii != 0) {
                index = Math.ceil(this.selectIndex / (24 / this.gap)) - 1;
            }

            this.dateStr =
                this.arrDate[index].day +
                ' ' +
                this.arrDate[index].hours[ii].data;

            console.log(this.dateStr);
        },

        //清除定时器
        clearTime() {
            if (this.timeId) {
                clearInterval(this.timeId);
                this.timeId = null;
            }
        }
    },
    watch: {}
};
</script>

<style scoped></style>
