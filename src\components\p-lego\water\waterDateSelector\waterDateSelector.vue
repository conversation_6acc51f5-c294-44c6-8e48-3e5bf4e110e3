<!-- @format -->

<template>
    <!-- 水环境常用时间选择组件 -->
    <div class="water-date-selector">
        <label>时间：</label>
        <ul class="water-date-selector-btns" v-if="option.typeMode === 'btns'">
            <li
                v-for="item in dateTypeOpts"
                :class="{ cur: dateType === item.value }"
                :key="item.value"
                @click="
                    dateType = item.value;
                    changeDate();
                "
            >
                {{ item.label }}
            </li>
        </ul>

        <el-select
            v-else
            style="width: 90px; margin-right: 15px"
            @change="changeDate"
            v-model="dateType"
            placeholder="请选择"
        >
            <el-option
                v-for="item in dateTypeOpts"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
        </el-select>

        <el-date-picker
            @change="changeDate"
            format="YYYY"
            value-format="YYYY"
            v-if="dateType == 'YEAR' || dateType == 'JD'"
            v-model="year"
            :clearable="false"
            type="year"
            style="width: 100px"
            placeholder="选择日期时间"
        >
        </el-date-picker>
        <el-date-picker
            format="YYYY-MM"
            value-format="YYYY-MM"
            @change="changeDate"
            v-if="dateType == 'MONTH' || dateType == 'LJ'"
            v-model="month"
            :clearable="false"
            type="month"
            style="width: 130px"
            placeholder="选择日期时间"
        >
        </el-date-picker>

        <el-select
            style="width: 90px"
            v-if="dateType == 'JD'"
            @change="changeDate"
            v-model="seasonType"
            :clearable="false"
            placeholder="请选择"
        >
            <el-option
                v-for="item in seasonOpt"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
        </el-select>
    </div>
</template>

<script>
export default {
    name: 'DateSelector',
    props: {
        option: {
            //配置项
            type: Object,
            default: function () {
                return {
                    dateType: '', //时间类型
                    date: '', //选中的时间  如 '2021-11-01'
                    dateTypeOpts: [], //时间类型选项
                    typeMode: 'select' //时间选项展现形式 默认为下拉形式'select' 可选按钮组形式'btns'
                };
            }
        }
    },
    data() {
        return {
            dateType: 'MONTH',
            dateTypeOpts: [],
            defaultDateTypeOpts: [
                {
                    value: 'MONTH',
                    label: '月'
                },
                {
                    value: 'LJ',
                    label: '累计月'
                },
                {
                    value: 'JD',
                    label: '季度'
                },
                {
                    value: 'YEAR',
                    label: '年'
                }
                /*  {
                    value: 'SQ',
                    label: '水期'
                } */
            ],
            year: this.$dayjs(new Date()).format('YYYY'),
            month: this.$dayjs(new Date()).format('YYYY-MM'),
            seasonOpt: [
                {
                    value: '1',
                    label: '1季度'
                },
                {
                    value: '2',
                    label: '2季度'
                },
                {
                    value: '3',
                    label: '3季度'
                },
                {
                    value: '4',
                    label: '4季度'
                }
            ],
            seasonType: '1'
        };
    },
    watch: {
        option: {
            deep: true,
            handler: 'init'
        }
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            this.dateType = this.option.dateType || this.dateType;
            this.dateTypeOpts =
                this.option.dateTypeOpts || this.defaultDateTypeOpts;
            let date = this.option.date;
            if (date) {
                this.year = date.slice(0, 4);
                this.month = date.slice(0, 7);
                if (this.dateType === 'JD') {
                    let m = Number(date.split('-')[1]);
                    this.seasonType = Math.ceil(m / 3) + '';
                }
            } else {
                this.month = this.$dayjs().format('YYYY-MM');
                this.year = this.month.slice(0, 4);
            }

            this.changeDate();
        },
        changeDate() {
            this.dealTime();
            this.$emit('change', {
                dateType: this.dateType,
                startDate: this.startDate,
                endDate: this.endDate,
                season: this.dateType === 'JD' ? this.seasonType : ''
            });
        },
        dealTime() {
            switch (this.dateType) {
                case 'MONTH':
                    this.startDate = this.month + '-01';
                    this.endDate = this.$dayjs(this.month)
                        .endOf('month')
                        .format('YYYY-MM-DD');
                    this.gisDate = this.month;
                    break;
                case 'LJ':
                    this.startDate = this.month.slice(0, 4) + '-01-01';
                    this.endDate = this.$dayjs(this.month)
                        .endOf('month')
                        .format('YYYY-MM-DD');
                    this.gisDate = this.month;
                    break;
                case 'YEAR':
                    this.startDate = this.year + '-01-01';
                    this.endDate = this.year + '-12-31';
                    this.gisDate = this.year;
                    break;
                case 'JD':
                    switch (this.seasonType) {
                        case '1':
                        case 1:
                            this.startDate = this.year + '-01-01';
                            this.endDate = this.year + '-03-31';

                            break;
                        case '2':
                        case 2:
                            this.startDate = this.year + '-04-01';
                            this.endDate = this.year + '-06-30';

                            break;
                        case '3':
                        case 3:
                            this.startDate = this.year + '-07-01';
                            this.endDate = this.year + '-09-31';

                            break;
                        case '4':
                        case 4:
                            this.startDate = this.year + '-10-01';
                            this.endDate = this.year + '-12-31';

                            break;
                    }
                    break;
            }
        }
    }
};
</script>

<style scoped lang="less">
.water-date-selector {
    display: flex;
    align-items: center;

    label {
        font-size: 16px;
    }

    &-btns {
        display: flex;
        border: 1px solid var(--el-color-primary);
        margin: 0;
        margin-right: 15px;
        list-style: none;
        padding: 0;
        border-radius: 4px;
        li {
            font-size: 16px;
            line-height: 34px;
            height: 34px;
            text-align: center;
            padding: 0 15px;
            margin: 0;
            cursor: pointer;
        }
        li:not(:last-child) {
            border-right: 1px solid var(--el-color-primary);
        }

        li.cur {
            background-color: var(--el-color-primary);
            color: #fff;
        }
    }

    ::v-deep .el-input__inner,
    ::v-deep .el-input {
        height: 36px;
        line-height: 36px;
    }
    ::v-deep .el-input__icon {
        line-height: 36px;
    }
}
</style>
