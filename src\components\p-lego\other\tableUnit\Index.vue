<!-- @format -->

<template>
    <div>
        <div class="lstwrap">
            <table class="yy-table fixed-table" style="table-layout: fixed">
                <thead>
                    <tr :class="tableStyle.border ? 'bottom' : ''">
                        <th style="width: 52px">序号</th>
                        <th
                            v-for="(item, index) in finalOption.listObj"
                            :key="index"
                        >
                            {{ item.head }}
                        </th>
                        <th style="width: 120px" v-if="finalOption.showHandel">
                            操作
                        </th>
                    </tr>
                </thead>
                <tbody v-if="list.length > 0">
                    <tr
                        v-for="(item, index) in list"
                        :key="index"
                        :class="tableStyle.border ? 'bottom' : ''"
                    >
                        <td style="width: 45px" v-if="finalOption.autoIndex">
                            {{ getIndex(index + 1) }}
                        </td>
                        <td style="width: 45px" v-else>
                            {{ index + 1 }}
                        </td>
                        <td
                            v-for="(td, idx) in finalOption.listObj"
                            :key="idx"
                            :style="{
                                width: td.width + 'px'
                            }"
                        >
                            <span v-if="!td.textOverflow">{{
                                item[td.key] || '-'
                            }}</span>
                            <div
                                v-if="td.textOverflow"
                                :title="item[td.key]"
                                class="fixed-width"
                                style="width: 99%"
                            >
                                {{ item[td.key] || '-' }}
                            </div>
                        </td>
                        <td
                            style="
                                width: 120px;
                                cursor: pointer;
                                color: #0393ff;
                            "
                            v-if="finalOption.showHandel"
                        >
                            <span
                                v-for="(name, idx2) in finalOption.handel"
                                :key="idx2"
                                @click="handelChange(item, name)"
                                >{{ name.name }}</span
                            >
                        </td>
                    </tr>
                </tbody>
                <tbody v-if="list.length === 0">
                    <tr style="text-align: center">
                        <td :colspan="finalOption.listObj.length + 2">
                            暂无数据
                        </td>
                    </tr>
                </tbody>
            </table>

            <div class="zy-fenye">
                <el-pagination
                    @current-change="handleCurrentChange"
                    :current-page="finalOption.pageNum"
                    :page-size="finalOption.pageSize"
                    layout="total, prev, pager, next, jumper"
                    :total="finalOption.listTotal"
                >
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        options: Object,
        tableData: {
            type: Array,
            default: () => {
                return [];
            }
        },
        tableStyle: {
            type: Object,
            default: () => {
                return {
                    border: false, // true带边框、默认false不带边框
                    thColor: '#06406d'
                };
            }
        }
    },
    data() {
        return {
            defaultOpts: {
                showHandel: false, // 是否展示操作项
                // 操作数组
                handel: [
                    {
                        name: '选择',
                        value: 'XZ'
                    },
                    {
                        name: '删除',
                        value: 'SC'
                    }
                ],
                autoIndex: true, // true 序号会随着页码递增。 false 不需要递增
                // 列表表头、取值
                listObj: [],

                listTotal: 0, // 数据总数
                pageSize: 10, // 分页
                pageNum: 1 // 分页
            },
            list: []
        };
    },
    watch: {
        tableData: {
            handler(val) {
                console.log('---监听---', val);
                this.list = val;
            },
            immediate: true
        }
    },
    computed: {
        finalOption() {
            return this.options
                ? {
                      ...this.defaultOpts,
                      ...this.options
                  }
                : this.defaultOpts;
        },
        // 序号随着页数递增
        getIndex() {
            return (index) => {
                let num = (this.finalOption.pageNum || 1) - 1;
                let i = num * this.finalOption.pageSize + index;
                return i;
            };
        }
    },
    created() {},
    mounted() {
        // this.initData();
    },
    methods: {
        // 合并相同QYMC列
        getRowCount(QYMC) {
            return this.list.filter((item) => item.QYMC === QYMC).length;
        },
        initData() {
            // 初始化数据
        },
        // 分页
        handleCurrentChange(val) {
            this.$emit('handleCurrentChange', val);
        },
        // 操作项事件
        handelChange(item, curHandel) {
            // item: 列表行数据、curHandel：操作按钮类型
            this.$emit('handelChange', item, curHandel);
        },
        reset() {},
        close() {
            this.$emit('close');
        }
    }
};
</script>

<style scoped>
.lstwrap {
    margin: 0 20px;
    height: 700px;
    overflow: hidden;
    background-color: #053360;
}
.yy-table {
    width: 100%;
    border: solid 1px #155a8d;
}

.yy-table td {
    font-size: 16px;
    color: #fff;
    height: 50px;
    padding: 0 5px;
    vertical-align: middle;
    text-align: center;
}

.yy-table th {
    font-size: 16px;
    color: #fff;
    height: 50px;
    padding: 0 5px;
    box-sizing: border-box;
    vertical-align: middle;
    background-color: #06406d;
}
.yy-table .bottom th {
    border: solid 1px #145a8d;
}
.yy-table .bottom td {
    border: solid 1px #0051a2;
}
.zy-fenye {
    position: absolute;
    bottom: 40px;
    left: 0;
    right: 40px;
    display: flex;
    justify-content: flex-end;
}
.flx1 >>> .el-range-editor.el-input__wrapper {
    width: 250px;
}
</style>
