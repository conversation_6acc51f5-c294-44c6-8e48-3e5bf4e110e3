/** @format */

export default `#ifdef GL_FRAGMENT_PRECISION_HIGH
	precision highp float;
#else
	precision mediump float;
#endif

uniform sampler2D u_Texture;
uniform float tex_height;  
const int stretch_height = {stretch_height};
const int stretch_offset = {stretch_offset};

varying vec2 v_UV;
void main(){

	vec4 color = vec4(texture2D(u_Texture, v_UV).rgb,1.0);
    float step = 1.0 / tex_height;

    bool inStretch = false;
    for (int i = stretch_offset; i <= stretch_height; i++) {
        float y = v_UV.y + float(i) * step;
        if (y <= 1.0) {  
            vec4 texColor = texture2D(u_Texture, vec2(v_UV.x, y));
            if(texColor.a>0.8){
                inStretch = true;
                break;
            }
        }
    }
    if(inStretch){
    	gl_FragColor = color;
    }else{
        discard;
    }
}`;
